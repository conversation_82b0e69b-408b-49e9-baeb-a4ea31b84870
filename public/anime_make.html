<!DOCTYPE html>
<html>
<head>
    <title>Binary Tree Visualization</title>
    <style>
        canvas {
            border: 1px solid #2c3e50;
            border-radius: 8px;
            background: #ecf0f1;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .container {
            text-align: center;
            padding: 20px;
            font-family: 'Arial', sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 style="color: #34495e;">Interactive Binary Tree</h2>
        <canvas id="treeCanvas" width="800" height="600"></canvas>
        <p style="color: #7f8c8d;">点击节点展开/折叠子树 | 右键添加子节点</p>
    </div>

    <script>
        class TreeNode {
            constructor(value, x, y, parent = null) {
                this.value = value;
                this.left = null;
                this.right = null;
                this.x = x;
                this.y = y;
                this.targetX = x;
                this.targetY = y;
                this.parent = parent;
                this.collapsed = false;
                this.radius = 20;
            }
        }

        class BinaryTree {
            constructor() {
                this.root = null;
                this.nodeSpacingX = 200;
                this.nodeSpacingY = 80;
            }

            insert(value) {
                if (!this.root) {
                    this.root = new TreeNode(value, 400, 50);
                    return;
                }
                this.insertNode(this.root, value);
            }

            insertNode(node, value) {
                if (value < node.value) {
                    if (!node.left) {
                        node.left = new TreeNode(value, node.x - this.nodeSpacingX, node.y + this.nodeSpacingY, node);
                    } else {
                        this.insertNode(node.left, value);
                    }
                } else {
                    if (!node.right) {
                        node.right = new TreeNode(value, node.x + this.nodeSpacingX, node.y + this.nodeSpacingY, node);
                    } else {
                        this.insertNode(node.right, value);
                    }
                }
            }
        }

        const canvas = document.getElementById('treeCanvas');
        const ctx = canvas.getContext('2d');
        const tree = new BinaryTree();
        let selectedNode = null;

        // 生成示例树
        [8, 3, 10, 1, 6, 14, 4, 7, 13].forEach(num => tree.insert(num));

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updatePositions(tree.root);
            drawNode(tree.root);
            requestAnimationFrame(animate);
        }

        function updatePositions(node) {
            if (!node) return;
            node.x += (node.targetX - node.x) * 0.1;
            node.y += (node.targetY - node.y) * 0.1;
            updatePositions(node.left);
            updatePositions(node.right);
        }

        function drawNode(node) {
            if (!node) return;

            // 绘制连接线
            if (node.left && !node.collapsed) {
                ctx.beginPath();
                ctx.moveTo(node.x, node.y + node.radius);
                ctx.lineTo(node.left.x, node.left.y - node.radius);
                ctx.strokeStyle = '#95a5a6';
                ctx.lineWidth = 2;
                ctx.stroke();
            }
            if (node.right && !node.collapsed) {
                ctx.beginPath();
                ctx.moveTo(node.x, node.y + node.radius);
                ctx.lineTo(node.right.x, node.right.y - node.radius);
                ctx.strokeStyle = '#95a5a6';
                ctx.lineWidth = 2;
                ctx.stroke();
            }

            // 绘制节点
            ctx.fillStyle = node === selectedNode ? '#e74c3c' : '#3498db';
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(node.value, node.x, node.y);

            if (!node.collapsed) {
                drawNode(node.left);
                drawNode(node.right);
            }
        }

        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            selectedNode = findNode(tree.root, x, y);
            if (selectedNode) {
                selectedNode.collapsed = !selectedNode.collapsed;
                collapseChildren(selectedNode, selectedNode.collapsed);
            }
        });

        canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            if (selectedNode) {
                const value = Math.floor(Math.random() * 100);
                if (!selectedNode.left) {
                    selectedNode.left = new TreeNode(
                        value,
                        selectedNode.x - tree.nodeSpacingX,
                        selectedNode.y + tree.nodeSpacingY,
                        selectedNode
                    );
                } else if (!selectedNode.right) {
                    selectedNode.right = new TreeNode(
                        value,
                        selectedNode.x + tree.nodeSpacingX,
                        selectedNode.y + tree.nodeSpacingY,
                        selectedNode
                    );
                }
            }
        });

        function findNode(node, x, y) {
            if (!node) return null;
            if (Math.sqrt((x - node.x)**2 + (y - node.y)**2) < node.radius) {
                return node;
            }
            return findNode(node.left, x, y) || findNode(node.right, x, y);
        }

        function collapseChildren(node, collapse) {
            if (node.left) node.left.targetX = collapse ? node.x : node.x - tree.nodeSpacingX;
            if (node.right) node.right.targetY = collapse ? node.y : node.y + tree.nodeSpacingY;
            if (node.left) collapseChildren(node.left, collapse);
            if (node.right) collapseChildren(node.right, collapse);
        }

        animate();
    </script>
</body>
</html>
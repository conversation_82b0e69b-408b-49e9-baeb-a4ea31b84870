import express from 'express';
import cors from 'cors';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';
import mysql from 'mysql2/promise';
import history from 'connect-history-api-fallback';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 判断环境
const isDevelopment = process.env.NODE_ENV !== 'production';

// 数据库配置
const dbConfig = {
  host: '**************',
  user: 'root',
  password: 'mydb123',
  database: 'teaching_assistant',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

const app = express();

// API中间件应该在history中间件之前
app.use(cors());
app.use(express.json());

// 添加请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// 定义API路由
// API接口 - 获取所有模型配置
app.get('/models', async (req, res) => {
  try {
    console.log('获取模型列表请求');
    const [rows] = await pool.execute('SELECT * FROM model_configs ORDER BY id');
    console.log('查询结果：', rows);
    
    res.json(rows.map(row => ({
      id: row.id,
      name: row.name,
      provider: row.provider,
      apiKey: row.api_key,
      appId: row.app_id,
      baseUrl: row.base_url,
      usageScenario: row.usage_scenario,
      status: row.status === 1 ? '正常' : '禁用'
    })));
  } catch (error) {
    console.error('获取模型列表失败:', error);
    res.status(500).json({ error: '获取模型列表失败' });
  }
});

// 添加新模型
app.post('/models', async (req, res) => {
  try {
    const { name, provider, apiKey, appId, baseUrl, usageScenario, status } = req.body;
    
    const [result] = await pool.execute(
      'INSERT INTO model_configs (name, provider, api_key, app_id, base_url, usage_scenario, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [name, provider, apiKey, appId || null, baseUrl, usageScenario, status ? 1 : 0]
    );
    
    res.status(201).json({
      id: result.insertId,
      name, 
      provider,
      apiKey: '******',
      appId,
      baseUrl,
      usageScenario,
      status
    });
  } catch (error) {
    console.error('添加模型失败:', error);
    res.status(500).json({ error: '添加模型失败' });
  }
});

// 更新模型
app.put('/models/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, provider, apiKey, appId, baseUrl, usageScenario, status } = req.body;
    
    let sql = 'UPDATE model_configs SET name = ?, provider = ?, base_url = ?, usage_scenario = ?, status = ?';
    const params = [name, provider, baseUrl, usageScenario, status ? 1 : 0];
    
    if (apiKey) {
      sql += ', api_key = ?';
      params.push(apiKey);
    }
    
    sql += ', app_id = ? WHERE id = ?';
    params.push(appId || null, id);
    
    await pool.execute(sql, params);
    
    res.json({
      id: parseInt(id),
      name,
      provider,
      apiKey: '******',
      appId,
      baseUrl,
      usageScenario,
      status
    });
  } catch (error) {
    console.error('更新模型失败:', error);
    res.status(500).json({ error: '更新模型失败' });
  }
});

// 测试API连接
app.post('/models/test', async (req, res) => {
  try {
    // 这里只是简单模拟测试过程
    const { provider, apiKey, baseUrl } = req.body;
    
    // 延迟1秒模拟API测试
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 如果API密钥和URL不为空，认为测试成功
    if (apiKey && baseUrl) {
      res.json({
        success: true,
        responseTime: Math.floor(Math.random() * 500) + 100,
        modelResponse: `${provider} API连接成功，服务可用`
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'API连接失败：API密钥或基础URL无效'
      });
    }
  } catch (error) {
    console.error('测试API连接失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '测试API连接失败: ' + error.message 
    });
  }
});

// 同步到.env文件
app.post('/models/sync', async (req, res) => {
  try {
    const { ARK_API_KEY, DEEPSEEK_API_KEY, MOONSHOT_API_KEY, XFYUN_APPID, XFYUN_SECRET_KEY } = req.body;
    
    // 构建.env文件内容
    let envContent = '# API密钥\n';
    
    if (ARK_API_KEY) {
      envContent += `ARK_API_KEY=${ARK_API_KEY}\n`;
    }
    
    if (DEEPSEEK_API_KEY) {
      envContent += `DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}\n`;
    }
    
    if (MOONSHOT_API_KEY) {
      envContent += `MOONSHOT_API_KEY=${MOONSHOT_API_KEY}\n`;
    }
    
    if (XFYUN_APPID) {
      envContent += `XFYUN_APPID=${XFYUN_APPID}\n`;
    }
    
    if (XFYUN_SECRET_KEY) {
      envContent += `XFYUN_SECRET_KEY=${XFYUN_SECRET_KEY}\n`;
    }
    
    envContent += '\n# 其他环境变量\nNODE_ENV=development\n';
    
    // 写入.env文件
    const envPath = path.join(__dirname, '../../../backend/.env');
    fs.writeFileSync(envPath, envContent);
    
    res.json({
      success: true,
      message: '已成功将API配置同步到环境变量文件。请重启应用以使更改生效。'
    });
  } catch (error) {
    console.error('同步到.env文件失败:', error);
    res.status(500).json({
      success: false,
      message: '同步失败: ' + error.message
    });
  }
});

// history中间件应该在定义完所有API路由后使用
app.use(history({
  disableDotRule: true,
  verbose: true,
  rewrites: [
    { 
      from: /^\/api\/.*/,
      to: function(context) {
        return context.parsedUrl.href;
      }
    }
  ]
}));

// 设置静态文件目录
app.use(express.static(path.join(__dirname, '../src')));
app.use('/assets', express.static(path.join(__dirname, '../src/assets')));

// 提供index.html
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../index.html'));
});

// 启动服务器
const PORT = process.env.PORT || 8888;
app.listen(PORT, () => {
  console.log(`管理员控制台服务器已启动，端口: ${PORT}`);
  console.log(`访问地址: http://localhost:4000`);
  
  // 测试数据库连接
  pool.getConnection()
    .then(conn => {
      console.log('数据库连接成功');
      // 测试查询
      return conn.query('SELECT 1 AS test').then(([rows]) => {
        console.log('数据库查询测试:', rows);
        // 检查model_configs表是否存在
        return conn.query('SHOW TABLES LIKE "model_configs"').then(([tables]) => {
          if (tables.length > 0) {
            console.log('model_configs表存在');
            // 查询表结构
            return conn.query('DESCRIBE model_configs').then(([columns]) => {
              console.log('model_configs表结构:', columns.map(col => col.Field));
              return conn.query('SELECT COUNT(*) as count FROM model_configs').then(([count]) => {
                console.log('model_configs记录数:', count[0].count);
                conn.release();
              });
            });
          } else {
            console.log('model_configs表不存在!');
            conn.release();
          }
        });
      }).catch(err => {
        console.error('数据库查询测试失败:', err);
        conn.release();
      });
    })
    .catch(err => {
      console.error('数据库连接失败:', err);
    });
}); 
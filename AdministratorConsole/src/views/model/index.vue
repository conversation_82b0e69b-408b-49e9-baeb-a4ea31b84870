<template>
  <div class="model-container">
    <div class="model-header">
      <h2>大模型管理</h2>
      <div class="header-buttons">
        <el-button type="success" @click="handleSync">同步到.env</el-button>
        <el-button type="primary" @click="handleAdd">添加大模型</el-button>
      </div>
    </div>
    
    <el-card class="model-card">
      <div class="model-usage-summary">
        <el-alert
          title="API密钥管理"
          type="info"
          description="所有大模型API密钥统一配置，将自动同步到应用各个部分。点击'同步到.env'按钮将当前配置写入环境变量文件。变更后需要重启应用才能生效。"
          show-icon
          :closable="false"
        />
      </div>
      
      <!-- 同步结果提示 -->
      <el-collapse-transition>
        <div v-if="syncResult" class="sync-result">
          <el-alert
            :title="syncResult.success ? '同步成功' : '同步失败'" 
            :type="syncResult.success ? 'success' : 'error'"
            :description="syncResult.message"
            show-icon
            @close="syncResult = null"
          />
        </div>
      </el-collapse-transition>
      
      <el-table :data="modelList" style="width: 100%" border v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="模型名称" width="150" />
        <el-table-column prop="provider" label="提供商" width="100" />
        <el-table-column prop="baseUrl" label="API基础URL" width="180" />
        <el-table-column prop="apiKey" label="API密钥">
          <template #default="scope">
            <el-tag type="info">{{ hideApiKey(scope.row.apiKey) }}</el-tag>
            <el-button size="small" text @click="showApiKey(scope.row)">
              <el-icon><View /></el-icon>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="appId" label="APPID" width="100">
          <template #default="scope">
            <span v-if="scope.row.appId">{{ scope.row.appId }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="usageScenario" label="使用场景" width="120" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="primary" @click="handleTest(scope.row)">测试</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="table-footer">
        <el-button size="small" @click="exportConfig">导出配置</el-button>
      </div>
    </el-card>
    
    <!-- 添加/编辑模型对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑大模型' : '添加大模型'"
      width="500px"
    >
      <el-form :model="modelForm" label-width="100px" ref="modelFormRef" :rules="rules">
        <el-form-item label="模型名称" prop="name">
          <el-input v-model="modelForm.name" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="提供商" prop="provider">
          <el-select v-model="modelForm.provider" placeholder="请选择提供商" style="width: 100%">
            <el-option label="火山引擎" value="volcano" />
            <el-option label="DeepSeek" value="deepseek" />
            <el-option label="Moonshot" value="moonshot" />
            <el-option label="讯飞" value="xfyun" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="API密钥" prop="apiKey">
          <el-input v-model="modelForm.apiKey" placeholder="请输入API密钥" show-password />
        </el-form-item>
        <el-form-item v-if="modelForm.provider === 'xfyun'" label="APPID" prop="appId">
          <el-input v-model="modelForm.appId" placeholder="请输入讯飞APPID" />
        </el-form-item>
        <el-form-item label="基础URL" prop="baseUrl">
          <el-input v-model="modelForm.baseUrl" placeholder="请输入API基础URL" />
        </el-form-item>
        <el-form-item label="使用场景" prop="usageScenario">
          <el-select v-model="modelForm.usageScenario" placeholder="请选择使用场景" style="width: 100%">
            <el-option label="聊天" value="chat" />
            <el-option label="习题生成" value="exercise" />
            <el-option label="预习资料" value="preview" />
            <el-option label="搜索" value="search" />
            <el-option label="教案" value="lessonPlan" />
            <el-option label="教案详情" value="lessonDetail" />
            <el-option label="语音转写" value="transcription" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="modelForm.statusSwitch"
            :active-value="true"
            :inactive-value="false"
            active-text="正常"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelForm">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 测试对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="API连接测试"
      width="600px"
    >
      <div class="test-dialog-content">
        <div v-if="testing">
          <el-card shadow="never" class="test-card">
            <el-skeleton :rows="5" animated />
          </el-card>
          <div class="test-loading">
            <el-progress type="circle" :percentage="testProgress" />
            <div class="progress-text">正在测试API连接...</div>
          </div>
        </div>
        <div v-else-if="testResult">
          <el-alert
            :title="testResult.success ? 'API连接成功' : 'API连接失败'"
            :type="testResult.success ? 'success' : 'error'"
            :description="testResult.message"
            show-icon
            :closable="false"
          />
          <div class="test-detail" v-if="testResult.success">
            <el-descriptions title="连接详情" :column="1" border>
              <el-descriptions-item label="模型名称">{{ currentTestModel.name }}</el-descriptions-item>
              <el-descriptions-item label="响应时间">{{ testResult.responseTime }}ms</el-descriptions-item>
              <el-descriptions-item label="模型返回信息">{{ testResult.modelResponse }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="startTest" :disabled="testing">重新测试</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看API密钥对话框 -->
    <el-dialog
      v-model="viewKeyDialogVisible"
      title="查看API密钥"
      width="500px"
    >
      <el-form>
        <el-form-item label="模型名称">
          <span>{{ currentViewKey.name }}</span>
        </el-form-item>
        <el-form-item label="提供商">
          <span>{{ currentViewKey.provider }}</span>
        </el-form-item>
        <el-form-item label="API密钥">
          <el-input v-model="currentViewKey.apiKey" readonly>
            <template #append>
              <el-button @click="copyApiKey(currentViewKey.apiKey)">复制</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="currentViewKey.appId" label="APPID">
          <el-input v-model="currentViewKey.appId" readonly>
            <template #append>
              <el-button @click="copyApiKey(currentViewKey.appId)">复制</el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewKeyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 同步确认对话框 -->
    <el-dialog
      v-model="syncDialogVisible"
      title="同步确认"
      width="500px"
    >
      <div class="sync-dialog-content">
        <p>确定要将当前API配置同步到环境变量文件(.env)吗？</p>
        <p class="warning-text">注意：此操作将覆盖现有的环境变量文件，且需要重启应用才能生效。</p>
        
        <div class="env-preview">
          <pre>{{ envPreview }}</pre>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="success" @click="confirmSync" :loading="syncing">确认同步</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { View } from '@element-plus/icons-vue'
import axios from 'axios'

const dialogVisible = ref(false)
const isEdit = ref(false)
const loading = ref(false)
const modelList = ref([])
const modelFormRef = ref(null)
const currentId = ref(null)
const testDialogVisible = ref(false)
const testing = ref(false)
const testProgress = ref(0)
const testResult = ref(null)
const currentTestModel = ref({})
const viewKeyDialogVisible = ref(false)
const currentViewKey = ref({})
const syncDialogVisible = ref(false)
const syncing = ref(false)
const syncResult = ref(null)

// 表单数据
const modelForm = reactive({
  name: '',
  provider: '',
  apiKey: '',
  appId: '',
  baseUrl: '',
  usageScenario: '',
  statusSwitch: true
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' }
  ],
  provider: [
    { required: true, message: '请选择提供商', trigger: 'change' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ],
  appId: [
    { required: false, message: '请输入讯飞APPID', trigger: 'blur' }
  ],
  baseUrl: [
    { required: true, message: '请输入API基础URL', trigger: 'blur' }
  ],
  usageScenario: [
    { required: true, message: '请选择使用场景', trigger: 'change' }
  ]
}

// 隐藏API密钥的函数
const hideApiKey = (key) => {
  if (!key) return ''
  const prefix = key.substring(0, 5)
  const suffix = key.substring(key.length - 4)
  return `${prefix}...${suffix}`
}

// 获取模型列表
const fetchModelList = async () => {
  loading.value = true
  try {
    console.log('开始获取模型列表...');
    const response = await axios.get('/models')
    console.log('获取模型列表成功:', response.data);
    modelList.value = response.data.map(model => ({
      id: model.id,
      name: model.name,
      provider: model.provider,
      apiKey: model.apiKey,
      appId: model.appId,
      baseUrl: model.baseUrl,
      usageScenario: model.usageScenario,
      status: model.status
    }))
    loading.value = false
  } catch (error) {
    console.error('获取模型列表失败', error)
    ElMessage.error('获取模型列表失败：' + error.message)
    loading.value = false
  }
}

// 添加模型
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑模型
const handleEdit = (row) => {
  isEdit.value = true
  currentId.value = row.id
  Object.assign(modelForm, {
    name: row.name,
    provider: row.provider,
    apiKey: row.apiKey,
    appId: row.appId || '',
    baseUrl: row.baseUrl,
    usageScenario: row.usageScenario,
    statusSwitch: row.status === '正常'
  })
  dialogVisible.value = true
}

// 测试API连接
const handleTest = (row) => {
  currentTestModel.value = { ...row }
  testDialogVisible.value = true
  testResult.value = null
  startTest()
}

// 开始测试连接
const startTest = () => {
  testing.value = true
  testProgress.value = 0
  testResult.value = null

  const progressInterval = setInterval(() => {
    testProgress.value += 5
    if (testProgress.value >= 100) {
      clearInterval(progressInterval)
    }
  }, 100)

  // 调用API测试连接
  axios.post('/models/test', {
    provider: currentTestModel.value.provider,
    apiKey: currentTestModel.value.apiKey,
    baseUrl: currentTestModel.value.baseUrl
  }).then(response => {
    testResult.value = {
      success: true,
      message: '成功连接到API服务器',
      responseTime: response.data.responseTime,
      modelResponse: response.data.modelResponse
    }
  }).catch(error => {
    testResult.value = {
      success: false,
      message: 'API连接失败：' + (error.response?.data?.message || error.message)
    }
  }).finally(() => {
    testing.value = false
    clearInterval(progressInterval)
    testProgress.value = 100
  })
}

// 查看完整API密钥
const showApiKey = (row) => {
  currentViewKey.value = { ...row }
  viewKeyDialogVisible.value = true
}

// 复制API密钥
const copyApiKey = (key) => {
  navigator.clipboard.writeText(key).then(() => {
    ElMessage.success('API密钥已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 提交表单
const submitForm = async () => {
  modelFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const modelData = {
          name: modelForm.name,
          provider: modelForm.provider,
          apiKey: modelForm.apiKey,
          appId: modelForm.appId,
          baseUrl: modelForm.baseUrl,
          usageScenario: modelForm.usageScenario,
          status: modelForm.statusSwitch
        }

        if (isEdit.value) {
          // 调用API更新模型
          await axios.put(`/models/${currentId.value}`, modelData)
          ElMessage.success('模型已更新')
        } else {
          // 调用API添加模型
          await axios.post('/models', modelData)
          ElMessage.success('模型已添加')
        }
        
        // 重新获取列表
        fetchModelList()
        dialogVisible.value = false
        resetForm()
      } catch (error) {
        console.error('保存模型失败', error)
        ElMessage.error('保存模型失败：' + error.message)
      }
    }
  })
}

// 取消表单
const cancelForm = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (modelFormRef.value) {
    modelFormRef.value.resetFields()
  }
  
  modelForm.name = ''
  modelForm.provider = ''
  modelForm.apiKey = ''
  modelForm.appId = ''
  modelForm.baseUrl = ''
  modelForm.usageScenario = ''
  modelForm.statusSwitch = true
  currentId.value = null
}

// 生成环境变量文件预览
const envPreview = computed(() => {
  let preview = '# API密钥\n';
  
  // 添加每个API的密钥
  const volcanoModel = modelList.value.find(m => m.provider === 'volcano');
  const deepseekModel = modelList.value.find(m => m.provider === 'deepseek');
  const moonshotModel = modelList.value.find(m => m.provider === 'moonshot');
  const xfyunModel = modelList.value.find(m => m.provider === 'xfyun');
  
  if (volcanoModel) {
    preview += `ARK_API_KEY=${volcanoModel.apiKey}\n`;
  }
  
  if (deepseekModel) {
    preview += `DEEPSEEK_API_KEY=${deepseekModel.apiKey}\n`;
  }
  
  if (moonshotModel) {
    preview += `MOONSHOT_API_KEY=${moonshotModel.apiKey}\n`;
  }
  
  if (xfyunModel) {
    preview += `XFYUN_APPID=${xfyunModel.appId}\n`;
    preview += `XFYUN_SECRET_KEY=${xfyunModel.apiKey}\n`;
  }
  
  preview += '\n# 其他环境变量\nNODE_ENV=development\n';
  
  return preview;
});

// 显示同步对话框
const handleSync = () => {
  syncDialogVisible.value = true;
};

// 确认同步到环境变量文件
const confirmSync = async () => {
  syncing.value = true
  
  try {
    // 准备要同步的数据
    const syncData = {
      ARK_API_KEY: '',
      DEEPSEEK_API_KEY: '',
      MOONSHOT_API_KEY: '',
      XFYUN_APPID: '',
      XFYUN_SECRET_KEY: ''
    }
    
    // 获取每个提供商的API密钥
    modelList.value.forEach(model => {
      if (model.provider === 'volcano') {
        syncData.ARK_API_KEY = model.apiKey
      } else if (model.provider === 'deepseek') {
        syncData.DEEPSEEK_API_KEY = model.apiKey
      } else if (model.provider === 'moonshot') {
        syncData.MOONSHOT_API_KEY = model.apiKey
      } else if (model.provider === 'xfyun') {
        syncData.XFYUN_APPID = model.appId
        syncData.XFYUN_SECRET_KEY = model.apiKey
      }
    })
    
    // 调用API同步到.env文件
    const response = await axios.post('/models/sync', syncData)
    
    // 显示同步结果
    syncResult.value = {
      success: response.data.success,
      message: response.data.message
    }
    
    syncDialogVisible.value = false
  } catch (error) {
    console.error('同步配置失败', error)
    syncResult.value = {
      success: false,
      message: '同步失败：' + error.message
    }
  } finally {
    syncing.value = false
  }
}

// 导出配置
const exportConfig = () => {
  // 准备导出数据
  const exportData = {
    models: modelList.value.map(model => ({
      name: model.name,
      provider: model.provider,
      apiKey: model.apiKey,
      baseUrl: model.baseUrl,
      usageScenario: model.usageScenario
    }))
  };
  
  // 转换为JSON字符串
  const jsonStr = JSON.stringify(exportData, null, 2);
  
  // 创建Blob
  const blob = new Blob([jsonStr], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  // 创建下载链接并触发下载
  const a = document.createElement('a');
  a.href = url;
  a.download = 'model_config_' + new Date().toISOString().slice(0, 10) + '.json';
  document.body.appendChild(a);
  a.click();
  
  // 清理
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 0);
  
  ElMessage.success('配置已导出');
};

// 页面加载时获取数据
onMounted(() => {
  fetchModelList()
})
</script>

<style scoped>
.model-container {
  padding: 20px;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.model-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.model-card {
  margin-bottom: 20px;
}

.model-usage-summary {
  margin-bottom: 20px;
}

.test-dialog-content {
  min-height: 250px;
  position: relative;
}

.test-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.progress-text {
  margin-top: 15px;
  color: #606266;
  font-size: 14px;
}

.test-detail {
  margin-top: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.model-header .header-buttons {
  display: flex;
  gap: 10px;
}

.sync-result {
  margin-bottom: 20px;
}

.sync-dialog-content {
  margin-bottom: 15px;
}

.warning-text {
  color: #E6A23C;
  font-weight: bold;
}

.env-preview {
  margin-top: 15px;
  background-color: #f6f8fa;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.env-preview pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}

.table-footer {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}
</style> 
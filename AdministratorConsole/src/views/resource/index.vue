<template>
  <div class="resource-container">
    <div class="resource-header">
      <h2>资源管理</h2>
      <el-button type="primary" @click="uploadDialogVisible = true">上传资源</el-button>
    </div>
    
    <el-card class="resource-card">
      <!-- 搜索和筛选区域 -->
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入资源名称或关键词"
          class="search-input"
          clearable
          @clear="loadResourceList"
        >
          <template #append>
            <el-button @click="loadResourceList">
              <el-icon><el-icon-search /></el-icon>
            </el-button>
          </template>
        </el-input>
        
        <el-select v-model="resourceType" placeholder="资源类型" clearable @change="loadResourceList" style="margin-left: 10px; width: 150px;">
          <el-option v-for="item in resourceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      
      <!-- 资源列表 -->
      <el-table :data="resourceList" style="width: 100%" border v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="资源名称" min-width="180">
          <template #default="scope">
            <div class="resource-name">
              <el-icon :class="getFileIconClass(scope.row.type)" class="file-icon"></el-icon>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getTagType(scope.row.type)">
              {{ getFileTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="100">
          <template #default="scope">
            {{ formatFileSize(scope.row.size) }}
          </template>
        </el-table-column>
        <el-table-column prop="uploader" label="上传者" width="120" />
        <el-table-column prop="uploadTime" label="上传时间" width="180" />
        <el-table-column prop="downloadCount" label="下载次数" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleDownload(scope.row)">下载</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 上传资源对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传资源"
      width="500px"
    >
      <el-form :model="uploadForm" label-width="100px" ref="uploadFormRef" :rules="rules">
        <el-form-item label="资源名称" prop="name">
          <el-input v-model="uploadForm.name" placeholder="请输入资源名称" />
        </el-form-item>
        
        <el-form-item label="资源类型" prop="type">
          <el-select v-model="uploadForm.type" placeholder="请选择资源类型" style="width: 100%">
            <el-option v-for="item in resourceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="资源描述" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            placeholder="请输入资源描述"
            :rows="3"
          />
        </el-form-item>
        
        <el-form-item label="文件上传" prop="file">
          <el-upload
            ref="uploadRef"
            class="resource-upload"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请上传资源文件，大小不超过100MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="可见范围" prop="visibleRange">
          <el-select v-model="uploadForm.visibleRange" placeholder="请选择可见范围" style="width: 100%">
            <el-option label="所有人可见" value="all" />
            <el-option label="仅教师可见" value="teacher" />
            <el-option label="指定班级可见" value="class" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="选择班级" v-if="uploadForm.visibleRange === 'class'" prop="visibleClasses">
          <el-select
            v-model="uploadForm.visibleClasses"
            multiple
            collapse-tags
            placeholder="请选择可见班级"
            style="width: 100%"
          >
            <el-option
              v-for="item in classList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload">确定上传</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 页面状态
const loading = ref(false)
const uploadDialogVisible = ref(false)
const searchKeyword = ref('')
const resourceType = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const uploadFormRef = ref(null)
const uploadRef = ref(null)
const fileList = ref([])

// 资源类型选项
const resourceTypeOptions = [
  { value: 'doc', label: '文档' },
  { value: 'video', label: '视频' },
  { value: 'audio', label: '音频' },
  { value: 'image', label: '图片' },
  { value: 'code', label: '代码' },
  { value: 'other', label: '其他' }
]

// 班级列表
const classList = ref([
  { id: 1, name: '计算机科学与技术2021级1班' },
  { id: 2, name: '计算机科学与技术2021级2班' },
  { id: 3, name: '人工智能2022级1班' },
  { id: 4, name: '人工智能2022级2班' },
  { id: 5, name: '数据科学2023级1班' }
])

// 资源列表数据
const resourceList = ref([
  {
    id: 1,
    name: 'Python基础教程.pdf',
    type: 'doc',
    size: 2048576, // 2MB
    uploader: '张三',
    uploadTime: '2023-08-20 10:30',
    downloadCount: 35,
    description: 'Python编程基础教程，适合初学者学习',
    visibleRange: 'all'
  },
  {
    id: 2,
    name: '深度学习简介.pptx',
    type: 'doc',
    size: 5242880, // 5MB
    uploader: '李四',
    uploadTime: '2023-08-18 15:45',
    downloadCount: 28,
    description: '深度学习课程介绍PPT',
    visibleRange: 'teacher'
  },
  {
    id: 3,
    name: 'JavaScript编程实例.zip',
    type: 'code',
    size: 10485760, // 10MB
    uploader: '王五',
    uploadTime: '2023-08-15 09:20',
    downloadCount: 42,
    description: 'JavaScript编程实例代码',
    visibleRange: 'class'
  },
  {
    id: 4,
    name: '数据结构与算法.mp4',
    type: 'video',
    size: 52428800, // 50MB
    uploader: '赵六',
    uploadTime: '2023-08-10 14:00',
    downloadCount: 56,
    description: '数据结构与算法视频讲解',
    visibleRange: 'all'
  },
  {
    id: 5,
    name: '机器学习模型示例.ipynb',
    type: 'code',
    size: 3145728, // 3MB
    uploader: '钱七',
    uploadTime: '2023-08-08 16:30',
    downloadCount: 20,
    description: '机器学习模型案例Jupyter Notebook',
    visibleRange: 'class'
  }
])

// 上传表单数据
const uploadForm = reactive({
  name: '',
  type: '',
  description: '',
  file: null,
  visibleRange: 'all',
  visibleClasses: []
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入资源名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ],
  visibleRange: [
    { required: true, message: '请选择可见范围', trigger: 'change' }
  ],
  visibleClasses: [
    { required: true, message: '请选择可见班级', trigger: 'change' }
  ]
}

// 生命周期钩子
onMounted(() => {
  loadResourceList()
  calculateTotal()
})

// 加载资源列表
const loadResourceList = () => {
  loading.value = true
  // 模拟API请求延迟
  setTimeout(() => {
    // 应用筛选条件
    let filteredList = [...resourceList.value]
    
    // 按类型筛选
    if (resourceType.value) {
      filteredList = filteredList.filter(item => item.type === resourceType.value)
    }
    
    // 按关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filteredList = filteredList.filter(item => 
        item.name.toLowerCase().includes(keyword) || 
        item.description.toLowerCase().includes(keyword)
      )
    }
    
    // 更新列表和总数
    resourceList.value = filteredList
    calculateTotal()
    loading.value = false
  }, 300)
}

// 计算总数
const calculateTotal = () => {
  total.value = resourceList.value.length
}

// 分页相关方法
const handleSizeChange = (val) => {
  pageSize.value = val
  loadResourceList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadResourceList()
}

// 获取文件类型图标类名
const getFileIconClass = (type) => {
  switch (type) {
    case 'doc':
      return 'el-icon-document'
    case 'video':
      return 'el-icon-video-camera'
    case 'audio':
      return 'el-icon-headset'
    case 'image':
      return 'el-icon-picture'
    case 'code':
      return 'el-icon-data-analysis'
    default:
      return 'el-icon-files'
  }
}

// 获取文件类型标签样式
const getTagType = (type) => {
  switch (type) {
    case 'doc':
      return 'primary'
    case 'video':
      return 'success'
    case 'audio':
      return 'warning'
    case 'image':
      return 'danger'
    case 'code':
      return 'info'
    default:
      return ''
  }
}

// 获取文件类型名称
const getFileTypeName = (type) => {
  switch (type) {
    case 'doc':
      return '文档'
    case 'video':
      return '视频'
    case 'audio':
      return '音频'
    case 'image':
      return '图片'
    case 'code':
      return '代码'
    default:
      return '其他'
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
  }
}

// 处理文件选择
const handleFileChange = (file) => {
  fileList.value = [file]
  uploadForm.file = file.raw
  
  // 如果未填写资源名称，自动使用文件名
  if (!uploadForm.name) {
    uploadForm.name = file.name
  }
  
  // 根据文件类型自动选择资源类型
  const extension = file.name.split('.').pop().toLowerCase()
  
  if (['doc', 'docx', 'pdf', 'txt', 'ppt', 'pptx', 'xls', 'xlsx'].includes(extension)) {
    uploadForm.type = 'doc'
  } else if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(extension)) {
    uploadForm.type = 'video'
  } else if (['mp3', 'wav', 'ogg', 'flac'].includes(extension)) {
    uploadForm.type = 'audio'
  } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(extension)) {
    uploadForm.type = 'image'
  } else if (['js', 'py', 'java', 'c', 'cpp', 'html', 'css', 'php', 'ipynb', 'zip'].includes(extension)) {
    uploadForm.type = 'code'
  } else {
    uploadForm.type = 'other'
  }
}

// 处理超出上传数量限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 处理资源下载
const handleDownload = (resource) => {
  // 实际应用中，这里应该调用API获取下载链接
  ElMessage.success(`开始下载：${resource.name}`)
  
  // 模拟下载计数增加
  const index = resourceList.value.findIndex(item => item.id === resource.id)
  if (index !== -1) {
    resourceList.value[index].downloadCount++
  }
}

// 处理资源删除
const handleDelete = (resource) => {
  ElMessageBox.confirm(
    `确定要删除资源 "${resource.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 在实际应用中，这里应该调用删除API
    resourceList.value = resourceList.value.filter(item => item.id !== resource.id)
    calculateTotal()
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 提交上传
const submitUpload = () => {
  uploadFormRef.value.validate((valid) => {
    if (valid) {
      if (!uploadForm.file) {
        ElMessage.warning('请选择要上传的文件')
        return
      }
      
      // 这里应该调用上传API
      // 模拟上传成功
      ElMessage.success('上传成功')
      
      // 添加到资源列表
      const newId = resourceList.value.length > 0 ? Math.max(...resourceList.value.map(item => item.id)) + 1 : 1
      const now = new Date()
      const uploadTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
      
      resourceList.value.unshift({
        id: newId,
        name: uploadForm.name,
        type: uploadForm.type,
        size: uploadForm.file.size,
        uploader: '当前用户', // 实际应用中应该是从用户信息中获取
        uploadTime: uploadTime,
        downloadCount: 0,
        description: uploadForm.description,
        visibleRange: uploadForm.visibleRange
      })
      
      // 重置表单
      uploadDialogVisible.value = false
      uploadForm.name = ''
      uploadForm.type = ''
      uploadForm.description = ''
      uploadForm.file = null
      uploadForm.visibleRange = 'all'
      uploadForm.visibleClasses = []
      fileList.value = []
      calculateTotal()
    }
  })
}
</script>

<style scoped>
.resource-container {
  padding: 10px;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.resource-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.resource-card {
  margin-bottom: 20px;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.search-input {
  width: 300px;
}

.resource-name {
  display: flex;
  align-items: center;
}

.file-icon {
  margin-right: 8px;
  font-size: 18px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.resource-upload {
  width: 100%;
}
</style> 
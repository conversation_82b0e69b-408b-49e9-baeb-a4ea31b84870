<template>
  <div class="class-container">
    <div class="class-header">
      <h2>班级管理</h2>
      <el-button type="primary" @click="dialogVisible = true">添加班级</el-button>
    </div>
    
    <div class="class-content">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="classItem in classList" :key="classItem.id">
          <el-card class="class-card" :body-style="{ padding: '0px' }">
            <div class="class-card-header" :class="{'class-inactive': classItem.status === '0'}">
              <h3>{{ classItem.name }}</h3>
              <el-tag v-if="classItem.status === '1'" type="success" size="small">进行中</el-tag>
              <el-tag v-else type="info" size="small">已结课</el-tag>
            </div>
            <div class="class-card-content">
              <p><i class="el-icon-user"></i> 班主任：{{ classItem.teacher }}</p>
              <p><i class="el-icon-user-solid"></i> 学生数：{{ classItem.studentCount }}</p>
              <p><i class="el-icon-date"></i> 创建时间：{{ classItem.createTime }}</p>
            </div>
            <div class="class-card-footer">
              <el-button size="small" @click="handleViewDetail(classItem)">查看详情</el-button>
              <el-dropdown>
                <el-button size="small">
                  更多操作<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleEdit(classItem)">编辑</el-dropdown-item>
                    <el-dropdown-item @click="handleManageStudents(classItem)">管理学生</el-dropdown-item>
                    <el-dropdown-item divided @click="handleDelete(classItem)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 添加/编辑班级对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑班级' : '添加班级'"
      width="500px"
    >
      <el-form :model="classForm" label-width="100px" ref="classFormRef" :rules="rules">
        <el-form-item label="班级名称" prop="name">
          <el-input v-model="classForm.name" placeholder="请输入班级名称" />
        </el-form-item>
        <el-form-item label="班主任" prop="teacher">
          <el-select v-model="classForm.teacher" placeholder="请选择班主任" style="width: 100%">
            <el-option label="张三" value="张三" />
            <el-option label="李四" value="李四" />
            <el-option label="王五" value="王五" />
            <el-option label="赵六" value="赵六" />
            <el-option label="钱七" value="钱七" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属学院" prop="department">
          <el-select v-model="classForm.department" placeholder="请选择所属学院" style="width: 100%">
            <el-option label="计算机科学与技术学院" value="计算机科学与技术学院" />
            <el-option label="人工智能学院" value="人工智能学院" />
            <el-option label="数学与统计学院" value="数学与统计学院" />
            <el-option label="物理学院" value="物理学院" />
            <el-option label="化学学院" value="化学学院" />
          </el-select>
        </el-form-item>
        <el-form-item label="入学年份" prop="year">
          <el-date-picker
            v-model="classForm.year"
            type="year"
            placeholder="选择年份"
            format="YYYY"
            value-format="YYYY"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="班级介绍" prop="description">
          <el-input
            v-model="classForm.description"
            type="textarea"
            placeholder="请输入班级介绍"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="classForm.status">
            <el-radio label="1">进行中</el-radio>
            <el-radio label="0">已结课</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 班级详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="班级详情"
      width="700px"
    >
      <div v-if="currentClass">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="班级名称">{{ currentClass.name }}</el-descriptions-item>
          <el-descriptions-item label="班主任">{{ currentClass.teacher }}</el-descriptions-item>
          <el-descriptions-item label="所属学院">{{ currentClass.department }}</el-descriptions-item>
          <el-descriptions-item label="入学年份">{{ currentClass.year }}</el-descriptions-item>
          <el-descriptions-item label="学生人数">{{ currentClass.studentCount }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="currentClass.status === '1'" type="success">进行中</el-tag>
            <el-tag v-else type="info">已结课</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ currentClass.createTime }}</el-descriptions-item>
          <el-descriptions-item label="班级介绍" :span="2">{{ currentClass.description }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-title">班级学生列表</div>
        <el-table :data="currentClass.students || []" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="studentId" label="学号" width="120" />
          <el-table-column prop="gender" label="性别" width="80">
            <template #default="scope">
              {{ scope.row.gender === '1' ? '男' : '女' }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" />
          <el-table-column prop="email" label="邮箱" />
        </el-table>
      </div>
    </el-dialog>
    
    <!-- 管理学生对话框 -->
    <el-dialog
      v-model="studentManageDialogVisible"
      title="管理班级学生"
      width="800px"
    >
      <div v-if="currentClass">
        <div class="student-manage-header">
          <h3>{{ currentClass.name }} - 学生管理</h3>
          <el-button type="primary" size="small" @click="addStudentDialogVisible = true">添加学生</el-button>
        </div>
        
        <el-table :data="currentClass.students || []" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="studentId" label="学号" width="120" />
          <el-table-column prop="gender" label="性别" width="80">
            <template #default="scope">
              {{ scope.row.gender === '1' ? '男' : '女' }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" width="150" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button 
                size="small" 
                type="danger" 
                @click="handleRemoveStudent(scope.row)"
              >移出班级</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    
    <!-- 添加学生到班级对话框 -->
    <el-dialog
      v-model="addStudentDialogVisible"
      title="添加学生到班级"
      width="800px"
    >
      <div>
        <el-input
          v-model="studentSearchKeyword"
          placeholder="请输入学生姓名或学号搜索"
          class="search-input"
          clearable
          @clear="searchStudents"
        >
          <template #append>
            <el-button @click="searchStudents">
              <el-icon><el-icon-search /></el-icon>
            </el-button>
          </template>
        </el-input>
        
        <el-table
          ref="addStudentTable"
          :data="availableStudents"
          style="width: 100%; margin-top: 20px;"
          border
          @selection-change="handleStudentSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="studentId" label="学号" width="120" />
          <el-table-column prop="gender" label="性别" width="80">
            <template #default="scope">
              {{ scope.row.gender === '1' ? '男' : '女' }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" width="150" />
          <el-table-column prop="email" label="邮箱" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addStudentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddStudents">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 页面状态
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const studentManageDialogVisible = ref(false)
const addStudentDialogVisible = ref(false)
const isEdit = ref(false)
const currentId = ref(0)
const currentClass = ref(null)
const classFormRef = ref(null)
const studentSearchKeyword = ref('')
const selectedStudents = ref([])

// 班级列表数据
const classList = ref([
  {
    id: 1,
    name: '计算机科学与技术2021级1班',
    teacher: '张三',
    department: '计算机科学与技术学院',
    year: '2021',
    studentCount: 35,
    createTime: '2021-09-01',
    status: '1',
    description: '计算机科学与技术专业2021级1班，主要学习计算机基础知识、程序设计、数据结构等课程。',
    students: [
      {
        id: 1,
        name: '张明',
        studentId: 'S202101001',
        gender: '1',
        phone: '13900138001',
        email: '<EMAIL>'
      },
      {
        id: 2,
        name: '李华',
        studentId: 'S202101002',
        gender: '1',
        phone: '13900138002',
        email: '<EMAIL>'
      }
    ]
  },
  {
    id: 2,
    name: '计算机科学与技术2021级2班',
    teacher: '李四',
    department: '计算机科学与技术学院',
    year: '2021',
    studentCount: 32,
    createTime: '2021-09-01',
    status: '1',
    description: '计算机科学与技术专业2021级2班，主要学习计算机基础知识、程序设计、数据结构等课程。',
    students: []
  },
  {
    id: 3,
    name: '人工智能2022级1班',
    teacher: '王五',
    department: '人工智能学院',
    year: '2022',
    studentCount: 30,
    createTime: '2022-09-01',
    status: '1',
    description: '人工智能专业2022级1班，主要学习人工智能算法、机器学习、深度学习等课程。',
    students: [
      {
        id: 3,
        name: '王芳',
        studentId: 'S202201001',
        gender: '2',
        phone: '13900138003',
        email: '<EMAIL>'
      },
      {
        id: 4,
        name: '赵敏',
        studentId: 'S202201002',
        gender: '2',
        phone: '13900138004',
        email: '<EMAIL>'
      }
    ]
  },
  {
    id: 4,
    name: '人工智能2022级2班',
    teacher: '赵六',
    department: '人工智能学院',
    year: '2022',
    studentCount: 28,
    createTime: '2022-09-01',
    status: '1',
    description: '人工智能专业2022级2班，主要学习人工智能算法、机器学习、深度学习等课程。',
    students: []
  },
  {
    id: 5,
    name: '数据科学2023级1班',
    teacher: '钱七',
    department: '数学与统计学院',
    year: '2023',
    studentCount: 25,
    createTime: '2023-09-01',
    status: '1',
    description: '数据科学专业2023级1班，主要学习统计学、数据分析、数据挖掘等课程。',
    students: [
      {
        id: 5,
        name: '刘强',
        studentId: 'S202301001',
        gender: '1',
        phone: '13900138005',
        email: '<EMAIL>'
      }
    ]
  }
])

// 可选学生列表（用于添加到班级）
const availableStudents = ref([
  {
    id: 6,
    name: '陈亮',
    studentId: 'S202301002',
    gender: '1',
    phone: '13900138006',
    email: '<EMAIL>'
  },
  {
    id: 7,
    name: '杨丽',
    studentId: 'S202301003',
    gender: '2',
    phone: '13900138007',
    email: '<EMAIL>'
  },
  {
    id: 8,
    name: '周杰',
    studentId: 'S202301004',
    gender: '1',
    phone: '13900138008',
    email: '<EMAIL>'
  }
])

// 表单数据
const classForm = reactive({
  name: '',
  teacher: '',
  department: '',
  year: '',
  description: '',
  status: '1'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入班级名称', trigger: 'blur' }
  ],
  teacher: [
    { required: true, message: '请选择班主任', trigger: 'change' }
  ],
  department: [
    { required: true, message: '请选择所属学院', trigger: 'change' }
  ],
  year: [
    { required: true, message: '请选择入学年份', trigger: 'change' }
  ]
}

// 生命周期钩子
onMounted(() => {
  // 初始化数据
})

// 查看班级详情
const handleViewDetail = (classItem) => {
  currentClass.value = { ...classItem }
  detailDialogVisible.value = true
}

// 编辑班级
const handleEdit = (classItem) => {
  isEdit.value = true
  currentId.value = classItem.id
  Object.assign(classForm, {
    name: classItem.name,
    teacher: classItem.teacher,
    department: classItem.department,
    year: classItem.year,
    description: classItem.description,
    status: classItem.status
  })
  dialogVisible.value = true
}

// 管理班级学生
const handleManageStudents = (classItem) => {
  currentClass.value = { ...classItem }
  studentManageDialogVisible.value = true
}

// 删除班级
const handleDelete = (classItem) => {
  ElMessageBox.confirm(
    `确定要删除班级 "${classItem.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 在实际应用中，这里应该调用删除API
    classList.value = classList.value.filter(item => item.id !== classItem.id)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 提交表单
const submitForm = () => {
  classFormRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        // 编辑现有班级
        const index = classList.value.findIndex(item => item.id === currentId.value)
        if (index !== -1) {
          // 保留原有的一些字段
          const oldStudentCount = classList.value[index].studentCount
          const oldCreateTime = classList.value[index].createTime
          const oldStudents = classList.value[index].students
          
          classList.value[index] = {
            ...classList.value[index],
            name: classForm.name,
            teacher: classForm.teacher,
            department: classForm.department,
            year: classForm.year,
            description: classForm.description,
            status: classForm.status,
            studentCount: oldStudentCount,
            createTime: oldCreateTime,
            students: oldStudents
          }
          ElMessage.success('更新成功')
        }
      } else {
        // 添加新班级
        const newId = classList.value.length > 0 ? Math.max(...classList.value.map(item => item.id)) + 1 : 1
        const now = new Date()
        const createTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`
        
        classList.value.push({
          id: newId,
          name: classForm.name,
          teacher: classForm.teacher,
          department: classForm.department,
          year: classForm.year,
          description: classForm.description,
          status: classForm.status,
          studentCount: 0,
          createTime: createTime,
          students: []
        })
        ElMessage.success('添加成功')
      }
      dialogVisible.value = false
      resetForm()
    }
  })
}

// 重置表单
const resetForm = () => {
  isEdit.value = false
  currentId.value = 0
  classForm.name = ''
  classForm.teacher = ''
  classForm.department = ''
  classForm.year = ''
  classForm.description = ''
  classForm.status = '1'
}

// 从班级移除学生
const handleRemoveStudent = (student) => {
  ElMessageBox.confirm(
    `确定要将学生 "${student.name}" 从当前班级移除吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 从班级移除学生
    if (currentClass.value) {
      currentClass.value.students = currentClass.value.students.filter(item => item.id !== student.id)
      
      // 同时更新原始班级列表中的数据
      const index = classList.value.findIndex(item => item.id === currentClass.value.id)
      if (index !== -1) {
        classList.value[index].students = currentClass.value.students
        classList.value[index].studentCount = currentClass.value.students.length
      }
      
      ElMessage.success('已将学生移出班级')
    }
  }).catch(() => {
    // 用户取消移除
  })
}

// 搜索可添加的学生
const searchStudents = () => {
  // 实际应用中应该调用API进行搜索
  if (studentSearchKeyword.value) {
    const keyword = studentSearchKeyword.value.toLowerCase()
    availableStudents.value = [
      {
        id: 6,
        name: '陈亮',
        studentId: 'S202301002',
        gender: '1',
        phone: '13900138006',
        email: '<EMAIL>'
      },
      {
        id: 7,
        name: '杨丽',
        studentId: 'S202301003',
        gender: '2',
        phone: '13900138007',
        email: '<EMAIL>'
      },
      {
        id: 8,
        name: '周杰',
        studentId: 'S202301004',
        gender: '1',
        phone: '13900138008',
        email: '<EMAIL>'
      }
    ].filter(item => 
      item.name.toLowerCase().includes(keyword) || 
      item.studentId.toLowerCase().includes(keyword)
    )
  } else {
    availableStudents.value = [
      {
        id: 6,
        name: '陈亮',
        studentId: 'S202301002',
        gender: '1',
        phone: '13900138006',
        email: '<EMAIL>'
      },
      {
        id: 7,
        name: '杨丽',
        studentId: 'S202301003',
        gender: '2',
        phone: '13900138007',
        email: '<EMAIL>'
      },
      {
        id: 8,
        name: '周杰',
        studentId: 'S202301004',
        gender: '1',
        phone: '13900138008',
        email: '<EMAIL>'
      }
    ]
  }
}

// 表格选择变化
const handleStudentSelectionChange = (selection) => {
  selectedStudents.value = selection
}

// 确认添加学生到班级
const confirmAddStudents = () => {
  if (selectedStudents.value.length === 0) {
    ElMessage.warning('请选择要添加的学生')
    return
  }
  
  if (currentClass.value) {
    // 将选中的学生添加到当前班级
    const newStudents = [...currentClass.value.students]
    
    selectedStudents.value.forEach(student => {
      // 检查是否已经在班级中
      const exists = newStudents.some(item => item.id === student.id)
      if (!exists) {
        newStudents.push(student)
      }
    })
    
    currentClass.value.students = newStudents
    currentClass.value.studentCount = newStudents.length
    
    // 同时更新原始班级列表中的数据
    const index = classList.value.findIndex(item => item.id === currentClass.value.id)
    if (index !== -1) {
      classList.value[index].students = newStudents
      classList.value[index].studentCount = newStudents.length
    }
    
    ElMessage.success(`成功添加 ${selectedStudents.value.length} 名学生到班级`)
    addStudentDialogVisible.value = false
    selectedStudents.value = []
  }
}
</script>

<style scoped>
.class-container {
  padding: 10px;
}

.class-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.class-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.class-content {
  margin-bottom: 20px;
}

.class-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.class-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.class-card-header {
  background-color: #409EFF;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.class-card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.class-inactive {
  background-color: #909399;
}

.class-card-content {
  padding: 15px;
}

.class-card-content p {
  margin: 10px 0;
  display: flex;
  align-items: center;
}

.class-card-content i {
  margin-right: 8px;
}

.class-card-footer {
  padding: 10px 15px;
  border-top: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
}

.student-manage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.student-manage-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.search-input {
  margin-bottom: 20px;
  width: 300px;
}
</style> 
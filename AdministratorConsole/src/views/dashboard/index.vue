<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-title">大模型数量</div>
            <el-icon class="stat-card-icon" color="#409EFF"><el-icon-cpu /></el-icon>
          </div>
          <div class="stat-card-count">5</div>
          <div class="stat-card-footer">较上周 <span class="up">+2</span></div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-title">教师数量</div>
            <el-icon class="stat-card-icon" color="#67C23A"><el-icon-user /></el-icon>
          </div>
          <div class="stat-card-count">12</div>
          <div class="stat-card-footer">较上周 <span class="up">+3</span></div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-title">学生数量</div>
            <el-icon class="stat-card-icon" color="#E6A23C"><el-icon-user /></el-icon>
          </div>
          <div class="stat-card-count">245</div>
          <div class="stat-card-footer">较上周 <span class="up">+15</span></div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-title">班级数量</div>
            <el-icon class="stat-card-icon" color="#F56C6C"><el-icon-office-building /></el-icon>
          </div>
          <div class="stat-card-count">8</div>
          <div class="stat-card-footer">较上周 <span class="up">+1</span></div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <div class="chart-header">
            <h4>模型使用统计</h4>
          </div>
          <div class="chart-placeholder">
            <el-empty description="暂无数据" />
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <div class="chart-header">
            <h4>系统访问量</h4>
          </div>
          <div class="chart-placeholder">
            <el-empty description="暂无数据" />
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :xs="24">
        <el-card class="recent-card">
          <div class="recent-header">
            <h4>最近活动</h4>
          </div>
          <el-table :data="recentActivities" style="width: 100%">
            <el-table-column prop="time" label="时间" width="180" />
            <el-table-column prop="user" label="用户" width="120" />
            <el-table-column prop="activity" label="活动" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const recentActivities = ref([
  {
    time: '2023-08-25 10:30',
    user: '张老师',
    activity: '创建了新班级"2023级人工智能班"'
  },
  {
    time: '2023-08-25 09:15',
    user: '系统管理员',
    activity: '添加了新大模型"ChatGPT-4"'
  },
  {
    time: '2023-08-24 16:42',
    user: '李老师',
    activity: '上传了新教学资源"Python基础教程"'
  },
  {
    time: '2023-08-24 14:20',
    user: '系统管理员',
    activity: '添加了新教师"王明"'
  },
  {
    time: '2023-08-23 11:05',
    user: '张老师',
    activity: '批量导入了30名新学生'
  }
])
</script>

<style scoped>
.dashboard-container {
  padding: 10px;
}

.stat-card {
  margin-bottom: 20px;
  height: 150px;
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.stat-card-title {
  font-size: 16px;
  color: #606266;
}

.stat-card-icon {
  font-size: 24px;
}

.stat-card-count {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.stat-card-footer {
  font-size: 14px;
  color: #909399;
}

.up {
  color: #67C23A;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card, .recent-card {
  margin-bottom: 20px;
}

.chart-header, .recent-header {
  margin-bottom: 20px;
}

.chart-header h4, .recent-header h4 {
  margin: 0;
  font-size: 16px;
  color: #606266;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style> 
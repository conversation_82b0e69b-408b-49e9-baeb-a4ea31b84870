<template>
  <div class="teacher-container">
    <div class="teacher-header">
      <h2>教师管理</h2>
      <el-button type="primary" @click="dialogVisible = true">添加教师</el-button>
    </div>
    
    <el-card class="teacher-card">
      <!-- 搜索区域 -->
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入教师姓名或工号"
          class="search-input"
          clearable
          @clear="loadTeacherList"
        >
          <template #append>
            <el-button @click="loadTeacherList">
              <el-icon><el-icon-search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      
      <!-- 教师列表 -->
      <el-table :data="teacherList" style="width: 100%" border v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="teacherId" label="工号" width="120" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="scope">
            {{ scope.row.gender === '1' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="150" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column prop="department" label="所属部门" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '在职' : '离职' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 添加/编辑教师对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑教师' : '添加教师'"
      width="500px"
    >
      <el-form :model="teacherForm" label-width="100px" ref="teacherFormRef" :rules="rules">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="teacherForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="teacherForm.gender">
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="工号" prop="teacherId">
          <el-input v-model="teacherForm.teacherId" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="teacherForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="teacherForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="所属部门" prop="department">
          <el-select v-model="teacherForm.department" placeholder="请选择所属部门" style="width: 100%">
            <el-option label="计算机科学与技术学院" value="计算机科学与技术学院" />
            <el-option label="人工智能学院" value="人工智能学院" />
            <el-option label="数学与统计学院" value="数学与统计学院" />
            <el-option label="物理学院" value="物理学院" />
            <el-option label="化学学院" value="化学学院" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="teacherForm.status">
            <el-radio label="1">在职</el-radio>
            <el-radio label="0">离职</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 页面状态
const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentId = ref(0)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const teacherFormRef = ref(null)

// 教师列表数据
const teacherList = ref([
  {
    id: 1,
    name: '张三',
    teacherId: 'T2023001',
    gender: '1',
    phone: '13800138001',
    email: '<EMAIL>',
    department: '计算机科学与技术学院',
    status: '1'
  },
  {
    id: 2,
    name: '李四',
    teacherId: 'T2023002',
    gender: '1',
    phone: '13800138002',
    email: '<EMAIL>',
    department: '人工智能学院',
    status: '1'
  },
  {
    id: 3,
    name: '王五',
    teacherId: 'T2023003',
    gender: '1',
    phone: '13800138003',
    email: '<EMAIL>',
    department: '数学与统计学院',
    status: '1'
  },
  {
    id: 4,
    name: '赵六',
    teacherId: 'T2023004',
    gender: '1',
    phone: '13800138004',
    email: '<EMAIL>',
    department: '物理学院',
    status: '0'
  },
  {
    id: 5,
    name: '钱七',
    teacherId: '********',
    gender: '2',
    phone: '13800138005',
    email: '<EMAIL>',
    department: '化学学院',
    status: '1'
  }
])

// 表单数据
const teacherForm = reactive({
  name: '',
  gender: '1',
  teacherId: '',
  phone: '',
  email: '',
  department: '',
  status: '1'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  teacherId: [
    { required: true, message: '请输入工号', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ]
}

// 生命周期钩子
onMounted(() => {
  loadTeacherList()
  calculateTotal()
})

// 加载教师列表
const loadTeacherList = () => {
  loading.value = true
  // 模拟API请求延迟
  setTimeout(() => {
    // 这里应该是真实的API调用，目前使用模拟数据
    // 实现搜索功能
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      teacherList.value = [
        {
          id: 1,
          name: '张三',
          teacherId: 'T2023001',
          gender: '1',
          phone: '13800138001',
          email: '<EMAIL>',
          department: '计算机科学与技术学院',
          status: '1'
        },
        {
          id: 2,
          name: '李四',
          teacherId: 'T2023002',
          gender: '1',
          phone: '13800138002',
          email: '<EMAIL>',
          department: '人工智能学院',
          status: '1'
        },
        {
          id: 3,
          name: '王五',
          teacherId: 'T2023003',
          gender: '1',
          phone: '13800138003',
          email: '<EMAIL>',
          department: '数学与统计学院',
          status: '1'
        },
        {
          id: 4,
          name: '赵六',
          teacherId: 'T2023004',
          gender: '1',
          phone: '13800138004',
          email: '<EMAIL>',
          department: '物理学院',
          status: '0'
        },
        {
          id: 5,
          name: '钱七',
          teacherId: '********',
          gender: '2',
          phone: '13800138005',
          email: '<EMAIL>',
          department: '化学学院',
          status: '1'
        }
      ].filter(item => 
        item.name.toLowerCase().includes(keyword) || 
        item.teacherId.toLowerCase().includes(keyword)
      )
    }
    calculateTotal()
    loading.value = false
  }, 300)
}

// 计算总数
const calculateTotal = () => {
  total.value = teacherList.value.length
}

// 分页相关方法
const handleSizeChange = (val) => {
  pageSize.value = val
  loadTeacherList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadTeacherList()
}

// 编辑教师
const handleEdit = (row) => {
  isEdit.value = true
  currentId.value = row.id
  Object.assign(teacherForm, {
    name: row.name,
    gender: row.gender,
    teacherId: row.teacherId,
    phone: row.phone,
    email: row.email,
    department: row.department,
    status: row.status
  })
  dialogVisible.value = true
}

// 删除教师
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除教师 "${row.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 在实际应用中，这里应该调用删除API
    teacherList.value = teacherList.value.filter(item => item.id !== row.id)
    calculateTotal()
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 提交表单
const submitForm = () => {
  teacherFormRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        // 编辑现有教师
        const index = teacherList.value.findIndex(item => item.id === currentId.value)
        if (index !== -1) {
          teacherList.value[index] = {
            ...teacherList.value[index],
            name: teacherForm.name,
            gender: teacherForm.gender,
            teacherId: teacherForm.teacherId,
            phone: teacherForm.phone,
            email: teacherForm.email,
            department: teacherForm.department,
            status: teacherForm.status
          }
          ElMessage.success('更新成功')
        }
      } else {
        // 添加新教师
        const newId = teacherList.value.length > 0 ? Math.max(...teacherList.value.map(item => item.id)) + 1 : 1
        teacherList.value.push({
          id: newId,
          name: teacherForm.name,
          gender: teacherForm.gender,
          teacherId: teacherForm.teacherId,
          phone: teacherForm.phone,
          email: teacherForm.email,
          department: teacherForm.department,
          status: teacherForm.status
        })
        calculateTotal()
        ElMessage.success('添加成功')
      }
      dialogVisible.value = false
      resetForm()
    }
  })
}

// 重置表单
const resetForm = () => {
  isEdit.value = false
  currentId.value = 0
  teacherForm.name = ''
  teacherForm.gender = '1'
  teacherForm.teacherId = ''
  teacherForm.phone = ''
  teacherForm.email = ''
  teacherForm.department = ''
  teacherForm.status = '1'
}
</script>

<style scoped>
.teacher-container {
  padding: 10px;
}

.teacher-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.teacher-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.teacher-card {
  margin-bottom: 20px;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.search-input {
  width: 300px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 
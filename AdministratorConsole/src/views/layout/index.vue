<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <div class="logo-container">
        <h1 class="logo-title">大模型教学管理系统</h1>
      </div>
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
      >
        <el-menu-item index="/dashboard">
          <el-icon><el-icon-menu /></el-icon>
          <span>控制台</span>
        </el-menu-item>
        <el-menu-item index="/model">
          <el-icon><el-icon-cpu /></el-icon>
          <span>大模型管理</span>
        </el-menu-item>
        <el-menu-item index="/teacher">
          <el-icon><el-icon-user /></el-icon>
          <span>教师管理</span>
        </el-menu-item>
        <el-menu-item index="/student">
          <el-icon><el-icon-user /></el-icon>
          <span>学生管理</span>
        </el-menu-item>
        <el-menu-item index="/class">
          <el-icon><el-icon-office-building /></el-icon>
          <span>班级管理</span>
        </el-menu-item>
        <el-menu-item index="/resource">
          <el-icon><el-icon-files /></el-icon>
          <span>资源管理</span>
        </el-menu-item>
      </el-menu>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <div class="navbar">
        <div class="right-menu">
          <el-dropdown>
            <span class="avatar-wrapper">
              管理员 <el-icon><el-icon-arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人设置</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 主内容 -->
      <div class="app-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const activeMenu = computed(() => {
  return '/' + route.path.split('/')[1]
})
</script>

<style scoped>
.app-wrapper {
  display: flex;
  height: 100vh;
  width: 100%;
}

.sidebar-container {
  width: 210px;
  height: 100%;
  background: #304156;
  transition: width 0.28s;
  overflow-y: auto;
}

.logo-container {
  height: 60px;
  line-height: 60px;
  text-align: center;
  background: #2b2f3a;
}

.logo-title {
  color: #fff;
  font-size: 16px;
  margin: 0;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navbar {
  height: 50px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  padding: 0 20px;
  justify-content: flex-end;
}

.right-menu {
  display: flex;
  align-items: center;
}

.avatar-wrapper {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.app-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f0f2f5;
}
</style> 
<template>
  <div class="student-container">
    <div class="student-header">
      <h2>学生管理</h2>
      <div>
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
        >
          <el-button type="primary" plain>批量导入</el-button>
        </el-upload>
        <el-button type="primary" @click="dialogVisible = true" style="margin-left: 10px;">添加学生</el-button>
      </div>
    </div>
    
    <el-card class="student-card">
      <!-- 搜索区域 -->
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入学生姓名、学号或班级"
          class="search-input"
          clearable
          @clear="loadStudentList"
        >
          <template #append>
            <el-button @click="loadStudentList">
              <el-icon><el-icon-search /></el-icon>
            </el-button>
          </template>
        </el-input>
        
        <el-select v-model="filterClass" placeholder="按班级筛选" clearable @change="loadStudentList" style="margin-left: 10px; width: 180px;">
          <el-option v-for="item in classList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      
      <!-- 学生列表 -->
      <el-table :data="studentList" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="studentId" label="学号" width="120" />
        <el-table-column prop="className" label="班级" width="180" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="scope">
            {{ scope.row.gender === '1' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="150" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '在读' : '休学' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 批量操作 -->
      <div class="batch-operation">
        <el-button type="danger" size="small" @click="handleBatchDelete">批量删除</el-button>
        <el-button type="primary" size="small" @click="handleExport">导出数据</el-button>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 添加/编辑学生对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑学生' : '添加学生'"
      width="500px"
    >
      <el-form :model="studentForm" label-width="100px" ref="studentFormRef" :rules="rules">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="studentForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="studentForm.gender">
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="学号" prop="studentId">
          <el-input v-model="studentForm.studentId" placeholder="请输入学号" />
        </el-form-item>
        <el-form-item label="班级" prop="className">
          <el-select v-model="studentForm.className" placeholder="请选择班级" style="width: 100%">
            <el-option v-for="item in classList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="studentForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="studentForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="studentForm.status">
            <el-radio label="1">在读</el-radio>
            <el-radio label="0">休学</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 批量导入对话框 -->
    <el-dialog v-model="importDialogVisible" title="批量导入学生" width="500px">
      <div v-if="fileList.length === 0">
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
        >
          <el-icon class="el-icon--upload"><el-icon-upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              请上传Excel文件(.xlsx/.xls)，<a href="#">下载模板</a>
            </div>
          </template>
        </el-upload>
      </div>
      <div v-else>
        <p>已选择文件: {{ fileList[0].name }}</p>
        <el-progress :percentage="uploadProgress" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleImport" :disabled="fileList.length === 0">开始导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 页面状态
const loading = ref(false)
const dialogVisible = ref(false)
const importDialogVisible = ref(false)
const isEdit = ref(false)
const currentId = ref(0)
const searchKeyword = ref('')
const filterClass = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const studentFormRef = ref(null)
const uploadProgress = ref(0)
const fileList = ref([])

// 班级列表
const classList = ref([
  { value: '计算机科学与技术2021级1班', label: '计算机科学与技术2021级1班' },
  { value: '计算机科学与技术2021级2班', label: '计算机科学与技术2021级2班' },
  { value: '人工智能2022级1班', label: '人工智能2022级1班' },
  { value: '人工智能2022级2班', label: '人工智能2022级2班' },
  { value: '数据科学2023级1班', label: '数据科学2023级1班' }
])

// 学生列表数据
const studentList = ref([
  {
    id: 1,
    name: '张明',
    studentId: 'S202101001',
    className: '计算机科学与技术2021级1班',
    gender: '1',
    phone: '13900138001',
    email: '<EMAIL>',
    status: '1'
  },
  {
    id: 2,
    name: '李华',
    studentId: 'S202101002',
    className: '计算机科学与技术2021级1班',
    gender: '1',
    phone: '13900138002',
    email: '<EMAIL>',
    status: '1'
  },
  {
    id: 3,
    name: '王芳',
    studentId: 'S202201001',
    className: '人工智能2022级1班',
    gender: '2',
    phone: '13900138003',
    email: '<EMAIL>',
    status: '1'
  },
  {
    id: 4,
    name: '赵敏',
    studentId: 'S202201002',
    className: '人工智能2022级1班',
    gender: '2',
    phone: '13900138004',
    email: '<EMAIL>',
    status: '0'
  },
  {
    id: 5,
    name: '刘强',
    studentId: 'S202301001',
    className: '数据科学2023级1班',
    gender: '1',
    phone: '13900138005',
    email: '<EMAIL>',
    status: '1'
  }
])

// 表单数据
const studentForm = reactive({
  name: '',
  gender: '1',
  studentId: '',
  className: '',
  phone: '',
  email: '',
  status: '1'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ],
  className: [
    { required: true, message: '请选择班级', trigger: 'change' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 生命周期钩子
onMounted(() => {
  loadStudentList()
  calculateTotal()
})

// 加载学生列表
const loadStudentList = () => {
  loading.value = true
  // 模拟API请求延迟
  setTimeout(() => {
    // 这里应该是真实的API调用，目前使用模拟数据
    let filteredList = [
      {
        id: 1,
        name: '张明',
        studentId: 'S202101001',
        className: '计算机科学与技术2021级1班',
        gender: '1',
        phone: '13900138001',
        email: '<EMAIL>',
        status: '1'
      },
      {
        id: 2,
        name: '李华',
        studentId: 'S202101002',
        className: '计算机科学与技术2021级1班',
        gender: '1',
        phone: '13900138002',
        email: '<EMAIL>',
        status: '1'
      },
      {
        id: 3,
        name: '王芳',
        studentId: 'S202201001',
        className: '人工智能2022级1班',
        gender: '2',
        phone: '13900138003',
        email: '<EMAIL>',
        status: '1'
      },
      {
        id: 4,
        name: '赵敏',
        studentId: 'S202201002',
        className: '人工智能2022级1班',
        gender: '2',
        phone: '13900138004',
        email: '<EMAIL>',
        status: '0'
      },
      {
        id: 5,
        name: '刘强',
        studentId: 'S202301001',
        className: '数据科学2023级1班',
        gender: '1',
        phone: '13900138005',
        email: '<EMAIL>',
        status: '1'
      }
    ]
    
    // 按班级筛选
    if (filterClass.value) {
      filteredList = filteredList.filter(item => item.className === filterClass.value)
    }
    
    // 按关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filteredList = filteredList.filter(item => 
        item.name.toLowerCase().includes(keyword) || 
        item.studentId.toLowerCase().includes(keyword) ||
        item.className.toLowerCase().includes(keyword)
      )
    }
    
    studentList.value = filteredList
    calculateTotal()
    loading.value = false
  }, 300)
}

// 计算总数
const calculateTotal = () => {
  total.value = studentList.value.length
}

// 分页相关方法
const handleSizeChange = (val) => {
  pageSize.value = val
  loadStudentList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadStudentList()
}

// 编辑学生
const handleEdit = (row) => {
  isEdit.value = true
  currentId.value = row.id
  Object.assign(studentForm, {
    name: row.name,
    gender: row.gender,
    studentId: row.studentId,
    className: row.className,
    phone: row.phone,
    email: row.email,
    status: row.status
  })
  dialogVisible.value = true
}

// 删除学生
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除学生 "${row.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 在实际应用中，这里应该调用删除API
    studentList.value = studentList.value.filter(item => item.id !== row.id)
    calculateTotal()
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 批量删除
const handleBatchDelete = () => {
  ElMessageBox.confirm(
    '确定要批量删除选中的学生吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 在实际应用中，这里应该调用批量删除API
    ElMessage.success('批量删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 导出数据
const handleExport = () => {
  ElMessage.success('学生数据导出成功')
}

// 提交表单
const submitForm = () => {
  studentFormRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        // 编辑现有学生
        const index = studentList.value.findIndex(item => item.id === currentId.value)
        if (index !== -1) {
          studentList.value[index] = {
            ...studentList.value[index],
            name: studentForm.name,
            gender: studentForm.gender,
            studentId: studentForm.studentId,
            className: studentForm.className,
            phone: studentForm.phone,
            email: studentForm.email,
            status: studentForm.status
          }
          ElMessage.success('更新成功')
        }
      } else {
        // 添加新学生
        const newId = studentList.value.length > 0 ? Math.max(...studentList.value.map(item => item.id)) + 1 : 1
        studentList.value.push({
          id: newId,
          name: studentForm.name,
          gender: studentForm.gender,
          studentId: studentForm.studentId,
          className: studentForm.className,
          phone: studentForm.phone,
          email: studentForm.email,
          status: studentForm.status
        })
        calculateTotal()
        ElMessage.success('添加成功')
      }
      dialogVisible.value = false
      resetForm()
    }
  })
}

// 重置表单
const resetForm = () => {
  isEdit.value = false
  currentId.value = 0
  studentForm.name = ''
  studentForm.gender = '1'
  studentForm.studentId = ''
  studentForm.className = ''
  studentForm.phone = ''
  studentForm.email = ''
  studentForm.status = '1'
}

// 处理文件选择
const handleFileChange = (file) => {
  fileList.value = [file]
  if (file.status === 'ready') {
    importDialogVisible.value = true
  }
}

// 处理批量导入
const handleImport = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  // 模拟上传进度
  uploadProgress.value = 0
  const timer = setInterval(() => {
    uploadProgress.value += 10
    if (uploadProgress.value >= 100) {
      clearInterval(timer)
      ElMessage.success('导入成功！新增学生30名')
      importDialogVisible.value = false
      fileList.value = []
      loadStudentList()
    }
  }, 300)
}
</script>

<style scoped>
.student-container {
  padding: 10px;
}

.student-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.student-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.student-card {
  margin-bottom: 20px;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.search-input {
  width: 300px;
}

.batch-operation {
  margin-top: 20px;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 
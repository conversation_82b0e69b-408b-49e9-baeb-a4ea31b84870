import { createRouter, createWebHistory } from 'vue-router'
import Layout from '../views/layout/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/login/index.vue')
    },
    {
      path: '/',
      component: Layout,
      redirect: '/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('../views/dashboard/index.vue'),
          meta: { title: '控制台', icon: 'dashboard' }
        },
        {
          path: 'model',
          name: 'Model',
          component: () => import('../views/model/index.vue'),
          meta: { title: '大模型管理', icon: 'model' }
        },
        {
          path: 'teacher',
          name: 'Teacher',
          component: () => import('../views/teacher/index.vue'),
          meta: { title: '教师管理', icon: 'teacher' }
        },
        {
          path: 'student',
          name: 'Student',
          component: () => import('../views/student/index.vue'),
          meta: { title: '学生管理', icon: 'student' }
        },
        {
          path: 'class',
          name: 'Class',
          component: () => import('../views/class/index.vue'),
          meta: { title: '班级管理', icon: 'class' }
        },
        {
          path: 'resource',
          name: 'Resource',
          component: () => import('../views/resource/index.vue'),
          meta: { title: '资源管理', icon: 'resource' }
        }
      ]
    }
  ]
})

export default router 
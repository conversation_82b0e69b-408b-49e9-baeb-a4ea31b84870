/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", <PERSON><PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  color: #303133;
}

a {
  text-decoration: none;
  color: #409EFF;
}

.container {
  padding: 20px;
}

/* 通用卡片样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

/* 通用表格样式 */
.table-container {
  margin-bottom: 20px;
}

/* 通用表单样式 */
.form-container {
  max-width: 600px;
  margin: 0 auto;
} 
# 大模型教学助手 - 管理员控制台

这是大模型教学助手应用的管理员控制台，用于管理大模型API密钥和系统配置。

## 功能特点

- 大模型API密钥管理
- 支持多种模型提供商：火山引擎、DeepSeek、Moonshot、讯飞等
- API密钥自动同步到环境变量
- API连接测试

## 技术栈

- 前端：Vue 3 + Element Plus + Vite
- 后端：Express.js + MySQL

## 安装和使用

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
# 同时启动前端和后端（推荐）
npm run start

# 或者分别启动
# 启动前端
npm run dev

# 启动后端
npm run server
```

### 构建生产版本

```bash
npm run build
```

## 功能说明

### 大模型管理

- **添加/编辑大模型**：配置各种大模型的API密钥和参数
- **测试连接**：测试API密钥是否可正常连接
- **同步到环境变量**：将配置同步到.env文件，供应用使用

### 数据库配置

系统使用MySQL数据库存储大模型配置，相关表结构为：

```sql
CREATE TABLE `model_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型名称',
  `provider` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提供商标识符',
  `api_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API密钥',
  `app_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'APP ID (如讯飞需要)',
  `base_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API基础URL',
  `usage_scenario` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '使用场景',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态，1=启用，0=禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_status` (`status`),
  KEY `idx_usage` (`usage_scenario`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
``` 
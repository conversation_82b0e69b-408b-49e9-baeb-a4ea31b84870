import { exec } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 使用Vite构建SPA并提供开发服务器
const viteProcess = exec('npm run dev', {
  cwd: path.resolve(__dirname)
});

viteProcess.stdout.on('data', (data) => {
  console.log(`Vite: ${data}`);
});

viteProcess.stderr.on('data', (data) => {
  console.error(`Vite错误: ${data}`);
});

// 务必等待Vite启动
setTimeout(() => {
  // 启动后端服务器
  const serverProcess = exec('node server/app.js', {
    cwd: path.resolve(__dirname)
  });

  serverProcess.stdout.on('data', (data) => {
    console.log(`Server: ${data}`);
  });

  serverProcess.stderr.on('data', (data) => {
    console.error(`Server错误: ${data}`);
  });

  console.log('管理员控制台已启动！');
  console.log('前端地址: http://localhost:4000');
  console.log('后端地址: http://localhost:8888');
}, 3000);

console.log('正在启动管理员控制台...');
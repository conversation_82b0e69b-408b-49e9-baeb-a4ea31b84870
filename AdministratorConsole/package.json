{"name": "admin-console", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/app.js", "start": "node start.js"}, "dependencies": {"axios": "^1.6.2", "body-parser": "^1.20.2", "connect-history-api-fallback": "^2.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "element-plus": "^2.3.14", "express": "^4.18.2", "mysql2": "^3.6.0", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9"}, "type": "module"}
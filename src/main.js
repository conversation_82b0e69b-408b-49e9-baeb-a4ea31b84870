import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './style.css'
import './assets/styles/main.css'
import { useUserStore } from './stores/user'
import { registerGlobalComponents } from './components/global'

const app = createApp(App)
const pinia = createPinia()

// 先初始化 Pinia
app.use(pinia)

// 初始化用户状态
const userStore = useUserStore()
userStore.initializeFromStorage()

// 注册全局组件
registerGlobalComponents(app)

// 再初始化路由和其他插件
app.use(router)
app.use(Antd)

app.mount('#app')

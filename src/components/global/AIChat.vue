<template>
  <div class="ai-chat-fixed" v-if="userStore.isAuthenticated">
    <!-- 悬浮按钮 -->
    <button class="ai-chat-button" @click="isOpen = !isOpen">
      <img v-if="!isOpen" src="/AI.png" alt="AI助手" class="ai-icon" />
      <span v-else class="close-icon">×</span>
    </button>

    <!-- 聊天窗口 -->
    <div class="ai-chat-window" v-if="isOpen">
      <div class="ai-chat-header">
        <div class="ai-chat-title">AI 助教</div>
        <div class="ai-chat-header-actions">
          <button class="ai-chat-clear" @click="clearChat">
            清空对话
          </button>
          <button class="ai-chat-close" @click="isOpen = false">×</button>
        </div>
      </div>

      <div class="ai-chat-messages" ref="messagesContainer">
        <div
          v-for="(msg, index) in messages"
          :key="index"
          class="ai-chat-message"
          :class="msg.type"
        >
          <div class="ai-chat-bubble">{{ msg.content }}</div>
          <div class="ai-chat-time">{{ formatTime(msg.time) }}</div>
        </div>
      </div>

      <div class="ai-chat-input-container">
        <input
          v-model="inputMessage"
          @keyup.enter="sendMessage"
          placeholder="输入您的问题..."
          class="ai-chat-input"
          ref="inputField"
        />
        <button class="ai-chat-send" @click="sendMessage" :disabled="loading">
          {{ loading ? '发送中...' : '发送' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import axios from 'axios'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const isOpen = ref(false)
const inputMessage = ref('')
const loading = ref(false)
const messages = ref([
  {
    type: 'ai',
    content: '你好！我是AI助教，请问有什么可以帮助你的？',
    time: new Date()
  }
])
const messagesContainer = ref(null)
const inputField = ref(null)

// 格式化时间
const formatTime = (date) => {
  const d = new Date(date)
  const hours = d.getHours().toString().padStart(2, '0')
  const minutes = d.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

// 发送消息
const sendMessage = async () => {
  // 检查是否有内容
  if (!inputMessage.value.trim() || loading.value) return

  // 添加用户消息
  const userMsg = {
    type: 'user',
    content: inputMessage.value,
    time: new Date()
  }
  messages.value.push(userMsg)

  // 清空输入框并设置加载状态
  const message = inputMessage.value
  inputMessage.value = ''
  loading.value = true

  try {
    // 发送请求
    const response = await axios.post('http://localhost:3000/api/chat', {
      message
    }, {
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      }
    })

    // 获取AI回复
    const aiContent = response.data.data?.content || '抱歉，我无法回答这个问题'

    // 添加AI消息
    messages.value.push({
      type: 'ai',
      content: aiContent,
      time: new Date()
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    // 添加错误消息
    messages.value.push({
      type: 'ai',
      content: '发送消息失败，请重试',
      time: new Date()
    })
  } finally {
    loading.value = false
  }
}

// 监听消息变化，滚动到底部
watch(messages, () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}, { deep: true })

// 聊天窗口打开时，聚焦输入框
watch(isOpen, (newVal) => {
  if (newVal) {
    nextTick(() => {
      inputField.value?.focus()
    })
  }
})

// 清空聊天内容
const clearChat = () => {
  messages.value = [{
    type: 'ai',
    content: '你好！我是AI助教，请问有什么可以帮助你的？',
    time: new Date()
  }];
};
</script>

<style scoped>
.ai-chat-fixed {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 9999;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.ai-chat-button {
  width: 60px;
  height: 60px;
  background: white;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  padding: 0;
  overflow: hidden;
}

.ai-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.close-icon {
  font-size: 30px;
  color: #4f46e5;
  font-weight: bold;
}

.ai-chat-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.ai-chat-window {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 400px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.ai-chat-header {
  padding: 16px 20px;
  background: #4c4de6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-chat-title {
  font-weight: 600;
  font-size: 18px;
  color: white;
}

.ai-chat-header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.ai-chat-clear {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px 12px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s;
}

.ai-chat-clear:hover {
  background: rgba(255, 255, 255, 0.3);
}

.ai-chat-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: white;
  opacity: 0.8;
  transition: all 0.3s;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-chat-close:hover {
  opacity: 1;
  transform: scale(1.1);
}

.ai-chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #f9fafb;
}

.ai-chat-messages::-webkit-scrollbar {
  width: 6px;
}

.ai-chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb {
  background: #c7c7c7;
  border-radius: 3px;
}

.ai-chat-message {
  display: flex;
  flex-direction: column;
  max-width: 85%;
}

.ai-chat-message.ai {
  align-self: flex-start;
}

.ai-chat-message.user {
  align-self: flex-end;
}

.ai-chat-bubble {
  padding: 12px 18px;
  border-radius: 12px;
  word-break: break-word;
  line-height: 1.6;
  font-size: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.ai-chat-message.ai .ai-chat-bubble {
  background: white;
  color: #2c3e50;
  border-top-left-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ai-chat-message.user .ai-chat-bubble {
  background: #4c4de6;
  color: white;
  border-top-right-radius: 4px;
  box-shadow: 0 2px 8px rgba(76, 77, 230, 0.2);
}

.ai-chat-time {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 6px;
  padding: 0 5px;
}

.ai-chat-message.user .ai-chat-time {
  text-align: right;
}

.ai-chat-input-container {
  padding: 16px;
  display: flex;
  gap: 12px;
  border-top: 1px solid #e5e7eb;
  background: white;
}

.ai-chat-input {
  flex: 1;
  padding: 12px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  outline: none;
  font-size: 15px;
  transition: all 0.3s;
  background: #f9fafb;
}

.ai-chat-input:focus {
  border-color: #4c4de6;
  box-shadow: 0 0 0 3px rgba(76, 77, 230, 0.2);
  background: white;
}

.ai-chat-send {
  padding: 12px 24px;
  background: #4c4de6;
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s;
  white-space: nowrap;
  box-shadow: 0 3px 8px rgba(76, 77, 230, 0.2);
}

.ai-chat-send:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.3);
  background: #3a3bb0;
}

.ai-chat-send:disabled {
  background: #9ca3af;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

@media (max-width: 480px) {
  .ai-chat-window {
    width: 320px;
  }
  
  .ai-chat-button {
    width: 50px;
    height: 50px;
    border-radius: 10px;
  }
}
</style>
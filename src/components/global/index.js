import { createApp } from 'vue'
import AIChat from './AIChat.vue'
import { useUserStore } from '@/stores/user'

export function registerGlobalComponents(_app) {
  // 创建AI聊天组件的挂载点
  const aiChatContainer = document.createElement('div')
  aiChatContainer.id = 'ai-chat-container'
  document.body.appendChild(aiChatContainer)

  // 创建并挂载AI聊天组件
  const aiChatApp = createApp(AIChat)

  // 获取用户存储
  const userStore = useUserStore()

  // 监听用户登录状态变化
  userStore.$subscribe((_mutation, state) => {
    updateAIChatVisibility(state.isAuthenticated)
  })

  // 初始检查用户是否已登录
  updateAIChatVisibility(userStore.isAuthenticated)

  // 挂载组件（初始可能是隐藏的）
  aiChatApp.mount('#ai-chat-container')

  // 更新AI聊天组件可见性的函数
  function updateAIChatVisibility(isAuthenticated) {
    if (aiChatContainer) {
      if (isAuthenticated) {
        aiChatContainer.style.display = ''
      } else {
        aiChatContainer.style.display = 'none'
      }
    }
  }
}
<script setup>
import { RouterView } from 'vue-router'
import { onErrorCaptured } from 'vue'
import AIChat from '@/components/global/AIChat.vue'

onErrorCaptured((err) => {
  console.error('App error:', err)
  return false
})
</script>

<template>
  <div id="app">
    <router-view></router-view>
    <AIChat />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  background-color: #f5f5f5;
}

#app {
  width: 100%;
  min-height: 100vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.main-content {
  flex: 1;
  overflow-y: auto;
}

.page-container {
  height: 100%;
  overflow-y: auto;
  padding: 24px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

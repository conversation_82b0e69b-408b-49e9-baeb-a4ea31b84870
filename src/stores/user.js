import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
  state: () => {
    // 安全地解析 userInfo
    let userInfo = {};
    try {
      const storedUserInfo = localStorage.getItem('userInfo');
      if (storedUserInfo) {
        userInfo = JSON.parse(storedUserInfo);
      }
    } catch (error) {
      console.error('Failed to parse userInfo from localStorage:', error);
    }

    return {
      token: localStorage.getItem('token') || '',
      userInfo,
      isAuthenticated: !!localStorage.getItem('token')
    };
  },

  getters: {
    user: (state) => state.userInfo,
    getUserInfo: (state) => state.userInfo,
    getToken: (state) => state.token,
    isLoggedIn: (state) => !!state.token,
    isStudent: (state) => state.userInfo?.role === 'student',
    isTeacher: (state) => state.userInfo?.role === 'teacher'
  },

  actions: {
    setUser(userInfo) {
      if (!userInfo) {
        return;
      }
      
      this.userInfo = userInfo;
      this.isAuthenticated = true;
      
      try {
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
      } catch (error) {
        console.error('保存用户信息到localStorage失败:', error);
      }
    },

    setToken(token) {
      if (!token) {
        return;
      }
      
      this.token = token;
      this.isAuthenticated = true;
      localStorage.setItem('token', token);
    },

    clearUser() {
      this.token = '';
      this.userInfo = {};
      this.isAuthenticated = false;
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
    },

    initializeFromStorage() {
      try {
        const token = localStorage.getItem('token');
        const storedUserInfo = localStorage.getItem('userInfo');

        if (token) {
          this.token = token;
          this.isAuthenticated = true;
          
          if (storedUserInfo) {
            const userInfo = JSON.parse(storedUserInfo);
            this.userInfo = userInfo;
          }
        } else {
          this.clearUser();
        }
      } catch (error) {
        console.error('初始化用户状态失败:', error);
        this.clearUser();
      }
    }
  }
}); 
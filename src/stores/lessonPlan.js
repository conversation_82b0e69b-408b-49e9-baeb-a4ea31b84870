import { defineStore } from 'pinia'

export const useLessonPlanStore = defineStore('lessonPlan', {
  state: () => ({
    selectedCourseId: null,
    selectedCourseName: '',
    totalHours: 40,
    originalTotalPeriods: null, // 数据库中的原始课时数
    syllabusContent: null,
    courseContent: null
  }),

  actions: {
    setCourse(courseId, courseName) {
      this.selectedCourseId = courseId
      this.selectedCourseName = courseName
    },
    setSyllabusData(data) {
      if (data) {
        this.syllabusContent = data.content
        this.courseContent = data.allocation
        this.totalHours = data.total || 40
        this.originalTotalPeriods = data.originalTotalPeriods || null
      }
    },
    clearData() {
      this.selectedCourseId = null
      this.selectedCourseName = ''
      this.syllabusContent = null
      this.courseContent = null
      this.totalHours = 40
      this.originalTotalPeriods = null
    }
  },

  persist: {
    enabled: true,
    strategies: [
      {
        storage: sessionStorage, // 使用 sessionStorage，这样刷新页面时数据还在，但关闭浏览器后会清除
      },
    ],
  },
})
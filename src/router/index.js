import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'
import { message } from 'ant-design-vue'

// 认证相关
import Login from '../views/auth/Login.vue'
import Register from '../views/auth/Register.vue'
import StudentLogin from '../views/student/Login.vue'
import StudentRegister from '../views/student/Register.vue'

// 首页及子页面
import Home from '../views/home/<USER>'
import Overview from '../views/home/<USER>'
import Features from '../views/home/<USER>'
import Technology from '../views/home/<USER>'
import Application from '../views/home/<USER>'
import CoreFunctions from '../views/home/<USER>'

// 工作台相关
import WorkStation from '../views/workstation/index.vue'

// 备课相关
import SmartPreparation from '../views/preparation/SmartPreparation.vue'
import LessonPlan from '../views/preparation/LessonPlan.vue'
import PPTMaker from '../views/preparation/PPTMaker.vue'

// 课堂相关
import ClassInteraction from '../views/classroom/ClassInteraction.vue'

// 作业和成绩相关
import GradeAnalysis from '@/views/assessment/GradeAnalysis.vue'
import PreviewAnalysis from '@/views/assessment/PreviewAnalysis.vue'
import PreviewEvaluation from '@/views/assessment/components/PreviewEvaluation.vue'
import StudentAnswers from '@/views/assessment/StudentAnswers.vue'
import PlagiarismAnalysis from '@/views/assessment/PlagiarismAnalysis.vue'
import ExercisePublish from '@/views/homework/ExercisePublish.vue'
import PreviewManagement from '@/views/homework/PreviewManagement.vue'
import StudentPreviewAnswers from '@/views/assessment/StudentPreviewAnswers.vue'

// 资源相关
import ResourceCenter from '../views/resources/ResourceCenter.vue'
import ExerciseMaker from '../views/resources/ExerciseMaker.vue'

// 测验路由
const assessmentRoutes = [
  {
    path: '/assessment/grade-analysis',
    name: 'GradeAnalysis',
    component: GradeAnalysis,
    meta: {
      requiresAuth: true,
      roles: ['teacher']
    }
  },
  {
    path: '/assessment/preview-evaluation',
    name: 'PreviewEvaluation',
    component: PreviewEvaluation,
    meta: {
      requiresAuth: true,
      roles: ['teacher']
    }
  },
  {
    path: '/assessment/student-answers/:classId/:chapterId/:batchId/:studentId',
    name: 'StudentAnswers',
    component: StudentAnswers,
    meta: {
      requiresAuth: true,
      roles: ['teacher']
    }
  },
  {
    path: '/assessment/student-preview/:classId/:chapterId/:studentId',
    name: 'StudentPreviewAnswers',
    component: StudentPreviewAnswers,
    meta: {
      requiresAuth: true,
      roles: ['teacher']
    }
  }
]

// 组合所有路由
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      requiresAuth: false,
      title: '首页'
    }
  },
  {
    path: '/overview',
    name: 'Overview',
    component: Overview,
    meta: {
      requiresAuth: false,
      title: '平台概述'
    }
  },
  {
    path: '/features',
    name: 'Features',
    component: Features,
    meta: {
      requiresAuth: false,
      title: '功能特点'
    }
  },
  {
    path: '/technology',
    name: 'Technology',
    component: Technology,
    meta: {
      requiresAuth: false,
      title: '技术架构'
    }
  },
  {
    path: '/application',
    name: 'Application',
    component: Application,
    meta: {
      requiresAuth: false,
      title: '应用场景'
    }
  },
  {
    path: '/core-functions',
    name: 'CoreFunctions',
    component: CoreFunctions,
    meta: {
      requiresAuth: false,
      title: '核心功能'
    }
  },

  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录'
    }
  },
  {
    path: '/student-login',
    name: 'StudentLogin',
    component: () => import('../views/student/Login.vue'),
    meta: {
      title: '学生登录',
      requiresAuth: false,
      role: 'student'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/auth/Register.vue'),
    meta: {
      title: '教师注册',
      requiresAuth: false,
      role: 'teacher'
    }
  },
  {
    path: '/student-register',
    name: 'StudentRegister',
    component: () => import('../views/student/Register.vue'),
    meta: {
      title: '学生注册',
      requiresAuth: false,
      role: 'student'
    }
  },
  {
    path: '/student-dashboard',
    name: 'StudentDashboard',
    component: () => import('../views/student/Dashboard.vue'),
    meta: {
      title: '学生面板',
      requiresAuth: true,
      role: 'student'
    }
  },
  {
    path: '/student/preview/:previewId',
    name: 'PreviewLearning',
    component: () => import('../views/student/components/dashboard/preview_learning.vue'),
    meta: {
      title: '预习学习',
      requiresAuth: true,
      role: 'student'
    }
  },
  {
    path: '/student/course-selection',
    name: 'CourseSelection',
    component: () => import('../views/student/components/dashboard/CourseSelection.vue'),
    meta: {
      title: '选课',
      requiresAuth: true,
      role: 'student'
    }
  },
  {
    path: '/student/exercise-homework-look/:classId/:batchId',
    name: 'homework-look',
    component: () => import('../views/student/components/dashboard/exercise_homework/homework_look.vue'),
    meta: {
      title: '作业详情',
      requiresAuth: true,
      role: 'student'
    }
  },
  {
    path: '/student/exercise-homework-learning/:classId/:batchId',
    name: 'homework-learning',
    component: () => import('../views/student/components/dashboard/exercise_homework/exercise_homework_learning.vue'),
    meta: {
      title: '作业学习',
      requiresAuth: true,
      role: 'student'
    }
  },
  {
    path: '/student/exercise-preview-learning/:classId/:chapterId',
    name: 'preview-learning',
    component: () => import('../views/student/components/dashboard/exercise_preview/exercise_preview_learning.vue'),
    meta: {
      title: '预习习题',
      requiresAuth: true,
      role: 'student'
    }
  },
  {
    path: '/student/exercise-preview-look/:classId/:chapterId',
    name: 'preview-look',
    component: () => import('../views/student/components/dashboard/exercise_preview/exercise_preview_look.vue'),
    meta: {
      title: '查看预习',
      requiresAuth: true,
      role: 'student'
    }
  },
  {
    path: '/student/shared-video/:id',
    name: 'SharedVideoDetail',
    component: () => import('../views/student/SharedVideoDetail.vue'),
    meta: {
      title: '分享视频详情',
      requiresAuth: true,
      role: 'student'
    }
  },
  {
    path: '/dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: {
      requiresAuth: true,
      role: 'teacher'
    },
    children: [
      {
        path: 'workstation',
        name: 'Workstation',
        component: () => import('../views/workstation/index.vue'),
        meta: {
          requiresAuth: true,
          role: 'teacher'
        }
      },
      {
        path: 'smart-preparation',
        name: 'SmartPreparation',
        component: () => import('../views/preparation/SmartPreparation.vue'),
        meta: {
          requiresAuth: true,
          role: 'teacher'
        }
      },
      {
        path: 'lesson-plan',
        name: 'LessonPlan',
        component: () => import('../views/preparation/LessonPlan.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'preparation/lesson/:courseId/:hour',
        name: 'LessonPlanDetail',
        component: () => import('../views/preparation/LessonPlanDetail.vue'),
        meta: {
          requiresAuth: true,
          title: '教案详情'
        }
      },
      {
        path: 'ppt-maker',
        name: 'PPTMaker',
        component: () => import('../views/preparation/PPTMaker.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'class-interaction',
        name: 'ClassInteraction',
        component: () => import('../views/classroom/ClassInteraction.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'grade-analysis',
        name: 'DashboardGradeAnalysis',
        component: GradeAnalysis,
        meta: {
          requiresAuth: true,
          role: 'teacher',
          title: '成绩分析'
        }
      },
      {
        path: 'preview-analysis',
        name: 'PreviewAnalysis',
        component: PreviewAnalysis,
        meta: { requiresAuth: true }
      },
      {
        path: 'exercise-publish',
        name: 'ExercisePublish',
        component: ExercisePublish,
        meta: {
          requiresAuth: true,
          role: 'teacher',
          title: '习题发布'
        }
      },
      {
        path: 'preview-management',
        name: 'PreviewManagement',
        component: PreviewManagement,
        meta: {
          requiresAuth: true,
          role: 'teacher',
          title: '预习管理'
        }
      },
      {
        path: 'resource-center',
        name: 'ResourceCenter',
        component: () => import('../views/resources/ResourceCenter.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'favorites',
        name: 'Favorites',
        component: () => import('../views/favorites/FavoritesView.vue'),
        meta: {
          requiresAuth: true,
          title: '我的收藏'
        }
      },
      {
        path: 'note-generator',
        name: 'NoteGenerator',
        component: () => import('../views/notes/NoteGenerator.vue'),
        meta: {
          requiresAuth: true,
          title: '笔记生成'
        }
      },
      {
        path: 'note-generator/:videoId',
        name: 'NoteGeneratorWithVideo',
        component: () => import('../views/notes/NoteGenerator.vue'),
        meta: {
          requiresAuth: true,
          title: '笔记生成'
        }
      },
      {
        path: 'exercise-maker',
        name: 'ExerciseMaker',
        component: () => import('../views/resources/ExerciseMaker.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'preview-maker',
        name: 'PreviewMaker',
        component: () => import('../views/resources/previewMaker.vue'),
        meta: {
          requiresAuth: true,
          title: '预习资料'
        }
      },
      {
        path: 'animation-maker',
        name: 'AnimationMaker',
        component: () => import('../views/preparation/AnimationMaker.vue'),
        meta: {
          requiresAuth: true,
          title: '动画制作'
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('../views/dashboard/Profile.vue'),
        meta: {
          requiresAuth: true,
          role: 'teacher',
          title: '个人信息'
        }
      },
      {
        path: 'plagiarism-analysis',
        name: 'PlagiarismAnalysis',
        component: PlagiarismAnalysis,
        meta: {
          requiresAuth: true,
          role: 'teacher',
          title: '查重分析'
        }
      }
    ]
  },
  ...assessmentRoutes
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 不需要认证的路由直接放行
  if (!to.meta.requiresAuth || to.path === '/') {
    next()
    return
  }

  // 需要认证但未登录
  if (!userStore.isLoggedIn) {
    next({
      path: to.meta.role === 'student' ? '/student-login' : '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 已登录且角色匹配
  const userRole = userStore.userInfo?.role
  if (!to.meta.role || to.meta.role === userRole) {
    next()
    return
  }

  // 角色不匹配，重定向到对应的首页
  message.error('您没有权限访问该页面')
  next(userRole === 'student' ? '/student-dashboard' : '/dashboard/workstation')
})

// 错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router
/* Ant Design 主题覆盖 */
@import './variables.css';

/* 按钮 */
.ant-btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
}

.ant-btn-link {
  color: var(--primary-color) !important;
}

.ant-btn-link:hover,
.ant-btn-link:focus {
  color: var(--primary-hover) !important;
}

/* 复选框 */
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-checkbox-indeterminate .ant-checkbox-inner::after {
  background-color: var(--primary-color) !important;
}

/* 单选框 */
.ant-radio-checked .ant-radio-inner {
  border-color: var(--primary-color) !important;
}

.ant-radio-inner::after {
  background-color: var(--primary-color) !important;
}

.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
  background-color: var(--primary-color) !important;
}

/* 选择器 */
.ant-select-focused .ant-select-selector,
.ant-select-selector:hover,
.ant-select-open .ant-select-selector {
  border-color: var(--primary-color) !important;
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}

/* 分页 */
.ant-pagination-item-active {
  border-color: var(--primary-color) !important;
}

.ant-pagination-item-active a {
  color: var(--primary-color) !important;
}

.ant-pagination-item:hover {
  border-color: var(--primary-color) !important;
}

.ant-pagination-item:hover a {
  color: var(--primary-color) !important;
}

.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* 标签 */
.ant-tag-blue {
  color: var(--primary-color) !important;
  background: rgba(76, 77, 230, 0.1) !important;
  border-color: rgba(76, 77, 230, 0.2) !important;
}

/* 开关 */
.ant-switch-checked {
  background-color: var(--primary-color) !important;
}

/* 滑块 */
.ant-slider-track {
  background-color: var(--primary-color) !important;
}

.ant-slider-handle {
  border-color: var(--primary-color) !important;
}

.ant-slider-handle:focus {
  box-shadow: 0 0 0 5px rgba(76, 77, 230, 0.2) !important;
}

/* 进度条 */
.ant-progress-bg {
  background-color: var(--primary-color) !important;
}

/* 表格 */
.ant-table-thead > tr > th {
  background: var(--background-color-dark) !important;
}

.ant-table-tbody > tr.ant-table-row-selected > td {
  background: var(--primary-light) !important;
}

/* 表单 */
.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-input-affix-wrapper:focus,
.ant-form-item-has-error .ant-input-focused,
.ant-form-item-has-error .ant-input-affix-wrapper-focused {
  border-color: var(--error-color) !important;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2) !important;
}

.ant-form-item-has-success.ant-form-item-has-feedback .ant-form-item-children-icon {
  color: var(--success-color) !important;
}

/* 日期选择器 */
.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
  background: var(--primary-color) !important;
}

.ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
  border-color: var(--primary-color) !important;
}

.ant-picker:hover, 
.ant-picker-focused {
  border-color: var(--primary-color) !important;
}

.ant-picker-focused {
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}

/* 输入框 */
.ant-input:hover {
  border-color: var(--primary-color) !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}

.ant-input-affix-wrapper:hover {
  border-color: var(--primary-color) !important;
}

.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}

/* 下拉菜单 */
.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu-title:hover {
  background-color: var(--primary-light) !important;
}

/* 步骤条 */
.ant-steps-item-process .ant-steps-item-icon {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-steps-item-finish .ant-steps-item-icon {
  border-color: var(--primary-color) !important;
}

.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
  color: var(--primary-color) !important;
}

.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {
  background-color: var(--primary-color) !important;
}

/* 警告框 */
.ant-alert-info {
  background-color: var(--primary-light) !important;
  border-color: var(--primary-color) !important;
}

/* 模态框 */
.ant-modal-title {
  color: var(--text-primary) !important;
}

/* 抽屉 */
.ant-drawer-title {
  color: var(--text-primary) !important;
}

/* 折叠面板 */
.ant-collapse-header-text {
  color: var(--text-primary) !important;
}

.ant-collapse-item:hover {
  border-color: var(--primary-color) !important;
}

/* 标签页 */
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--primary-color) !important;
}

.ant-tabs-ink-bar {
  background: var(--primary-color) !important;
}

.ant-tabs-tab:hover {
  color: var(--primary-hover) !important;
}

/* 通知 */
.ant-notification-notice-icon-info {
  color: var(--primary-color) !important;
}

/* 消息 */
.ant-message-info .anticon,
.ant-message-loading .anticon {
  color: var(--primary-color) !important;
}

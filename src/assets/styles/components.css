/* 导入变量 */
@import './variables.css';

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
  cursor: pointer;
  border: none;
  outline: none;
  text-decoration: none;
}

.btn-icon {
  margin-right: 0.5rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-primary:active {
  background-color: var(--primary-active);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
}

.btn-secondary:active {
  background-color: var(--secondary-active);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-hover);
}

.btn-success:active {
  background-color: var(--success-active);
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background-color: var(--warning-hover);
}

.btn-warning:active {
  background-color: var(--warning-active);
}

.btn-danger {
  background-color: var(--error-color);
  color: white;
}

.btn-danger:hover {
  background-color: var(--error-hover);
}

.btn-danger:active {
  background-color: var(--error-active);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color-dark);
  color: var(--text-secondary);
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:active {
  background-color: var(--primary-light);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: var(--font-size-md);
}

.btn-block {
  display: block;
  width: 100%;
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid var(--border-color-dark);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-normal), box-shadow var(--transition-normal);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-text {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--border-radius-md);
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.badge-success {
  background-color: var(--success-color);
  color: white;
}

.badge-warning {
  background-color: var(--warning-color);
  color: white;
}

.badge-danger {
  background-color: var(--error-color);
  color: white;
}

.badge-info {
  background-color: var(--info-color);
  color: white;
}

/* 警告框样式 */
.alert {
  position: relative;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
}

.alert-primary {
  color: var(--primary-active);
  background-color: var(--primary-light);
  border-color: var(--primary-color);
}

.alert-secondary {
  color: var(--secondary-active);
  background-color: var(--secondary-light);
  border-color: var(--secondary-color);
}

.alert-success {
  color: var(--success-active);
  background-color: var(--success-light);
  border-color: var(--success-color);
}

.alert-warning {
  color: var(--warning-active);
  background-color: var(--warning-light);
  border-color: var(--warning-color);
}

.alert-danger {
  color: var(--error-active);
  background-color: var(--error-light);
  border-color: var(--error-color);
}

.alert-info {
  color: var(--info-active);
  background-color: var(--info-light);
  border-color: var(--info-color);
}

/* 列表样式 */
.list-group {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: var(--border-radius-md);
}

.list-group-item {
  position: relative;
  display: block;
  padding: var(--spacing-md);
  background-color: #fff;
  border: 1px solid var(--border-color);
}

.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.list-group-item:last-child {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}

.list-group-item + .list-group-item {
  border-top-width: 0;
}

.list-group-item-action {
  width: 100%;
  color: var(--text-secondary);
  text-align: inherit;
  cursor: pointer;
}

.list-group-item-action:hover,
.list-group-item-action:focus {
  z-index: 1;
  color: var(--text-primary);
  text-decoration: none;
  background-color: var(--background-color);
}

/* 表格样式 */
.table {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--spacing-sm);
  vertical-align: top;
  border-top: 1px solid var(--border-color);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--border-color);
  background-color: var(--background-color);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.table tbody + tbody {
  border-top: 2px solid var(--border-color);
}

.table-hover tbody tr:hover {
  background-color: var(--background-color-light);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.table-bordered {
  border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--border-color);
}

/* 功能卡片样式 */
.feature-card {
  display: flex;
  flex-direction: column;
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  height: 100%;
  text-decoration: none;
  color: var(--text-primary);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-md);
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.feature-card h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.feature-card p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 0;
  flex-grow: 1;
}

/* 工具卡片样式 */
.tool-card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.tool-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.tool-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xxl);
  margin-bottom: var(--spacing-md);
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.tool-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.tool-features {
  margin-bottom: var(--spacing-md);
}

.feature-item {
  margin-bottom: var(--spacing-md);
}

.feature-item h4 {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.feature-item p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 0;
}

.tool-action {
  margin-top: var(--spacing-md);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .feature-card, .tool-card {
    padding: var(--spacing-md);
  }
  
  .feature-icon, .tool-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
  }
  
  .feature-card h3, .tool-card h3 {
    font-size: var(--font-size-md);
  }
  
  .feature-item h4 {
    font-size: var(--font-size-sm);
  }
}

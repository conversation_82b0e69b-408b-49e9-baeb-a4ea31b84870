/* 作业与成绩部分统一样式 */
@import './variables.css';

/* 页面容器 */
.assessment-page {
  padding: var(--spacing-lg);
  background: var(--background-color);
  min-height: calc(100vh - 64px);
}

/* 页面标题区域 */
.assessment-header {
  margin-bottom: var(--spacing-xl);
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

.assessment-header h1 {
  font-size: var(--font-size-xxl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-semibold);
}

.assessment-header .subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}

/* 内容区域 */
.assessment-content {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0;
  background: transparent;
}

/* 卡片容器 */
.assessment-card {
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.assessment-card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.assessment-card-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.assessment-card-content {
  padding: var(--spacing-lg);
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: all var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.stat-title {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 筛选卡片 */
.filter-card {
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.filter-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.filter-content {
  padding: var(--spacing-lg);
}

/* 图表卡片 */
.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.chart-card {
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.chart-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
}

.chart-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.chart-container {
  height: 400px;
  padding: var(--spacing-md);
  width: 100%;
}

/* 习题管理 */
.exercise-management {
  width: 100%;
}

.tab-content {
  background: var(--background-color-light);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  min-height: 500px;
  width: 100%;
  overflow: hidden;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.selection-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  align-items: center;
  background: var(--background-color-dark);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
}

.filter-item {
  flex-shrink: 0;
}

/* 习题卡片 */
.exercise-card {
  width: 100%;
  cursor: pointer;
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-xs);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.exercise-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.exercise-card-selected {
  border: 1px solid var(--primary-color);
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2);
}

/* 学生答案详情 */
.student-answers-container {
  padding: var(--spacing-lg);
  background: var(--background-color);
  min-height: calc(100vh - 64px);
}

.overview-card,
.nav-card,
.answers-section {
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.overview-header,
.nav-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
}

.overview-header h3,
.nav-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.overview-content {
  padding: var(--spacing-lg);
}

.nav-buttons {
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.nav-button {
  min-width: 40px;
  height: 40px;
  font-weight: var(--font-weight-medium);
}

.correct-btn {
  border-color: var(--success-color);
}

.wrong-btn {
  border-color: var(--error-color);
}

.unanswered-btn {
  border-color: var(--border-color);
  color: var(--text-tertiary);
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xxxl) 0;
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .assessment-page {
    padding: var(--spacing-md);
  }
  
  .tab-header {
    flex-direction: column;
  }
  
  .header-left {
    flex-direction: column;
    width: 100%;
  }
  
  .selection-actions {
    margin-top: var(--spacing-md);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .nav-buttons {
    padding: var(--spacing-sm);
  }
  
  .nav-button {
    min-width: 32px;
    height: 32px;
  }
}

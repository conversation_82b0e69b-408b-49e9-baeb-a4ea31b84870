/* 导入所有样式文件 */
@import './variables.css';
@import './layout.css';
@import './components.css';
@import './utilities.css';
@import './assessment.css';
@import './ant-design-override.css';

/* 全局基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-normal);
}

a:hover {
  color: var(--primary-hover);
}

img {
  max-width: 100%;
  height: auto;
}

/* 清除列表样式 */
ul, ol {
  list-style: none;
}

/* 表单元素基础样式 */
button, input, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* 焦点样式 */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-color-light);
}

::-webkit-scrollbar-thumb {
  background: var(--text-quaternary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 动画过渡 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 页面容器 */
#app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

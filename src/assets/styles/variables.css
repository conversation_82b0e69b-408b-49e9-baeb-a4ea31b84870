:root {
  /* 颜色系统 */
  --primary-color: #4c4de6;
  --primary-hover: #6364e9;
  --primary-active: #3e3fc9;
  --primary-light: #eeeeff;

  --secondary-color: #4c4de6;
  --secondary-hover: #6364e9;
  --secondary-active: #3e3fc9;
  --secondary-light: #eeeeff;

  --success-color: #52c41a;
  --success-hover: #73d13d;
  --success-active: #389e0d;
  --success-light: #f6ffed;

  --warning-color: #faad14;
  --warning-hover: #ffc53d;
  --warning-active: #d48806;
  --warning-light: #fffbe6;

  --error-color: #f5222d;
  --error-hover: #ff4d4f;
  --error-active: #cf1322;
  --error-light: #fff1f0;

  --info-color: #4c4de6;
  --info-hover: #6364e9;
  --info-active: #3e3fc9;
  --info-light: #eeeeff;

  /* 中性色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-quaternary: #cccccc;

  --border-color: #e8e8e8;
  --border-color-dark: #d9d9d9;
  --border-color-light: #f0f0f0;

  --background-color: #f5f7fa;
  --background-color-light: #ffffff;
  --background-color-dark: #f0f2f5;

  /* 字体系统 */
  --font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;

  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  --font-size-xxxl: 30px;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 间距系统 */
  --spacing-xxs: 4px;
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  --spacing-xxxl: 64px;

  /* 边框圆角 */
  --border-radius-sm: 2px;
  --border-radius-md: 4px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-xxl: 16px;
  --border-radius-circle: 50%;

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-xxl: 0 12px 24px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* 布局宽度 */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-xxl: 1440px;
  --container-max: 1600px;

  /* Z-index层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

/* 响应式断点 */
@custom-media --breakpoint-xs (max-width: 575px);
@custom-media --breakpoint-sm (min-width: 576px);
@custom-media --breakpoint-md (min-width: 768px);
@custom-media --breakpoint-lg (min-width: 992px);
@custom-media --breakpoint-xl (min-width: 1200px);
@custom-media --breakpoint-xxl (min-width: 1600px);

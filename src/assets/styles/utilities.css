/* 导入变量 */
@import './variables.css';

/* 间距工具类 */
/* Margin */
.m-0 { margin: 0 !important; }
.m-xxs { margin: var(--spacing-xxs) !important; }
.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }
.m-xxl { margin: var(--spacing-xxl) !important; }
.m-xxxl { margin: var(--spacing-xxxl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-xxs { margin-top: var(--spacing-xxs) !important; }
.mt-xs { margin-top: var(--spacing-xs) !important; }
.mt-sm { margin-top: var(--spacing-sm) !important; }
.mt-md { margin-top: var(--spacing-md) !important; }
.mt-lg { margin-top: var(--spacing-lg) !important; }
.mt-xl { margin-top: var(--spacing-xl) !important; }
.mt-xxl { margin-top: var(--spacing-xxl) !important; }
.mt-xxxl { margin-top: var(--spacing-xxxl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-xxs { margin-right: var(--spacing-xxs) !important; }
.mr-xs { margin-right: var(--spacing-xs) !important; }
.mr-sm { margin-right: var(--spacing-sm) !important; }
.mr-md { margin-right: var(--spacing-md) !important; }
.mr-lg { margin-right: var(--spacing-lg) !important; }
.mr-xl { margin-right: var(--spacing-xl) !important; }
.mr-xxl { margin-right: var(--spacing-xxl) !important; }
.mr-xxxl { margin-right: var(--spacing-xxxl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-xxs { margin-bottom: var(--spacing-xxs) !important; }
.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }
.mb-xl { margin-bottom: var(--spacing-xl) !important; }
.mb-xxl { margin-bottom: var(--spacing-xxl) !important; }
.mb-xxxl { margin-bottom: var(--spacing-xxxl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-xxs { margin-left: var(--spacing-xxs) !important; }
.ml-xs { margin-left: var(--spacing-xs) !important; }
.ml-sm { margin-left: var(--spacing-sm) !important; }
.ml-md { margin-left: var(--spacing-md) !important; }
.ml-lg { margin-left: var(--spacing-lg) !important; }
.ml-xl { margin-left: var(--spacing-xl) !important; }
.ml-xxl { margin-left: var(--spacing-xxl) !important; }
.ml-xxxl { margin-left: var(--spacing-xxxl) !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-xxs { margin-left: var(--spacing-xxs) !important; margin-right: var(--spacing-xxs) !important; }
.mx-xs { margin-left: var(--spacing-xs) !important; margin-right: var(--spacing-xs) !important; }
.mx-sm { margin-left: var(--spacing-sm) !important; margin-right: var(--spacing-sm) !important; }
.mx-md { margin-left: var(--spacing-md) !important; margin-right: var(--spacing-md) !important; }
.mx-lg { margin-left: var(--spacing-lg) !important; margin-right: var(--spacing-lg) !important; }
.mx-xl { margin-left: var(--spacing-xl) !important; margin-right: var(--spacing-xl) !important; }
.mx-xxl { margin-left: var(--spacing-xxl) !important; margin-right: var(--spacing-xxl) !important; }
.mx-xxxl { margin-left: var(--spacing-xxxl) !important; margin-right: var(--spacing-xxxl) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-xxs { margin-top: var(--spacing-xxs) !important; margin-bottom: var(--spacing-xxs) !important; }
.my-xs { margin-top: var(--spacing-xs) !important; margin-bottom: var(--spacing-xs) !important; }
.my-sm { margin-top: var(--spacing-sm) !important; margin-bottom: var(--spacing-sm) !important; }
.my-md { margin-top: var(--spacing-md) !important; margin-bottom: var(--spacing-md) !important; }
.my-lg { margin-top: var(--spacing-lg) !important; margin-bottom: var(--spacing-lg) !important; }
.my-xl { margin-top: var(--spacing-xl) !important; margin-bottom: var(--spacing-xl) !important; }
.my-xxl { margin-top: var(--spacing-xxl) !important; margin-bottom: var(--spacing-xxl) !important; }
.my-xxxl { margin-top: var(--spacing-xxxl) !important; margin-bottom: var(--spacing-xxxl) !important; }

/* Padding */
.p-0 { padding: 0 !important; }
.p-xxs { padding: var(--spacing-xxs) !important; }
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }
.p-xxl { padding: var(--spacing-xxl) !important; }
.p-xxxl { padding: var(--spacing-xxxl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-xxs { padding-top: var(--spacing-xxs) !important; }
.pt-xs { padding-top: var(--spacing-xs) !important; }
.pt-sm { padding-top: var(--spacing-sm) !important; }
.pt-md { padding-top: var(--spacing-md) !important; }
.pt-lg { padding-top: var(--spacing-lg) !important; }
.pt-xl { padding-top: var(--spacing-xl) !important; }
.pt-xxl { padding-top: var(--spacing-xxl) !important; }
.pt-xxxl { padding-top: var(--spacing-xxxl) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-xxs { padding-right: var(--spacing-xxs) !important; }
.pr-xs { padding-right: var(--spacing-xs) !important; }
.pr-sm { padding-right: var(--spacing-sm) !important; }
.pr-md { padding-right: var(--spacing-md) !important; }
.pr-lg { padding-right: var(--spacing-lg) !important; }
.pr-xl { padding-right: var(--spacing-xl) !important; }
.pr-xxl { padding-right: var(--spacing-xxl) !important; }
.pr-xxxl { padding-right: var(--spacing-xxxl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-xxs { padding-bottom: var(--spacing-xxs) !important; }
.pb-xs { padding-bottom: var(--spacing-xs) !important; }
.pb-sm { padding-bottom: var(--spacing-sm) !important; }
.pb-md { padding-bottom: var(--spacing-md) !important; }
.pb-lg { padding-bottom: var(--spacing-lg) !important; }
.pb-xl { padding-bottom: var(--spacing-xl) !important; }
.pb-xxl { padding-bottom: var(--spacing-xxl) !important; }
.pb-xxxl { padding-bottom: var(--spacing-xxxl) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-xxs { padding-left: var(--spacing-xxs) !important; }
.pl-xs { padding-left: var(--spacing-xs) !important; }
.pl-sm { padding-left: var(--spacing-sm) !important; }
.pl-md { padding-left: var(--spacing-md) !important; }
.pl-lg { padding-left: var(--spacing-lg) !important; }
.pl-xl { padding-left: var(--spacing-xl) !important; }
.pl-xxl { padding-left: var(--spacing-xxl) !important; }
.pl-xxxl { padding-left: var(--spacing-xxxl) !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-xxs { padding-left: var(--spacing-xxs) !important; padding-right: var(--spacing-xxs) !important; }
.px-xs { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
.px-sm { padding-left: var(--spacing-sm) !important; padding-right: var(--spacing-sm) !important; }
.px-md { padding-left: var(--spacing-md) !important; padding-right: var(--spacing-md) !important; }
.px-lg { padding-left: var(--spacing-lg) !important; padding-right: var(--spacing-lg) !important; }
.px-xl { padding-left: var(--spacing-xl) !important; padding-right: var(--spacing-xl) !important; }
.px-xxl { padding-left: var(--spacing-xxl) !important; padding-right: var(--spacing-xxl) !important; }
.px-xxxl { padding-left: var(--spacing-xxxl) !important; padding-right: var(--spacing-xxxl) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-xxs { padding-top: var(--spacing-xxs) !important; padding-bottom: var(--spacing-xxs) !important; }
.py-xs { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
.py-sm { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }
.py-md { padding-top: var(--spacing-md) !important; padding-bottom: var(--spacing-md) !important; }
.py-lg { padding-top: var(--spacing-lg) !important; padding-bottom: var(--spacing-lg) !important; }
.py-xl { padding-top: var(--spacing-xl) !important; padding-bottom: var(--spacing-xl) !important; }
.py-xxl { padding-top: var(--spacing-xxl) !important; padding-bottom: var(--spacing-xxl) !important; }
.py-xxxl { padding-top: var(--spacing-xxxl) !important; padding-bottom: var(--spacing-xxxl) !important; }

/* 文本工具类 */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-md { font-size: var(--font-size-md) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-xxl { font-size: var(--font-size-xxl) !important; }
.text-xxxl { font-size: var(--font-size-xxxl) !important; }

.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--error-color) !important; }
.text-info { color: var(--info-color) !important; }

.text-dark { color: var(--text-primary) !important; }
.text-muted { color: var(--text-secondary) !important; }
.text-light { color: var(--text-tertiary) !important; }
.text-lighter { color: var(--text-quaternary) !important; }

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-break: break-word !important;
  word-wrap: break-word !important;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 背景工具类 */
.bg-primary { background-color: var(--primary-color) !important; }
.bg-primary-light { background-color: var(--primary-light) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-secondary-light { background-color: var(--secondary-light) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-success-light { background-color: var(--success-light) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-warning-light { background-color: var(--warning-light) !important; }
.bg-danger { background-color: var(--error-color) !important; }
.bg-danger-light { background-color: var(--error-light) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-info-light { background-color: var(--info-light) !important; }
.bg-light { background-color: var(--background-color-light) !important; }
.bg-dark { background-color: var(--background-color-dark) !important; }

/* 边框工具类 */
.border { border: 1px solid var(--border-color) !important; }
.border-top { border-top: 1px solid var(--border-color) !important; }
.border-right { border-right: 1px solid var(--border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-left { border-left: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-top-0 { border-top: 0 !important; }
.border-right-0 { border-right: 0 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left-0 { border-left: 0 !important; }

.border-primary { border-color: var(--primary-color) !important; }
.border-secondary { border-color: var(--secondary-color) !important; }
.border-success { border-color: var(--success-color) !important; }
.border-warning { border-color: var(--warning-color) !important; }
.border-danger { border-color: var(--error-color) !important; }
.border-info { border-color: var(--info-color) !important; }
.border-light { border-color: var(--border-color-light) !important; }
.border-dark { border-color: var(--border-color-dark) !important; }

.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-md { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-xxl { border-radius: var(--border-radius-xxl) !important; }
.rounded-circle { border-radius: var(--border-radius-circle) !important; }
.rounded-0 { border-radius: 0 !important; }

/* 显示工具类 */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* 位置工具类 */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

/* 尺寸工具类 */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }

.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }

.vw-100 { width: 100vw !important; }
.vh-100 { height: 100vh !important; }

/* 阴影工具类 */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-xxl { box-shadow: var(--shadow-xxl) !important; }

/* 响应式显示工具类 */
@media (max-width: 575px) {
  .d-none-xs { display: none !important; }
  .d-block-xs { display: block !important; }
  .d-flex-xs { display: flex !important; }
}

@media (min-width: 576px) and (max-width: 767px) {
  .d-none-sm { display: none !important; }
  .d-block-sm { display: block !important; }
  .d-flex-sm { display: flex !important; }
}

@media (min-width: 768px) and (max-width: 991px) {
  .d-none-md { display: none !important; }
  .d-block-md { display: block !important; }
  .d-flex-md { display: flex !important; }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .d-none-lg { display: none !important; }
  .d-block-lg { display: block !important; }
  .d-flex-lg { display: flex !important; }
}

@media (min-width: 1200px) {
  .d-none-xl { display: none !important; }
  .d-block-xl { display: block !important; }
  .d-flex-xl { display: flex !important; }
}

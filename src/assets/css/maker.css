/* 制作器容器 */
.maker-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: calc(100vh - 120px);
}

.maker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
}

.maker-header h2 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: normal;
}

.credits {
  color: #666;
  font-size: 13px;
}

/* 工作区布局 */
.maker-workspace {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 10px;
  flex: 1;
  min-height: 0;
}

/* 输入区域 */
.input-section, .preview-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* 表单区域 */
.form-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

/* 表单组件样式 */
.form-group {
  margin-bottom: 15px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
}

/* 输入框样式 */
.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.3s;
}

/* 文本域样式 */
textarea {
  min-height: 70px;  /* 继续减小高度 */
  resize: vertical;
  line-height: 1.4;
}

/* 下拉框组样式 */
.input-group {
  display: flex;
  gap: 10px;
}

.input-group select {
  flex: 1;
  height: 30px;
}

/* 复选框组样式 */
.checkbox-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 5px 0;
}

.checkbox-group label {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #333;
}

/* 自定义复选框样式 */
.checkbox-group input[type="checkbox"] {
  width: 14px;
  height: 14px;
  border: 1px solid #e8e8e8;
  background-color: #f9f9f9;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
  appearance: none;
  margin: 0;
}

.checkbox-group input[type="checkbox"]:checked {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
}

.checkbox-group input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid var(--primary-color);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* 进度条样式 */
input[type="range"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #fff;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 难度等级标签 */
.difficulty-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.difficulty-labels span {
  font-size: 12px;
  color: #999;
  transition: all 0.2s;
}

.difficulty-labels span.active {
  color: var(--primary-color);
  transform: scale(1.1);
}

/* 数量调节按钮组 */
.number-input {
  display: flex;
  align-items: center;
  width: 120px;
  height: 30px;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
}

.number-input button {
  width: 30px;
  height: 100%;
  border: none;
  background: #f9f9f9;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.number-input button:hover {
  background: #f0f0f0;
}

.number-input input {
  width: 60px;
  height: 100%;
  border: none;
  border-left: 1px solid #e8e8e8;
  border-right: 1px solid #e8e8e8;
  text-align: center;
  font-size: 13px;
}

/* 预览区域 */
.preview-area {
  flex: 1;
  border: 1px dashed #e8e8e8;
  border-radius: 2px;
  padding: 12px;
  margin-bottom: 10px;
  overflow-y: auto;
}

/* 标题样式 */
.input-section h3,
.preview-section h3 {
  margin-bottom: 10px;
  color: #333;
  font-size: 13px;
  font-weight: normal;
}

/* 按钮容器 */
/* .button-container {
  margin-top: 10px;
  padding: 8px;
  border-top: 1px solid #f0f0f0;
  background: white;
  display: flex;
  gap: 8px;
} */

/* 提交按钮
.submit-btn {
  flex: 1;
  padding: 6px 12px;
  background: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.submit-btn:hover {
  background: var(--primary-hover);
} */

.submit-btn:disabled {
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  color: #999;
  cursor: not-allowed;
}

/* 生成按钮调整为次要按钮 */
.generate-btn {
  flex: 1;
  padding: 6px 12px;
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

/* 下载按钮 */
.download-btn {
  padding: 5px 12px;
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.download-btn:hover {
  background: var(--primary-light);
  border-color: var(--primary-hover);
}

/* 滚动条样式 */
.input-section::-webkit-scrollbar,
.preview-area::-webkit-scrollbar {
  width: 6px;
}

.input-section::-webkit-scrollbar-thumb,
.preview-area::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

/* 聊天区域 */
.chat-section {
  margin-top: 20px;
}

.chat-messages {
  height: 200px;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  overflow-y: auto;
  margin-bottom: 10px;
}

.chat-input {
  display: flex;
  gap: 10px;
}

.chat-input textarea {
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  color: #333;
  flex: 1;
  height: 60px;
  resize: none;
  padding: 8px;
  border-radius: 4px;
}

.chat-input textarea:focus {
  background-color: #fff;
  border-color: #40a9ff;
}

.send-btn {
  padding: 0 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.send-btn:hover {
  background: #40a9ff;
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .maker-workspace {
    grid-template-columns: 1fr;
  }

  .maker-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .input-section, .preview-section {
    padding: 15px;
  }

  .form-group {
    margin-bottom: 12px;
  }
}

/* 小屏幕手机 */
@media (max-width: 375px) {
  .maker-header h2 {
    font-size: 18px;
  }

  .input-section, .preview-section {
    padding: 10px;
  }
}

/* 预览内容样式 */
.preview-content {
  line-height: 1.6;
}

.preview-content h4 {
  margin-bottom: 15px;
  text-align: center;
  color: #1890ff;
  font-size: 14px;
}

.preview-content .section {
  margin-bottom: 20px;
}

.preview-content .section-title {
  font-weight: normal;
  margin-bottom: 12px;
  color: #333;
  font-size: 13px;
}
/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* 左侧面板样式 */
.left-panel {
    width: 420px;
    background-color: #fff;
    padding: 20px;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

/* 教师信息样式 */
.teacher-info {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.avatar {
    width: 80px;
    height: 80px;
    margin-right: 15px;
}

.info-details h3 {
    margin-bottom: 5px;
    color: #2c3e50;
}

.info-details p {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* 日历部分样式 */
.calendar-section {
    margin: 20px 0;
    flex-grow: 1;
    min-height: 380px;
}

#calendar {
    height: 100%;
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #eee;
}

/* 待办事项样式 */
.todo-section {
    margin-top: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #eee;
}

.todo-section h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.todo-list {
    list-style: none;
}

.todo-list li {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.todo-list .time {
    width: 80px;
    color: #666;
}

/* 主要内容区域样式 */
.main-content {
    flex: 1;
    padding: 25px;
    background-color: #f5f5f5;
}

/* 顶部菜单栏样式 */
.top-menu {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.top-menu ul {
    display: flex;
    list-style: none;
    gap: 20px;
}

.top-menu a {
    text-decoration: none;
    color: #666;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s;
}

.top-menu a.active,
.top-menu a:hover {
    background: var(--primary-color);
    color: #fff;
}

/* 课表区域样式 */
.schedule-resources {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.today-schedule {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.schedule-list {
    margin-top: 15px;
}

.schedule-item {
    display: flex;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 10px;
}

.schedule-item .time {
    width: 150px;
    color: #666;
}

.class-info h4 {
    margin-bottom: 5px;
    color: #2c3e50;
}

/* AI资源区域样式 */
.ai-resources {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.resource-box {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.resource-box h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.resource-content {
    color: #666;
    font-style: italic;
}

/* 功能模块样式 */
.function-modules {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: calc(100vh - 140px);
    overflow: hidden;
}

.module-container {
    display: none;
    height: 100%;
}

.module-container.active {
    display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
        padding: 0;
        margin: 0;
        width: 100%;
    }

    .left-panel {
        width: 100%;
        height: auto;
        padding: 0;
        margin: 0;
        box-shadow: none;
    }

    .teacher-info {
        padding: 10px;
        margin: 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .calendar-section {
        margin: 0;
        padding: 0;
        border: none;
        border-radius: 0;
    }

    .todo-section {
        margin: 0;
        padding: 10px;
        border: none;
        border-radius: 0;
        border-top: 1px solid #f0f0f0;
    }

    .main-content {
        padding: 0;
        margin: 0;
        width: 100%;
    }

    .top-menu {
        padding: 10px 0;
        margin: 0;
        border-radius: 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .top-menu ul {
        padding: 0 10px;
        margin: 0;
        gap: 10px;
    }

    .maker-workspace {
        margin: 0;
        padding: 0;
        gap: 0;
    }

    .input-section,
    .preview-section {
        margin: 0;
        padding: 0;
        border: none;
        border-radius: 0;
        box-shadow: none;
    }

    .form-content {
        padding: 10px;
        margin: 0;
    }

    .preview-area {
        margin: 0;
        padding: 10px;
        border: none;
    }

    .function-modules {
        margin: 0;
        padding: 0;
        border-radius: 0;
        box-shadow: none;
    }
}

/* 针对更小屏幕的调整 */
@media (max-width: 480px) {
    .maker-header {
        padding: 10px;
        margin: 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .preview-section {
        margin: 0;
        border-top: 1px solid #f0f0f0;
    }

    .chat-section {
        padding: 10px;
        margin: 0;
    }

    .chat-input {
        padding: 10px;
        margin: 0;
    }

    .logout-btn {
        width: 100%;
        padding: 12px 0;
        margin: 0;
        border-radius: 0;
        border-top: 1px solid #f0f0f0;
    }
}

/* 调整表单组件间距 */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    margin-bottom: 10px;
    display: block;
    color: #333;
    font-weight: 500;
}

/* 输入框高度调整 */
textarea {
    min-height: 120px;
}
/* 日历容器 */
.calendar-section {
  margin: 6px 0;
  padding: 4px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  height: 270px;
  overflow: hidden;  /* 防止内容溢出 */
  box-shadow: inset 0 0 0 1px #fff;
  position: relative;
}

/* 日历主容器 */
.fc {
  height: 100%;
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 10px;
  color: #666;
  background: #fff;
  border-radius: 2px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
}

/* 表格背景 */
.fc-theme-standard td,
.fc-theme-standard th {
  border-color: #f5f5f5 !important;
  background: #fff !important;
}

/* 日历头部 */
.fc .fc-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: 2px;
  margin-bottom: 1px !important;
  padding: 0;
  min-height: 20px;
  user-select: none;  /* 防止文字被选中 */
}

/* 月份标题 */
.fc .fc-toolbar-title {
  font-size: 9px !important;
  color: #333;
  font-weight: normal !important;
  padding: 0 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
  line-height: 1;
  letter-spacing: -0.2px;
}

/* 导航按钮组 */
.fc .fc-button-group {
  display: flex;
  gap: 0;
  background: #fff;
  border-radius: 2px;
  padding: 1px;
}

/* 导航按钮 */
.fc .fc-button {
  background: #f9f9f9 !important;
  border: 1px solid #f0f0f0 !important;
  color: #666 !important;
  padding: 0 !important;
  font-size: 9px !important;
  height: 16px !important;
  min-width: 16px !important;
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  line-height: 1 !important;
  transition: all 0.15s;
  box-shadow: none !important;
}

.fc .fc-button:hover {
  background: #f0f7ff !important;
  border-color: #bae7ff !important;
  color: #1890ff !important;
}

.fc .fc-button:active {
  background: #e6f7ff !important;
  transform: translateY(0.5px);
}

/* 今天按钮 */
.fc .fc-today-button {
  padding: 0 4px !important;
  background: #f0f7ff !important;
  border-color: #bae7ff !important;
  color: #1890ff !important;
}

.fc .fc-today-button:hover {
  background: #e6f7ff !important;
}

/* 月/周切换按钮 */
.fc .fc-dayGridMonth-button,
.fc .fc-timeGridWeek-button {
  background: #f9f9f9 !important;
  border-color: #f0f0f0 !important;
  color: #666 !important;
}

.fc .fc-button-active {
  background: #f0f7ff !important;
  border-color: #bae7ff !important;
  color: #1890ff !important;
  font-weight: normal !important;
}

/* 左右切换按钮 */
.fc .fc-prev-button,
.fc .fc-next-button {
  background: #f9f9f9 !important;
  border-color: #f0f0f0 !important;
  color: #666 !important;
}

.fc .fc-prev-button:hover,
.fc .fc-next-button:hover {
  background: #f0f7ff !important;
  border-color: #bae7ff !important;
  color: #1890ff !important;
}

/* 表头 */
.fc .fc-col-header {
  background: #fafafa !important;
  border-top: 1px solid #f5f5f5;
  border-bottom: 1px solid #f5f5f5;
  height: 16px;
}

.fc .fc-col-header-cell {
  padding: 0 !important;
  font-size: 9px;
  font-weight: normal !important;
  color: #999;
  line-height: 16px;
  letter-spacing: -0.2px;
  text-transform: uppercase;
  background: #fafafa !important;
}

/* 日期网格 */
.fc .fc-scrollgrid {
  border: none !important;
}

.fc .fc-scrollgrid td {
  border-right: none !important;
}

/* 日期单元格 */
.fc .fc-daygrid-day {
  position: relative;
  z-index: 1;
  min-height: 22px !important;
  cursor: pointer;  /* 添加指针样式 */
  transition: background-color 0.1s;
}

.fc .fc-daygrid-day:hover {
  background: #fafafa;
}

.fc .fc-daygrid-day:active {
  background: #f5f5f5;
}

.fc .fc-daygrid-day.fc-day-today::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f0f7ff;
  opacity: 0.3;
  z-index: -1;
}

/* 日期数字容器 */
.fc .fc-daygrid-day-top {
  flex-direction: row !important;
  margin: 0;
  padding: 1px;
}

/* 日期数字 */
.fc .fc-daygrid-day-number {
  font-size: 9px;
  color: #666;
  width: 12px;
  height: 12px;
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 0 !important;
  margin: 1px;
  transition: all 0.1s;
  letter-spacing: -0.3px;
  cursor: pointer;
  user-select: none;
}

.fc .fc-daygrid-day-number:hover {
  background: #f0f0f0;
  color: #333;
}

/* 今天 */
.fc .fc-day-today {
  background: #f8fbff !important;
}

.fc .fc-day-today .fc-daygrid-day-number {
  background: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
}

.fc .fc-day-today .fc-daygrid-day-number:hover {
  background: #bae7ff;
}

/* 周末 */
.fc .fc-day-sat,
.fc .fc-day-sun {
  background: #fafafa !important;
}

.fc .fc-day-sat .fc-daygrid-day-number,
.fc .fc-day-sun .fc-daygrid-day-number {
  color: #999;
}

/* 非当前月 */
.fc .fc-day-other {
  background: #fcfcfc !important;
}

.fc .fc-day-other .fc-daygrid-day-number {
  color: #ccc;
  opacity: 0.4;
}

/* 事件容器 */
.fc .fc-daygrid-day-events {
  margin: 1px 0 0 !important;
  padding: 0 1px !important;
  min-height: 0 !important;
}

/* 事件样式 */
.fc .fc-event {
  position: relative;
  z-index: 2;
  background: #f0f7ff !important;
  border: 1px solid #e6f7ff !important;
  color: #1890ff !important;
  font-size: 8px !important;
  padding: 0 1px !important;
  margin: 1px !important;
  border-radius: 1px !important;
  height: 10px !important;
  line-height: 8px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.1s;
  letter-spacing: -0.2px;
  cursor: pointer;
  user-select: none;
  box-shadow: 0 0 0 1px #fff;
}

.fc .fc-event::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: #1890ff;
  opacity: 0.8;
}

.fc .fc-event:hover {
  background: #e6f7ff !important;
  border-color: #69c0ff !important;
  transform: translateY(-0.5px);
  z-index: 1;
}

.fc .fc-event:active {
  transform: translateY(0);
}

/* 更多事件链接 */
.fc .fc-daygrid-more-link {
  font-size: 8px;
  color: #1890ff;
  padding: 0 1px;
  line-height: 10px;
  height: 10px;
  letter-spacing: -0.2px;
  margin: 0;
  padding: 1px 2px;
  background: #f9f9f9;
  border-radius: 2px;
}

.fc .fc-daygrid-more-link:hover {
  background: #f0f0f0;
  text-decoration: none;
}

/* 边框样式 */
.fc td, .fc th {
  border-color: #fafafa !important;
}

/* 更多事件弹窗 */
.fc-popover {
  border: 1px solid #f0f0f0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-radius: 2px !important;
  z-index: 1000 !important;
  min-width: 120px;
  background: #fff !important;
}

.fc-popover-header {
  background: #fafafa !important;
  padding: 4px 6px !important;
  font-size: 9px !important;
  border-bottom: 1px solid #f0f0f0 !important;
  user-select: none;
}

.fc-popover-close {
  font-size: 14px !important;
  opacity: 0.5;
}

.fc-popover-body {
  padding: 4px !important;
  max-height: 200px;
  overflow-y: auto;
}

.fc-popover .fc-event {
  margin: 2px !important;
}

/* 滚动条样式 */
.fc-scroller {
  scrollbar-width: thin;
  scrollbar-color: #e8e8e8 transparent;
}

.fc-scroller::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.fc-scroller::-webkit-scrollbar-track {
  background: #fafafa;
}

.fc-scroller::-webkit-scrollbar-thumb {
  background-color: #e8e8e8;
  border-radius: 2px;
}

.fc-scroller::-webkit-scrollbar-thumb:hover {
  background-color: #d9d9d9;
}

.fc-scroller::-webkit-scrollbar-corner {
  background: transparent;
}

/* 弹窗滚动条 */
.fc-popover-body::-webkit-scrollbar {
  width: 4px;
}

.fc-popover-body::-webkit-scrollbar-track {
  background: transparent;
}

.fc-popover-body::-webkit-scrollbar-thumb {
  background-color: #e8e8e8;
  border-radius: 2px;
}

/* 加载状态 */
.fc-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.fc-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #f0f0f0;
  border-top-color: #1890ff;
  border-radius: 50%;
  animation: fc-spin 0.8s linear infinite;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes fc-spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .calendar-section {
    height: 250px;
    padding: 3px;
  }

  .fc .fc-toolbar {
    gap: 1px;
  }

  .fc .fc-toolbar-title {
    font-size: 8px !important;
    max-width: 70px;
    letter-spacing: -0.3px;
  }
}

/* 工具栏容器 */
.fc .fc-toolbar.fc-header-toolbar {
  margin: 0 !important;
  padding: 2px 0;
  border-bottom: 1px solid #f5f5f5;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 日历内容区域 */
.fc .fc-view-harness {
  background: #fff;
  position: relative;
  z-index: 1;
}

/* 日期网格容器 */
.fc-daygrid-body {
  width: 100% !important;
}

/* 日期单元格悬停效果 */
.fc .fc-daygrid-day:hover .fc-daygrid-day-number {
  background: #f0f0f0;
  color: #333;
}

/* 事件标题 */
.fc .fc-event-title {
  padding: 0 2px;
}

/* 事件时间 */
.fc .fc-event-time {
  display: none;
}

/* 日期选择效果 */
.fc .fc-daygrid-day.fc-day-selected {
  background: var(--primary-light) !important;
}

.fc .fc-daygrid-day.fc-day-selected .fc-daygrid-day-number {
  background: var(--primary-color);
  color: #fff;
}

/* 周末样式优化 */
.fc .fc-day-sat,
.fc .fc-day-sun {
  background: #fafafa !important;
}

.fc .fc-day-sat .fc-daygrid-day-number,
.fc .fc-day-sun .fc-daygrid-day-number {
  color: #999;
}

/* 非当前月日期样式优化 */
.fc .fc-day-other {
  background: #fcfcfc !important;
}

.fc .fc-day-other .fc-daygrid-day-number {
  color: #ccc;
  opacity: 0.4;
}

/* 今天样式优化 */
.fc .fc-day-today {
  position: relative;
}

.fc .fc-day-today::after {
  content: '';
  position: absolute;
  top: 1px;
  right: 1px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 6px 6px 0;
  border-color: transparent #1890ff transparent transparent;
  opacity: 0.3;
}

/* 工具栏按钮组优化 */
.fc .fc-button-group {
  background: #fafafa;
  border-radius: 2px;
  padding: 1px;
}

.fc .fc-button-group .fc-button {
  margin: 0 !important;
  border-radius: 1px !important;
}

/* 动画效果 */
@keyframes fc-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fc-popover {
  animation: fc-fade-in 0.2s ease-out;
}

.fc .fc-event {
  animation: fc-fade-in 0.3s ease-out;
}
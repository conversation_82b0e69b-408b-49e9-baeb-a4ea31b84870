<template>
  <div class="lesson-plan-detail">
    <!-- 左侧边栏 -->
    <div class="sidebar">
      <!-- 课程选项 -->
      <div class="course-section">
        <h3>课程信息</h3>
        <div class="course-basic-info">
          <div class="info-row">
            <span class="label">课程名称：</span>
            <span class="value">{{ courseInfo.name }}</span>
          </div>
          <div class="info-row">
            <span class="label">总课时：</span>
            <span class="value">{{ courseInfo.totalHours }}</span>
          </div>
        </div>
      </div>

      <!-- 课时目录 -->
      <div class="hour-catalog">
        <h3>课时目录</h3>
        <div class="hour-list">
          <a-menu
            v-model:selectedKeys="selectedHour"
            mode="inline"
            :style="{ border: 'none' }"
          >
            <a-menu-item
              v-for="(content, hour) in hoursList"
              :key="hour"
              @click="selectHour(hour)"
            >
              <template #icon>
                <clock-circle-outlined />
              </template>
              <div class="hour-menu-item">
                <span class="hour-title">{{ hour }}</span>
                <div class="hour-content-preview">
                  <template v-for="(item, index) in content" :key="index">
                    <a-tag
                      :color="getDifficultyColor(item)"
                      class="content-tag"
                    >
                      {{ item.replace(/\[(简单|中等|困难)\]$/, '') }}
                    </a-tag>
                  </template>
                </div>
              </div>
              <a-tag :color="getHourStatus(hour).color" class="hour-status">
                {{ getHourStatus(hour).text }}
              </a-tag>
            </a-menu-item>
          </a-menu>
        </div>
      </div>
    </div>

    <!-- 右侧内容区 -->
    <div class="main-content">
      <!-- 顶部标题和操作栏 -->
      <div class="content-header">
        <div class="title-section">
          <div class="title-main">
            <h2>{{ currentHour }} 教案</h2>
            <a-tag :color="getHourStatus(currentHour).color">
              {{ getHourStatus(currentHour).text }}
            </a-tag>
          </div>
          <div class="title-info">
            <span class="course-name">{{ courseName }}</span>
            <a-divider type="vertical" />
            <span class="lesson-content">
              当前内容：
              <template v-if="currentHourContent.length">
                <a-tag
                  v-for="(item, index) in currentHourContent"
                  :key="index"
                  :color="getDifficultyColor(item)"
                >
                  {{ item.replace(/\[(简单|中等|困难)\]$/, '') }}
                </a-tag>
              </template>
              <span v-else class="no-content">未分配内容</span>
            </span>
          </div>
        </div>
        <div class="actions">
          <a-button @click="router.back()">返回</a-button>
          <a-button type="primary" @click="showTemplateModal" :loading="loading">
            <template #icon><thunderbolt-outlined /></template>
            生成教案
          </a-button>
          <a-button type="primary" @click="openResourceSearch">
            <template #icon><search-outlined /></template>
            搜索相关资源
          </a-button>
          <a-button type="success" @click="savePlan" :loading="loading">
            <template #icon><save-outlined /></template>
            保存
          </a-button>
          <a-button @click="exportPlan" :loading="loading">
            <template #icon><download-outlined /></template>
            导出
          </a-button>
        </div>
      </div>

      <!-- 教案生成后的功能提示卡片 -->
      <div v-if="showFeatureTips" class="feature-tips">
        <div class="tips-content">
          <div class="tips-header">
            <bulb-outlined class="tips-icon" />
            <span>教案已生成,您可以:</span>
            <close-outlined class="close-icon" @click="closeFeatureTips" />
          </div>
          <div class="tips-actions">
            <a-button type="primary" @click="goToPPTMaker">
              <template #icon><file-ppt-outlined /></template>
              制作PPT
            </a-button>
            <a-button type="primary" @click="goToExerciseMaker">
              <template #icon><plus-outlined /></template>
              生成习题
            </a-button>
          </div>
        </div>
      </div>

      <!-- 教案内容区 -->
      <div class="content-body">
        <a-spin :spinning="loading">
          <template v-if="currentPlan && (currentPlan.objectives || currentPlan.keyPoints?.length || currentPlan.teachingProcess?.length)">
            <div class="plan-section" :class="{ 'loading-shimmer': loading }">
              <div class="section-header">
                <h4>教学目标</h4>
                <a-tooltip title="设置合适的教学目标">
                  <info-circle-outlined />
                </a-tooltip>
              </div>
              <a-textarea
                v-model:value="currentPlan.objectives"
                :rows="4"
                placeholder="请输入本节课的教学目标..."
                :bordered="true"
                class="custom-textarea"
                :class="{ 'shimmer-text': loading }"
              />
            </div>

            <div class="plan-section" :class="{ 'loading-shimmer': loading }">
              <div class="section-header">
                <h4>教学重点</h4>
                <a-tooltip title="添加本节课的重点内容">
                  <info-circle-outlined />
                </a-tooltip>
              </div>
              <div class="key-points">
                <template v-for="(point, index) in currentPlan.keyPoints" :key="index">
                  <div class="point-item">
                    <a-input
                      v-model:value="currentPlan.keyPoints[index]"
                      placeholder="请输入重点内容..."
                      class="custom-input"
                      :class="{ 'shimmer-text': loading }"
                    >
                      <template #prefix>{{ index + 1 }}.</template>
                    </a-input>
                    <minus-circle-outlined
                      class="delete-icon"
                      @click="removeKeyPoint(index)"
                    />
                  </div>
                </template>
                <a-button type="dashed" block @click="addKeyPoint" class="add-button">
                  <plus-outlined /> 添加重点
                </a-button>
              </div>
            </div>

            <div class="plan-section" :class="{ 'loading-shimmer': loading }">
              <h4>教学过程</h4>
              <a-collapse v-model:activeKey="activeProcessKeys">
                <a-collapse-panel
                  v-for="(process, index) in currentPlan.teachingProcess"
                  :key="index"
                  :header="process.stage"
                >
                  <div class="process-content">
                    <a-input
                      v-model:value="process.duration"
                      class="duration-input"
                      addon-after="分钟"
                      :class="{ 'shimmer-text': loading }"
                    />
                    <a-textarea
                      v-model:value="process.content"
                      :rows="4"
                      placeholder="请输入教学内容..."
                      :class="{ 'shimmer-text': loading }"
                    />
                  </div>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </template>
          <div v-else class="empty-plan-container">
            <div class="empty-plan-content">
              <thunderbolt-outlined class="empty-icon" />
              <h3>暂无教案内容</h3>
              <p>点击右上角的「生成教案」按钮，快速创建一份专业的教案内容</p>
              <a-button type="primary" @click="showTemplateModal" :loading="loading">
                <template #icon><thunderbolt-outlined /></template>
                立即生成教案
              </a-button>
            </div>
          </div>
        </a-spin>
      </div>
    </div>

    <!-- 添加资源搜索弹窗 -->
    <resource-search-modal
      v-model:visible="resourceModalVisible"
      :initial-keyword="currentHour"
      :course-name="courseName"
      :current-content="currentHourContent"
    />

    <!-- 添加模板选择弹窗 -->
    <lesson-plan-template-modal
      v-model:visible="templateModalVisible"
      :loading="loading"
      @generate="handleTemplateGenerate"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLessonPlanStore } from '@/stores/lessonPlan'
import {
  ClockCircleOutlined,
  ThunderboltOutlined,
  SaveOutlined,
  DownloadOutlined,
  PlusOutlined,
  MinusCircleOutlined,
  InfoCircleOutlined,
  SearchOutlined,
  FilePptOutlined,
  BulbOutlined,
  CloseOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import axios from '@/axios'
import ResourceSearchModal from './ResourceSearchModal.vue'
import LessonPlanTemplateModal from './LessonPlanTemplateModal.vue'

const route = useRoute()
const router = useRouter()
const lessonPlanStore = useLessonPlanStore()

// 状态变量
const loading = ref(false)
const courseName = computed(() => lessonPlanStore.selectedCourseName || '未命名课程')
const totalHours = computed(() => lessonPlanStore.totalHours)
const hoursList = computed(() => lessonPlanStore.courseContent || {})
const selectedHour = ref([])
const currentPlan = ref(null)
const activeProcessKeys = ref(['0', '1', '2', '3'])
const resourceModalVisible = ref(false)
const templateModalVisible = ref(false)
const showFeatureTips = ref(false)

// 存储所有课时的教案状态
const hoursStatus = ref({})

// 计算当前课时
const currentHour = computed(() => {
  return route.params.hour || '未选择'
})

// 获取当前课时的内容
const currentHourContent = computed(() => {
  if (!currentHour.value || !hoursList.value || !hoursList.value[currentHour.value]) {
    return [];
  }
  return hoursList.value[currentHour.value];
});

// 根据难度返回对应的颜色
const getDifficultyColor = (content) => {
  if (content.includes('[简单]')) return 'success';
  if (content.includes('[中等]')) return 'warning';
  if (content.includes('[困难]')) return 'error';
  return 'default';
};

// 从课时字符串中提取数字作为titleId
const getTitleId = (hour) => {
  if (!hour) return null;
  const match = hour.match(/\d+/);
  return match ? parseInt(match[0]) : null;
};

// 选择课时
const selectHour = async (hour) => {
  selectedHour.value = [hour];
  // 更新路由参数但不重新加载页面
  router.replace({
    params: {
      ...route.params,
      hour: hour
    }
  });
  // 加载选中课时的教案
  await loadLessonPlan(hour);
};

// 加载教案内容
const loadLessonPlan = async (hour) => {
  if (!hour) return;

  try {
    loading.value = true;
    const titleId = getTitleId(hour);

    if (!titleId) {
      message.error('无效的课时信息');
      return;
    }

    const response = await axios.get(
      `/api/lesson-detail/detail/${route.params.courseId}/${titleId}`
    );

    if (response.data.success) {
      if (response.data.data) {
        currentPlan.value = {
          title: response.data.data.title,
          content: response.data.data.content || '',
          objectives: response.data.data.teaching_goals || '',
          keyPoints: response.data.data.teaching_methods || [],
          teachingProcess: response.data.data.teaching_steps || [],
          title_id: response.data.data.title_id,
          createTime: response.data.data.createTime,
          updateTime: response.data.data.updateTime
        };
        // 如果已有教案,显示功能提示卡片
        if (currentPlan.value.objectives || currentPlan.value.teaching_goals) {
          showFeatureTips.value = true;
        }
      } else {
        // 如果没有教案数据,设置为null以显示空状态
        currentPlan.value = null;
        showFeatureTips.value = false;
      }
    }
  } catch (error) {
    console.error('加载教案失败:', error);
    message.error('加载教案失败');
  } finally {
    loading.value = false;
  }
};

// 获取课时状态
const getHourStatus = (hour) => {
  // 如果是当前选中的课时，显示为"当前"
  if (hour === currentHour.value) {
    return { color: 'blue', text: '当前' };
  }

  // 从状态缓存中获取该课时的状态
  if (hoursStatus.value[hour]) {
    return { color: 'success', text: '已完成' };
  }

  return { color: 'default', text: '待完成' };
};

// 保存教案
const savePlan = async () => {
  if (!currentPlan.value) {
    message.warning('请先生成或编辑教案');
    return;
  }

  try {
    loading.value = true;
    const planData = {
      courseId: route.params.courseId,
      hour: currentHour.value,
      plan: {
        title: currentPlan.value.title || currentHour.value,
        content: currentPlan.value.content || '',
        objectives: currentPlan.value.objectives || '',
        methods: JSON.stringify(currentPlan.value.keyPoints || []),
        teachingProcess: JSON.stringify(currentPlan.value.teachingProcess || [])
      }
    };

    const response = await axios.post('/api/lesson-detail/save', planData);

    if (response.data.success) {
      message.success('保存成功');
      // 重新加载教案数据
      await loadLessonPlan(currentHour.value);
      // 保存成功后显示功能提示卡片
      showFeatureTips.value = true;

      // 更新课时状态
      hoursStatus.value[currentHour.value] = true;

      // 重新加载所有课时状态
      await loadAllHoursStatus();
    } else {
      throw new Error(response.data.message || '保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败：' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 生成教案
const showTemplateModal = () => {
  templateModalVisible.value = true
}

// 添加模板选择处理函数
const handleTemplateGenerate = async ({ templateId, customRequirements }) => {
  try {
    loading.value = true;
    const titleId = getTitleId(currentHour.value);

    if (!currentHourContent.value || currentHourContent.value.length === 0) {
      message.warning('当前课时没有教学内容');
      return;
    }

    const response = await axios.post('/api/lesson-detail/generate', {
      courseId: route.params.courseId,
      hour: currentHour.value,
      titleId: titleId,
      content: Array.isArray(currentHourContent.value) ?
        currentHourContent.value[0] :
        currentHourContent.value,
      templateId,
      customRequirements
    });

    if (response.data.success) {
      currentPlan.value = response.data.data;
      message.success('教案生成成功');
      templateModalVisible.value = false;
      // 显示功能提示卡片
      showFeatureTips.value = true;

      // 更新课时状态
      hoursStatus.value[currentHour.value] = true;

      // 重新加载所有课时状态
      await loadAllHoursStatus();
    }
  } catch (error) {
    console.error('生成教案失败:', error);
    message.error(error.response?.data?.message || '生成教案失败');
  } finally {
    loading.value = false;
  }
}

// 加载所有课时的状态
const loadAllHoursStatus = async () => {
  try {
    const courseId = route.params.courseId;
    if (!courseId) return;

    const response = await axios.get(`/api/lesson-detail/hours-status/${courseId}`);

    if (response.data.success) {
      hoursStatus.value = response.data.data;
      console.log('课时状态加载成功:', hoursStatus.value);
    }
  } catch (error) {
    console.error('加载课时状态失败:', error);
  }
};

// 初始化数据
onMounted(async () => {
  const { courseId, hour } = route.params;
  if (!courseId || !hour) {
    message.error('缺少必要参数');
    router.push('/dashboard/preparation/lesson-plan');
    return;
  }

  selectedHour.value = [hour];

  // 先加载所有课时状态
  await loadAllHoursStatus();

  // 再加载当前选中课时的详细教案
  await loadLessonPlan(hour);
});

// 监听路由参数变化
watch(
  () => route.params.hour,
  async (newHour, oldHour) => {
    if (newHour && newHour !== oldHour) {
      selectedHour.value = [newHour];
      await loadLessonPlan(newHour);
    }
  }
);

// 添加教学重点
const addKeyPoint = () => {
  if (!currentPlan.value) return
  currentPlan.value.keyPoints.push('')
}

// 删除教学重点
const removeKeyPoint = (index) => {
  if (!currentPlan.value) return
  currentPlan.value.keyPoints.splice(index, 1)
}

// 导出教案
const exportPlan = async () => {
  try {
    const titleId = getTitleId(currentHour.value)
    const response = await axios.get(
      `/api/lesson-detail/export/${route.params.courseId}/${titleId}`,
      { responseType: 'blob' }
    )

    if (response.data) {
      const blob = new Blob([response.data], { type: 'application/msword' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${courseName.value}-${currentHour.value}教案.doc`
      link.click()
      window.URL.revokeObjectURL(url)
    }
  } catch (error) {
    console.error('导出教案失败:', error)
    message.error('导出教案失败')
  }
}

// 跳转到习题生成页面
const goToExerciseMaker = () => {
  router.push({
    name: 'ExerciseMaker',
    query: {
      courseId: route.query.courseId,
      lessonId: currentPlan.value?.title_id,
      lessonTitle: currentPlan.value?.title
    }
  })
}


const goToPPTMaker = () => {
  if (!currentPlan.value) {
    message.warning('请先生成或保存教案');
    return;
  }

  // 获取当前内容的文本，如果有多个内容项，用逗号连接
  let contentTitle = "";
  if (currentHourContent.value && currentHourContent.value.length > 0) {
    contentTitle = currentHourContent.value
      .map(item => item.replace(/\[(简单|中等|困难)\]$/, ''))
      .join(', ');
  } else {
    // 如果没有内容项，则使用课时标题
    contentTitle = currentHour.value;
  }

  router.push({
    name: 'PPTMaker',
    query: {
      courseId: route.params.courseId,
      title: contentTitle, // 使用当前内容作为标题，而不是课时名称
      originalHour: currentHour.value, // 保留原始课时用于API调用
      courseName: courseName.value,
      content: JSON.stringify({
        objectives: currentPlan.value.objectives,
        keyPoints: currentPlan.value.keyPoints,
        teachingProcess: currentPlan.value.teachingProcess
      }),
      autoAnalyzeImages: 'true' // 添加这个参数，表示需要自动分析图片
    }
  });
};

// 修改课程选项区域的显示
const courseInfo = computed(() => {
  return {
    name: courseName.value,
    totalHours: totalHours.value,
    syllabusContent: lessonPlanStore.syllabusContent
  }
});

const openResourceSearch = () => {
  resourceModalVisible.value = true
}

// 关闭功能提示卡片
const closeFeatureTips = () => {
  showFeatureTips.value = false
}
</script>

<style scoped>
.lesson-plan-detail {
  display: flex;
  height: 100vh;
  background-color: rgba(246, 248, 252, 0.8);
  background-image:
    radial-gradient(at 47% 33%, rgba(76, 77, 230, 0.04) 0, transparent 59%),
    radial-gradient(at 82% 65%, rgba(118, 201, 255, 0.07) 0, transparent 55%);
  position: relative;
  margin: 0;
}

/* 背景装饰元素 */
.lesson-plan-detail::before {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: rgba(99, 102, 241, 0.1);
  top: -100px;
  left: 10%;
  filter: blur(70px);
  z-index: 0;
  animation: float 15s ease-in-out infinite;
}

.lesson-plan-detail::after {
  content: '';
  position: absolute;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: rgba(79, 209, 197, 0.1);
  bottom: -50px;
  right: 15%;
  filter: blur(60px);
  z-index: 0;
  animation: float 20s ease-in-out infinite reverse;
}

@keyframes float {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(-15px, 15px) rotate(5deg); }
  50% { transform: translate(15px, -15px) rotate(-5deg); }
  75% { transform: translate(-15px, -15px) rotate(3deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}

.sidebar {
  width: 280px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-right: 1px solid rgba(240, 240, 240, 0.6);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.course-section {
  padding: 16px;
  border-bottom: 1px solid rgba(240, 240, 240, 0.6);
}

.course-section h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  background: linear-gradient(135deg, #4c4de6 0%, #5971e6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.course-basic-info {
  padding: 12px 16px;
  background: rgba(250, 250, 250, 0.5);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 8px;
  margin-top: 8px;
  border: 1px solid rgba(240, 240, 240, 0.8);
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
}

.value {
  color: #1f2937;
  font-weight: 500;
}

.hour-catalog {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.hour-catalog h3 {
  margin: 0;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  border-bottom: 1px solid rgba(240, 240, 240, 0.6);
  background: linear-gradient(135deg, #4c4de6 0%, #5971e6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hour-list {
  flex: 1;
  overflow-y: auto;
}

/* 自定义滚动条 */
.hour-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.hour-list::-webkit-scrollbar-thumb {
  background: rgba(76, 77, 230, 0.3);
  border-radius: 10px;
}

.hour-list::-webkit-scrollbar-track {
  background: rgba(245, 245, 245, 0.3);
  border-radius: 10px;
}

.hour-list :deep(.ant-menu-item) {
  height: auto;
  line-height: 1.5;
  padding: 12px 16px;
  white-space: normal;
  transition: all 0.3s;
  margin: 4px 8px !important;
  border-radius: 8px;
  overflow: hidden;
}

.hour-list :deep(.ant-menu-item:hover) {
  background-color: rgba(76, 77, 230, 0.08);
}

.hour-list :deep(.ant-menu-item-selected) {
  background-color: rgba(76, 77, 230, 0.12) !important;
}

.hour-menu-item {
  flex: 1;
  overflow: hidden;
  margin-right: 8px;
}

.hour-title {
  font-weight: 600;
  margin-bottom: 6px;
  display: block;
  color: #1f2937;
}

.hour-content-preview {
  font-size: 12px;
  color: #666;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.content-tag {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-radius: 12px;
}

.hour-status {
  margin-left: 8px;
  border-radius: 12px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
  z-index: 1;
  position: relative;
}

.content-header {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 20px 24px;
  border-radius: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.content-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4c4de6, #5971e6);
  opacity: 0.8;
  border-radius: 16px 16px 0 0;
}

.title-section {
  flex: 1;
  margin-right: 24px;
}

.title-main {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.title-main h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  background: linear-gradient(135deg, #4c4de6 0%, #5971e6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title-info {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.course-name {
  font-weight: 500;
}

.lesson-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.no-content {
  color: #999;
  font-style: italic;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  flex-shrink: 0;
}

.actions button {
  transition: all 0.3s;
  min-width: 100px;
}

.actions button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.15);
}

/* 下拉菜单样式 */
:deep(.ant-dropdown-menu) {
  padding: 8px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s;
}

:deep(.ant-dropdown-menu-item:hover) {
  background-color: rgba(76, 77, 230, 0.05);
}

:deep(.ant-dropdown-menu-item-icon) {
  margin-right: 8px;
  font-size: 16px;
}

.content-body {
  flex: 1;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.content-body::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4c4de6, #5971e6);
  opacity: 0.8;
  border-radius: 16px 16px 0 0;
}

/* 自定义滚动条 */
.content-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.content-body::-webkit-scrollbar-thumb {
  background: rgba(76, 77, 230, 0.3);
  border-radius: 10px;
}

.content-body::-webkit-scrollbar-track {
  background: rgba(245, 245, 245, 0.3);
  border-radius: 10px;
}

.plan-section {
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 12px;
  border: 1px solid rgba(240, 240, 240, 0.8);
  transition: all 0.3s;
}

.plan-section:hover {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.plan-section h4 {
  margin-bottom: 16px;
  color: #1f2937;
  font-weight: 600;
  font-size: 1.1rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.custom-textarea {
  background: rgba(250, 250, 250, 0.5);
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid rgba(240, 240, 240, 0.8);
}

.custom-textarea:hover, .custom-input:hover {
  border-color: #4c4de6;
}

.key-points {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.point-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.custom-input {
  background: rgba(250, 250, 250, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(240, 240, 240, 0.8);
}

.delete-icon {
  color: #ff4d4f;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.delete-icon:hover {
  color: #ff7875;
  transform: scale(1.2);
}

.add-button {
  margin-top: 8px;
  border-style: dashed;
  border-color: rgba(76, 77, 230, 0.3);
  background: rgba(76, 77, 230, 0.05);
  transition: all 0.3s;
}

.add-button:hover {
  border-color: #4c4de6;
  background: rgba(76, 77, 230, 0.1);
  transform: translateY(-2px);
}

.duration-input {
  width: 120px;
  margin-bottom: 12px;
  border-radius: 8px;
}

:deep(.ant-collapse) {
  background: transparent;
  border: none;
}

:deep(.ant-collapse-header) {
  font-weight: 600;
  background: rgba(250, 250, 250, 0.5);
  border-radius: 8px !important;
  margin-bottom: 8px;
  border: 1px solid rgba(240, 240, 240, 0.8);
}

:deep(.ant-collapse-content) {
  background: transparent;
  border: none;
}

:deep(.ant-collapse-content-box) {
  padding: 16px !important;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(240, 240, 240, 0.8);
}

:deep(.ant-empty) {
  margin: 48px 0;
}

:deep(.ant-divider-vertical) {
  display: none;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.content-header, .content-body {
  animation: fadeIn 0.5s ease-out forwards;
}

.content-body {
  animation-delay: 0.1s;
}

/* 文本加载动画效果 */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

.loading-shimmer {
  position: relative;
  overflow: hidden;
}

.loading-shimmer::before {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  height: 100%;
  width: 100%;
  z-index: 1;
  left: 0;
  top: 0;
  animation: pulse 1.5s infinite ease-in-out;
  border-radius: 12px;
}

.shimmer-text {
  position: relative;
  overflow: hidden;
}

.shimmer-text::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 8px;
}

.shimmer-text::before {
  content: '';
  position: absolute;
  background-image:
    linear-gradient(90deg, rgba(76, 77, 230, 0.05) 25%, transparent 25%),
    linear-gradient(90deg, transparent 75%, rgba(76, 77, 230, 0.05) 75%),
    linear-gradient(90deg, transparent 50%, rgba(76, 77, 230, 0.05) 50%);
  background-size: 300% 100%;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  animation: shimmer 3s infinite;
  border-radius: 8px;
}

/* 禁用文本输入动画的实际内容，保留占位符显示 */
.shimmer-text :deep(textarea),
.shimmer-text :deep(input) {
  color: transparent;
  background: rgba(250, 250, 250, 0.7);
  position: relative;
  z-index: 1;
}

.shimmer-text :deep(.ant-input-affix-wrapper > input.ant-input) {
  color: transparent;
  background: transparent;
}

.shimmer-text :deep(.ant-input-prefix),
.shimmer-text :deep(.ant-input-suffix),
.shimmer-text :deep(.ant-input-group-addon) {
  color: rgba(0, 0, 0, 0.25);
}

/* 为加载动画的占位符文本创建效果 */
.shimmer-text :deep(textarea)::placeholder,
.shimmer-text :deep(input)::placeholder {
  color: rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 3;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .lesson-plan-detail {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid rgba(240, 240, 240, 0.6);
    max-height: 250px;
  }

  .main-content {
    padding: 16px;
  }

  .content-header {
    flex-direction: column;
    gap: 16px;
  }

  .actions {
    width: 100%;
    justify-content: space-between;
  }

  .actions button {
    min-width: auto;
    flex: 1;
  }

  .title-section {
    margin-right: 0;
  }

  .title-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  :deep(.ant-divider-vertical) {
    display: none;
  }
}

/* 功能提示卡片样式 */
.feature-tips {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  animation: slideIn 0.5s ease-out;
}

.tips-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(240, 240, 240, 0.8);
  min-width: 200px;
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #1f2937;
  font-weight: 500;
}

.tips-icon {
  color: #4c4de6;
  font-size: 18px;
}

.close-icon {
  margin-left: auto;
  cursor: pointer;
  color: #999;
  transition: all 0.3s;
}

.close-icon:hover {
  color: #666;
  transform: rotate(90deg);
}

.tips-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tips-actions button {
  width: 100%;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* 空教案提示样式 */
.empty-plan-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 2rem;
}

.empty-plan-content {
  text-align: center;
  max-width: 400px;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(240, 240, 240, 0.8);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  animation: fadeIn 0.6s ease-out forwards;
}

.empty-icon {
  font-size: 3rem;
  color: #4c4de6;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #4c4de6 0%, #5971e6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.empty-plan-content h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
  font-weight: 600;
}

.empty-plan-content p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.empty-plan-content button {
  min-width: 180px;
  height: 40px;
  font-size: 1rem;
  transition: all 0.3s;
}

.empty-plan-content button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(76, 77, 230, 0.2);
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .feature-tips {
    position: fixed;
    right: 0;
    left: 0;
    top: auto;
    bottom: 0;
    transform: none;
    animation: slideUp 0.5s ease-out;
  }

  .tips-content {
    border-radius: 12px 12px 0 0;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
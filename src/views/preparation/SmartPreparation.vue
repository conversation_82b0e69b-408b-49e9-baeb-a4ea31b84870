<template>
  <div class="smart-preparation">
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="left-section">
          <div class="title-area">
            <h1 class="welcome-title">智能备课</h1>
            <p class="welcome-subtitle">AI辅助教案编写，提供个性化教学建议</p>
          </div>
        </div>
        <div class="right-section">
          <div class="decoration-circle"></div>
          <div class="decoration-circle"></div>
        </div>
      </div>
    </div>

    <!-- 功能跳转卡片 -->
    <div class="function-cards">
      <div class="function-card" @click="navigateTo('/dashboard/lesson-plan')">
        <div class="card-icon lesson-icon">
          <i class="fas fa-book"></i>
        </div>
        <div class="card-content">
          <h3>教案制作</h3>
          <p>根据教学目标智能生成教案框架，快速完成备课</p>
        </div>
      </div>

      <div class="function-card" @click="navigateTo('/dashboard/ppt-maker')">
        <div class="card-icon ppt-icon">
          <i class="fas fa-file-powerpoint"></i>
        </div>
        <div class="card-content">
          <h3>PPT制作</h3>
          <p>智能生成教学演示文稿，丰富课堂内容</p>
        </div>
      </div>

      <div class="function-card" @click="navigateTo('/dashboard/exercise-maker')">
        <div class="card-icon exercise-icon">
          <i class="fas fa-tasks"></i>
        </div>
        <div class="card-content">
          <h3>习题生成</h3>
          <p>智能匹配课程内容，生成多样化习题</p>
        </div>
      </div>

      <div class="function-card" @click="navigateTo('/dashboard/resource-center')">
        <div class="card-icon resource-icon">
          <i class="fas fa-database"></i>
        </div>
        <div class="card-content">
          <h3>资源库</h3>
          <p>支持团队协作备课，共享优质教学资源</p>
        </div>
      </div>
    </div>

    <!-- 快速开始区域 -->
    <div class="preparation-tools">
      <div class="tool-section">
        <div class="quick-start-header">
          <div class="header-left">
            <h3>快速开始</h3>
            <span class="subtitle">查看或创建新的备课计划</span>
          </div>
          <a-button type="primary" size="middle" @click="startPreparation" class="start-btn">
            <i class="fas fa-plus mr-1"></i> 开始备课
          </a-button>
        </div>
        
        <div class="recent-lessons">
          <div class="recent-header">
            <i class="fas fa-history"></i> 最近备课
          </div>
          <div class="lesson-list">
            <a-spin :spinning="loading">
              <div 
                v-for="lesson in recentLessons" 
                :key="lesson.id" 
                class="lesson-item"
              >
                <div class="lesson-item-content">
                  <div class="lesson-main" @click="viewLessonPlan(lesson)">
                    <span class="lesson-title">{{ lesson.mainTitle || lesson.title }}</span>
                    <span class="lesson-date">{{ lesson.date }}</span>
                  </div>
                  <div class="lesson-actions">
                    <a-button type="link" @click.stop="viewLessonPlan(lesson)" class="action-btn">
                      <i class="fas fa-edit"></i>
                    </a-button>
                    <a-button type="link" @click.stop="confirmDelete(lesson.id)" class="action-btn">
                      <i class="fas fa-trash"></i>
                    </a-button>
                  </div>
                </div>
              </div>
              <a-empty v-if="!loading && recentLessons.length === 0" description="暂无备课记录" />
            </a-spin>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from '@/axios'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const recentLessons = ref([])
const loading = ref(false)

// 路由导航函数 - 尝试使用多种方式进行导航
const navigateTo = (path) => {
  console.log('尝试导航到:', path)
  try {
    // 使用window.location直接跳转，避免Vue Router可能的问题
    window.location.href = path;
  } catch (error) {
    console.error('导航错误:', error)
    message.error('页面跳转出错')
  }
}

// 获取最近备课记录
async function fetchRecentLessons() {
  try {
    loading.value = true
    const response = await axios.get('/api/smart-preparation/recent')
    if (response.data.success) {
      recentLessons.value = response.data.data
    }
  } catch (error) {
    message.error('获取最近备课记录失败')
    console.error('获取最近备课记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 开始备课
async function startPreparation() {
  try {
    // 这里可以直接跳转到教案制作页面
    navigateTo('/dashboard/lesson-plan')
  } catch (error) {
    message.error('操作失败')
    console.error('开始备课失败:', error)
  }
}

// 查看教案详情
const viewLessonPlan = (lesson) => {
  // 从课时标题中提取课时号（例如："课时4" -> "4"）
  const titleMatch = lesson.title.match(/课时(\d+)/);
  if (!titleMatch) {
    message.error('无效的课时信息');
    return;
  }
  const hour = `课时${titleMatch[1]}`;
  
  // 跳转到教案详情页面
  window.location.href = `/dashboard/preparation/lesson/${lesson.course_code}/${hour}`;
};

// 确认删除
const confirmDelete = (id) => {
  message.warning('删除功能开发中...');
};

// 根据难度返回对应的颜色
const getDifficultyColor = (difficulty) => {
  const colors = {
    '简单': 'success',
    '中等': 'warning',
    '困难': 'error'
  }
  return colors[difficulty] || 'default'
}

onMounted(() => {
  fetchRecentLessons()
})
</script>

<style scoped>
.smart-preparation {
  padding: 24px;
  min-height: 100vh;
  background-color: rgba(246, 248, 252, 0.8);
  background-image: 
    radial-gradient(at 47% 33%, rgba(76, 77, 230, 0.04) 0, transparent 59%), 
    radial-gradient(at 82% 65%, rgba(118, 201, 255, 0.07) 0, transparent 55%);
}

/* 欢迎区域样式 - 与工作站保持一致 */
.welcome-section {
  background: linear-gradient(135deg, rgba(76, 77, 230, 0.85) 0%, rgba(58, 59, 191, 0.85) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: white;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(76, 77, 230, 0.2);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease-in-out;
  margin-bottom: 1.5rem;
  max-width: 100%;
  min-height: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-section:hover {
  box-shadow: 0 12px 36px rgba(76, 77, 230, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.title-area {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.welcome-title {
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  line-height: 1.3;
  margin: 0;
}

.welcome-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.right-section {
  position: relative;
  height: 80px;
  width: 80px;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.decoration-circle:nth-child(1) {
  width: 60px;
  height: 60px;
  top: 10px;
  right: 10px;
}

.decoration-circle:nth-child(2) {
  width: 30px;
  height: 30px;
  top: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
}

/* 功能卡片样式 */
.function-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.function-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: rgba(76, 77, 230, 0.2);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
}

.lesson-icon {
  background: rgba(76, 77, 230, 0.15);
  color: #4c4de6;
}

.ppt-icon {
  background: rgba(250, 140, 22, 0.15);
  color: #fa8c16;
}

.exercise-icon {
  background: rgba(82, 196, 26, 0.15);
  color: #52c41a;
}

.resource-icon {
  background: rgba(114, 46, 209, 0.15);
  color: #722ed1;
}

.function-card:hover .card-icon {
  transform: scale(1.1);
}

.card-content h3 {
  font-size: 1.125rem;
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.card-content p {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

/* 工具区域样式 */
.preparation-tools {
  margin-bottom: 1.5rem;
}

.tool-section {
  background: white;
  padding: 1.25rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.quick-start-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.header-left h3 {
  font-size: 1.125rem;
  color: #1f2937;
  font-weight: 600;
  margin: 0;
  margin-bottom: 0.25rem;
}

.subtitle {
  color: #6b7280;
  font-size: 0.85rem;
}

.start-btn {
  display: flex;
  align-items: center;
  height: 2.25rem;
  padding: 0 1rem;
  font-weight: 500;
  background: #4c4de6;
  border-color: #4c4de6;
  box-shadow: 0 2px 6px rgba(76, 77, 230, 0.25);
  transition: all 0.3s ease;
}

.start-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.3);
  background: #5758e8;
  border-color: #5758e8;
}

.recent-header {
  color: #4b5563;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recent-header i {
  color: #4c4de6;
}

.lesson-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.lesson-item {
  background: #f9fafb;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.lesson-item:hover {
  border-color: #4c4de6;
  background: white;
  box-shadow: 0 2px 8px rgba(76, 77, 230, 0.1);
}

.lesson-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.6rem 0.8rem;
}

.lesson-main {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding-right: 0.5rem;
}

.lesson-title {
  color: #1f2937;
  font-size: 0.9rem;
  font-weight: 500;
}

.lesson-date {
  color: #6b7280;
  font-size: 0.8rem;
  white-space: nowrap;
  margin-left: 1rem;
}

.lesson-actions {
  display: flex;
  gap: 0.25rem;
}

.action-btn {
  padding: 0.25rem;
  height: 28px;
  width: 28px;
  min-width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  color: #4c4de6;
  background: rgba(76, 77, 230, 0.1);
  border-radius: 4px;
}

.lesson-actions i {
  font-size: 0.875rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .smart-preparation {
    padding: 16px;
  }

  .welcome-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .right-section {
    display: none;
  }

  .function-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .tool-section {
    padding: 1rem;
  }
}
</style> 
<template>
  <a-modal
    :visible="visible"
    title="选择教案模板"
    width="600px"
    :footer="null"
    @cancel="handleCancel"
    @update:visible="(val) => $emit('update:visible', val)"
    class="template-modal"
    :maskClosable="false"
  >
    <div class="template-container">
      <div class="modal-top-decoration"></div>

      <!-- 模板选择区 -->
      <div class="template-selection">
        <div class="section-title">
          <span class="title-icon"><book-outlined /></span>
          <span>教案模板</span>
        </div>
        <a-radio-group v-model:value="selectedTemplate" class="template-group">
          <div
            v-for="template in templates"
            :key="template.id"
            :class="['template-item', { active: selectedTemplate === template.id }]"
            @click="selectedTemplate = template.id"
          >
            <div class="template-card">
              <div class="template-icon-wrapper">
                <div class="template-icon" :class="`template-icon-${template.id}`">
                  <component :is="template.icon" />
                </div>
              </div>
              <div class="template-content">
                <div class="template-title">{{ template.name }}</div>
                <div class="template-desc">{{ template.description }}</div>
              </div>
              <div class="template-check" v-if="selectedTemplate === template.id">
                <check-circle-filled />
              </div>
            </div>
          </div>
        </a-radio-group>
      </div>

      <!-- 自定义要求输入区 -->
      <div class="custom-requirements">
        <div class="section-title">
          <span class="title-icon"><form-outlined /></span>
          <span>自定义要求</span>
        </div>
        <a-textarea
          v-model:value="customRequirements"
          :rows="4"
          placeholder="请输入您对教案的特殊要求或建议，例如：教学目标、教学重点、班级特点等..."
          class="custom-textarea"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
          :disabled="!selectedTemplate"
          class="confirm-btn"
        >
          <template #icon><thunderbolt-outlined /></template>
          生成教案
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue'
import {
  BookOutlined,
  ExperimentOutlined,
  TeamOutlined,
  BulbOutlined,
  FormOutlined,
  ThunderboltOutlined,
  CheckCircleFilled
} from '@ant-design/icons-vue'

defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'generate', 'cancel'])

// 模板数据
const templates = [
  {
    id: 1,
    name: '传统讲授型',
    description: '以教师讲授为主，突出知识点讲解和课堂练习',
    icon: BookOutlined
  },
  {
    id: 2,
    name: '探究实验型',
    description: '强调学生动手实践，培养探究能力和创新思维',
    icon: ExperimentOutlined
  },
  {
    id: 3,
    name: '互动讨论型',
    description: '注重师生互动和生生互动，培养表达和思辨能力',
    icon: TeamOutlined
  },
  {
    id: 4,
    name: '启发思考型',
    description: '以问题为导向，培养学生独立思考和解决问题的能力',
    icon: BulbOutlined
  }
]

const selectedTemplate = ref(null)
const customRequirements = ref('')

// 处理取消
const handleCancel = () => {
  selectedTemplate.value = null
  customRequirements.value = ''
  emit('update:visible', false)
  emit('cancel')
}

// 处理确认
const handleConfirm = () => {
  if (!selectedTemplate.value) return

  emit('generate', {
    templateId: selectedTemplate.value,
    customRequirements: customRequirements.value
  })
}
</script>

<style scoped>
.template-modal {
  border-radius: 16px;
  overflow: hidden;
}

.template-modal :deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 12px 48px rgba(76, 77, 230, 0.15);
  border: 1px solid rgba(76, 77, 230, 0.1);
}

.template-modal :deep(.ant-modal-header) {
  background: transparent;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  padding: 20px 24px;
}

.template-modal :deep(.ant-modal-title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  background: linear-gradient(135deg, #4c4de6 0%, #5971e6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.template-modal :deep(.ant-modal-body) {
  padding: 0;
}

.template-modal :deep(.ant-modal-close) {
  color: #4c4de6;
}

.template-container {
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.modal-top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4c4de6, #5971e6);
  z-index: 1;
}

.template-selection {
  padding: 16px 16px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 14px;
  align-self: flex-start;
  width: 100%;
  max-width: 560px;
}

.title-icon {
  color: #4c4de6;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.template-group {
  display: grid;
  grid-template-columns: repeat(2, minmax(220px, 260px));
  gap: 14px;
  width: 100%;
  max-width: 560px;
  justify-content: center;
  animation: fadeIn 0.5s ease-out forwards;
}

.template-item {
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
  display: flex;
  justify-content: center;
}

.template-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  max-width: 260px;
  border: 2px solid transparent;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background: white;
  transition: all 0.3s;
}

.template-item:hover .template-card {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(76, 77, 230, 0.12);
  border-color: rgba(76, 77, 230, 0.3);
}

.template-item.active .template-card {
  border-color: var(--primary-color);
  background: rgba(76, 77, 230, 0.02);
  box-shadow: 0 8px 24px rgba(76, 77, 230, 0.12);
}

.template-icon-wrapper {
  width: 100%;
  padding-top: 55%; /* 更紧凑的比例 */
  position: relative;
  overflow: hidden;
}

.template-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  transition: all 0.3s;
  font-size: 28px;
}

.template-content {
  padding: 12px 14px;
  flex: 1;
  display: flex;
  flex-direction: column;
  text-align: center;
}

.template-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 6px;
  text-align: center;
}

.template-desc {
  font-size: 12px;
  color: #4b5563;
  line-height: 1.4;
  flex: 1;
  text-align: center;
  margin: 0 auto;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-check {
  position: absolute;
  top: 10px;
  right: 10px;
  color: var(--primary-color);
  font-size: 20px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  z-index: 2;
  animation: scaleIn 0.3s ease-out forwards;
}

/* 为每个模板设置不同的主题色 */
.template-icon-1 {
  background: linear-gradient(135deg, #4c4de6, #5971e6); /* 蓝色系 - 传统讲授型 */
}

.template-icon-2 {
  background: linear-gradient(135deg, #10B981, #34D399); /* 绿色系 - 探究实验型 */
}

.template-icon-3 {
  background: linear-gradient(135deg, #F59E0B, #FBBF24); /* 橙色系 - 互动讨论型 */
}

.template-icon-4 {
  background: linear-gradient(135deg, #EC4899, #F472B6); /* 粉色系 - 启发思考型 */
}

.template-item.active .template-icon-1,
.template-item:hover .template-icon-1 {
  background: linear-gradient(135deg, #5971e6, #4c4de6);
}

.template-item.active .template-icon-2,
.template-item:hover .template-icon-2 {
  background: linear-gradient(135deg, #34D399, #10B981);
}

.template-item.active .template-icon-3,
.template-item:hover .template-icon-3 {
  background: linear-gradient(135deg, #FBBF24, #F59E0B);
}

.template-item.active .template-icon-4,
.template-item:hover .template-icon-4 {
  background: linear-gradient(135deg, #F472B6, #EC4899);
}

.custom-requirements {
  padding: 14px 16px 16px;
  background: rgba(246, 248, 252, 0.7);
  border-top: 1px solid rgba(229, 231, 235, 0.6);
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.custom-textarea {
  background: white;
  border-radius: 8px;
  transition: all 0.3s;
  resize: none;
  border-color: #d9d9d9;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  height: 70px;
  font-size: 14px;
  width: 100%;
  max-width: 560px;
}

.custom-textarea:hover,
.custom-textarea:focus {
  border-color: #4c4de6;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 14px;
  padding: 14px 16px 16px;
  width: 100%;
}

.confirm-btn {
  display: flex;
  align-items: center;
  height: 38px;
  padding: 0 20px;
  font-weight: 500;
  background: linear-gradient(90deg, #4c4de6, #5971e6);
  border-color: #4c4de6;
  box-shadow: 0 2px 6px rgba(76, 77, 230, 0.25);
  transition: all 0.3s ease;
  min-width: 110px;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.3);
  background: linear-gradient(90deg, #5758e8, #6a80e8);
  border-color: #5758e8;
}

.confirm-btn[disabled] {
  opacity: 0.6;
  transform: none;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .template-group {
    grid-template-columns: minmax(200px, 240px);
  }

  .template-modal {
    width: 90% !important;
    max-width: 400px;
  }

  .template-selection {
    padding: 14px 10px 6px;
  }

  .custom-requirements {
    padding: 6px 10px 10px;
  }

  .modal-footer {
    padding: 6px 10px 10px;
  }

  .template-item:hover .template-card {
    transform: translateY(-2px);
  }

  .custom-textarea {
    height: 60px;
  }

  .section-title {
    margin-bottom: 10px;
    font-size: 14px;
  }

  .template-card {
    max-width: 240px;
  }

  .template-content {
    padding: 10px;
  }

  .template-icon {
    font-size: 24px;
  }

  .template-title {
    font-size: 13px;
    margin-bottom: 4px;
  }

  .template-desc {
    font-size: 11px;
    line-height: 1.3;
  }

  .confirm-btn {
    min-width: 100px;
    height: 36px;
    padding: 0 16px;
  }
}
</style>
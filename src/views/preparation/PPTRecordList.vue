<template>
  <div class="ppt-record-list">
    <a-card title="PPT记录列表" :bordered="false">
      <template #extra>
        <a-button type="primary" @click="refreshList">
          <template #icon><reload-outlined /></template>
          刷新
        </a-button>
      </template>

      <a-table
        :columns="columns"
        :data-source="records"
        :loading="loading"
        :pagination="{ pageSize: 10 }"
        row-key="id"
      >
        <!-- 课时标题列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <span>{{ record.title }}</span>
          </template>

          <!-- 状态列 -->
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'create_time'">
            <span>{{ formatDate(record.create_time) }}</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button 
                type="link" 
                @click="previewPPT(record)"
                :disabled="!record.download_url"
              >
                预览
              </a-button>
              <a-button 
                type="link" 
                @click="downloadPPT(record)"
                :disabled="!record.download_url"
              >
                下载
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- PPT预览模态框 -->
    <a-modal
      v-model:visible="previewModalVisible"
      title="PPT预览"
      width="90%"
      style="top: 20px"
      :footer="null"
    >
      <div class="preview-container">
        <iframe
          v-if="previewUrl"
          :src="previewUrl"
          width="100%"
          height="600"
          frameborder="0"
          allowfullscreen
        ></iframe>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import axios from '../../utils/axios'
import { ReloadOutlined } from '@ant-design/icons-vue'

// 定义表格列
const columns = [
  {
    title: '课时',
    dataIndex: 'title',
    key: 'title',
    width: '20%'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '15%'
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
    width: '20%'
  },
  {
    title: '操作',
    key: 'action',
    width: '15%'
  }
]

// 状态变量
const records = ref([])
const loading = ref(false)
const previewModalVisible = ref(false)
const previewUrl = ref('')

// 获取PPT记录列表
const fetchRecords = async () => {
  try {
    loading.value = true
    const response = await axios.get('/api/ppt/records')
    if (response.data.flag) {
      records.value = response.data.data
    } else {
      message.error(response.data.desc || '获取记录失败')
    }
  } catch (error) {
    console.error('获取PPT记录失败:', error)
    message.error('获取记录失败')
  } finally {
    loading.value = false
  }
}

// 刷新列表
const refreshList = () => {
  fetchRecords()
}

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'processing':
      return 'processing'
    case 'failed':
      return 'error'
    default:
      return 'default'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'processing':
      return '生成中'
    case 'failed':
      return '失败'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 下载PPT
const downloadPPT = (record) => {
  if (!record.download_url) return
  
  // 使用window.location.origin代替import.meta.env
  const baseUrl = window.location.origin
  window.open(`${baseUrl}${record.download_url}`, '_blank')
}

// 预览PPT
const previewPPT = (record) => {
  if (!record.View_url) {
    message.warning('未找到PPT预览链接')
    return
  }
  
  const fileUrl = record.View_url
  // 使用pfile.com.cn的预览服务
  const previewServiceUrl = 'http://www.pfile.com.cn/api/profile/onlinePreview?url='
  
  // 直接在新窗口打开预览页面
  window.open(previewServiceUrl + encodeURIComponent(fileUrl), '_blank')
}

// 组件挂载时获取记录
onMounted(() => {
  fetchRecords()
})
</script>

<style scoped>
.ppt-record-list {
  padding: 24px;
}

:deep(.ant-card-head) {
  min-height: 48px;
}

:deep(.ant-card-head-title) {
  padding: 12px 0;
}

.preview-container {
  width: 100%;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.ant-modal-body) {
  padding: 12px;
}
</style> 
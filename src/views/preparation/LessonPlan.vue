<template>
  <div class="lesson-plan-container">
    <!-- 背景装饰元素 -->
    <div class="bg-decoration circle-1"></div>
    <div class="bg-decoration circle-2"></div>
    <div class="bg-decoration circle-3"></div>

    <!-- 错误提示 -->
    <a-alert
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      show-icon
      closable
      class="error-alert"
    />

    <!-- 页面标题区域 -->
    <div class="page-header">
      <h2 class="page-title">教案计划</h2>
      <p class="page-description">上传课程大纲，智能生成课时分配</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧课程大纲 -->
      <div class="syllabus-section">
        <div class="section-header">
          <div class="header-title">
            <h3>课程大纲</h3>
            <span class="header-subtitle">上传或查看课程章节结构</span>
          </div>
          <a-button type="primary" class="upload-btn" @click="handleUploadClick">
            <upload-outlined />
            上传教材目录
          </a-button>
        </div>

        <!-- 基本信息表单 -->
        <div class="basic-info-form">
          <a-form :model="basicInfo" layout="horizontal">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="课程名称">
                  <a-select
                    v-model:value="basicInfo.courseId"
                    placeholder="请选择课程"
                    @change="handleCourseChange"
                    class="custom-select"
                  >
                    <a-select-option
                      v-for="course in coursesList"
                      :key="course.id"
                      :value="course.id"
                    >
                      {{ course.course_name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="总课时">
                  <div class="input-with-tip">
                    <div class="input-with-button">
                      <a-input-number
                        v-model:value="basicInfo.totalPeriods"
                        :min="1"
                        :max="100"
                        :default-value="16"
                        placeholder="请输入总课时"
                        class="custom-input-number"
                      />
                      <a-tooltip title="重置为数据库中的默认课时数">
                        <a-button
                          type="link"
                          size="small"
                          class="reset-button"
                          @click="resetToDefaultPeriods"
                          :disabled="!originalTotalPeriods"
                        >
                          <sync-outlined />
                        </a-button>
                      </a-tooltip>
                    </div>
                    <div v-if="originalTotalPeriods && basicInfo.totalPeriods !== originalTotalPeriods" class="period-tip">
                      <info-circle-outlined style="color: #1890ff; margin-right: 4px" />
                      <span>数据库默认值: {{ originalTotalPeriods }}</span>
                    </div>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="教师">
                  <a-input
                    v-model:value="basicInfo.teacherName"
                    disabled
                    placeholder="自动获取"
                    class="custom-input"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <div class="syllabus-content">
          <a-spin :spinning="loading" tip="加载中...">
            <template v-if="syllabusContent">
              <div class="tree-container">
                <a-tree
                  :tree-data="syllabusTreeData"
                  :selectable="false"
                  :default-expanded-keys="defaultExpandedKeys"
                  class="custom-tree"
                />
              </div>
            </template>
            <a-empty v-else description="暂无课程大纲，请上传" />
          </a-spin>
        </div>
      </div>

      <!-- 右侧课时分配 -->
      <div class="hours-section">
        <div class="section-header">
          <div class="header-title">
            <h3>课时分配</h3>
            <span class="header-subtitle">根据大纲智能分配教学内容</span>
          </div>
          <div class="header-actions">
            <div class="input-with-button">
              <a-input-number
                v-model:value="totalHours"
                :min="1"
                :max="100"
                :default-value="16"
                placeholder="总课时"
                class="hours-input"
                @change="handleTotalHoursChange"
              />
              <a-tooltip title="重置为数据库中的默认课时数">
                <a-button
                  type="link"
                  size="small"
                  class="reset-button"
                  @click="resetToDefaultHours"
                  :disabled="!originalTotalPeriods"
                >
                  <sync-outlined />
                </a-button>
              </a-tooltip>
            </div>
          </div>
        </div>

        <div class="hours-content">
          <a-spin :spinning="loading" tip="处理中...">
            <template v-if="courseContent">
              <div class="hours-list">
                <div
                  v-for="(content, hour, index) in courseContent"
                  :key="hour"
                  class="hour-item"
                  :class="{ active: selectedHour === hour }"
                  :style="{ '--index': index }"
                  @click="selectHour(hour)"
                >
                  <div class="hour-header">
                    <span class="hour-title">{{ hour }}</span>
                    <a-tag :color="getHourStatus(hour).color" class="status-tag">
                      {{ getHourStatus(hour).text }}
                    </a-tag>
                  </div>
                  <div class="hour-content">
                    <ul>
                      <li v-for="(item, index) in content" :key="index">
                        {{ item }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </template>
            <div v-else class="empty-state">
              <a-empty description="请先生成课时分配" />
              <p class="empty-hint">上传课程大纲后，点击"生成课时"按钮分配教学内容</p>
            </div>
          </a-spin>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { UploadOutlined, SyncOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import axios from '@/axios'
import { useRouter } from 'vue-router'
import { useLessonPlanStore } from '@/stores/lessonPlan'

const router = useRouter()
const lessonPlanStore = useLessonPlanStore()

// 状态管理
const loading = ref(false)
const errorMessage = ref('')
const syllabusContent = ref(null)
const courseContent = ref(null)
const selectedHour = ref(null)
const totalHours = ref(16)
const coursesList = ref([])
const originalTotalPeriods = ref(null) // 存储从数据库获取的原始课时数

// 基本信息
const basicInfo = ref({
  courseId: lessonPlanStore.selectedCourseId,
  courseName: '',
  totalPeriods: 16,
  teacherName: '',
  teacherId: null
})

// 获取教师信息和课程列表
const fetchTeacherAndCourses = async () => {
  try {
    // 从localStorage获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (userInfo.name && userInfo.system_teacher_id) {
      basicInfo.value.teacherName = userInfo.name
      basicInfo.value.teacherId = userInfo.system_teacher_id

      // 获取教师的课程列表
      const response = await axios.get(`/api/courses/teacher/${userInfo.system_teacher_id}`)
      if (response.data.success) {
        coursesList.value = response.data.data.map(course => ({
          id: course.course_code,
          course_name: course.course_name,
          total_periods: course.total_periods || null,
          semester: course.semester
        }))

        // 如果有存储的课程ID，使用它；否则使用第一个课程
        if (lessonPlanStore.selectedCourseId) {
          basicInfo.value.courseId = lessonPlanStore.selectedCourseId
          await handleCourseChange(lessonPlanStore.selectedCourseId)
        } else if (coursesList.value.length > 0) {
          basicInfo.value.courseId = coursesList.value[0].id
          await handleCourseChange(coursesList.value[0].id)
        }
      }
    } else {
      message.error('请先登录')
    }
  } catch (error) {
    console.error('获取教师和课程信息失败:', error)
    message.error('获取基本信息失败')
  }
}

// 处理课程选择
const handleCourseChange = async (courseId) => {
  const course = coursesList.value.find(c => c.id === courseId)
  if (course) {
    basicInfo.value.courseId = courseId
    basicInfo.value.courseName = course.course_name
    // 保存原始课时数和当前课时数
    originalTotalPeriods.value = course.total_periods || null
    basicInfo.value.totalPeriods = course.total_periods || 16

    // 保存选择到 store
    lessonPlanStore.setCourse(courseId, course.course_name)

    // 获取课程大纲和课时分配
    try {
      loading.value = true
      const response = await axios.get(`/api/syllabus/${courseId}`)
      if (response.data.success) {
        if (response.data.data) {
          syllabusContent.value = response.data.data.content
          courseContent.value = response.data.data.allocation
          // 优先使用大纲中的课时数，如果没有则使用课程的total_periods
          basicInfo.value.totalPeriods = response.data.data.total || course.total_periods || 16
          totalHours.value = response.data.data.total || course.total_periods || 16
          // 保存原始课时数
          originalTotalPeriods.value = course.total_periods || null

          // 保存大纲数据到 store
          lessonPlanStore.setSyllabusData(response.data.data)

          message.success('已加载课程大纲和课时分配')
        } else {
          syllabusContent.value = null
          courseContent.value = null
          message.info('暂无课程大纲，请上传')
        }
      }
    } catch (error) {
      console.error('获取课程大纲失败:', error)
      message.error('获取课程大纲失败')
      syllabusContent.value = null
      courseContent.value = null
    } finally {
      loading.value = false
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTeacherAndCourses()
})

// 重置为默认课时数
const resetToDefaultPeriods = () => {
  if (originalTotalPeriods.value) {
    basicInfo.value.totalPeriods = originalTotalPeriods.value
    message.success('已重置为数据库默认课时数')
  }
}

// 重置课时分配的课时数
const resetToDefaultHours = () => {
  if (originalTotalPeriods.value) {
    totalHours.value = originalTotalPeriods.value
    message.success('已重置为数据库默认课时数')
    // 重置后自动重新生成课时分配
    if (syllabusContent.value) {
      generateHoursPlan()
    }
  }
}

// 处理总课时变化
const handleTotalHoursChange = (value) => {
  // 当总课时变化时，如果已有大纲内容，自动重新生成课时分配
  if (syllabusContent.value && value > 0) {
    generateHoursPlan()
  }
}

// 处理上传按钮点击
const handleUploadClick = async (e) => {
  // 阻止事件冒泡，避免触发上传组件的默认行为
  e.stopPropagation();

  if (!basicInfo.value.courseId) {
    message.error('请先选择课程')
    return
  }

  // 添加对课时的检查
  if (!basicInfo.value.totalPeriods) {
    message.error('请先设置总课时数')
    return
  }

  // 如果当前课时数与数据库默认值不同，显示确认对话框
  if (originalTotalPeriods.value && basicInfo.value.totalPeriods !== originalTotalPeriods.value) {
    const confirmed = await new Promise(resolve => {
      Modal.confirm({
        title: '课时数确认',
        content: `您当前设置的课时数(${basicInfo.value.totalPeriods})与数据库默认值(${originalTotalPeriods.value})不同，确定要使用当前设置的课时数吗？`,
        okText: '使用当前设置',
        cancelText: '使用默认值',
        onOk: () => resolve(true),
        onCancel: () => {
          basicInfo.value.totalPeriods = originalTotalPeriods.value
          resolve(false)
        }
      })
    })

    if (!confirmed) return
  }

  // 如果已有大纲内容，则询问是覆盖还是叠加
  let actionType = 'replace'
  if (syllabusContent.value) {
    actionType = await new Promise(resolve => {
      Modal.confirm({
        title: '大纲处理方式',
        content: '已有大纲内容，请选择操作方式',
        okText: '覆盖现有大纲',
        cancelText: '叠加到现有大纲',
        onOk: () => resolve('replace'),
        onCancel: () => resolve('append'),
        closable: true,
        onClose: () => resolve(null),
      })
    })

    if (!actionType) return // 用户关闭了提示框
  }

  // 存储用户选择，供后续上传使用
  localStorage.setItem('syllabus_action_type', actionType)

  // 用户选择完操作方式后，手动触发上传文件选择框
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true
  input.accept = '.jpg,.jpeg,.png' // 只接受JPG和PNG格式

  input.onchange = (e) => {
    if (e.target.files.length > 0) {
      const fileList = Array.from(e.target.files)
      // 检查文件并上传
      handleFilesUpload(fileList)
    }
  }

  input.click()
}

// 处理文件上传
const handleFilesUpload = async (fileList) => {
  if (fileList.length === 0) return

  // 检查文件类型和大小
  const acceptedTypes = ['image/jpeg', 'image/png']; // 只接受JPG和PNG
  for (const file of fileList) {
    if (!acceptedTypes.includes(file.type)) {
      message.error(`文件类型不支持: ${file.name}，请只上传 JPG 或 PNG 格式的图片`);
      return;
    }

    if (file.size > 20 * 1024 * 1024) { // 减小到20MB
      message.error('文件过大，请控制在20MB以下')
      return
    }
  }

  loading.value = true
  const hide = message.loading('正在识别大纲内容，请耐心等待...', 0)

  try {
    const formData = new FormData()

    // 添加所有图片到formData
    fileList.forEach((file) => {
      formData.append('images', file)
    })

    // 添加重要的元数据
    formData.append('totalHours', basicInfo.value.totalPeriods.toString()) // 使用用户设置的课时数
    formData.append('courseId', basicInfo.value.courseId)
    formData.append('teacherId', basicInfo.value.teacherId)

    // 如果有原始课时数，也一并发送，便于后端记录
    if (originalTotalPeriods.value) {
      formData.append('originalTotalPeriods', originalTotalPeriods.value.toString())
    }

    // 获取之前选择的操作类型
    const actionType = localStorage.getItem('syllabus_action_type') || 'replace'

    // 如果是叠加模式，将现有大纲内容一起发送
    if (syllabusContent.value && actionType === 'append') {
      formData.append('existingSyllabus', JSON.stringify(syllabusContent.value))
      formData.append('actionType', 'append')
    } else {
      formData.append('actionType', 'replace')
    }

    console.log('正在上传文件:', fileList.map(f => ({name: f.name, type: f.type, size: `${(f.size / 1024).toFixed(2)}KB`})));

    const response = await axios.post('/api/lesson-plan/process-syllabus', formData, {
      timeout: 300000, // 设置300秒超时，大幅增加处理时间
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.data.success) {
      syllabusContent.value = response.data.data.syllabus
      courseContent.value = response.data.data.allocation
      totalHours.value = response.data.data.total

      // 如果返回了原始课时数，更新存储
      if (response.data.data.originalTotalPeriods) {
        originalTotalPeriods.value = response.data.data.originalTotalPeriods
      }

      // 更新存储
      lessonPlanStore.setSyllabusData({
        content: response.data.data.syllabus,
        allocation: response.data.data.allocation,
        total: response.data.data.total,
        originalTotalPeriods: originalTotalPeriods.value
      })

      message.success('大纲识别成功，已自动生成课时分配')

      // 清除本地存储的操作类型
      localStorage.removeItem('syllabus_action_type')
    } else {
      throw new Error(response.data.message || '处理失败')
    }
  } catch (error) {
    console.error('处理大纲失败:', error)
    if (error.code === 'ECONNABORTED') {
      message.error('处理超时，请重试或分批上传')
    } else if (error.response && error.response.status === 500) {
      message.error('服务器错误：上传的图片格式可能不被OpenAI API支持，请确保上传的是标准JPG或PNG格式，并尝试使用另一张图片')
    } else if (error.response && error.response.data) {
      message.error(error.response.data.message || '处理失败')
    } else {
      message.error(error.message || '处理失败')
    }

    // 清除本地存储的操作类型
    localStorage.removeItem('syllabus_action_type')
  } finally {
    hide()
    loading.value = false
  }
}

// 生成课时分配 - 现在是内部函数，不再需要用户手动触发
const generateHoursPlan = async (skipConfirm = false) => {
  if (!syllabusContent.value) {
    return
  }

  if (!totalHours.value || totalHours.value < 1) {
    message.error('请设置有效的总课时数')
    return
  }

  // 如果当前课时数与数据库默认值不同，且不跳过确认，显示确认对话框
  if (!skipConfirm && originalTotalPeriods.value && totalHours.value !== originalTotalPeriods.value) {
    const confirmed = await new Promise(resolve => {
      Modal.confirm({
        title: '课时数确认',
        content: `您当前设置的课时数(${totalHours.value})与数据库默认值(${originalTotalPeriods.value})不同，确定要使用当前设置的课时数吗？`,
        okText: '使用当前设置',
        cancelText: '使用默认值',
        onOk: () => resolve(true),
        onCancel: () => {
          totalHours.value = originalTotalPeriods.value
          resolve(false)
        }
      })
    })

    if (!confirmed) return
  }

  loading.value = true
  try {
    // 调用后端生成课时分配
    const response = await axios.post('/api/lesson-plan/generate-hours', {
      courseId: basicInfo.value.courseId,
      syllabus: syllabusContent.value,
      totalPeriods: basicInfo.value.totalPeriods
    })

    if (response.data.success) {
      courseContent.value = response.data.data.allocation
      message.success('课时分配生成成功')
    } else {
      throw new Error(response.data.message)
    }
  } catch (error) {
    message.error('生成课时分配失败：' + error.message)
    console.error('生成失败:', error)
  } finally {
    loading.value = false
  }
}

// 选择课时
const selectHour = (hour) => {
  if (!basicInfo.value.courseId) {
    message.warning('请先选择课程')
    return
  }
  router.push(`/dashboard/preparation/lesson/${basicInfo.value.courseId}/${hour}`)
}

// 获取课时状态
const getHourStatus = (hour) => {
  if (hour === selectedHour.value) {
    return { color: 'blue', text: '当前选择' }
  }
  return { color: 'default', text: '待处理' };
};

// 转换大纲数据为树形结构
const syllabusTreeData = computed(() => {
  if (!syllabusContent.value) return []

  const convertToTreeData = (obj, parentKey = '0') => {
    let index = 0
    return Object.entries(obj).map(([key, value]) => {
      index++
      const currentKey = `${parentKey}-${index}`

      // 处理空字符串或null的情况
      if (!value) {
        return {
          title: key,
          key: currentKey,
          isLeaf: true
        }
      }

      if (typeof value === 'object' && !Array.isArray(value)) {
        const children = convertToTreeData(value, currentKey)
        return {
          title: key,
          key: currentKey,
          children: children.length > 0 ? children : undefined,
          isLeaf: children.length === 0
        }
      }

      return {
        title: `${key}${value ? `: ${value}` : ''}`,
        key: currentKey,
        isLeaf: true
      }
    })
  }

  return convertToTreeData(syllabusContent.value)
})

// 默认展开的树节点
const defaultExpandedKeys = computed(() => {
  if (!syllabusTreeData.value.length) return []
  return syllabusTreeData.value.map(node => node.key)
})
</script>

<style scoped>
.lesson-plan-container {
  padding: 20px;
  min-height: 100vh;
  height: 100vh;
  width: 100%;
  background-color: rgba(246, 248, 252, 0.8);
  background-image:
    radial-gradient(at 47% 33%, rgba(76, 77, 230, 0.04) 0, transparent 59%),
    radial-gradient(at 82% 65%, rgba(118, 201, 255, 0.07) 0, transparent 55%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  flex: 1;
}

/* 背景装饰元素 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  filter: blur(50px);
  z-index: 0;
  opacity: 0.4;
}

.circle-1 {
  width: 300px;
  height: 300px;
  background: rgba(99, 102, 241, 0.15);
  top: -100px;
  left: 10%;
  animation: float 15s ease-in-out infinite;
}

.circle-2 {
  width: 400px;
  height: 400px;
  background: rgba(99, 179, 237, 0.1);
  bottom: -150px;
  right: 5%;
  animation: float 20s ease-in-out infinite reverse;
}

.circle-3 {
  width: 200px;
  height: 200px;
  background: rgba(79, 209, 197, 0.15);
  top: 40%;
  right: 30%;
  animation: float 18s ease-in-out infinite 2s;
}

@keyframes float {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(-10px, 15px) rotate(5deg); }
  50% { transform: translate(10px, -10px) rotate(-5deg); }
  75% { transform: translate(-15px, -15px) rotate(3deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}

.page-header {
  margin-bottom: 20px;
  padding: 0;
  position: relative;
  z-index: 1;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  background: linear-gradient(135deg, #4c4de6 0%, #5971e6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-description {
  color: #6b7280;
  font-size: 0.95rem;
  margin: 0;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 24px;
  overflow: hidden;
  padding: 0;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
}

.syllabus-section,
.hours-section {
  flex: 1;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
  position: relative;
  animation: fadeIn 0.5s ease-out forwards;
}

.syllabus-section::after,
.hours-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4c4de6, #5971e6);
  opacity: 0.8;
}

.syllabus-section:hover,
.hours-section:hover {
  box-shadow: 0 12px 42px rgba(0, 0, 0, 0.12);
  border-color: rgba(76, 77, 230, 0.3);
  transform: translateY(-3px);
}

.section-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(250, 250, 250, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.header-title {
  display: flex;
  flex-direction: column;
}

.section-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.header-subtitle {
  font-size: 0.85rem;
  color: #6b7280;
  margin-top: 4px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.upload-btn,
.generate-btn {
  display: flex;
  align-items: center;
  height: 2.25rem;
  padding: 0 1rem;
  font-weight: 500;
  background: #4c4de6;
  border-color: #4c4de6;
  box-shadow: 0 2px 6px rgba(76, 77, 230, 0.25);
  transition: all 0.3s ease;
}

.upload-btn:hover,
.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.3);
  background: #5758e8;
  border-color: #5758e8;
}

.hours-input {
  width: 80px;
  border-radius: 4px;
}

.input-with-button {
  display: flex;
  align-items: center;
}

.reset-button {
  padding: 0 4px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
}

.input-with-tip {
  display: flex;
  flex-direction: column;
}

.period-tip {
  font-size: 12px;
  color: #1890ff;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.syllabus-content,
.hours-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.hours-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.hour-item {
  padding: 16px;
  border: 1px solid rgba(229, 231, 235, 0.8);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background: rgba(250, 250, 250, 0.6);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
  animation: fadeIn 0.3s ease-out forwards;
  animation-delay: calc(var(--index, 0) * 0.05s);
}

.hour-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #4c4de6, #5971e6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hour-item:hover {
  border-color: rgba(76, 77, 230, 0.5);
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 8px 24px rgba(76, 77, 230, 0.15);
  transform: translateY(-2px);
}

.hour-item:hover::before {
  opacity: 1;
}

.hour-item.active {
  background: rgba(76, 77, 230, 0.08);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-color: rgba(76, 77, 230, 0.5);
}

.hour-item.active::before {
  opacity: 1;
}

.hour-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.hour-title {
  font-weight: 600;
  font-size: 1rem;
  color: #1f2937;
}

.status-tag {
  border-radius: 12px;
  padding: 0 10px;
  height: 22px;
  line-height: 22px;
  font-size: 0.8rem;
}

.hour-content ul {
  margin: 0;
  padding-left: 20px;
}

.hour-content li {
  margin-bottom: 6px;
  color: #374151;
  line-height: 1.5;
}

.custom-select,
.custom-input-number,
.custom-input {
  width: 100%;
  border-radius: 6px;
}

.custom-select:hover,
.custom-input-number:hover,
.custom-input:hover {
  border-color: #4c4de6;
}

.custom-input:disabled {
  color: #000000d9;
  background-color: rgba(245, 245, 245, 0.8);
  cursor: not-allowed;
  opacity: 1;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: 100%;
}

.empty-hint {
  text-align: center;
  color: #6b7280;
  font-size: 0.9rem;
  margin-top: 1rem;
}

.error-alert {
  margin: 0 0 20px 0;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 8px;
  border: 1px solid rgba(255, 77, 79, 0.2);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.15);
}

.basic-info-form {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  background: rgba(250, 250, 250, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.basic-info-form :deep(.ant-form-item) {
  margin-bottom: 0;
}

.basic-info-form :deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

.basic-info-form :deep(.ant-select),
.basic-info-form :deep(.ant-input-number) {
  width: 100%;
}

.basic-info-form :deep(.ant-input[disabled]) {
  color: #000000d9;
  background-color: #f5f5f5;
  cursor: not-allowed;
  opacity: 1;
}

.tree-container {
  padding: 8px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.custom-tree {
  background: transparent;
}

:deep(.ant-tree-title) {
  font-size: 0.95rem;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: rgba(76, 77, 230, 0.1);
}

:deep(.ant-tree-node-selected) {
  background-color: rgba(76, 77, 230, 0.1) !important;
}

/* 自定义滚动条 */
.syllabus-content::-webkit-scrollbar,
.hours-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.syllabus-content::-webkit-scrollbar-thumb,
.hours-content::-webkit-scrollbar-thumb {
  background: rgba(76, 77, 230, 0.3);
  border-radius: 10px;
}

.syllabus-content::-webkit-scrollbar-track,
.hours-content::-webkit-scrollbar-track {
  background: rgba(245, 245, 245, 0.3);
  border-radius: 10px;
}

:deep(.ant-empty) {
  margin: 32px 0 16px 0;
}

/* 添加全局动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.syllabus-section {
  animation-delay: 0s;
}

.hours-section {
  animation-delay: 0.15s;
}

.mr-1 {
  margin-right: 0.25rem;
}

@media (max-width: 768px) {
  .lesson-plan-container {
    padding: 16px;
    height: 100vh;
    min-height: 100vh;
    width: 100%;
    margin: 0;
  }

  .page-header {
    padding: 0;
    margin-bottom: 16px;
  }

  .main-content {
    flex-direction: column;
    padding: 0;
    gap: 16px;
    width: 100%;
    box-sizing: border-box;
  }

  .syllabus-section,
  .hours-section {
    margin-bottom: 0;
    width: 100%;
  }

  .syllabus-section:hover,
  .hours-section:hover {
    transform: none;
  }

  .section-header {
    padding: 12px 16px;
  }

  .header-actions {
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }

  .basic-info-form :deep(.ant-row) {
    flex-direction: column;
  }

  .basic-info-form :deep(.ant-col) {
    width: 100%;
    margin-bottom: 8px;
  }

  .hour-item {
    padding: 12px;
  }

  .hour-item::before {
    width: 3px;
  }

  .error-alert {
    margin: 0 0 16px 0;
  }

  .bg-decoration {
    opacity: 0.2;
  }

  .circle-1, .circle-2, .circle-3 {
    width: 150px;
    height: 150px;
  }
}
</style>
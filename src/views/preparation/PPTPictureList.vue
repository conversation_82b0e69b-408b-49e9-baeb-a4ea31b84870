<template>
  <div class="ppt-picture-list">
    <!-- 添加推荐图片分析按钮 -->
    <div class="analysis-actions" v-if="Object.keys(pptOutline).length > 0">
      <a-space>
        <a-button type="primary" @click="analyzeOutlineForImages" :loading="analyzingImages">
          <template #icon><SearchOutlined /></template>
          分析PPT大纲获取推荐图片
        </a-button>
        <a-button type="primary" @click="generateAIPictures" :loading="generatingAIPictures" danger>
          <template #icon><BulbOutlined /></template>
          AI生成PPT插图
        </a-button>
      </a-space>
    </div>

    <!-- AI生成插图展示区域 -->
    <div v-if="Object.keys(aiGeneratedImages).length > 0" class="ai-images-results">
      <a-divider>AI生成插图</a-divider>

      <a-row :gutter="[16, 16]">
        <a-col :span="8" v-for="(imageUrl, imageName) in aiGeneratedImages" :key="imageName">
          <a-card hoverable class="image-card">
            <template #cover>
              <div class="image-container">
                <img
                  :key="imageUrl"
                  :alt="imageName"
                  :src="getProxyImageUrl(imageUrl)"
                  class="keyword-image"
                  @error="(e) => handleProxyImageError(e, imageUrl)"
                  @load="handleImageLoad(imageUrl)"
                  @click="previewImage(imageUrl, imageName, 'AI生成插图')"
                  loading="lazy"
                  style="cursor: pointer"
                  v-show="!isImageErrored(imageUrl)"
                />
                <div class="image-loading-overlay" v-if="isImageLoading(imageUrl)">
                  <a-spin />
                </div>
                <div v-if="isImageErrored(imageUrl)" class="image-error-text">
                  加载失败
                </div>
              </div>
            </template>
            <template #actions>
              <download-outlined key="download" @click="downloadImageByPath(imageUrl, imageName, 'AI生成插图')" />
            </template>
            <a-card-meta :title="imageName">
              <template #description>
                <div class="picture-info">
                  <div>生成时间: {{ formatDate(new Date()) }}</div>
                  <a-tag color="red">AI生成</a-tag>
                </div>
              </template>
            </a-card-meta>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 添加页面推荐图片展示区域 -->
    <div v-if="Object.keys(imageAnalysisResult).length > 0" class="analysis-results">
      <a-divider>PPT页面配图推荐</a-divider>

      <a-collapse v-model:activeKey="activePageKeys">
        <a-collapse-panel v-for="(pageData, pageNumber) in imageAnalysisResult" :key="pageNumber">
          <template #header>
            <span class="page-header">{{ pageNumber }}: {{ pageData.content }}</span>
          </template>

          <div class="keyword-section" v-for="(images, keyword) in pageData.keywords" :key="keyword">
            <h4 class="keyword-title">{{ keyword }}</h4>

            <a-row :gutter="[16, 16]" v-if="images && images.length > 0">
              <a-col :span="8" v-for="(imagePath, index) in images" :key="imagePath">
                <a-card hoverable class="image-card">
                  <template #cover>
                    <div class="image-container">
                      <img
                        :key="imagePath"
                        :alt="String(keyword)"
                        :src="getProxyImageUrl(imagePath)"
                        class="keyword-image"
                        @error="(e) => handleProxyImageError(e, imagePath)"
                        @load="handleImageLoad(imagePath)"
                        @click="previewImage(imagePath, keyword, pageData.content)"
                        loading="lazy"
                        style="cursor: pointer"
                        v-show="!isImageErrored(imagePath)"
                      />
                      <div class="image-loading-overlay" v-if="isImageLoading(imagePath)">
                        <a-spin />
                      </div>
                      <div v-if="isImageErrored(imagePath)" class="image-error-text">
                        加载失败
                      </div>
                    </div>
                  </template>
                  <template #actions>
                    <download-outlined @click="downloadImageByPath(imagePath, keyword, pageData.content)" />
                  </template>
                </a-card>
              </a-col>
            </a-row>
            <a-empty v-else description="暂无图片，系统仍在努力获取中..." />
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>

    <!-- 图片网格展示 - 仅在有图片时显示 -->
    <div class="picture-grid" v-if="pictures.length > 0">
      <a-spin :spinning="loading">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="picture in pictures" :key="picture.id">
            <a-card hoverable class="picture-card">
              <template #cover>
                <img :alt="picture.name" :src="picture.url" class="picture-image" />
              </template>
              <template #actions>
                <download-outlined key="download" @click="downloadImage(picture)" />
              </template>
              <a-card-meta :title="picture.name">
                <template #description>
                  <div class="picture-info">
                    <div>{{ formatDate(picture.uploadTime) }}</div>
                    <a-tag color="blue">{{ picture.category }}</a-tag>
                  </div>
                </template>
              </a-card-meta>
            </a-card>
          </a-col>
        </a-row>
      </a-spin>
    </div>

    <!-- 修改图片预览Modal组件 -->
    <a-modal
      v-model:visible="previewVisible"
      :footer="null"
      :width="800"
      @cancel="previewVisible = false"
      :title="previewTitle"
    >
      <div class="image-preview-container">
        <img
          :key="previewImageSrc"
          :src="previewImageSrc"
          class="preview-image"
          @error="handlePreviewImageErrorDirect"
        />
         <div v-if="previewError" class="preview-error-text">
             图片加载失败。
         </div>
        <div class="preview-actions">
          <a-button type="primary" @click="downloadImageByPath(previewImageUrl, previewTitle, previewContent)">
            <template #icon><download-outlined /></template>
            下载图片
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, defineProps } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  DownloadOutlined,
  SearchOutlined,
  BulbOutlined
} from '@ant-design/icons-vue';
import axios from '../../utils/axios';
import { useRoute } from 'vue-router';

// 接收从父组件传递的props
const props = defineProps({
  courseId: {
    type: String,
    default: ''
  },
  hour: {
    type: String,
    default: ''
  },
  pptOutline: {
    type: Object,
    default: () => ({})
  }
});

// 添加计算属性来提取PPT标题和章节信息
const pptTitle = computed(() => props.pptOutline.title || '未命名PPT');
const pptChapters = computed(() => {
  if (!props.pptOutline.chapters || !props.pptOutline.chapters.length) {
    return [];
  }

  // 提取所有章节标题和内容
  return props.pptOutline.chapters.map(chapter => ({
    title: chapter.chapterTitle,
    contents: chapter.chapterContents ?
      chapter.chapterContents.map(item => item.chapterTitle) : []
  }));
});

// 添加监听函数以响应PPT大纲变化
watch(() => props.pptOutline, (newValue) => {
  console.log('PPT大纲已更新:', newValue);
  // 不再调用已删除的函数
  // if (Object.keys(newValue).length > 0) {
  //   loadRecommendedPictures();
  // }
}, { deep: true });

// 类型定义
interface Picture {
  id: number;
  name: string;
  url: string;
  category: string;
  tags: string[];
  uploadTime: string;
}

// 状态变量
const pictures = ref<Picture[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(12);
const searchQuery = ref('');
const categoryFilter = ref('');

// 分类选项
const categories = [
  { label: '自然风景', value: 'nature' },
  { label: '商业图表', value: 'business' },
  { label: '人物', value: 'people' },
  { label: '科技', value: 'technology' },
  { label: '教育', value: 'education' },
  { label: '抽象', value: 'abstract' }
];

// 添加localStorage键名常量
const STORAGE_KEY_PREFIX = 'ppt_image_analysis_';
const AI_STORAGE_KEY_PREFIX = 'ppt_ai_images_';

// 使用computed生成一个唯一的缓存键，基于课程ID和大纲
const storageKey = computed(() => {
  if (!props.courseId || !props.pptOutline.title) return '';
  return `${STORAGE_KEY_PREFIX}${props.courseId}_${props.pptOutline.title}`;
});

// AI生成图片的缓存键
const aiStorageKey = computed(() => {
  if (!props.courseId || !props.pptOutline.title) return '';
  return `${AI_STORAGE_KEY_PREFIX}${props.courseId}_${props.pptOutline.title}`;
});

// 修改加载图片函数
const loadPictures = async () => {
  loading.value = true;
  try {
    try {
      const response = await axios.get('/api/ppt/pictures', {
        params: {
          page: currentPage.value,
          pageSize: pageSize.value,
          query: searchQuery.value,
          category: categoryFilter.value
        }
      });

      if (response.data.flag && response.data.data?.items?.length > 0) {
        pictures.value = response.data.data.items;
        total.value = response.data.data.total;
      } else {
        // API返回空数据
        pictures.value = [];
        total.value = 0;
      }
    } catch (error) {
      console.error('加载图片失败:', error);
      message.error('加载图片失败，请稍后重试');
      pictures.value = [];
      total.value = 0;
    }
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadPictures();
};

// 分类过滤
const handleCategoryChange = () => {
  currentPage.value = 1;
  loadPictures();
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadPictures();
};

// 每页数量变化
const handleSizeChange = (current: number, size: number) => {
  currentPage.value = 1;
  pageSize.value = size;
  loadPictures();
};

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 下载图片
const downloadImage = (picture: Picture) => {
  const link = document.createElement('a');
  link.href = picture.url;
  link.download = picture.name || 'image.jpg';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  message.success('开始下载图片');
};

// 添加一个loadFromCache函数用于从本地存储加载数据
const loadFromCache = () => {
  if (!storageKey.value) return false;

  try {
    const cachedData = localStorage.getItem(storageKey.value);
    if (cachedData) {
      let parsedData = JSON.parse(cachedData);

      // 在加载缓存数据后也进行过滤
      let filteredImageCount = 0;
      for (const pageKey in parsedData) {
        const page = parsedData[pageKey];
        for (const keyword in page.keywords) {
          const images = page.keywords[keyword];
          if (Array.isArray(images)) {
            const filteredImages = images.filter(url => typeof url === 'string' && !url.includes('img.doc.xuehai.net'));
            page.keywords[keyword] = filteredImages;
            filteredImageCount += filteredImages.length;
          }
        }
      }
      console.log(`从缓存加载并过滤后，有效图片数量: ${filteredImageCount}`);

      imageAnalysisResult.value = parsedData;

      // 如果有缓存数据，默认展开第一个页面
      if (Object.keys(parsedData).length > 0) {
        activePageKeys.value = [Object.keys(parsedData)[0]];
      }

      console.log('从缓存加载图片分析结果:', Object.keys(parsedData).length, '页');
      return true;
    }
  } catch (e) {
    console.error('读取或过滤缓存失败:', e);
    // 如果读取或过滤出错，清空缓存
    localStorage.removeItem(storageKey.value);
  }
  return false;
};

// 添加一个saveToCache函数用于保存数据到本地存储
const saveToCache = (data) => {
  if (!storageKey.value) return;

  try {
    localStorage.setItem(storageKey.value, JSON.stringify(data));
    console.log('图片分析结果已保存到缓存');
  } catch (e) {
    console.error('保存缓存失败:', e);
  }
};

// 保存AI生成的图片到缓存
const saveAIImagesToCache = (data) => {
  if (!aiStorageKey.value) return;

  try {
    localStorage.setItem(aiStorageKey.value, JSON.stringify(data));
    console.log('AI生成的图片已保存到缓存');
  } catch (e) {
    console.error('保存AI图片缓存失败:', e);
  }
};

// 清除AI图片缓存
const clearAIImagesCache = () => {
  if (!aiStorageKey.value) return;

  try {
    // 清除缓存
    localStorage.removeItem(aiStorageKey.value);
    console.log('AI图片缓存已清除');

    // 清空状态
    aiGeneratedImages.value = {};
  } catch (e) {
    console.error('清除AI图片缓存失败:', e);
  }
};

// 从缓存加载AI生成的图片
const loadAIImagesFromCache = () => {
  if (!aiStorageKey.value) return false;

  try {
    const cachedData = localStorage.getItem(aiStorageKey.value);
    if (cachedData) {
      const parsedData = JSON.parse(cachedData);

      if (typeof parsedData === 'object' && parsedData !== null) {
        // 检查是否是新格式的缓存数据（包含outline和images）
        if (parsedData.outline && parsedData.images) {
          // 新格式缓存

          // 检查大纲是否匹配
          const cachedOutline = parsedData.outline;
          const currentOutline = JSON.stringify(props.pptOutline);

          if (cachedOutline === currentOutline) {
            // 大纲匹配，加载图片
            const imageData = parsedData.images;
            const imageCount = Object.keys(imageData).length;

            if (imageCount > 0) {
              aiGeneratedImages.value = imageData;
              console.log('从缓存加载AI生成的图片:', imageCount, '张');
              return true;
            }
          } else {
            // 大纲不匹配，不加载缓存
            console.log('缓存的大纲与当前大纲不匹配，不加载缓存');
            return false;
          }
        } else {
          // 旧格式缓存（直接是图片数据）
          // 为了兼容旧版本，仍然尝试加载，但应该尽快更新为新格式
          console.log('检测到旧格式缓存，尝试加载');

          // 检查缓存数据是否有效
          const imageCount = Object.keys(parsedData).length;
          if (imageCount > 0) {
            // 将旧格式缓存转换为新格式
            const newCacheData = {
              outline: JSON.stringify(props.pptOutline),
              images: parsedData,
              timestamp: Date.now()
            };
            saveAIImagesToCache(newCacheData);

            aiGeneratedImages.value = parsedData;
            console.log('从旧格式缓存加载AI生成的图片:', imageCount, '张');
            return true;
          }
        }
      }

      // 缓存数据无效或为空，清除缓存
      console.log('缓存中的图片数据无效或为空，清除缓存');
      localStorage.removeItem(aiStorageKey.value);
    }
  } catch (e) {
    console.error('读取AI图片缓存失败:', e);
    // 如果读取出错，清空缓存
    localStorage.removeItem(aiStorageKey.value);
  }
  return false;
};

// AI生成PPT插图
const generateAIPictures = async () => {
  if (Object.keys(props.pptOutline).length === 0) {
    message.warning('请先加载PPT大纲');
    return;
  }

  generatingAIPictures.value = true;
  message.info('AI开始生成PPT插图，请稍候...');

  try {
    const response = await axios.post('/api/ppt/generate-ai-pictures', {
      outline: props.pptOutline
    }, {
      timeout: 180000 // 增加超时时间到180秒，因为AI生成可能需要更长时间
    });

    console.log('AI生成插图结果:', response.data);

    if (response.data.flag && response.data.data) {
      // 清空之前的结果
      aiGeneratedImages.value = {};

      // 将生成的图片链接保存到状态中
      const imageData = response.data.data;

      // 检查返回的数据格式
      console.log('返回的图片数据类型:', typeof imageData);
      console.log('返回的图片数据内容:', imageData);

      // 处理返回的图片数据
      if (typeof imageData === 'object' && imageData !== null) {
        // 将数据保存到状态中
        aiGeneratedImages.value = imageData;

        // 将AI生成的图片保存到localStorage，并关联当前的PPT大纲
        const cacheData = {
          outline: JSON.stringify(props.pptOutline),
          images: imageData,
          timestamp: Date.now()
        };
        saveAIImagesToCache(cacheData);

        const imageCount = Object.keys(imageData).length;
        if (imageCount > 0) {
          message.success(`AI成功生成${imageCount}张PPT插图`);

          // 生成成功后，等待一下再尝试从数据库重新加载
          setTimeout(async () => {
            try {
              // 尝试从数据库重新加载图片，确保数据库中的数据已经更新
              const dbResponse = await axios.post('/api/ppt/load-ai-pictures', {
                outline: props.pptOutline
              });

              if (dbResponse.data.flag && dbResponse.data.data) {
                console.log('生成后从数据库重新加载图片成功:', dbResponse.data.data);
                // 更新图片数据
                aiGeneratedImages.value = dbResponse.data.data;
                // 更新缓存，并关联当前的PPT大纲
                const cacheData = {
                  outline: JSON.stringify(props.pptOutline),
                  images: dbResponse.data.data,
                  timestamp: Date.now()
                };
                saveAIImagesToCache(cacheData);
              }
            } catch (reloadError) {
              console.error('生成后重新加载图片失败:', reloadError);
            }
          }, 2000); // 等待两秒，确保数据库写入完成
        } else {
          message.warning('AI生成了插图，但没有返回有效的图片链接');
        }
      } else {
        message.error('AI生成插图返回了无效的数据格式');
      }
    } else {
      message.error(response.data.desc || 'AI生成插图失败');

      // 即使前端显示失败，也尝试从数据库加载，因为后端可能已经成功生成并保存了图片
      setTimeout(async () => {
        try {
          const dbResponse = await axios.post('/api/ppt/load-ai-pictures', {
            outline: props.pptOutline
          });

          if (dbResponse.data.flag && dbResponse.data.data) {
            console.log('前端显示失败但从数据库加载成功:', dbResponse.data.data);
            // 更新图片数据
            aiGeneratedImages.value = dbResponse.data.data;
            // 更新缓存，并关联当前的PPT大纲
            const cacheData = {
              outline: JSON.stringify(props.pptOutline),
              images: dbResponse.data.data,
              timestamp: Date.now()
            };
            saveAIImagesToCache(cacheData);
            // 显示成功消息
            message.success(`成功从数据库加载${Object.keys(dbResponse.data.data).length}张AI生成的PPT插图`);
          }
        } catch (reloadError) {
          console.error('尝试从数据库加载图片失败:', reloadError);
        }
      }, 3000); // 等待三秒，确保数据库写入完成
    }
  } catch (error) {
    console.error('AI生成插图时出错:', error);
    message.error('AI生成插图时出错，请稍后重试');

    // 即使出错，也尝试从数据库加载，因为后端可能已经成功生成并保存了图片
    setTimeout(async () => {
      try {
        const dbResponse = await axios.post('/api/ppt/load-ai-pictures', {
          outline: props.pptOutline
        });

        if (dbResponse.data.flag && dbResponse.data.data) {
          console.log('生成出错但从数据库加载成功:', dbResponse.data.data);
          // 更新图片数据
          aiGeneratedImages.value = dbResponse.data.data;
          // 更新缓存，并关联当前的PPT大纲
          const cacheData = {
            outline: JSON.stringify(props.pptOutline),
            images: dbResponse.data.data,
            timestamp: Date.now()
          };
          saveAIImagesToCache(cacheData);
          // 显示成功消息
          message.success(`成功从数据库加载${Object.keys(dbResponse.data.data).length}张AI生成的PPT插图`);
        }
      } catch (reloadError) {
        console.error('尝试从数据库加载图片失败:', reloadError);
      }
    }, 3000); // 等待三秒，确保数据库写入完成
  } finally {
    generatingAIPictures.value = false;
  }
};

// 分析PPT大纲获取推荐图片 - 增强版
const analyzeOutlineForImages = async () => {
  if (Object.keys(props.pptOutline).length === 0) {
    message.warning('请先加载PPT大纲');
    return;
  }

  // 先尝试从缓存加载
  if (loadFromCache()) {
    message.success('已从缓存加载图片分析结果');
    return;
  }

  analyzingImages.value = true;
  let retryCount = 0;
  const maxRetries = 2;

  const fetchData = async () => {
    try {
      console.log(`尝试获取图片数据，第${retryCount + 1}次尝试...`);
      const response = await axios.post('/api/ppt/analyze-images', {
        outline: props.pptOutline
      }, {
        timeout: 120000 // 增加超时时间到120秒，因为图片分析可能需要更长时间
      });

      console.log('分析结果原始数据:', response.data);

      if (response.data.flag) {
        // 检查返回的数据结构
        if (!response.data.data || Object.keys(response.data.data).length === 0) {
          message.warning('未找到推荐图片，请尝试更换PPT内容或手动搜索图片');
          imageAnalysisResult.value = {};
          return;
        }

        // 前端额外过滤无效链接和特定域名
        const data = response.data.data;
        let validImageCount = 0;

        // 检查图片链接是否有效的函数
        const isValidImageUrl = (url) => {
          if (!url || typeof url !== 'string') return false;

          // 排除特定域名
          if (url.includes('img.doc.xuehai.net')) return false;

          // 移除URL参数部分进行检查
          const urlWithoutParams = url.split('?')[0].toLowerCase();

          // 检查是否以图片扩展名结尾
          const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
          return validExtensions.some(ext => urlWithoutParams.endsWith(ext));
        };

        // 过滤每个页面的图片链接
        for (const pageKey in data) {
          const page = data[pageKey];

          for (const keyword in page.keywords) {
            const images = page.keywords[keyword];

            if (Array.isArray(images)) {
              // 只保留有效的图片链接 (isValidImageUrl 现在包含域名检查)
              const validImages = images.filter(url => isValidImageUrl(url));
              page.keywords[keyword] = validImages;
              validImageCount += validImages.length;
            }
          }
        }

        console.log(`前端过滤后的有效图片数量 (排除 xuehai.net): ${validImageCount}`);

        // 使用过滤后的数据
        imageAnalysisResult.value = data;

        // 保存到缓存中 (保存的是已过滤的数据)
        saveToCache(data);

        // 默认展开第一个页面
        if (Object.keys(data).length > 0) {
          activePageKeys.value = [Object.keys(data)[0]];
        }

        if (validImageCount > 0) {
          message.success(`PPT大纲分析完成，找到${validImageCount}张有效配图`);
        } else {
          message.warning('未找到符合要求的图片，请尝试更换PPT内容');
        }
      } else {
        message.error(`分析失败: ${response.data.desc}`);
        console.error('分析错误详情:', response.data);
      }
    } catch (error) {
      console.error('调用分析API失败:', error);

      // 处理重试逻辑
      if (retryCount < maxRetries) {
        retryCount++;
        message.warning(`网络请求失败，正在第${retryCount}次重试...`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试
        return fetchData(); // 递归调用自身重试
      } else {
        message.error('网络错误，请检查网络连接');
      }
    }
  };

  try {
    await fetchData();
  } finally {
    analyzingImages.value = false;
  }
};

// 图片加载状态跟踪: 'loading', 'loaded', 'error'
const imageStatus = ref<Record<string, 'loading' | 'loaded' | 'error'>>({});

// --- Helper functions for image status ---
const getImageStatus = (url: string) => imageStatus.value[url] || 'loading'; // Default to loading
const isImageLoading = (url: string) => getImageStatus(url) === 'loading';
const isImageErrored = (url: string) => getImageStatus(url) === 'error';
const isImageLoaded = (url: string) => getImageStatus(url) === 'loaded';

// --- Helper function to get Proxy URL ---
const getProxyImageUrl = (originalUrl: string) => {
    if (!originalUrl) return '';

    // 对于AI生成的图片URL，不使用代理，直接返回原始URL
    if (originalUrl.includes('oaiusercontent.com') || originalUrl.includes('filesystem.site')) {
        console.log('检测到AI生成的图片URL，不使用代理:', originalUrl);
        return originalUrl; // 直接返回原始URL
    }

    // 对于其他图片，使用代理
    return `/api/ppt/stream-image?url=${encodeURIComponent(originalUrl)}`;
};

// --- Image Error/Load Handling (Simplified) ---
const handleImageLoad = (url: string) => {
  // 当 img 的 src (代理URL) 加载成功时调用
  // console.log('图片通过代理加载成功 (原始URL):', url); // url 参数是原始 URL
  imageStatus.value[url] = 'loaded';
};

const handleProxyImageError = (e: Event, url: string) => {
  // 当 img 的 src (代理URL) 加载失败时调用
  console.error(`图片代理加载错误 (原始URL):`, url);
  imageStatus.value[url] = 'error'; // 标记对应的原始 URL 为错误
};

// --- 预览相关 (Simplified) ---
const previewVisible = ref(false);
const previewImageSrc = ref(''); // 这个 src *总是* 代理 URL
const previewImageUrl = ref(''); // 原始图片 URL (用于下载)
const previewTitle = ref('');
const previewContent = ref('');
const previewError = ref(false);

const previewImage = (imagePath, keyword, pageContent) => {
  previewImageUrl.value = imagePath; // 保存原始 URL
  previewTitle.value = keyword || '图片预览';
  previewContent.value = pageContent || '';
  previewError.value = false;

  // 直接设置预览 src 为代理 URL
  previewImageSrc.value = getProxyImageUrl(imagePath);
  console.log(`预览图片 (代理): ${previewImageSrc.value}`);
  previewVisible.value = true;
};

const handlePreviewImageErrorDirect = (e: Event) => {
    console.error(`预览图片代理加载失败: ${previewImageSrc.value}`);
    message.error('无法加载预览图片');
    previewError.value = true;
};

// --- 下载相关 ---
const downloadImageByPath = (imagePath, keyword, pageContent) => {
  try {
    if (!imagePath || typeof imagePath !== 'string') {
      message.error('图片路径无效');
      return;
    }
    const originalImagePath = imagePath;

    // --- 增加详细日志 ---
    console.log('--- 开始下载 ---');
    console.log('原始图片路径:', originalImagePath);
    console.log('关键词 (keyword):', keyword);
    console.log('页面内容 (pageContent):', pageContent);

    // --- 文件名逻辑修改 ---
    let fileNameBase = '';

    // 1. 如果是AI生成插图，优先使用keyword作为文件名（插图名称）
    if (pageContent === 'AI生成插图' && keyword && typeof keyword === 'string' && keyword.trim() !== '') {
        fileNameBase = keyword.trim();
        console.log('使用插图名称作为文件名:', fileNameBase);
    }
    // 2. 其他情况优先使用 pageContent
    else if (pageContent && typeof pageContent === 'string' && pageContent.trim() !== '') {
        fileNameBase = pageContent.trim();
        console.log('使用 pageContent 作为文件名基础:', fileNameBase);
    }
    // 3. 如果 pageContent 无效，则回退到 keyword
    else if (keyword && typeof keyword === 'string' && keyword.trim() !== '') {
        fileNameBase = keyword.trim();
        console.warn('pageContent 无效，回退到使用 keyword 作为文件名基础:', fileNameBase);
    }
    // 4. 如果两者都无效，则使用默认名称
    else {
        fileNameBase = '幻灯片图片';
        console.warn('pageContent 和 keyword 均无效，使用默认文件名');
    }

    // 清理文件名：移除非法字符，替换空格，限制长度
    const sanitizedFileName = fileNameBase
      .replace(/[\\\/:\*\?"<>\|]/g, '_') // 移除非法字符
      .replace(/\s+/g, '_')             // 将连续空格替换为单个下划线
      .substring(0, 100);              // 限制文件名长度

    console.log('清理后的文件名 (无扩展名):', sanitizedFileName);

    // 文件扩展名推断
    const urlParts = originalImagePath.split('?')[0].split('.');
    const extension = urlParts.length > 1 ? urlParts[urlParts.length - 1].toLowerCase() : 'jpg';
    const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    const finalExtension = validExtensions.includes(extension) ? extension : 'jpg';

    const fullFileName = `${sanitizedFileName}.${finalExtension}`;
    console.log('最终完整文件名:', fullFileName);

    message.loading('正在准备下载链接...', 1);

    const link = document.createElement('a');
    link.download = fullFileName; // **设置浏览器保存时的文件名**

    const encodedImageUrl = encodeURIComponent(originalImagePath);
    // **将清理后的、不带扩展名的文件名传递给后端**
    // 后端需要正确处理这个 name 参数来设置 Content-Disposition
    const encodedFileNameForBackend = encodeURIComponent(sanitizedFileName);
    link.href = `/api/ppt/download-for-client?url=${encodedImageUrl}&name=${encodedFileNameForBackend}`;

    console.log('生成的下载链接:', link.href);

    document.body.appendChild(link);
    link.click();

    setTimeout(() => {
      if (document.body.contains(link)) {
        document.body.removeChild(link);
      }
    }, 1000);

    message.success(`开始下载: ${fullFileName}`);

  } catch (error) {
    console.error('下载图片失败:', error);
    message.error('下载图片时发生错误');
  }
};

// 从数据库加载AI生成的插图
const loadAIPicturesFromDatabase = async () => {
  if (Object.keys(props.pptOutline).length === 0) {
    console.log('大纲数据为空，无法从数据库加载AI插图');
    return false;
  }

  try {
    console.log('尝试从数据库加载AI生成的插图...');
    const response = await axios.post('/api/ppt/load-ai-pictures', {
      outline: props.pptOutline
    });

    console.log('从数据库加载AI插图响应:', response.data);

    if (response.data.flag && response.data.data) {
      // 检查数据库返回的数据是否有效
      const imageData = response.data.data;
      const imageCount = Object.keys(imageData).length;

      if (imageCount > 0) {
        // 清空之前的结果
        aiGeneratedImages.value = {};

        // 将数据保存到状态中
        aiGeneratedImages.value = imageData;

        // 将数据保存到缓存，并关联当前的PPT大纲
        const cacheData = {
          outline: JSON.stringify(props.pptOutline),
          images: imageData,
          timestamp: Date.now()
        };
        saveAIImagesToCache(cacheData);

        message.success(`成功从数据库加载${imageCount}张AI生成的PPT插图`);
        return true;
      } else {
        // 数据库返回了空数据，清除缓存
        console.log('数据库返回了空的图片数据，清除缓存');
        clearAIImagesCache();
        return false;
      }
    } else if (response.data.needGenerate) {
      console.log('数据库中没有找到AI插图，需要生成');
      // 清除缓存，因为数据库中没有数据
      clearAIImagesCache();
      return false;
    } else {
      console.log('从数据库加载AI插图失败:', response.data.desc);
      // 加载失败时也清除缓存
      clearAIImagesCache();
      return false;
    }
  } catch (error) {
    console.error('从数据库加载AI插图时出错:', error);
    // 出错时清除缓存
    clearAIImagesCache();
    return false;
  }
};

// 自动加载或生成AI插图
const autoLoadOrGenerateAIPictures = async () => {
  // 如果大纲为空，不执行
  if (Object.keys(props.pptOutline).length === 0) {
    console.log('大纲数据为空，无法加载或生成AI插图');
    return;
  }

  // 如果已经有AI生成的插图，不需要重新加载或生成
  if (Object.keys(aiGeneratedImages.value).length > 0) {
    console.log('已有AI生成的插图，不需要重新加载或生成');
    return;
  }

  // 1. 先尝试从数据库加载（优先从数据库加载，确保数据是最新的）
  const loadedFromDB = await loadAIPicturesFromDatabase();
  if (loadedFromDB) {
    console.log('从数据库加载AI插图成功');
    return;
  }

  // 2. 如果数据库中没有，尝试从缓存加载
  // 注意：如果数据库中没有数据，缓存应该已经被清除
  // 但为了安全起见，还是尝试从缓存加载
  const loadedFromCache = loadAIImagesFromCache();
  if (loadedFromCache) {
    console.log('从缓存加载AI插图成功');

    // 从缓存加载成功后，仍然验证一下数据库中是否有该数据
    // 这是为了确保缓存和数据库保持一致
    setTimeout(async () => {
      try {
        const verifyResponse = await axios.post('/api/ppt/load-ai-pictures', {
          outline: props.pptOutline
        });

        // 如果数据库中没有数据，清除缓存
        if (!verifyResponse.data.flag || !verifyResponse.data.data || Object.keys(verifyResponse.data.data).length === 0) {
          console.log('缓存中有数据但数据库中没有，清除缓存');
          clearAIImagesCache();
          // 自动生成新的插图
          message.info('数据库中没有插图数据，正在为您自动生成PPT插图，请稍候...');
          await generateAIPictures();
        }
      } catch (error) {
        console.error('验证数据库中的插图数据失败:', error);
      }
    }, 500);

    return;
  }

  // 3. 如果缓存和数据库中都没有，自动生成
  console.log('缓存和数据库中都没有AI插图，开始自动生成');
  message.info('正在为您自动生成PPT插图，请稍候...');
  await generateAIPictures();
};

// 修改组件挂载逻辑
onMounted(() => {
  // 加载图片
  loadPictures();

  // 尝试从缓存加载图片分析结果
  loadFromCache();

  // 自动加载或生成AI插图
  setTimeout(() => {
    if (Object.keys(props.pptOutline).length > 0) {
      autoLoadOrGenerateAIPictures();
    } else {
      console.log('大纲数据尚未加载，暂不执行自动加载或生成AI插图');
    }
  }, 1000); // 给一点时间让大纲数据加载

  // 检查路由参数，如果请求自动分析图片，则执行分析
  const route = useRoute();
  if (route.query.autoAnalyzeImages === 'true') {
    console.log('检测到自动分析图片请求，准备执行分析...');
    // 等待大纲数据加载完成后再执行分析
    setTimeout(() => {
      if (Object.keys(props.pptOutline).length > 0) {
        // 只有缓存中没有数据时才执行分析
        if (Object.keys(imageAnalysisResult.value).length === 0) {
          analyzeOutlineForImages();
        }
      } else {
        console.log('大纲数据尚未加载，暂不执行自动分析');
      }
    }, 1000); // 给一点时间让大纲数据加载
  }
});

// 修改大纲监听逻辑，当大纲变化时清除缓存和结果，并自动加载或生成AI插图
watch(() => props.pptOutline, (newValue, oldValue) => {
  console.log('PPT大纲已更新:', newValue);

  // 检查大纲是否实质性变化
  const newTitle = newValue.title || '';
  const oldTitle = oldValue.title || '';

  // 如果标题变化，清除缓存和分析结果
  if (newTitle !== oldTitle) {
    console.log('大纲标题变化，清除缓存和分析结果');

    // 清除图片分析结果和缓存
    imageAnalysisResult.value = {};
    if (storageKey.value) {
      localStorage.removeItem(storageKey.value);
    }

    // 清除AI生成插图和缓存
    aiGeneratedImages.value = {};
    if (aiStorageKey.value) {
      localStorage.removeItem(aiStorageKey.value);
    }

    // 如果有新的大纲数据，自动加载或生成AI插图
    if (Object.keys(newValue).length > 0) {
      setTimeout(() => {
        autoLoadOrGenerateAIPictures();
      }, 500);
    }

    // 自动分析新大纲
    const route = useRoute();
    if (route.query.autoAnalyzeImages === 'true' && Object.keys(newValue).length > 0) {
      setTimeout(() => {
        analyzeOutlineForImages();
      }, 1000);
    }
  }
}, { deep: true });

// 添加树形数据结构
interface TreeNode {
  key: string;
  title: string;
  children?: TreeNode[];
}

// 创建树形数据结构
const outlineTreeData = computed<TreeNode[]>(() => {
  if (!props.pptOutline.chapters || !props.pptOutline.chapters.length) {
    return [];
  }

  return props.pptOutline.chapters.map((chapter, chapterIndex) => {
    const chapterNode: TreeNode = {
      key: `chapter-${chapterIndex}`,
      title: chapter.chapterTitle,
      children: []
    };

    if (chapter.chapterContents && chapter.chapterContents.length) {
      chapterNode.children = chapter.chapterContents.map((section, sectionIndex) => {
        const sectionNode: TreeNode = {
          key: `chapter-${chapterIndex}-section-${sectionIndex}`,
          title: section.chapterTitle,
          children: []
        };

        if (section.chapterContents && section.chapterContents.length) {
          sectionNode.children = section.chapterContents.map((subsection, subsectionIndex) => ({
            key: `chapter-${chapterIndex}-section-${sectionIndex}-subsection-${subsectionIndex}`,
            title: subsection.chapterTitle
          }));
        }

        return sectionNode;
      });
    }

    return chapterNode;
  });
});

// 默认展开所有一级章节
const defaultExpandedKeys = computed(() => {
  return outlineTreeData.value.map(node => node.key);
});

// 添加图片分析相关状态
const analyzingImages = ref(false);
const imageAnalysisResult = ref<Record<string, any>>({});
const activePageKeys = ref<string[]>([]);

// 添加AI生成插图相关状态
const generatingAIPictures = ref(false);
const aiGeneratedImages = ref<Record<string, string>>({});
</script>

<style scoped>
.ppt-picture-list {
  padding: 24px;
}

.action-bar {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
}

.picture-grid {
  margin-bottom: 24px;
  min-height: 200px;
}

.pagination-container {
  text-align: center;
  margin-top: 24px;
}

.picture-card {
  width: 100%;
  margin-bottom: 0;
}

.picture-image {
  height: 180px;
  object-fit: cover;
}

.picture-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.outline-info {
  margin-bottom: 16px;
  background: white;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.outline-info h3 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.outline-info p {
  margin: 0;
  color: #666;
}

.recommended-pictures {
  margin-bottom: 24px;
}

/* 添加大纲相关样式 */
.outline-card {
  margin-bottom: 24px;
  background-color: #fff;
}

.outline-header {
  text-align: center;
  margin-bottom: 16px;
}

.outline-header h2 {
  margin-bottom: 8px;
  color: #1890ff;
}

.subtitle {
  color: #666;
  font-size: 14px;
}

.outline-tree {
  margin: 8px 0;
}

.tree-node-title {
  font-size: 14px;
}

/* 优化推荐图片样式 */
.recommended-pictures {
  margin-top: 24px;
}

/* 添加新样式 */
.analysis-actions {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.analysis-results, .ai-images-results {
  margin-top: 24px;
  margin-bottom: 24px;
}

.ai-images-results .image-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  font-weight: 500;
  font-size: 16px;
}

.keyword-section {
  margin-bottom: 16px;
}

.keyword-title {
  margin: 10px 0;
  color: #1890ff;
  font-weight: 500;
}

.image-card {
  height: 100%;
}

.image-container {
  position: relative;
  width: 100%;
  height: 150px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.keyword-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
}

.keyword-image:hover {
  transform: scale(1.05);
}

.image-loading-overlay {
  position: absolute;
  top: 0; left: 0; width: 100%; height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  transition: opacity 0.3s;
  z-index: 2;
}

.image-error-text {
  color: #ff4d4f;
  font-size: 12px;
  text-align: center;
  padding: 5px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  z-index: 1;
  white-space: normal;
  max-width: 90%;
}

/* 在style部分添加预览相关的样式 */
.image-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  background-color: #eee;
  display: block;
  margin: 0 auto;
}

.preview-error-text {
    text-align: center;
    color: #ff4d4f;
    padding: 20px;
}

.preview-actions {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}
</style>

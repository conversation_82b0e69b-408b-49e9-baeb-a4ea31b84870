<template>
  <div class="ppt-maker">
    <!-- 顶部标题栏 -->
    <div class="header">
      <a-page-header
        title="智能PPT制作"
        @back="$router.back()"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="generatePPT" :loading="generating">
              <template #icon><thunderbolt-outlined /></template>
              生成PPT
            </a-button>
            <a-tooltip v-if="courseId || selectedCourseCode" placement="bottom">
              <template #title>
                将使用课程代码: {{ courseId || selectedCourseCode }}
              </template>
              <info-circle-outlined style="margin-left: 8px; cursor: pointer;" />
            </a-tooltip>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <!-- 主要内容区 -->
    <div class="content">
      <a-tabs v-model:activeKey="activeTab" :destroyInactiveTabPane="false">
        <a-tab-pane key="maker" tab="PPT制作">
          <a-row :gutter="24">
            <!-- 左侧设置面板 -->
            <a-col :xs="24" :sm="10" :md="8" :lg="6" :xl="6">
              <a-card title="PPT设置" :bordered="false">
                <!-- 生成模式选择 -->
                <a-form layout="vertical">
                  <a-form-item label="生成模式">
                    <a-radio-group v-model:value="settings.mode">
                      <a-radio-button value="outline">大纲模式</a-radio-button>
                      <a-radio-button value="auto">智能模式</a-radio-button>
                    </a-radio-group>
                  </a-form-item>

                  <!-- 大纲模式设置 -->
                  <template v-if="settings.mode === 'outline'">
                    <a-form-item label="PPT标题">
                      <a-input
                        v-model:value="settings.title"
                        placeholder="请输入PPT标题"
                        allow-clear
                      />
                    </a-form-item>

                    <a-form-item label="选择课程课时">
                      <a-space direction="vertical" style="width: 100%">
                        <a-select
                          placeholder="请先选择课程"
                          style="width: 100%"
                          @change="handleCourseChange"
                          :loading="loadingTemplates"
                          show-search
                          :filter-option="filterOption"
                          allow-clear
                        >
                          <a-select-option v-for="course in courseList" :key="course.code" :value="course.code">
                            {{ course.name }}
                          </a-select-option>
                        </a-select>
                        
                        <div v-if="selectedCourseCode" class="selected-course-info">
                          <a-tag color="blue">当前课程代码: {{ selectedCourseCode }}</a-tag>
                        </div>
                        
                        <a-select
                          placeholder="请选择PPT大纲"
                          style="width: 100%"
                          @change="loadTemplate"
                          :disabled="!selectedCourseCode || courseTitles.length === 0"
                          show-search
                          :filter-option="filterOption"
                          allow-clear
                        >
                          <a-select-option v-for="title in courseTitles" :key="title.id" :value="title.id">
                            {{ title.title }} - {{ title.contentTitle }}
                          </a-select-option>
                        </a-select>
                      </a-space>
                    </a-form-item>

                    
                  </template>

                  <!-- 智能模式设置 -->
                  <template v-if="settings.mode === 'auto'">
                    <a-form-item label="生成模式">
                      <a-radio-group v-model:value="settings.autoMode">
                        <a-radio-button value="text">文本模式</a-radio-button>
                        <a-radio-button value="file">文件模式</a-radio-button>
                      </a-radio-group>
                    </a-form-item>

                    <a-form-item label="生成内容" v-if="settings.autoMode === 'text'">
                      <a-textarea
                        v-model:value="settings.autoContent"
                        :rows="6"
                        placeholder="请输入您想要生成PPT的内容，例如：'人工智能的发展历史及应用'"
                      />
                      <div class="tips">系统将自动分析您的内容，生成结构化的PPT</div>
                    </a-form-item>

                    <a-form-item label="上传文件" v-else>
                      <a-upload
                        v-model:fileList="fileList"
                        :before-upload="beforeUpload"
                        accept=".pdf,.docx,.txt"
                        :maxCount="1"
                      >
                        <a-button>
                          <upload-outlined /> 选择文件
                        </a-button>
                      </a-upload>
                    </a-form-item>

                    <a-alert message="智能模式将自动分析输入内容生成PPT大纲" type="info" show-icon />
                  </template>

                  <!-- 通用设置 -->
                  <a-form-item label="语言">
                    <a-select v-model:value="settings.language">
                      <a-select-option value="cn">中文</a-select-option>
                      <a-select-option value="en">英文</a-select-option>
                      <a-select-option value="ja">日语</a-select-option>
                    </a-select>
                  </a-form-item>

                  <a-form-item label="高级设置">
                    <a-space direction="vertical" style="width: 100%">
                      <a-checkbox v-model:checked="settings.isCardNote">生成演讲备注</a-checkbox>
                      <a-checkbox v-model:checked="settings.search">联网搜索增强</a-checkbox>
                      <a-checkbox v-model:checked="settings.isFigure">自动配图</a-checkbox>
                    </a-space>
                  </a-form-item>
                </a-form>
              </a-card>
            </a-col>

            <!-- 右侧预览面板 -->
            <a-col :xs="24" :sm="14" :md="16" :lg="18" :xl="18">
              <a-card title="PPT大纲" :bordered="false">
                <!-- 生成状态显示 -->
                <template v-if="generating">
                  <div class="generating-status">
                    <a-spin :spinning="true">
                      <div class="progress-info">
                        <p>{{ progressMessage }}</p>
                        <a-progress :percent="progressPercent" status="active" />
                      </div>
                    </a-spin>
                  </div>
                </template>

                <!-- 生成结果显示 -->
                <template v-else-if="generatedResult">
                  <div class="result-info">
                    <a-result
                      :status="generatedResult.status"
                      :title="generatedResult.title"
                      :sub-title="generatedResult.message"
                    >
                      
                      <template #extra>
                        <a-space>
                          <a-button
                            type="primary"
                            @click="downloadPPT"
                            v-if="generatedResult.downloadUrl"
                          >
                            下载PPT
                          </a-button>
                          <a-button
                            type="primary"
                            @click="showPreviewModal"
                            v-if="generatedResult.downloadUrl"
                          >
                            <template #icon><eye-outlined /></template>
                            在线预览PPT
                          </a-button>
                        </a-space>
                      </template>
                    </a-result>
                  </div>
                </template>

                <!-- 默认提示 -->
                <template v-else>
                  <div v-if="settings.outlineContent && settings.mode === 'outline' && settings.outlineFormat === 'json'" class="outline-preview">
                    <div class="outline-header">
                      <h3>{{ settings.title || '未命名PPT' }}</h3>
                      <a-space>
                        <a-tooltip title="JSON结构参考">
                          <a-button size="small" @click="showJsonHelp">
                            <template #icon><question-circle-outlined /></template>
                          </a-button>
                        </a-tooltip>
                        <a-tooltip title="格式化JSON">
                          <a-button size="small" @click="formatJSON">
                            <template #icon><code-outlined /></template>
                          </a-button>
                        </a-tooltip>
                        <a-button type="primary" size="small" @click="toggleEditMode">
                          {{ isEditing ? '预览' : '编辑' }}
                        </a-button>
                      </a-space>
                    </div>
                    
                    <!-- 编辑模式 -->
                    <div v-if="isEditing" class="outline-editor">
                      <div class="friendly-editor">
                        <a-form layout="vertical">
                          <a-form-item label="标题">
                            <a-input v-model:value="editableOutline.title" placeholder="请输入PPT标题" />
                          </a-form-item>
                          
                          <a-form-item label="副标题">
                            <a-input v-model:value="editableOutline.subTitle" placeholder="请输入副标题（可选）" />
                          </a-form-item>
                          
                          <a-divider>章节内容</a-divider>
                          
                          <div v-for="(chapter, index) in editableOutline.chapters" :key="'chapter-'+index" class="chapter-edit-item">
                            <div class="chapter-edit-header">
                              <span class="chapter-number">第 {{ index + 1 }} 章</span>
                              <a-space>
                                <a-button 
                                  size="small" 
                                  danger 
                                  @click="removeChapter(index)"
                                  v-if="editableOutline.chapters.length > 1"
                                >
                                  <template #icon><delete-outlined /></template>
                                </a-button>
                              </a-space>
                            </div>
                            
                            <a-form-item label="章节标题">
                              <a-input v-model:value="chapter.chapterTitle" placeholder="请输入章节标题" />
                            </a-form-item>
                            
                            <a-form-item label="章节内容">
                              <div 
                                v-for="(content, contentIndex) in chapter.chapterContents || []" 
                                :key="'content-'+index+'-'+contentIndex"
                                class="content-edit-item"
                              >
                                <a-input 
                                  v-model:value="content.chapterTitle" 
                                  placeholder="请输入内容" 
                                  class="content-input"
                                />
                                <a-button 
                                  type="text" 
                                  danger 
                                  @click="removeContent(chapter, contentIndex)"
                                >
                                  <template #icon><minus-circle-outlined /></template>
                                </a-button>
                              </div>
                              
                              <a-button type="dashed" block @click="addContent(chapter)" class="add-btn">
                                <template #icon><plus-outlined /></template>
                                添加内容
                              </a-button>
                            </a-form-item>
                          </div>
                          
                          <a-button type="dashed" block @click="addChapter" class="add-chapter-btn">
                            <template #icon><plus-outlined /></template>
                            添加章节
                          </a-button>
                          
                          <div class="action-buttons">
                            <a-button type="primary" @click="saveOutline">
                              保存并预览
                            </a-button>
                          </div>
                        </a-form>
                      </div>
                    </div>
                    
                    <!-- 预览模式 -->
                    <div v-else class="outline-structure">
                      <template v-if="parsedOutline && parsedOutline.chapters">
                        <div class="outline-meta">
                          <div class="meta-item"><strong>标题:</strong> {{ parsedOutline.title || '未设置' }}</div>
                          <div class="meta-item"><strong>副标题:</strong> {{ parsedOutline.subTitle || '未设置' }}</div>
                        </div>
                        <div v-for="(chapter, index) in parsedOutline.chapters" :key="index" class="chapter-item">
                          <div class="chapter-title">{{ index + 1 }}. {{ chapter.chapterTitle }}</div>
                          <div v-if="chapter.chapterContents && chapter.chapterContents.length" class="chapter-contents">
                            <div v-for="(content, cIndex) in chapter.chapterContents" :key="cIndex" class="content-item">
                              {{ index + 1 }}.{{ cIndex + 1 }} {{ content.chapterTitle }}
                            </div>
                          </div>
                        </div>
                      </template>
                      <a-empty v-else description="解析大纲内容失败，请检查JSON格式" />
                    </div>
                  </div>
                  <a-empty v-else description="请选择课程和PPT大纲">
                    <template #image>
                      <div class="empty-image">
                        <file-outlined style="font-size: 60px; color: #1890ff;" />
                      </div>
                    </template>
                    <template #description>
                      <div class="empty-description">
                        <p>请在左侧选择课程和PPT大纲模板来开始制作</p>
                      </div>
                    </template>
                  </a-empty>
                </template>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>

        <a-tab-pane key="records" tab="PPT记录">
          <PPTRecordList />
        </a-tab-pane>

        <a-tab-pane key="picture" tab="PPT插图">
          <PPTPictureList 
            :course-id="courseId" 
            :hour="originalHour"
            :ppt-outline="parsedOutline"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  ThunderboltOutlined,
  FileOutlined,
  EyeOutlined,
  UploadOutlined,
  CodeOutlined,
  QuestionCircleOutlined,
  PlusOutlined,
  MinusCircleOutlined,
  DeleteOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import axios from '../../utils/axios'
import PPTRecordList from './PPTRecordList.vue'
import PPTPictureList from './PPTPictureList.vue'
import type { UploadFile } from 'ant-design-vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 从路由参数获取数据
const courseId = ref<string>(route.query.courseId as string || '')
const originalHour = ref<string>(route.query.originalHour as string || '')
const courseName = route.query.courseName as string
const contentTitle = route.query.title as string 

// 解析教案内容
const planContent = computed(() => {
  try {
    return route.query.content ? JSON.parse(route.query.content as string) : null
  } catch (e) {
    console.error('解析教案内容失败:', e)
    return null
  }
})

// 添加类型定义
interface GeneratedResult {
  status: 'success' | 'error'
  title: string
  message: string
  downloadUrl?: string
  pptUrl?: string
}

interface PPTTemplate {
  id: number
  title: string
  content: {
    title: string
    subTitle: string
    chapters: Array<{
      chapterTitle: string
      chapterContents: Array<{
        chapterTitle: string
        chapterContents: null | any[]
      }> | null
    }>
  }
}

// 添加模板相关类型定义
interface TemplateItem {
  id: number
  title: string
  contentTitle: string
  courseCode: string
  courseName: string
  titleId: number
  createTime: string
}

// 状态变量
const generating = ref(false)
const progressPercent = ref(0)
const progressMessage = ref('')
const generatedResult = ref<GeneratedResult | null>(null)
const fileList = ref<UploadFile[]>([])

// 设置默认值
const settings = ref({
  mode: 'outline',
  title: contentTitle || '', // 使用内容作为标题
  outlineFormat: 'json',
  outlineContent: '',
  language: 'cn',
  isCardNote: true,
  search: true,
  isFigure: true,
  templateId: '',
  autoMode: 'text',
  autoContent: '',
})

// 计算属性
const outlinePlaceholder = computed(() => {
  if (settings.value.outlineFormat === 'markdown') {
    return `# PPT标题

## 第一章
- 要点1
- 要点2

## 第二章
- 要点1
- 要点2`
  } else {
    return `{
  "title": "PPT标题",
  "subTitle": "副标题",
  "chapters": [
    {
      "chapterTitle": "第一章",
      "chapterContents": [
        {
          "chapterTitle": "要点1",
          "chapterContents": null
        }
      ]
    }
  ]
}`
  }
})

// 添加标签页状态
const activeTab = ref('maker')

// 添加文件上传处理
const beforeUpload = (file: File) => {
  // 验证文件大小和类型
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB');
    return false;
  }
  return true;
};

// 修改generatePPT方法，使其能够正确处理不同模式
// 添加模板相关状态
const availableTemplates = ref<Array<TemplateItem>>([])
const loadingTemplates = ref(false)
const selectedCourseCode = ref<string>('')
const courseList = ref<Array<{code: string, name: string}>>([])
const courseTitles = ref<Array<TemplateItem>>([])

// 添加当前加载模板ID
const currentTemplateId = ref(null)

// 过滤选项
const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 处理课程变更
const handleCourseChange = (courseCode: string) => {
  selectedCourseCode.value = courseCode
  
  if (!courseCode) {
    courseTitles.value = []
    return
  }
  
  // 过滤该课程下的所有模板
  courseTitles.value = availableTemplates.value.filter(
    template => template.courseCode === courseCode
  )
  
  if (courseTitles.value.length === 0) {
    message.info('该课程暂无保存的PPT大纲')
  }
}

// 获取所有可用的PPT模板
const fetchAvailableTemplates = async () => {
  loadingTemplates.value = true
  try {
    const response = await axios.get('/api/ppt/templates')
    if (response.data.flag) {
      availableTemplates.value = response.data.data
      console.log('获取到可用模板:', availableTemplates.value.length)
      
      // 提取课程列表
      const courses = new Map<string, string>()
      availableTemplates.value.forEach(template => {
        if (template.courseCode && template.courseName && !courses.has(template.courseCode)) {
          courses.set(template.courseCode, template.courseName)
        }
      })
      
      courseList.value = Array.from(courses).map(([code, name]) => ({
        code,
        name
      }))
      
      console.log('提取的课程列表:', courseList.value.length)
    } else {
      console.warn('获取模板列表失败:', response.data.desc)
    }
  } catch (error) {
    console.error('获取模板列表错误:', error)
  } finally {
    loadingTemplates.value = false
  }
}

// 加载模板
const loadTemplate = async (templateId) => {
  if (!templateId) return;

  try {
    message.loading('正在加载模板...', 0);
    const template = availableTemplates.value.find(t => t.id === templateId);
    
    if (!template) {
      message.error('找不到所选模板');
      return;
    }

    // 格式化课时标题
    const formattedTitle = formatHourTitle(template.title);
    console.log('加载模板，原始标题:', template.title, '格式化后:', formattedTitle);

    const response = await axios.get(`/api/ppt/template/${template.courseCode}/${template.title}`);
    message.destroy();

    if (!response.data.flag) {
      message.error(response.data.desc || '加载模板失败');
      return;
    }

    const templateData = response.data.data;
    if (templateData?.content) {
      // 如果内容中有title字段，使用它，否则使用模板的contentTitle
      settings.value.title = templateData.content.title || template.contentTitle || template.title;
      settings.value.outlineContent = JSON.stringify(templateData.content, null, 2);
      
      // 记住当前模板ID，以便后续更新而非新增
      currentTemplateId.value = templateData.id;
      console.log('加载模板成功，ID:', currentTemplateId.value);
      
      // 更新编辑表单的内容，便于用户直接编辑
      try {
        const content = templateData.content;
        editableOutline.title = content.title || '';
        editableOutline.subTitle = content.subTitle || '';
        
        if (Array.isArray(content.chapters) && content.chapters.length > 0) {
          editableOutline.chapters = JSON.parse(JSON.stringify(content.chapters));
          
          // 确保每个章节的chapterContents都是数组
          editableOutline.chapters.forEach(chapter => {
            if (!Array.isArray(chapter.chapterContents)) {
              chapter.chapterContents = [];
            }
          });
        }
      } catch (error) {
        console.error('加载模板到编辑表单失败:', error);
      }
      
      message.success('模板加载成功');
    } else {
      message.warning('模板内容为空');
    }
  } catch (error) {
    message.destroy();
    console.error('加载模板失败:', error);
    message.error('加载模板失败');
  }
};

// 从课时名称中提取数字ID
const getTitleId = (hourName) => {
  if (!hourName) return null;
  const match = hourName.match(/\d+/);
  return match ? parseInt(match[0]) : null;
};

// 格式化标题，将复合标题转换为简单的"课时X"格式
const formatHourTitle = (title) => {
  if (!title) return null;
  
  // 如果标题格式为 "xxx - 课时Y"，提取为简单的 "课时Y" 格式
  if (title.includes(' - ')) {
    const parts = title.split(' - ');
    if (parts.length > 1 && parts[parts.length-1].startsWith('课时')) {
      return parts[parts.length-1];
    }
  }
  
  // 如果已经是"课时X"格式，直接返回
  if (title.startsWith('课时')) {
    return title;
  }
  
  // 从标题中提取数字，构造"课时X"格式
  const titleMatch = title.match(/\d+/);
  if (titleMatch) {
    return `课时${titleMatch[0]}`;
  }
  
  // 如果无法提取，返回原标题
  return title;
};

// 修改generatePPT方法中的语法错误
const generatePPT = async () => {
  try {
    generating.value = true;
    progressPercent.value = 0;
    progressMessage.value = '正在准备生成...';
    generatedResult.value = null;

    // 根据不同模式调用不同的生成函数
    if (settings.value.mode === 'auto') {
      // 智能模式 - 调用generateAutoPPT函数
      await generateAutoPPT();
      return; // 重要：提前返回，避免执行下面的大纲模式代码
    }
    
    // 以下是大纲模式的逻辑
    // 检查必要参数
    if (!courseId.value || !originalHour.value) {
      throw new Error('缺少必要参数');
    }

    // 检查标题和大纲内容
    if (!settings.value.title) {
      throw new Error('请输入PPT标题');
    }

    if (!settings.value.outlineContent) {
      throw new Error('请填写PPT大纲');
    }

    console.log(`准备生成PPT，课程ID: ${courseId.value}, 课时: ${originalHour.value}`);

    // 优先使用URL参数中的课程ID，其次使用用户选择的课程ID，最后才使用临时ID
    let tempCourseId = courseId.value || selectedCourseCode.value;
    
    // 优先使用路由中的原始课时名称，如果没有则构造"课时X"格式
    let tempHour = originalHour.value;
    
    console.log('生成PPT过程中的课程信息:', {
      路由参数课程ID: courseId.value,
      当前选择课程ID: selectedCourseCode.value,
      原始课时: originalHour.value,
      课时中提取的titleId: originalHour.value ? getTitleId(originalHour.value) : null
    });
    
    // 格式化课时名称
    if (tempHour) {
      tempHour = formatHourTitle(tempHour);
    }
    
    // 如果没有课程ID和课时，使用临时ID
    if (!tempCourseId) {
      // 完全没有选择课程时使用临时ID
      tempCourseId = 'T' + Math.floor(Math.random() * 900 + 100).toString(); // 形如 T123
      const titleMatch = settings.value.title.match(/\d+/);
      if (titleMatch) {
        // 从标题中提取数字，构造"课时X"格式
        tempHour = `课时${titleMatch[0]}`;
      } else {
        // 如果标题中没有数字，使用临时课时格式
        tempHour = `临时课时_${Date.now().toString().substring(8, 13)}`;
      }
      console.log('使用临时ID生成PPT:', { tempCourseId, tempHour });
    } else if (!tempHour) {
      // 有课程ID但没有课时，从标题中提取课时
      tempHour = formatHourTitle(settings.value.title);
      if (!tempHour.startsWith('课时')) {
        // 如果格式化后的标题不是课时格式，创建一个默认课时
        tempHour = `课时1`;
      }
      console.log('使用选择的课程ID生成PPT:', { tempCourseId, tempHour });
    }

    console.log('最终使用的参数:', { 
      tempCourseId, 
      tempHour,
      titleId: getTitleId(tempHour) 
    });

    // 发起生成请求
    const response = await axios.post(`/api/ppt/generate/${tempCourseId}/${tempHour}`, {
      title: settings.value.title,
      content: JSON.parse(settings.value.outlineContent),
      templateId: currentTemplateId.value
    });
    
    if (!response.data.flag) {
      throw new Error(response.data.desc || '生成失败');
    }

    const sid = response.data.data.sid;
    
    // 轮询进度
    await pollPPTProgress(sid);

  } catch (error) {
    console.error('生成PPT失败:', error);
    message.error(error.message || '生成失败');
    generatedResult.value = {
      status: 'error',
      title: '生成失败',
      message: error.message || '未知错误'
    };
  } finally {
    generating.value = false;
  }
};

const generateAutoPPT = async () => {
  try {
    // 进度显示已在主函数中设置，这里无需重复设置
    // 检查智能模式的必要参数
    if (settings.value.autoMode === 'file') {
      if (fileList.value.length === 0) {
        throw new Error('请选择上传文件');
      }
    } else { // 文本模式
      if (!settings.value.autoContent) {
        throw new Error('请输入生成内容');
      }
    }

    // 准备FormData
    const formData = new FormData();
    formData.append('autoMode', settings.value.autoMode);

    if (settings.value.autoMode === 'file') {
      const file = fileList.value[0] as unknown as File;
      formData.append('file', file);
    } else {
      formData.append('content', settings.value.autoContent);
    }

    // 显示正在处理状态
    progressMessage.value = '正在处理您的' + 
      (settings.value.autoMode === 'file' ? '文件' : '内容') + 
      '并生成PPT...';

    // 发送请求
    const response = await axios.post(
      '/api/ppt/generateAuto',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );

    if (!response.data.flag) {
      throw new Error(response.data.desc || '生成失败');
    }

    const sid = response.data.data.sid;
    
    // 轮询进度
    await pollPPTProgress(sid);
  } catch (error) {
    // 错误处理已在主函数中完成，这里仅抛出异常
    throw error;
  }
};

// 使用与大纲模式相同的轮询函数
const pollPPTProgress = async (sid: string) => {
  try {
    // 轮询进度
    let isCompleted = false;
    let retryCount = 0;
    
    while (!isCompleted && retryCount < 60) {
      const progressResponse = await axios.get('/api/ppt/progress', {
        params: { sid }
      });
      
      if (!progressResponse.data.flag) {
        throw new Error(progressResponse.data.desc || '查询进度失败');
      }
      
      const progressData = progressResponse.data.data;
      
      // 更新进度
      progressPercent.value = progressData.percent || 0;
      progressMessage.value = '正在生成PPT: ' + progressPercent.value + '%';
      
      // 检查是否完成
      if (progressData.status === 'completed') {
        generatedResult.value = {
          status: 'success',
          title: '生成成功',
          message: 'PPT已生成完成',
          downloadUrl: progressData.downloadUrl,
          pptUrl: progressData.pptUrl
        };
        isCompleted = true;
      } else if (progressData.status === 'failed') {
        throw new Error(progressData.message || 'PPT生成失败');
      } else {
        // 等待10秒后再查询
        await new Promise(resolve => setTimeout(resolve, 10000));
        retryCount++;
      }
    }
    
    if (retryCount >= 60) {
      throw new Error('生成超时，请稍后查看记录');
    }
  } catch (error) {
    generatedResult.value = {
      status: 'error',
      title: '生成失败',
      message: error instanceof Error ? error.message : '未知错误'
    };
  } finally {
    generating.value = false;
  }
};

// 下载PPT
const downloadPPT = () => {
  if (!generatedResult.value?.downloadUrl) return
  
  window.open(generatedResult.value.downloadUrl, '_blank')
}

// 修改预览PPT的功能
const showPreviewModal = () => {
  if (!generatedResult.value?.downloadUrl) return;
  
  
  // 直接使用原始的讯飞PPT URL
  let fileUrl = generatedResult.value.pptUrl;
  
  // 如果没有原始URL，才使用本地URL
  if (!fileUrl) {
    fileUrl = `${window.location.origin}${generatedResult.value.downloadUrl}`;
    message.warning('未获取到原始PPT链接，将使用本地链接');
  } else {
    message.success('使用原始PPT链接预览');
  }
  
  // 使用pfile.com.cn的预览服务
  const previewServiceUrl = 'http://www.pfile.com.cn/api/profile/onlinePreview?url=';
  
  // 打开预览页面前确认URL
  console.log('最终预览URL:', fileUrl);
  
  // 直接在新窗口打开预览页面
  window.open(previewServiceUrl + encodeURIComponent(fileUrl), '_blank');
};

// 添加编辑状态
const isEditing = ref(false);

// 可编辑的大纲数据（表单形式）
interface ChapterContent {
  chapterTitle: string;
  chapterContents: null | ChapterContent[];
}

interface Chapter {
  chapterTitle: string;
  chapterContents: ChapterContent[] | null;
}

interface EditableOutline {
  title: string;
  subTitle: string;
  chapters: Chapter[];
}

const editableOutline = reactive<EditableOutline>({
  title: '',
  subTitle: '',
  chapters: [
    {
      chapterTitle: '',
      chapterContents: []
    }
  ]
});

// 添加章节
const addChapter = () => {
  editableOutline.chapters.push({
    chapterTitle: '',
    chapterContents: []
  });
};

// 删除章节
const removeChapter = (index: number) => {
  if (editableOutline.chapters.length > 1) {
    editableOutline.chapters.splice(index, 1);
  } else {
    message.warning('至少需要保留一个章节');
  }
};

// 添加内容
const addContent = (chapter: Chapter) => {
  if (!chapter.chapterContents) {
    chapter.chapterContents = [];
  }
  chapter.chapterContents.push({
    chapterTitle: '',
    chapterContents: null
  });
};

// 删除内容
const removeContent = (chapter: Chapter, index: number) => {
  if (chapter.chapterContents) {
    chapter.chapterContents.splice(index, 1);
  }
};

// 保存大纲
const saveOutline = async () => {
  try {
    // 验证数据
    if (!editableOutline.title.trim()) {
      message.warning('请输入PPT标题');
      return;
    }
    
    // 验证每个章节
    for (let i = 0; i < editableOutline.chapters.length; i++) {
      const chapter = editableOutline.chapters[i];
      if (!chapter.chapterTitle.trim()) {
        message.warning(`请输入第${i+1}章的标题`);
        return;
      }
    }
    
    // 转换为JSON
    settings.value.title = editableOutline.title;
    settings.value.outlineContent = JSON.stringify(editableOutline, null, 2);
    
    // 保存到数据库
    if (selectedCourseCode.value || courseId.value) {
      const tempCourseId = courseId.value || selectedCourseCode.value;
      
      // 优先使用路由中的原始课时名称，如果没有则检查是否有数字可以组成"课时X"格式
      let tempHour = originalHour.value;
      
      // 格式化课时名称
      if (tempHour) {
        tempHour = formatHourTitle(tempHour);
      } else {
        // 从标题中提取课时格式
        tempHour = formatHourTitle(editableOutline.title);
        if (!tempHour.startsWith('课时')) {
          // 如果无法提取出课时格式，使用默认课时
          tempHour = `课时1`;
        }
      }
      
      console.log('保存大纲使用的参数:', { 
        tempCourseId, 
        tempHour,
        titleId: getTitleId(tempHour)
      });
      message.loading('正在保存大纲...', 0);
      
      try {
        // 使用当前模板ID或课程信息保存
        let url = '';
        let data = {
          title: settings.value.title,
          content: JSON.parse(settings.value.outlineContent)
        };
        
        if (currentTemplateId.value) {
          // 如果有模板ID，直接更新该模板
          url = `/api/ppt/template/${tempCourseId}/byTitleId/${currentTemplateId.value}`;
          console.log('使用模板ID更新:', url);
        } else {
          // 否则使用课程代码和课时格式保存
          url = `/api/ppt/template/${tempCourseId}/${tempHour}`;
          console.log('使用课时名称保存模板:', url);
        }
        
        const response = await axios.post(url, data);
        message.destroy();
        
        if (response.data.flag) {
          // 保存成功，记录模板ID
          if (response.data.templateId) {
            currentTemplateId.value = response.data.templateId;
            console.log('保存成功，模板ID:', currentTemplateId.value);
          }
          message.success('已保存大纲内容到数据库');
        } else {
          message.warning('保存到数据库失败：' + response.data.desc);
        }
      } catch (error) {
        message.destroy();
        console.error('保存到数据库失败:', error);
        message.error('保存到数据库失败');
      }
    } else {
      message.info('请先选择一个课程以保存大纲');
    }
    
    // 切换到预览模式
    isEditing.value = false;
  } catch (e) {
    console.error('保存大纲失败:', e);
    message.error('保存失败，请检查输入内容');
  }
};

// 切换编辑/预览模式
const toggleEditMode = async () => {
  if (isEditing.value) {
    // 从编辑模式切换到预览模式，调用保存函数
    await saveOutline();
    // saveOutline函数中已经切换了isEditing状态，不需要再次设置
  } else {
    // 从预览模式切换到编辑模式
    try {
      // 解析当前JSON到编辑表单
      const currentOutline = JSON.parse(settings.value.outlineContent);
      editableOutline.title = currentOutline.title || '';
      editableOutline.subTitle = currentOutline.subTitle || '';
      
      // 确保chapters数组存在且有内容
      if (Array.isArray(currentOutline.chapters) && currentOutline.chapters.length > 0) {
        editableOutline.chapters = JSON.parse(JSON.stringify(currentOutline.chapters));
        
        // 确保每个章节的chapterContents都是数组
        editableOutline.chapters.forEach(chapter => {
          if (!Array.isArray(chapter.chapterContents)) {
            chapter.chapterContents = [];
          }
        });
      } else {
        // 重置为默认章节
        editableOutline.chapters = [
          {
            chapterTitle: '第一章',
            chapterContents: []
          }
        ];
      }
      
      isEditing.value = true;
    } catch (e) {
      console.error('解析大纲内容失败:', e);
      message.error('大纲内容格式错误，无法编辑');
    }
  }
};

// 格式化JSON
const formatJSON = () => {
  try {
    const obj = JSON.parse(settings.value.outlineContent);
    settings.value.outlineContent = JSON.stringify(obj, null, 2);
    message.success('格式化成功');
  } catch (e) {
    message.error('JSON格式错误，无法格式化');
  }
};

// 显示JSON帮助信息
const showJsonHelp = () => {
  // 使用简单的loading提示
  message.info('正在准备JSON结构参考...');
  
  // 打开新窗口显示帮助信息
  const helpWindow = window.open('', '_blank');
  if (helpWindow) {
    helpWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>PPT大纲JSON结构参考</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          pre { background-color: #f5f5f5; padding: 20px; border-radius: 4px; overflow: auto; }
          code { font-family: Consolas, Monaco, monospace; }
          h1 { color: #1890ff; }
          .note { color: #666; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <h1>PPT大纲JSON结构参考</h1>
        <div class="note">下面是PPT大纲的标准JSON结构格式，您可以根据此格式编写自己的大纲。</div>
        <pre><code>{
  "title": "PPT标题，会显示在首页和标题中",
  "subTitle": "PPT副标题，显示在首页",
  "chapters": [
    {
      "chapterTitle": "第一章标题",
      "chapterContents": [
        {
          "chapterTitle": "第一章第一节内容",
          "chapterContents": null
        },
        {
          "chapterTitle": "第一章第二节内容",
          "chapterContents": null
        }
      ]
    },
    {
      "chapterTitle": "第二章标题",
      "chapterContents": [
        {
          "chapterTitle": "第二章第一节内容",
          "chapterContents": null
        }
      ]
    }
  ]
}</code></pre>
      </body>
      </html>
    `);
    helpWindow.document.close();
  } else {
    message.error('无法打开帮助窗口，请检查浏览器是否阻止了弹出窗口');
  }
};

// 添加一个计算属性用于解析大纲内容
const parsedOutline = computed(() => {
  try {
    return settings.value.outlineContent ? JSON.parse(settings.value.outlineContent) : {};
  } catch (e) {
    console.error('解析大纲内容失败:', e);
    return {};
  }
});

// 修改组件挂载逻辑
onMounted(async () => {
  // 从路由参数获取数据
  if (route.query.courseId && route.query.originalHour) {
    // 更新ref的值
    courseId.value = route.query.courseId.toString();
    originalHour.value = route.query.originalHour.toString();
    
    console.log('从教案页面跳转，参数:', { 
      courseId: courseId.value,
      originalHour: originalHour.value,
      title: route.query.title,
      courseName: route.query.courseName
    });

    // 获取所有可用模板
    await fetchAvailableTemplates();
    
    // 如果有必要参数，说明是从教案页面跳转来的
    if (courseId.value && originalHour.value) {
      try {
        // 尝试自动选择正确的课程
        if (courseId.value) {
          selectedCourseCode.value = courseId.value;
          handleCourseChange(courseId.value);
          
          // 如果课程下有对应的大纲，自动选择第一个匹配的
          const matchedTemplate = courseTitles.value.find(t => t.title === originalHour.value);
          if (matchedTemplate) {
            setTimeout(() => {
              loadTemplate(matchedTemplate.id);
            }, 100);
          }
        }
        
        // 从数据库获取PPT模板
        const response = await axios.get(`/api/ppt/template/${courseId.value}/${originalHour.value}`);
        
        if (response.data.flag && response.data.data?.content) {
          // 使用数据库中保存的PPT大纲
          settings.value.outlineContent = JSON.stringify(response.data.data.content, null, 2);
          console.log('成功加载PPT大纲:', response.data.data.content);
          
          // 设置标题
          if (response.data.data.content.title) {
            settings.value.title = response.data.data.content.title;
          }
        } else {
          console.warn('未找到PPT大纲，使用空白模板');
          // 使用空白模板
          settings.value.outlineContent = JSON.stringify({
            title: route.query.title?.toString() || "", 
            subTitle: route.query.courseName?.toString() || "",
            chapters: [
              {
                chapterTitle: "第一章",
                chapterContents: [
                  {
                    chapterTitle: "1.1 节",
                    chapterContents: null
                  }
                ]
              }
            ]
          }, null, 2);
        }
      } catch (error) {
        console.error('加载PPT大纲失败:', error);
        message.error('加载PPT大纲失败');
      }
    } else {
      // 从菜单直接打开，显示引导信息
      console.log('从菜单直接打开 PPT 制作页面');
      
      // 简单提示用户选择课程
      if (availableTemplates.value.length > 0) {
        message.info('请在左侧选择课程和PPT大纲模板');
      }
      
      // 清空大纲内容，显示空状态
      settings.value.outlineContent = '';
      settings.value.title = '';
    }
  } else {
    // 直接打开页面的情况，加载模板列表
    await fetchAvailableTemplates();
  }
});
</script>

<style scoped>
.ppt-maker {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 24px;
  background: white;
  border-radius: 2px;
}

.content {
  margin-bottom: 24px;
}

.generating-status {
  padding: 48px;
  text-align: center;
}

.progress-info {
  max-width: 480px;
  margin: 0 auto;
}

.result-info {
  padding: 24px;
  text-align: center;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-card-head) {
  min-height: 48px;
}

:deep(.ant-card-head-title) {
  padding: 12px 0;
}

.preview-container {
  width: 100%;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.ant-modal-body) {
  padding: 12px;
}

/* 添加智能模式特定样式 */
.auto-mode-alert {
  margin: 16px 0;
}
.upload-section {
  margin-top: 8px;
}

.tips {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}

.outline-preview {
  padding: 16px 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.outline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 16px;
}

.outline-editor {
  text-align: left;
  padding: 0 16px;
  flex-grow: 1;
  height: calc(100vh - 300px);
  min-height: 400px;
}

.outline-textarea {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  resize: none;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 12px;
  height: 100%;
  width: 100%;
}

.outline-structure {
  margin-top: 24px;
  text-align: left;
  padding: 0 24px;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

.outline-meta {
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
}

.meta-item {
  margin-bottom: 8px;
}

.chapter-item {
  margin-bottom: 24px;
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
}

.chapter-title {
  font-weight: bold;
  font-size: 16px;
  color: #1890ff;
  margin-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.chapter-contents {
  margin-top: 8px;
}

.content-item {
  margin-left: 24px;
  margin-bottom: 8px;
  position: relative;
  padding-left: 8px;
}

/* 下拉选择器样式 */
:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-space-vertical) {
  width: 100%;
}

:deep(.ant-select-item-option-content) {
  white-space: normal;
  word-break: break-word;
}

:deep(.ant-select-selection-item) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  line-clamp: 1;
}

.friendly-editor {
  padding: 0 16px;
  overflow-y: auto;
  height: 100%;
}

.chapter-edit-item {
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  position: relative;
}

.chapter-edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chapter-number {
  font-weight: bold;
  color: #1890ff;
}

.content-edit-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.content-input {
  flex: 1;
  margin-right: 8px;
}

.add-btn {
  margin-top: 8px;
}

.add-chapter-btn {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  margin-bottom: 24px;
}

.selected-course-info {
  margin: 5px 0;
  display: flex;
  align-items: center;
}

.empty-description {
  margin-bottom: 20px;
}

.guide-list {
  list-style-type: disc;
  padding-left: 20px;
}

.empty-image {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 模板选择样式 */
:global(.template-selection) {
  text-align: center;
}

:global(.template-tip) {
  margin-bottom: 20px;
  color: #666;
  font-size: 16px;
}

:global(.template-cards-container) {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 15px;
}

:global(.template-card) {
  width: 200px;
  height: 200px;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #f9f9f9;
  position: relative;
  overflow: hidden;
}

:global(.template-card:hover) {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

:global(.template-card-inner) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:global(.template-card h3) {
  margin-top: 0;
  margin-bottom: 5px;
  color: #1890ff;
}

:global(.template-card .subtitle) {
  color: #888;
  font-size: 14px;
  margin-bottom: 10px;
}

:global(.template-summary) {
  font-size: 12px;
  color: #666;
  text-align: left;
  flex-grow: 1;
  overflow: hidden;
}

:global(.ant-modal-content) {
  overflow: visible !important;
}
</style> 
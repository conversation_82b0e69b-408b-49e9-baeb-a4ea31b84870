<template>
  <div class="animation-maker">
    <div class="page-header">
      <h1>动画制作</h1>
      <p class="description">
        使用AI技术生成交互式教学动画，帮助学生更好地理解抽象概念
      </p>
    </div>

    <!-- 选项卡 -->
    <a-tabs v-model:activeKey="activeTab">
      <a-tab-pane key="create" tab="创建动画">
        <a-row :gutter="24">
      <a-col :span="12">
        <a-card class="input-card" title="动画内容设置">
          <a-form layout="vertical">

            <a-form-item label="动画内容描述">
              <a-textarea
                v-model:value="animationContent"
                :rows="8"
                placeholder="请详细描述您想要制作的动画内容，例如：创建一个可交互的导数可视化动画，展示函数 f(x) = x³ 在不同点的切线变化，用户可以拖动点来观察导数值的变化。"
              />
            </a-form-item>

            <a-form-item label="交互要求">
              <a-textarea
                v-model:value="interactionRequirements"
                :rows="4"
                placeholder="描述您希望动画具有的交互功能，例如：用户可以拖动点、调整参数、点击按钮切换不同视图等"
              />
            </a-form-item>

            <a-form-item>
              <a-button
                type="primary"
                :loading="generating"
                @click="generateAnimation"
                :disabled="!animationContent"
                block
              >
                {{ generating ? '生成中...' : '生成动画' }}
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card class="result-card" title="生成结果">
          <template v-if="generating">
            <div class="loading-container">
              <a-spin tip="AI正在创建您的动画，这可能需要一些时间...">
                <div class="loading-content">
                  <p>正在分析内容并生成交互式动画</p>
                  <p class="loading-tip">复杂的动画可能需要等待1-2分钟，请耐心等待</p>
                </div>
              </a-spin>
            </div>
          </template>

          <template v-else-if="animationUrl">
            <div class="result-container">
              <div class="success-message">
                <a-result status="success" title="动画生成成功！">
                  <template #extra>
                    <a-button type="primary" @click="openAnimation">
                      查看动画
                    </a-button>
                  </template>
                </a-result>
              </div>
            </div>
          </template>

          <template v-else>
            <div class="empty-container">
              <a-empty description="请在左侧填写动画内容并点击生成按钮">
                <template #image>
                  <div class="empty-image">
                    <play-circle-outlined style="font-size: 60px; color: #1890ff;" />
                  </div>
                </template>
              </a-empty>
            </div>
          </template>
        </a-card>

        <a-card v-if="animationUrl" class="tips-card" title="使用提示" style="margin-top: 16px;">
          <ul class="tips-list">
            <li>生成的动画已保存，您可以随时点击"查看动画"按钮重新打开</li>
            <li>动画使用HTML、CSS和JavaScript实现，支持大多数现代浏览器</li>
            <li>您可以将动画嵌入到PPT或课件中，或直接在课堂上展示</li>
            <li>如果动画效果不理想，可以尝试修改描述后重新生成</li>
          </ul>
        </a-card>
      </a-col>
    </a-row>
      </a-tab-pane>

      <a-tab-pane key="records" tab="动画记录">
        <div class="records-container">
          <a-table
            :dataSource="animationRecords"
            :columns="recordColumns"
            :loading="loadingRecords"
            :pagination="{ pageSize: 10 }"
            rowKey="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="primary" size="small" @click="previewAnimation(record.id)">
                  预览
                </a-button>
              </template>
              <template v-if="column.key === 'create_time'">
                {{ formatDate(record.create_time) }}
              </template>
            </template>
          </a-table>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 背景装饰 -->
    <div class="decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { message } from 'ant-design-vue'
import axios from '@/utils/axios' // 使用配置好的axios实例
import { PlayCircleOutlined } from '@ant-design/icons-vue'

// 状态变量
const activeTab = ref('create') // 默认选中创建动画选项卡
const animationTitle = ref('')
const animationContent = ref('')
const interactionRequirements = ref('')
const generating = ref(false)
const animationUrl = ref('')
const currentTaskId = ref('')
const pollingInterval = ref(null)

// 动画记录相关状态
const animationRecords = ref([])
const loadingRecords = ref(false)

// 表格列定义
const recordColumns = [
  {
    title: '动画名称',
    dataIndex: 'anime_text',
    key: 'anime_text',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    align: 'center',
  },
]

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载动画记录
async function loadAnimationRecords() {
  try {
    loadingRecords.value = true
    const response = await axios.get('/api/animation/records')
    if (response.data.success) {
      animationRecords.value = response.data.data
    } else {
      throw new Error(response.data.message || '获取记录失败')
    }
  } catch (error) {
    console.error('加载动画记录失败:', error)
    message.error('加载动画记录失败')
  } finally {
    loadingRecords.value = false
  }
}

// 预览动画
async function previewAnimation(id) {
  try {
    message.loading('正在加载动画...', 0)
    const response = await axios.get(`/api/animation/records/${id}`)
    if (response.data.success) {
      message.destroy()
      message.success('动画加载成功')
      window.open(response.data.data.filePath, '_blank')
    } else {
      throw new Error(response.data.message || '加载动画失败')
    }
  } catch (error) {
    message.destroy()
    console.error('预览动画失败:', error)
    message.error('预览动画失败: ' + (error.message || '未知错误'))
  }
}

// 监听选项卡切换
watch(activeTab, (newValue) => {
  if (newValue === 'records') {
    loadAnimationRecords()
  }
})

// 在组件加载时恢复状态
onMounted(() => {
  // 从 localStorage 恢复状态
  try {
    const savedState = localStorage.getItem('animationMakerState')
    if (savedState) {
      const state = JSON.parse(savedState)
      animationTitle.value = state.title || ''
      animationContent.value = state.content || ''
      interactionRequirements.value = state.requirements || ''
      animationUrl.value = state.url || ''

      // 恢复任务ID
      if (state.taskId) {
        currentTaskId.value = state.taskId
        // 如果有任务ID但没有URL，说明任务可能还在进行中
        if (!animationUrl.value) {
          // 开始轮询任务状态
          startPollingTaskStatus()
        }
      }
    }

    // 如果默认选中的是记录选项卡，加载记录
    if (activeTab.value === 'records') {
      loadAnimationRecords()
    }
  } catch (error) {
    console.error('恢复状态失败:', error)
  }
})

// 组件卸载前清除轮询
onBeforeUnmount(() => {
  stopPollingTaskStatus()
})

// 开始轮询任务状态
function startPollingTaskStatus() {
  if (!currentTaskId.value) return

  generating.value = true

  // 先立即查询一次
  checkTaskStatus()

  // 设置定时轮询
  pollingInterval.value = setInterval(() => {
    checkTaskStatus()
  }, 3000) // 每3秒轮询一次
}

// 停止轮询
function stopPollingTaskStatus() {
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value)
    pollingInterval.value = null
  }
}

// 检查任务状态
async function checkTaskStatus() {
  if (!currentTaskId.value) return

  try {
    const response = await axios.get(`/api/animation/status/${currentTaskId.value}`)

    if (response.data.status === 'completed') {
      // 任务完成
      generating.value = false
      animationUrl.value = response.data.filePath
      message.success('动画生成成功！')
      stopPollingTaskStatus()

      // 更新存储的状态
      saveState()

      // 如果在记录页面，刷新记录列表
      if (activeTab.value === 'records') {
        loadAnimationRecords()
      }
    } else if (response.data.status === 'failed') {
      // 任务失败
      generating.value = false
      message.error(response.data.message || '生成动画失败')
      stopPollingTaskStatus()
    }
    // 如果是 pending 或 processing，继续轮询
  } catch (error) {
    console.error('检查任务状态失败:', error)
    // 如果请求失败，不要停止轮询，可能是网络问题
  }
}

// 保存状态到localStorage
function saveState() {
  try {
    localStorage.setItem('animationMakerState', JSON.stringify({
      title: animationTitle.value,
      content: animationContent.value,
      requirements: interactionRequirements.value,
      url: animationUrl.value,
      taskId: currentTaskId.value
    }))
  } catch (error) {
    console.error('保存状态失败:', error)
  }
}

// 监听输入字段变化，保存到 localStorage
watch([animationTitle, animationContent, interactionRequirements], () => {
  // 只有在非生成状态时才保存输入内容
  if (!generating.value) {
    saveState()
  }
})

// 生成动画
const generateAnimation = async () => {
  if (!animationContent.value) {
    message.warning('请输入动画内容描述')
    return
  }

  try {
    // 停止之前的轮询（如果有）
    stopPollingTaskStatus()

    // 设置生成状态
    generating.value = true
    animationUrl.value = ''

    // 构建请求内容
    const content = `${animationTitle.value ? animationTitle.value + '：' : ''}${animationContent.value}${
      interactionRequirements.value ? '，交互要求：' + interactionRequirements.value : ''
    }`

    // 调用后端API创建任务
    const response = await axios.post('/api/animation/generate', {
      content
    })

    if (response.data.success) {
      // 保存任务ID
      currentTaskId.value = response.data.taskId

      // 保存状态
      saveState()

      // 开始轮询任务状态
      startPollingTaskStatus()

      message.info('动画生成任务已创建，正在后台处理中...')
    } else {
      throw new Error(response.data.message || '创建任务失败')
    }
  } catch (error) {
    console.error('生成动画失败:', error)
    message.error(error.message || '生成动画失败，请稍后重试')
    generating.value = false
  }
}

// 打开动画
const openAnimation = () => {
  if (animationUrl.value) {
    window.open(animationUrl.value, '_blank')
  }
}
</script>

<style scoped>
.animation-maker {
  padding: 24px;
  position: relative;
  min-height: calc(100vh - 64px);
  overflow: hidden;
}

.page-header {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f1f1f;
}

.description {
  font-size: 16px;
  color: #666;
  max-width: 800px;
}

.input-card,
.result-card,
.tips-card {
  height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.loading-container,
.result-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.loading-content {
  margin-top: 16px;
}

.loading-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.success-message {
  width: 100%;
}

.tips-list {
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  color: #666;
}

/* 装饰元素 */
.decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.5;
}

.circle-1 {
  width: 300px;
  height: 300px;
  background: rgba(99, 102, 241, 0.15);
  top: -100px;
  left: 10%;
  animation: float 15s ease-in-out infinite;
}

.circle-2 {
  width: 400px;
  height: 400px;
  background: rgba(99, 179, 237, 0.1);
  bottom: -150px;
  right: 5%;
  animation: float 20s ease-in-out infinite reverse;
}

.circle-3 {
  width: 200px;
  height: 200px;
  background: rgba(79, 209, 197, 0.15);
  top: 40%;
  right: 30%;
  animation: float 18s ease-in-out infinite 2s;
}

@keyframes float {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(-10px, 15px) rotate(5deg); }
  50% { transform: translate(10px, -10px) rotate(-5deg); }
  75% { transform: translate(-15px, -15px) rotate(3deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}
</style>

<template>
  <a-modal
    :visible="visible"
    @update:visible="emit('update:visible', $event)"
    title="相关教学资源"
    width="80%"
    style="top: 20px"
    :footer="null"
    @cancel="handleClose"
    class="resource-modal"
    :maskClosable="false"
    :bodyStyle="{ maxHeight: 'calc(90vh - 150px)', overflowY: 'auto', padding: 0 }"
  >
    <div class="modal-top-decoration"></div>
    <div class="resource-search">
      <!-- 搜索框和筛选器 -->
      <div class="search-section mb-4 flex flex-wrap items-center gap-3">
        <a-input-search
          v-model:value="searchQuery"
          placeholder="请输入要搜索的内容"
          class="flex-1 search-input min-w-[250px]"
          :loading="isLoading"
          @search="handleSearch"
          :default-value="initialKeyword"
        >
          <template #enterButton>
            <a-button type="primary" :loading="isLoading" class="search-btn">
              <template #icon v-if="!isLoading"><search-outlined /></template>
              搜索
            </a-button>
          </template>
        </a-input-search>

        <!-- 筛选器 -->
        <div class="filter-container flex items-center gap-2 flex-wrap">
          <span class="filter-label">排序:</span>
          <a-select
            v-model:value="sortBy"
            style="width: 110px"
            size="middle"
            @change="handleSearch"
            class="filter-select"
          >
            <a-select-option value="totalrank">综合排序</a-select-option>
            <a-select-option value="click">播放最多</a-select-option>
            <a-select-option value="pubdate">最新发布</a-select-option>
          </a-select>
          
          <span class="filter-label ml-2">时长:</span>
          <a-select
            v-model:value="duration"
            style="width: 110px"
            size="middle"
            @change="handleSearch"
            class="filter-select"
          >
            <a-select-option value="0">全部时长</a-select-option>
            <a-select-option value="1">10分钟以下</a-select-option>
            <a-select-option value="2">10-30分钟</a-select-option>
            <a-select-option value="3">30-60分钟</a-select-option>
            <a-select-option value="4">60分钟以上</a-select-option>
          </a-select>
        </div>
      </div>

      <!-- 搜索结果区域 - 使用标签页而不是并排显示 -->
      <a-tabs v-model:activeKey="activeTabKey" class="resource-tabs">
        <!-- 视频搜索标签页 -->
        <a-tab-pane key="videos" :tab="videoTabTitle">
          <!-- 加载状态 -->
          <div v-if="isLoading && !videos.length" class="loading-container">
            <a-spin>
              <template #indicator>
                <loading-outlined style="font-size: 24px" spin />
              </template>
            </a-spin>
          </div>

          <!-- 视频列表 -->
          <div v-if="!isLoading && videos.length > 0" class="video-grid">
            <a-card
              v-for="video in videos"
              :key="video.bvid"
              hoverable
              class="video-card"
              @click="openVideo(video)"
            >
              <template #cover>
                <div class="video-thumbnail">
                  <img 
                    :src="getProxyImageUrl(video.pic)"
                    class="w-full h-full object-cover"
                    :alt="video.title"
                    @error="handleImageError"
                    loading="lazy"
                  />
                  <div class="video-duration">{{ video.duration }}</div>
                  <div class="video-play-count">
                    <play-circle-outlined />
                    {{ formatNumber(video.play) }}
                  </div>
                </div>
              </template>
              <a-card-meta :title="video.title">
                <template #description>
                  <div class="video-meta">
                    <span class="author">{{ video.author }}</span>
                    <span class="pubdate">{{ video.pubdate }}</span>
                  </div>
                </template>
              </a-card-meta>
            </a-card>
          </div>

          <!-- 分页器 -->
          <div v-if="!isLoading && videos.length > 0" class="mt-4 flex justify-center">
            <a-pagination
              v-model:current="currentPage"
              :total="total"
              :page-size="20"
              size="small"
              show-quick-jumper
              @change="handlePageChange"
              class="custom-pagination"
            />
          </div>

          <!-- 无结果提示 -->
          <a-empty
            v-if="!isLoading && videos.length === 0 && !error"
            description="暂无视频结果"
            class="empty-state"
          />
        </a-tab-pane>

        <!-- AI资源标签页 -->
        <a-tab-pane key="ai" :tab="aiTabTitle">
          <!-- 加载状态 -->
          <a-skeleton active :loading="aiLoading" v-if="aiLoading">
            <template #paragraph>
              <div class="h-40"></div>
            </template>
          </a-skeleton>

          <!-- 搜索结果 -->
          <template v-if="!aiLoading && aiResults.length > 0">
            <div class="ai-results-grid">
              <a-card 
                v-for="(result, index) in aiResults" 
                :key="index"
                class="search-result-card"
                @click="openAIPreview(result)"
              >
                <template #title>
                  <div class="result-title truncate">
                    {{ result.title }}
                  </div>
                </template>
                <template #extra>
                  <a-tag :color="getSourceColor(result.source)">
                    {{ result.source }}
                  </a-tag>
                </template>
                <p class="result-description line-clamp-3">{{ result.description }}</p>
              </a-card>
            </div>
          </template>
          
          <!-- 无结果提示 -->
          <a-empty 
            v-if="!aiLoading && (!aiResults || aiResults.length === 0) && !aiError"
            description="暂无AI推荐结果" 
            class="empty-state"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 视频播放弹窗 -->
    <a-modal
      v-model:visible="videoModalVisible"
      :title="currentVideo?.title"
      :footer="null"
      width="60%"
      centered
      destroyOnClose
      @cancel="handleCloseVideo"
      wrapClassName="video-modal"
      class="inner-modal"
    >
      <div class="modal-top-decoration"></div>
      <div class="video-container">
        <div class="aspect-w-16 aspect-h-9">
          <iframe
            v-if="videoModalVisible"
            :src="currentVideoUrl"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            class="w-full h-full"
          ></iframe>
        </div>
        
        <!-- 视频信息 -->
        <div class="video-info mt-4">
          <div class="flex items-center justify-between mb-2">
            <span class="flex items-center gap-2">
              <user-outlined />
              {{ currentVideo?.author }}
            </span>
            <span class="flex items-center gap-2">
              <play-circle-outlined />
              {{ currentVideo ? formatNumber(currentVideo.play) : 0 }}
            </span>
          </div>
          <div class="flex items-center justify-between">
            <span class="flex items-center gap-2">
              <clock-circle-outlined />
              {{ currentVideo?.duration }}
            </span>
            <span class="flex items-center gap-2">
              <calendar-outlined />
              {{ currentVideo?.pubdate }}
            </span>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- AI资源预览弹窗 -->
    <a-modal
      v-model:visible="aiPreviewVisible"
      :title="currentAIResult?.title"
      :footer="null"
      width="60%"
      centered
      destroyOnClose
      @cancel="handleCloseAIPreview"
      wrapClassName="ai-preview-modal"
      class="inner-modal"
    >
      <div class="modal-top-decoration"></div>
      <div class="ai-preview-container">
        <div class="preview-content">
          <div class="mb-6">
            <a-tag :color="getSourceColor(currentAIResult?.source)" class="mb-3">
              {{ currentAIResult?.source }}
            </a-tag>
            <p class="text-gray-600 preview-description">{{ currentAIResult?.description }}</p>
          </div>
          
          <!-- 操作按钮 -->
          <div class="preview-actions mt-6 flex items-center justify-center">
            <a-button 
              type="primary" 
              @click="openOriginalLink"
              class="action-button"
            >
              <template #icon><link-outlined /></template>
              查看原文
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, h } from 'vue'
import axios from '../../axios'
import {
  VideoCameraOutlined,
  RobotOutlined,
  LoadingOutlined,
  LinkOutlined,
  SearchOutlined,
  UserOutlined,
  PlayCircleOutlined,
  CalendarOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 定义视频接口
interface BilibiliVideo {
  bvid: string
  title: string
  pic: string
  author: string
  play: number
  duration: string
  pubdate: string
}

// 定义AI搜索结果接口
interface AISearchResult {
  title: string
  description: string
  url: string
  source: string
}

interface Props {
  visible: boolean
  initialKeyword?: string
  courseName?: string
  currentContent?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  initialKeyword: '',
  courseName: '',
  currentContent: () => []
})

const emit = defineEmits(['update:visible'])

// 状态变量
const searchQuery = ref(formatSearchKeyword())
const videos = ref<BilibiliVideo[]>([])
const isLoading = ref(false)
const error = ref('')
const currentPage = ref(1)
const total = ref(0)
const sortBy = ref('totalrank')
const duration = ref('0')
const activeTabKey = ref('videos')

// 视频相关
const videoModalVisible = ref(false)
const currentVideo = ref<BilibiliVideo | null>(null)

// AI搜索相关
const aiResults = ref<AISearchResult[]>([])
const aiLoading = ref(false)
const aiError = ref('')
const aiPreviewVisible = ref(false)
const currentAIResult = ref<AISearchResult | null>(null)
let eventSource: EventSource | null = null

// 计算标签页标题
const videoTabTitle = computed(() => {
  return h(
    'span',
    { class: 'tab-title' },
    [
      h(VideoCameraOutlined),
      h('span', null, 'B站教学视频'),
      h('span', { class: 'tab-count' }, !isLoading.value && videos.value.length > 0 ? `(${videos.value.length})` : '')
    ]
  )
})

const aiTabTitle = computed(() => {
  return h(
    'span',
    { class: 'tab-title' },
    [
      h(RobotOutlined),
      h('span', null, 'AI推荐资源'),
      h('a-tag', { color: 'blue', class: 'ml-1' }, 'Beta'),
      h('span', { class: 'tab-count' }, !aiLoading.value && aiResults.value.length > 0 ? `(${aiResults.value.length})` : '')
    ]
  )
})

// 计算属性
const currentVideoUrl = computed(() => {
  if (!currentVideo.value?.bvid) return ''
  return `//player.bilibili.com/player.html?bvid=${currentVideo.value.bvid}&page=1&high_quality=1&danmaku=0`
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
}

const formatNumber = (num: number): string => {
  if (!num && num !== 0) return '0'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toString()
}

const handleSearch = async () => {
  if (!searchQuery.value.trim()) return

  isLoading.value = true
  aiLoading.value = true
  error.value = ''
  aiError.value = ''
  videos.value = []
  aiResults.value = []

  try {
    // 同时执行B站搜索和AI搜索
    handleBilibiliSearch()
    handleAISearch(searchQuery.value)
  } catch (err) {
    console.error('Search error:', err)
    error.value = '搜索失败，请稍后重试'
  }
}

const handleBilibiliSearch = async () => {
  try {
    const response = await axios.get('/api/bilibili/search', {
      params: {
        keyword: searchQuery.value,
        page: currentPage.value,
        order: sortBy.value,
        duration: duration.value
      }
    })

    if (response.data.code === 0) {
      videos.value = response.data.data
      total.value = response.data.total
    } else {
      error.value = response.data.message || '搜索失败'
    }
  } catch (err) {
    console.error('Bilibili search error:', err)
    error.value = '搜索失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

const handleAISearch = async (keyword: string) => {
  aiLoading.value = true
  aiError.value = ''
  aiResults.value = []

  if (eventSource) {
    eventSource.close()
  }

  eventSource = new EventSource(`/api/ai-search/search?keyword=${encodeURIComponent(keyword)}`)

  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data)
    
    switch (data.type) {
      case 'result':
        aiResults.value.push(data.data as AISearchResult)
        break
      
      case 'complete':
        aiLoading.value = false
        eventSource!.close()
        break
      
      case 'error':
        aiError.value = data.message
        aiLoading.value = false
        eventSource!.close()
        break
    }
  }

  eventSource.onerror = () => {
    aiError.value = '搜索连接出错，请重试'
    aiLoading.value = false
    eventSource!.close()
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  handleBilibiliSearch()
}

const openVideo = (video: any) => {
  currentVideo.value = video
  videoModalVisible.value = true
}

const handleCloseVideo = () => {
  currentVideo.value = null
  videoModalVisible.value = false
}

const openAIPreview = (result: any) => {
  currentAIResult.value = result
  aiPreviewVisible.value = true
}

const handleCloseAIPreview = () => {
  currentAIResult.value = null
  aiPreviewVisible.value = false
}

const openOriginalLink = () => {
  if (currentAIResult.value?.url) {
    window.open(currentAIResult.value.url, '_blank')
  }
}

const getSourceColor = (source: string | undefined): string => {
  if (!source) return 'default'
  
  switch (source) {
    case '技术文章':
      return 'blue'
    case '教育资源':
      return 'green'
    case '学术资源':
      return 'purple'
    case '视频教程':
      return 'orange'
    default:
      return 'default'
  }
}

const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.src = 'https://placehold.co/672x378/e5e7eb/475569?text=Video'
}

// 处理图片URL的方法
const getProxyImageUrl = (url: string) => {
  // 如果URL以//开头，添加https:前缀
  let fullUrl = url
  if (url.startsWith('//')) {
    fullUrl = 'https:' + url
  }
  return `/api/bilibili/image?url=${encodeURIComponent(fullUrl)}`
}

// 格式化搜索关键词
function formatSearchKeyword(): string {
  if (!props.courseName) return ''
  
  // 如果有当前课时内容，使用第一个内容（移除难度标记）
  if (props.currentContent && props.currentContent.length > 0) {
    const content = props.currentContent[0].replace(/\[(简单|中等|困难)\]$/, '')
    return `${props.courseName}${content}`
  }
  
  // 如果没有课时内容，则只使用课程名称
  return props.courseName
}

// 生命周期
onMounted(() => {
  if (searchQuery.value) {
    handleSearch()
  }
})

onUnmounted(() => {
  if (eventSource) {
    eventSource.close()
  }
})
</script>

<style scoped>
.resource-modal {
  border-radius: 16px;
  overflow: hidden;
}

.resource-modal :deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 12px 48px rgba(76, 77, 230, 0.15);
  border: 1px solid rgba(76, 77, 230, 0.1);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.resource-modal :deep(.ant-modal-header) {
  background: transparent;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  padding: 16px 24px;
}

.resource-modal :deep(.ant-modal-title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  background: linear-gradient(135deg, #4c4de6 0%, #5971e6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.resource-modal :deep(.ant-modal-body) {
  padding: 0 !important;
  flex: 1;
  overflow: hidden;
}

.resource-modal :deep(.ant-modal-close) {
  color: #4c4de6;
}

.modal-top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4c4de6, #5971e6);
  z-index: 10;
}

.resource-search {
  padding: 12px 16px;
}

/* 搜索区域样式 */
.search-section {
  background: rgba(246, 248, 252, 0.7);
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid rgba(229, 231, 235, 0.6);
}

.search-input :deep(.ant-input) {
  height: 38px;
  border-radius: 8px 0 0 8px;
  border-color: #d1d5db;
}

.search-input :deep(.ant-input:focus) {
  border-color: #4c4de6;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.1);
}

.search-btn {
  height: 38px;
  background: linear-gradient(90deg, #4c4de6, #5971e6);
  border-color: #4c4de6;
  border-radius: 0 8px 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.search-btn:hover {
  background: linear-gradient(90deg, #5758e8, #6a80e8);
  border-color: #5758e8;
}

.filter-label {
  color: #4b5563;
  font-size: 14px;
}

.filter-select {
  height: 38px;
}

.filter-select :deep(.ant-select-selector) {
  border-radius: 6px !important;
  border-color: #d1d5db !important;
  height: 38px !important;
  display: flex;
  align-items: center;
}

.filter-select:hover :deep(.ant-select-selector) {
  border-color: #4c4de6 !important;
}

/* 标签页样式 */
.resource-tabs {
  margin-top: 12px;
}

.resource-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 12px;
}

.resource-tabs :deep(.ant-tabs-tab) {
  padding: 8px 12px;
  transition: all 0.3s;
}

.resource-tabs :deep(.ant-tabs-tab-active) {
  background: rgba(76, 77, 230, 0.05);
  border-radius: 8px 8px 0 0;
}

.resource-tabs :deep(.ant-tabs-ink-bar) {
  background: #4c4de6;
  height: 3px;
  border-radius: 3px 3px 0 0;
}

.tab-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-count {
  font-size: 12px;
  color: #6b7280;
  margin-left: 4px;
}

/* 视频网格 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.video-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(229, 231, 235, 0.6);
  height: 100%;
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(76, 77, 230, 0.15);
  border-color: rgba(76, 77, 230, 0.3);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.video-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
}

.video-duration {
  position: absolute;
  bottom: 6px;
  right: 6px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 11px;
}

.video-play-count {
  position: absolute;
  top: 6px;
  right: 6px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1px 6px;
  border-radius: 4px;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.video-card :deep(.ant-card-body) {
  padding: 8px 10px;
}

.video-card :deep(.ant-card-meta-title) {
  white-space: normal !important;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.author {
  color: #4b5563;
  font-weight: 500;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 60%;
}

.pubdate {
  color: #6b7280;
  font-size: 11px;
}

/* AI结果网格 */
.ai-results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.search-result-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(229, 231, 235, 0.6);
  cursor: pointer;
  height: 100%;
}

.search-result-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(76, 77, 230, 0.12);
  border-color: rgba(76, 77, 230, 0.3);
}

.search-result-card :deep(.ant-card-head) {
  min-height: auto;
  padding: 8px 12px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
}

.search-result-card :deep(.ant-card-head-title) {
  padding: 0;
}

.result-title {
  font-size: 15px;
  font-weight: 600;
  color: #4c4de6;
}

.search-result-card :deep(.ant-card-body) {
  padding: 12px;
}

.result-description {
  font-size: 13px;
  color: #4b5563;
  line-height: 1.6;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.custom-pagination :deep(.ant-pagination-item-active) {
  border-color: #4c4de6;
}

.custom-pagination :deep(.ant-pagination-item-active a) {
  color: #4c4de6;
}

/* 内部弹窗样式 */
.inner-modal {
  border-radius: 16px;
  overflow: hidden;
}

.inner-modal :deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 12px 48px rgba(76, 77, 230, 0.15);
  border: 1px solid rgba(76, 77, 230, 0.1);
  max-height: 85vh;
}

.inner-modal :deep(.ant-modal-header) {
  background: transparent;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  padding: 12px 16px;
}

.inner-modal :deep(.ant-modal-title) {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
}

.inner-modal :deep(.ant-modal-body) {
  padding: 16px !important;
  max-height: calc(85vh - 110px);
  overflow-y: auto;
}

.inner-modal :deep(.ant-modal-close) {
  color: #4c4de6;
}

.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.aspect-w-16 > iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.video-info {
  padding: 12px;
  background: rgba(246, 248, 252, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(229, 231, 235, 0.6);
  font-size: 13px;
  color: #4b5563;
}

.preview-description {
  line-height: 1.6;
  background: rgba(246, 248, 252, 0.5);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(229, 231, 235, 0.6);
  font-size: 13px;
}

.action-button {
  background: linear-gradient(90deg, #4c4de6, #5971e6);
  border-color: #4c4de6;
  border-radius: 8px;
  height: 40px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.15);
  transition: all 0.3s;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(76, 77, 230, 0.25);
  background: linear-gradient(90deg, #5758e8, #6a80e8);
  border-color: #5758e8;
}

.empty-state {
  margin: 40px 0;
}

.empty-state :deep(.ant-empty-image) {
  opacity: 0.7;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgba(76, 77, 230, 0.15);
  border-radius: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
  
  .ai-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .resource-search {
    padding: 10px;
  }
  
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }
  
  .ai-results-grid {
    grid-template-columns: 1fr;
  }
  
  .video-card :deep(.ant-card-meta-title) {
    font-size: 12px;
  }
  
  .filter-container {
    width: 100%;
    margin-top: 8px;
  }
  
  .inner-modal {
    width: 90% !important;
  }
}

@media (max-width: 576px) {
  .resource-modal {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 8px;
  }
  
  .resource-modal :deep(.ant-modal-header) {
    padding: 12px;
  }
  
  .resource-search {
    padding: 8px;
  }
  
  .search-section {
    padding: 8px;
  }
  
  .search-btn {
    height: 36px;
  }
  
  .search-input :deep(.ant-input) {
    height: 36px;
  }
  
  .inner-modal {
    width: 95% !important;
  }
}
</style>

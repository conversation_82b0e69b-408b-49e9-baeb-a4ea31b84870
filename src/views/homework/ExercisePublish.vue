<template>
  <div class="assessment-page">
    <div class="assessment-header">
      <h1>习题发布</h1>
      <p class="subtitle">创建和发布习题给您的班级学生</p>
    </div>

    <div class="assessment-content">

    <a-spin :spinning="loading">
      <!-- 班级选择 -->
      <div class="class-selector">
        <a-select
          v-model:value="selectedClassId"
          style="width: 300px;"
          placeholder="选择班级"
          @change="handleClassChange"
        >
          <a-select-option v-for="item in classList" :key="item.id" :value="item.id">
            {{ item.class_name }}
          </a-select-option>
        </a-select>
        <a-button type="primary" @click="refreshData">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>

      <!-- 无班级提示 -->
      <div v-if="!hasClass" class="empty-container">
        <a-empty description="暂无班级">
          <template #description>
            <p>您还没有创建任何班级</p>
          </template>
          <a-button type="primary" @click="goToClassManagement">
            前往班级管理
          </a-button>
        </a-empty>
      </div>

      <!-- 习题管理内容 -->
      <div v-else-if="selectedClassId" class="content-container">
        <exercise-management :class-id="selectedClassId" />
      </div>
    </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import axios from '@/axios'
import { ReloadOutlined } from '@ant-design/icons-vue'
import ExerciseManagement from '@/views/classroom/components/ExerciseManagement.vue'

const router = useRouter()
const loading = ref(false)
const hasClass = ref(false)
const classList = ref([])
const selectedClassId = ref(null)

// 获取教师的班级列表
const fetchTeacherClasses = async () => {
  try {
    loading.value = true
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.system_teacher_id) {
      throw new Error('未找到教师信息')
    }

    const response = await axios.get(`/api/classes/teacher/${userInfo.system_teacher_id}`)
    if (response.data.success) {
      classList.value = response.data.data
      hasClass.value = response.data.data.length > 0

      if (hasClass.value) {
        // 获取上次选中的班级ID
        const lastSelectedClassId = localStorage.getItem('lastSelectedClassId')
        let targetClass

        if (lastSelectedClassId) {
          targetClass = response.data.data.find(c => c.id === parseInt(lastSelectedClassId))
        }

        // 如果找不到上次选中的班级，则使用第一个班级
        if (!targetClass) {
          targetClass = response.data.data[0]
        }

        selectedClassId.value = targetClass.id
      }
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    message.error('获取班级列表失败')
  } finally {
    loading.value = false
  }
}

const handleClassChange = (classId) => {
  selectedClassId.value = classId
  localStorage.setItem('lastSelectedClassId', classId)
}

const refreshData = () => {
  fetchTeacherClasses()
}

const goToClassManagement = () => {
  router.push('/dashboard/class-interaction')
}

onMounted(() => {
  fetchTeacherClasses()
})
</script>

<style scoped>
/* 使用统一的样式类名，样式已在assessment.css中定义 */
.class-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

.empty-container, .content-container {
  background: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  min-height: 400px;
}

@media (max-width: 768px) {
  .class-selector {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .class-selector .ant-select {
    width: 100% !important;
  }
}
/* 重写Ant Design主题色 */
:deep(.ant-btn-primary) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff !important;
}

:deep(.ant-btn-primary:hover),
:deep(.ant-btn-primary:focus) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  color: #ffffff !important;
}

:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-select-selector:hover),
:deep(.ant-select-open .ant-select-selector) {
  border-color: var(--primary-color) !important;
}

:deep(.ant-select-item-option-selected:not(.ant-select-item-option-disabled)) {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

:deep(.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}
</style>

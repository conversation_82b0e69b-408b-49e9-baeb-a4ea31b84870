<script setup lang="ts">
import { ref, onMounted } from 'vue'


const isLoading = ref(false)
const analysisData = ref({
  averageScore: 0,
  passRate: 0,
  excellentRate: 0,
  distribution: []
})

// 初始化数据
onMounted(() => {
  // TODO: 从API获取数据
})
</script>

<template>
  <div class="assessment-page">
    <div class="assessment-header">
      <h1>成绩分析</h1>
      <p class="subtitle">分析学生的成绩数据，帮助教师了解学生学习情况</p>
    </div>

    <div class="assessment-content">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-title">平均分</div>
          <div class="stat-value">{{ analysisData.averageScore }}</div>
        </div>

        <div class="stat-card">
          <div class="stat-title">及格率</div>
          <div class="stat-value">{{ analysisData.passRate }}%</div>
        </div>

        <div class="stat-card">
          <div class="stat-title">优秀率</div>
          <div class="stat-value">{{ analysisData.excellentRate }}%</div>
        </div>
      </div>

      <div class="assessment-card">
        <div class="assessment-card-header">
          <h3>分数分布</h3>
        </div>
        <div class="assessment-card-content">
          <div class="chart-container">
            <p class="text-center">图表加载中...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 使用统一的样式类名，样式已在assessment.css中定义 */
.text-center {
  text-align: center;
}
</style>
<template>
  <div class="dashboard" :class="{ 'first-load': isFirstLoad }">
    <!-- 你好这是测试-->
    <a-layout>
      <!-- 头部 -->
      <a-layout-header class="header">
        <div class="logo">
          <img
            :src="avatarUrl"
            :alt="teacherInfo.name"
            class="avatar-img"
            @error="handleAvatarError"
          >
          <span class="teacher-name">{{ teacherInfo.name }}</span>
        </div>
        <div class="header-right">
          <a-dropdown>
            <a class="user-info" @click.prevent>
              <span>{{ teacherInfo.system_teacher_id || '未登录' }}</span>
              <down-outlined />
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile" @click="goToProfile">
                  <user-outlined />
                  个人信息
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <logout-outlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 主布局 -->
      <a-layout class="main-layout">
        <!-- 侧边栏 -->
        <a-layout-sider
          :width="siderWidth"
          :collapsed="collapsed"
          :collapsed-width="80"
          theme="light"
          class="sider"
        >
          <div class="sider-content" :class="{ 'collapsed': collapsed }">
            <!-- 系统标志 -->
            <div class="system-logo" :class="{ 'collapsed': collapsed }">
              <div class="logo-icon-wrapper" @click="toggleCollapse">
                <read-outlined class="logo-icon" />
              </div>
              <span class="logo-text" v-show="!collapsed">智教助手</span>
              <!-- 恢复折叠/展开按钮 -->
              <div class="top-collapse-button" @click="toggleCollapse" v-if="!collapsed">
                <menu-fold-outlined />
              </div>
            </div>

            <a-menu
              v-model:selectedKeys="selectedKeys"
              v-model:openKeys="openKeys"
              mode="inline"
              theme="light"
              class="sidebar-menu"
              :inline-collapsed="collapsed"
              @click="handleMenuClick"
              @openChange="handleOpenChange"
            >
              <a-menu-item key="workstation">
                <template #icon><desktop-outlined /></template>
                <span>工作台</span>
              </a-menu-item>

              <a-menu-item key="class-interaction">
                <template #icon><team-outlined /></template>
                <span>教学班管理</span>
              </a-menu-item>

              <a-sub-menu key="preparation">
                <template #icon><book-outlined /></template>
                <template #title>备课管理</template>
                <a-menu-item key="lesson-plan">教案制作</a-menu-item>
                <a-menu-item key="ppt-maker">PPT制作</a-menu-item>
                <a-menu-item key="exercise-maker">习题生成</a-menu-item>
                <a-menu-item key="note-generator">笔记生成</a-menu-item>
                <a-menu-item key="preview-maker">预习资料</a-menu-item>
                <a-menu-item key="animation-maker">动画制作</a-menu-item>
              </a-sub-menu>

              <a-sub-menu key="homework">
                <template #icon><file-outlined /></template>
                <template #title>作业与成绩</template>
                <a-menu-item key="exercise-publish">习题发布</a-menu-item>
                <a-menu-item key="preview-management">预习管理</a-menu-item>
                <a-menu-item key="grade-analysis" @click="$router.push('/assessment/grade-analysis')">成绩分析</a-menu-item>
                <a-menu-item key="preview-analysis" @click="$router.push('/assessment/preview-analysis')">预习分析</a-menu-item>
                <a-menu-item key="plagiarism-analysis">查重分析</a-menu-item>
              </a-sub-menu>

              <a-menu-item key="resource-center">
                <template #icon><folder-outlined /></template>
                <span>资源中心</span>
              </a-menu-item>
            </a-menu>
          </div>
        </a-layout-sider>

        <!-- 主内容区 -->
        <a-layout-content :class="['content', {'content-expanded': collapsed}]">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  DownOutlined,
  LogoutOutlined,
  UserOutlined,
  DesktopOutlined,
  BookOutlined,
  TeamOutlined,
  FileOutlined,
  FolderOutlined,
  ReadOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import axios from '@/utils/axios'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const selectedKeys = ref([])
const openKeys = ref([])
const showProfileModal = ref(false)
const siderWidth = 220
const collapsed = ref(false)
const isFirstLoad = ref(true)
const isMobileView = ref(false)

const teacherInfo = ref({
  avatar: '/default-avatar.png',
  name: '未登录',
  system_teacher_id: '',
  school_teacher_id: '',
  subject: '',
  phone: ''
})
const profileForm = ref({
  name: '',
  system_teacher_id: '',
  school_teacher_id: '',
  phone: '',
  subject: ''
})

const formatPhone = (phone) => {
  if (!phone) return '未设置'
  return `${phone.slice(0, 3)}****${phone.slice(-4)}`
}

const handleMenuClick = async ({ key }) => {
  try {
    // 直接跳转到相应路由
    const targetPath = `/dashboard/${key}`
    if (route.path === targetPath) {
      return
    }
    await router.push(targetPath)
  } catch (error) {
    console.error('导航失败:', error)
    message.error('页面跳转失败，请稍后重试')
  }
}

const handleLogout = () => {
  userStore.clearUser()
  router.push('/login')
}

const handleOpenChange = (keys) => {
  // 移除自动展开侧边栏的逻辑
  openKeys.value = keys;
}

const setSelectedKeys = () => {
  const path = route.path
  const pathParts = path.split('/')
  const key = pathParts[pathParts.length - 1]
  selectedKeys.value = [key]
}

const fetchTeacherInfo = async () => {
  try {
    const userInfo = userStore.user;
    if (!userInfo || !userInfo.id) {
      userStore.initializeFromStorage();

      // 再次检查用户信息
      const refreshedUserInfo = userStore.user;
      if (!refreshedUserInfo || !refreshedUserInfo.id) {
        message.error('用户信息获取失败，请重新登录');
        router.push('/login');
        return;
      }

      teacherInfo.value = refreshedUserInfo;
    } else {
      teacherInfo.value = userInfo;
    }

    // 更新表单信息
    profileForm.value = {
      name: teacherInfo.value.name || '',
      system_teacher_id: teacherInfo.value.system_teacher_id || '',
      school_teacher_id: teacherInfo.value.school_teacher_id || '',
      phone: teacherInfo.value.phone || '',
      subject: teacherInfo.value.subject || ''
    };
  } catch (error) {
    console.error('获取教师信息失败:', error);
    message.error('获取教师信息失败，请重试');
  }
};

const handleProfileUpdate = async () => {
  try {
    const response = await axios.put('/api/auth/profile', {
      id: teacherInfo.value.id,
      ...profileForm.value
    })

    if (response.data.success) {
      message.success('个人信息更新成功')
      showProfileModal.value = false
      userStore.setUser(response.data.data)
      await fetchTeacherInfo()
    } else {
      message.error(response.data.message || '更新失败')
    }
  } catch (error) {
    message.error('更新失败，请稍后重试')
  }
}

const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}

// 检测是否为移动设备的函数
const checkMobileView = () => {
  isMobileView.value = window.innerWidth <= 768
  if (isMobileView.value && !collapsed.value) {
    collapsed.value = true
  }
}

// 监听窗口大小变化
const setupResizeListener = () => {
  checkMobileView() // 初始检查
  window.addEventListener('resize', checkMobileView)
}

// 移除事件监听器
const cleanupResizeListener = () => {
  window.removeEventListener('resize', checkMobileView)
}

const goToProfile = () => {
  router.push('/dashboard/profile')
}

// 处理头像加载错误
const handleAvatarError = (e) => {
  e.target.src = '/avatar/avatar-0001.jpg'
}

// 计算头像URL
const avatarUrl = computed(() => {
  if (teacherInfo.value && teacherInfo.value.avatar) {
    // 检查头像URL是否是绝对URL
    if (teacherInfo.value.avatar.startsWith('http')) {
      return teacherInfo.value.avatar;
    }

    // 如果路径已经是以/开头，则直接使用
    if (teacherInfo.value.avatar.startsWith('/')) {
      // 移除可能存在的/public前缀
      return teacherInfo.value.avatar.replace(/^\/public/, '');
    } else {
      // 没有/开头的路径，加上/
      return `/${teacherInfo.value.avatar}`;
    }
  }
  // 默认头像
  return '/avatar/avatar-0001.jpg';
})

// 设置组件挂载和卸载的钩子函数
onMounted(() => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    message.error('请先登录')
    router.push('/login')
    return
  }

  // 初始化数据
  Promise.all([
    fetchTeacherInfo(),
    setSelectedKeys()
  ]).then(() => {
    // 设置resize监听器
    setupResizeListener()

    // 500ms后移除首次加载标志，启用动画
    setTimeout(() => {
      isFirstLoad.value = false
    }, 500)
  })

  // 如果当前在 dashboard 根路径，重定向到工作台
  if (route.path === '/dashboard') {
    router.push('/dashboard/workstation')
  }
})

// 组件卸载时清理
onUnmounted(() => {
  cleanupResizeListener()
})
</script>

<style scoped>
/* 基础布局样式 */
.dashboard {
  min-height: 100vh;
  background: #f5f7fa;
}

.dashboard.first-load .ant-menu-submenu-title,
.dashboard.first-load .ant-menu-item {
  transition: none !important;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background: #fff;
  height: 64px;
  line-height: 64px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.main-layout {
  min-height: calc(100vh - 64px);
  margin-top: 64px;
  display: flex;
}

/* 固定侧边栏样式 */
.sider {
  position: fixed;
  left: 0;
  height: calc(100vh - 64px);
  overflow-y: hidden; /* 修改为hidden，避免收缩时可能出现的滚动条闪烁 */
  overflow-x: hidden;
  z-index: 100;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  transition: width 0.3s cubic-bezier(0.2, 0, 0, 1); /* 使用更流畅的贝塞尔曲线 */
}

/* 侧边栏内容容器，添加过渡效果 */
.sider-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1);
}

/* 主内容区样式 */
.content {
  margin-left: 220px;
  padding: 0px;
  background: #fff;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
  flex: 1;
  transition: margin-left 0.3s cubic-bezier(0.2, 0, 0, 1); /* 匹配侧边栏过渡 */
}

/* 滚动条美化 */
.sider::-webkit-scrollbar,
.content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.sider::-webkit-scrollbar-thumb,
.content::-webkit-scrollbar-thumb {
  background: #d0d1f8;
  border-radius: 10px;
}

.sider::-webkit-scrollbar-track,
.content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 10px;
}

/* 头部样式 */
.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.avatar-img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(76, 77, 230, 0.15);
  transition: all 0.3s ease;
}

.avatar-img:hover {
  border-color: rgba(76, 77, 230, 0.4);
  transform: scale(1.05);
}

.teacher-name {
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  color: #4c4de6;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(76, 77, 230, 0.08);
}

/* 侧边栏菜单 */
.sidebar-menu {
  border-right: none !important;
  margin-top: 20px; /* 增加顶部边距，补偿移除头像后的空间 */
  flex: 1;
  overflow-y: auto; /* 允许菜单滚动 */
  overflow-x: hidden;
}

/* 侧边栏菜单滚动条 */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: #d0d1f8;
  border-radius: 10px;
}

/* 菜单样式优化 */
:deep(.ant-menu-item) {
  margin: 4px 16px !important;
  padding-left: 16px !important;
  border-radius: 6px;
  font-weight: 500;
  height: 44px;
  line-height: 44px;
  transition: all 0.2s ease;
}

:deep(.ant-menu-submenu-title) {
  margin: 4px 16px !important;
  padding-left: 16px !important;
  border-radius: 6px;
  font-weight: 500;
}

:deep(.ant-menu-item-selected) {
  background-color: rgba(76, 77, 230, 0.1) !important;
  color: #4c4de6 !important;
  font-weight: 600;
}

:deep(.ant-menu-item:hover),
:deep(.ant-menu-submenu-title:hover) {
  color: #4c4de6 !important;
  background-color: rgba(76, 77, 230, 0.05) !important;
}

:deep(.ant-menu-item::after) {
  border-right: 3px solid #4c4de6 !important;
  border-radius: 0 !important;
}

:deep(.ant-menu-submenu-selected) {
  color: #4c4de6 !important;
}

:deep(.ant-menu-submenu-arrow) {
  color: inherit !important;
}

:deep(.ant-menu-inline .ant-menu-item),
:deep(.ant-menu-inline .ant-menu-submenu-title) {
  width: calc(100% - 32px);
}

:deep(.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow) {
  right: 16px;
}

/* 弹出菜单样式 */
:deep(.ant-menu-submenu-popup) {
  box-shadow: 0 3px 12px rgba(76, 77, 230, 0.15);
  border-radius: 8px;
  overflow: hidden;
}

:deep(.ant-menu-submenu-popup .ant-menu-sub) {
  border-radius: 8px;
}

:deep(.ant-menu-submenu-popup .ant-menu-item) {
  margin: 0 !important;
  padding-left: 16px !important;
  border-radius: 0;
}

:deep(.ant-menu-submenu-popup .ant-menu-item:hover) {
  background-color: rgba(76, 77, 230, 0.05) !important;
}

:deep(.ant-menu-submenu-popup .ant-menu-item-selected) {
  background-color: rgba(76, 77, 230, 0.1) !important;
  color: #4c4de6 !important;
}

/* 路由过渡动画 */
.fade-enter-active {
  transition: opacity 0.3s ease;
}

.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 添加内容切换动画 */
@keyframes content-fade-in {
  from { opacity: 0.7; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 系统标志样式 */
.system-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.system-logo.collapsed {
  justify-content: center;
  padding: 16px 0;
}

.logo-icon-wrapper {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #4c4de6 0%, #7476ff 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(76, 77, 230, 0.2);
  cursor: pointer;
}

.logo-icon {
  font-size: 20px;
  color: white;
}

.system-logo.collapsed .logo-icon-wrapper {
  width: 40px;
  height: 40px;
}

.logo-icon-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.3);
}

.logo-text {
  color: #333;
  font-size: 16px;
  font-weight: 600;
  opacity: 1;
  transition: opacity 0.2s ease;
}

/* 折叠按钮样式 */
.top-collapse-button {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(76, 77, 230, 0.08);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #4c4de6;
  z-index: 10;
}

.top-collapse-button:hover {
  background: rgba(76, 77, 230, 0.15);
  transform: translateY(-50%) scale(1.1);
}

/* 调整折叠状态下按钮的位置 */
.system-logo.collapsed .top-collapse-button {
  display: none;
}

/* 折叠状态菜单项的样式 */
:deep(.ant-menu-inline-collapsed) {
  width: 80px !important;
}

:deep(.ant-menu-inline-collapsed .ant-menu-item),
:deep(.ant-menu-inline-collapsed .ant-menu-submenu-title) {
  padding: 0 !important;
  text-align: center;
  margin: 4px 0 !important;
  height: 44px !important;
  line-height: 44px !important;
  width: 80px !important;
}

/* 隐藏折叠状态下的子菜单箭头 */
:deep(.ant-menu-inline-collapsed .ant-menu-submenu-arrow) {
  display: none !important;
}

/* 确保折叠状态下的文本不显示 */
:deep(.ant-menu-inline-collapsed) .ant-menu-title-content {
  display: none !important;
}

/* 修复展开菜单的箭头 */
:deep(.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow) {
  right: 16px !important;
}

/* 内容区响应式调整 */
.content-expanded {
  margin-left: 80px;
}

/* 为小屏幕优化 */
@media (max-width: 768px) {
  .sider {
    position: absolute;
    z-index: 1000;
    box-shadow: 3px 0 12px rgba(0, 0, 0, 0.1);
  }

  .content {
    margin-left: 0 !important;
  }
}
</style>
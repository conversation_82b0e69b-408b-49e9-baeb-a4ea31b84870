.maker-container {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
}

.sidebar {
  width: 280px;
  background: white;
  padding: 16px;
  border-right: 1px solid #edf2f7;
  display: flex;
  flex-direction: column;
  box-shadow: 1px 0 2px rgba(0, 0, 0, 0.03);
}

.course-select {
  margin-bottom: 16px;
}

.lesson-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.lesson-list .ant-spin-nested-loading {
  height: 100%;
}

.lesson-list .ant-spin-container {
  height: 100%;
  overflow-y: auto;
}

.lesson-list .ant-menu-item {
  margin: 0;
  padding: 12px 16px;
}

.lesson-list .ant-menu-item:hover {
  background: #f0f7ff;
}

.lesson-list .ant-menu-item-selected {
  background: #e6f4ff;
}

.maker-workspace {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  background: #f8fafc;
}

.input-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-content {
  padding: 24px 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 28px;
  color: #1e293b;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 12px;
}

.form-header {
  margin-bottom: 28px;
}

.label {
  font-size: 15px;
  color: #475569;
  margin-bottom: 10px;
  font-weight: 500;
}

.exercise-type-section {
  display: flex;
  gap: 48px;
  align-items: center;
}

.exercise-phase-buttons {
  display: flex;
  gap: 12px;
}

.exercise-phase-buttons .ant-btn {
  min-width: 100px;
  height: 36px;
  border-radius: 6px;
}

.form-row {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  margin-bottom: 20px;
}

.form-item {
  flex: 1;
}

.form-section {
  margin-bottom: 28px;
}

.form-item-label {
  margin-bottom: 8px;
  color: #4b5563;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.form-actions .ant-btn {
  min-width: 140px;
  height: 40px;
  font-size: 16px;
  border-radius: 8px;
}

.exercise-list {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.exercise-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.exercise-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.exercise-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.exercise-title {
  font-weight: 600;
  color: #334155;
  font-size: 15px;
  flex: 1;
  margin-right: 16px;
}

.exercise-tags {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.options {
  margin: 12px 0;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
}

.option {
  margin: 8px 0;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  color: #475569;
}

.exercise-footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
}

.answer, .analysis {
  margin: 8px 0;
  line-height: 1.6;
}

.answer strong, .analysis strong {
  color: #475569;
  margin-right: 8px;
}

.analysis {
  color: #64748b;
}

.placeholder {
  text-align: center;
  color: #94a3b8;
  font-size: 15px;
  padding: 40px 0;
}

.lesson-menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.chapter-title {
  font-weight: 600;
  color: #334155;
}

.lesson-count {
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
}

/* Modal styles */
.batch-form {
  padding: 12px;
}

.batch-form-header {
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.batch-form-header h4 {
  margin: 8px 0;
  color: #1e293b;
  font-size: 14px;
}

.question-type-item {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.question-type-header {
  margin-bottom: 12px;
}

.question-type-name {
  font-weight: 500;
  color: #1e293b;
}

.question-type-content {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.question-count, .question-difficulty {
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-label {
  width: 40px;
  color: #4b5563;
  font-size: 13px;
}

.batch-summary {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.total-count {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.count-value {
  color: #1890ff;
  font-weight: 600;
  margin: 0 5px;
}

.count-warning {
  color: #ff4d4f;
  font-size: 13px;
}

div.form-section .knowledge-point-section {
  margin-top: 16px;
}

div.knowledge-point-section .selector-header {
  margin-bottom: 12px;
}

div.knowledge-point-section .batch-info {
  margin-top: 8px;
  opacity: 0.8;
}

div.form-item-content .help-text {
  margin-top: 8px;
  font-size: 12px;
}

div.batch-info {
  margin: 16px 0;
}

div.no-batch-info {
  margin: 16px 0;
}

div.selected-knowledge-point {
  margin-top: 16px;
}

div.selected-knowledge-point .ant-tag {
  font-size: 14px;
  padding: 6px 10px;
}

.knowledge-point-info {
  margin-top: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  color: #64748b;
  font-weight: 500;
}

.info-value {
  color: #94a3b8;
  font-style: italic;
}

/* 自定义"去发布习题"按钮样式 */
.publish-button {
  background-color: #1890ff !important; /* 蓝色背景 */
  border-color: #1890ff !important;
  color: white !important;
}
.publish-button:hover {
  background-color: #40a9ff !important; /* 悬停时的浅蓝色 */
  border-color: #40a9ff !important;
}

@media (max-width: 768px) {
  .maker-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    padding: 12px;
  }

  .maker-workspace {
    height: 0;
    flex: 1;
    padding: 16px;
    gap: 16px;
  }

  .form-content {
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .form-item {
    width: 100%;
  }
  
  .question-type-content {
    flex-direction: column;
    gap: 16px;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-actions .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .selected-knowledge-point {
    margin-top: 8px;
  }
}

<template>
  <div class="preview-maker-container">
    <div class="page-header">
      <h1>预习资料生成</h1>
      <p class="description">为课程章节创建智能预习资料</p>
    </div>
    
    <div class="main-content">
      <!-- 左侧课时目录 -->
      <div class="sidebar">
        <div class="course-select">
          <a-select
            v-model:value="selectedCourse"
            placeholder="请选择课程"
            style="width: 100%"
            @change="handleCourseChange"
            :loading="loading"
          >
            <a-select-option 
              v-for="course in coursesList" 
              :key="course.id" 
              :value="course.id"
            >
              {{ course.course_name }}
            </a-select-option>
          </a-select>
        </div>
        
        <div class="lesson-list">
          <a-spin :spinning="loading">
            <a-menu
              v-model:selectedKeys="selectedKeys"
              mode="inline"
              :style="{ height: '100%', borderRight: 0 }"
            >
              <template v-if="lessonList.length > 0">
                <a-menu-item 
                  v-for="lesson in lessonList" 
                  :key="lesson.id"
                  @click="selectLesson(lesson)"
                >
                  <div class="lesson-menu-item">
                    <span class="lesson-title" :class="{ 'chapter-title': lesson.isChapter }">
                      {{ lesson.title }}
                    </span>
                    <span class="lesson-count" v-if="lesson.content.length > 0">
                      {{ lesson.content.length }}个小节
                    </span>
                  </div>
                </a-menu-item>
              </template>
              <a-empty v-else description="暂无课时" />
            </a-menu>
          </a-spin>
        </div>
      </div>

      <!-- 右侧预习资料生成区域 -->
      <div class="maker-workspace">
        <div class="input-section">
          <div class="form-content">
            <h3 class="section-title">{{ currentLesson?.title || '请选择课时' }}</h3>
            
            <div class="form-actions">
              <a-button 
                type="primary" 
                :loading="generating"
                @click="generatePreviewMaterial"
                size="large"
              >
                {{ generating ? '生成中...' : '生成预习资料' }}
              </a-button>
              
              <a-button 
                type="default" 
                size="large"
                @click="goToPublishPage"
                class="publish-button"
              >
                <template #icon><send-outlined /></template>
                去发布预习
              </a-button>
            </div>
          </div>
        </div>

        <!-- 预习资料展示 -->
        <div class="preview-material" v-if="previewContent">
          <div class="preview-material-header">
            <h3>预习资料</h3>
            <div class="preview-material-actions">
              <a-button type="primary" @click="downloadPreviewMaterial">
                下载资料
              </a-button>
            </div>
          </div>
          
          <div class="preview-material-content">
            <div class="preview-material-title">
              {{ currentLesson?.title }} - 预习资料
              <span v-if="expectedDuration" class="reading-time">预计阅读时间: {{ expectedDuration }}分钟</span>
            </div>
            <div class="preview-material-body" v-html="renderMathFormula(previewContent)"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from '@/utils/axios'
import { message } from 'ant-design-vue'
import katex from 'katex'
import 'katex/dist/katex.min.css'
import { useRouter } from 'vue-router'
import { SendOutlined } from '@ant-design/icons-vue'

// 状态定义
const router = useRouter()
const selectedCourse = ref('')
const selectedKeys = ref([])
const currentLesson = ref(null)
const generating = ref(false)
const previewContent = ref(null)
const expectedDuration = ref(null)
const loading = ref(false)

const coursesList = ref([])
const lessonList = ref([])

// 方法定义
const handleCourseChange = async (courseId) => {
  try {
    loading.value = true
    // 获取课程大纲和课时列表
    const response = await axios.get(`/api/syllabus/${courseId}`)
    if (response.data.success) {
      const syllabusData = response.data.data
      if (syllabusData && syllabusData.content) {
        // 将大纲内容转换为列表形式
        lessonList.value = convertContentToList(syllabusData.content)
        
        // 如果有课时，自动选择第一个非章节的课时
        const firstLesson = lessonList.value.find(lesson => !lesson.isChapter)
        if (firstLesson) {
          await selectLesson(firstLesson)
          selectedKeys.value = [firstLesson.id]
        }
      } else {
        lessonList.value = []
        previewContent.value = null
        message.info('该课程暂无大纲内容')
      }
    }
  } catch (error) {
    console.error('获取课时列表失败:', error)
    message.error('获取课时列表失败')
    lessonList.value = []
    previewContent.value = null
  } finally {
    loading.value = false
  }
}

// 将大纲内容转换为列表形式
const convertContentToList = (content) => {
  const list = []
  
  const processNode = (node, parentTitle = '') => {
    Object.entries(node).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        // 如果是章节标题，添加到列表
        list.push({
          id: `chapter_${Object.values(value)[0]}`, // 使用第一个子节点的ID
          title: key,
          content: Object.keys(value),
          isChapter: true
        })
        // 递归处理子节点
        processNode(value, key)
      } else {
        // 如果是具体内容项，添加到列表
        list.push({
          id: `section_${value}`, // 使用content中的数字作为ID
          title: key,
          content: [],
          isChapter: false
        })
      }
    })
  }
  
  processNode(content)
  return list
}

// 获取教师课程列表
const fetchCoursesList = async () => {
  try {
    loading.value = true
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.system_teacher_id) {
      throw new Error('未找到教师信息')
    }

    const response = await axios.get(`/api/courses/teacher/${userInfo.system_teacher_id}`)
    if (response.data.success) {
      coursesList.value = response.data.data.map(course => ({
        id: course.course_code,
        course_name: course.course_name
      }))
      
      // 如果有课程，自动选择第一个
      if (coursesList.value.length > 0) {
        selectedCourse.value = coursesList.value[0].id
        await handleCourseChange(selectedCourse.value)
      }
    }
  } catch (error) {
    console.error('获取课程列表失败:', error)
    message.error('获取课程列表失败')
  } finally {
    loading.value = false
  }
}

const selectLesson = async (lesson) => {
  currentLesson.value = lesson
  
  // 如果是章节标题，不获取预习资料
  if (lesson.isChapter) {
    previewContent.value = null
    return
  }

  // 从课时标题中获取 titleId
  const titleId = lesson.id.split('_')[1]
  
  // 获取预习资料
  await fetchPreviewMaterial()
}

const fetchPreviewMaterial = async () => {
  if (!currentLesson.value || !selectedCourse.value) return
  
  try {
    loading.value = true
    const titleId = currentLesson.value.id.split('_')[1]
    
    const response = await axios.get(
      `/api/previews/section/${selectedCourse.value}/${titleId}`
    )
    
    if (response.data.success && response.data.data) {
      // 直接存储content，或者从data.content中获取
      if (typeof response.data.data === 'string') {
        previewContent.value = response.data.data
        // 提取预计阅读时间
        const durationMatch = previewContent.value.match(/## 预计阅读时间\s*\n*([0-9]+)/);
        expectedDuration.value = durationMatch && durationMatch[1] ? parseInt(durationMatch[1], 10) : null;
      } else {
        previewContent.value = response.data.data.content
        expectedDuration.value = response.data.data.expected_duration || null
      }
    } else {
      previewContent.value = null
      expectedDuration.value = null
    }
  } catch (error) {
    console.error('获取预习资料失败:', error)
    message.error('获取预习资料失败')
    previewContent.value = null
    expectedDuration.value = null
  } finally {
    loading.value = false
  }
}

const generatePreviewMaterial = async () => {
  if (!selectedCourse.value || !currentLesson.value) {
    message.warning('请先选择课程和课时')
    return
  }

  if (currentLesson.value.isChapter) {
    message.warning('请选择具体的小节而不是章节')
    return
  }

  generating.value = true
  try {
    const response = await axios.post('/api/previews/generate', {
      courseCode: selectedCourse.value,
      titleId: currentLesson.value.id.split('_')[1], // 只取数字部分
      chapterTitle: currentLesson.value.title
    }, {
      timeout: 120000 // 增加超时时间到2分钟
    })

    if (response.data.success) {
      message.success('预习资料生成成功')
      previewContent.value = response.data.data
      
      // 提取预计阅读时间
      const durationMatch = previewContent.value.match(/## 预计阅读时间\s*\n*([0-9]+)/);
      if (durationMatch && durationMatch[1]) {
        expectedDuration.value = parseInt(durationMatch[1], 10);
      } else {
        expectedDuration.value = null;
      }
    }
  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      message.error('生成预习资料超时，请稍后重试')
    } else {
      console.error('生成预习资料失败:', error)
      message.error('生成预习资料失败，请重试')
    }
  } finally {
    generating.value = false
  }
}

const downloadPreviewMaterial = () => {
  if (!previewContent.value) {
    message.warning('没有可下载的预习资料')
    return
  }
  
  const fileName = `${currentLesson.value.title}-预习资料.txt`
  const blob = new Blob([previewContent.value], { type: 'text/plain;charset=utf-8' })
  
  // 创建下载链接
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  link.click()
  
  // 释放URL对象
  URL.revokeObjectURL(link.href)
}

// 渲染数学公式的函数
const renderMathFormula = (text) => {
  if (!text) return '';
  
  // 如果文本中包含LaTeX公式（\( \) 或 $ $）
  if (text.includes('\\(') || text.includes('$')) {
    try {
      // 替换 \( ... \) 或 $ ... $ 格式的数学公式
      return text.replace(/\\\((.*?)\\\)|\$(.*?)\$/g, (match, group1, group2) => {
        const formula = group1 || group2;
        try {
          return katex.renderToString(formula, { throwOnError: false });
        } catch (e) {
          console.error('KaTeX渲染错误:', e);
          return match; // 如果渲染错误，返回原始文本
        }
      });
    } catch (e) {
      console.error('公式替换错误:', e);
      return text;
    }
  }
  
  return text;
};

// 跳转到预习发布页面
const goToPublishPage = () => {
  router.push('/dashboard/preview-management');
}

// 组件挂载时获取课程列表
onMounted(() => {
  fetchCoursesList()
})
</script>

<style scoped>
.preview-maker-container {
  padding: 24px;
  max-width: 1800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.page-header .description {
  color: #666;
  font-size: 14px;
}

.main-content {
  display: flex;
  gap: 24px;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.sidebar {
  width: 280px;
  background: white;
  padding: 20px;
  border-right: 1px solid #edf2f7;
  display: flex;
  flex-direction: column;
  box-shadow: 1px 0 2px rgba(0, 0, 0, 0.03);
}

.course-select {
  margin-bottom: 16px;
}

.course-select :deep(.ant-select-focused .ant-select-selector) {
  border-color: #4c4de6 !important;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}

.course-select :deep(.ant-select-item-option-selected:not(.ant-select-item-option-disabled)) {
  background-color: rgba(76, 77, 230, 0.1);
  color: #4c4de6;
}

.course-select :deep(.ant-select-item-option-active:not(.ant-select-item-option-disabled)) {
  background-color: rgba(76, 77, 230, 0.05);
}

.lesson-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

.lesson-list :deep(.ant-spin-nested-loading) {
  height: 100%;
}

.lesson-list :deep(.ant-spin-container) {
  height: 100%;
  overflow-y: auto;
}

.lesson-list :deep(.ant-menu-item) {
  margin: 4px 0;
  padding: 10px 16px;
  border-radius: 6px;
}

.lesson-list :deep(.ant-menu-item:hover) {
  background: rgba(76, 77, 230, 0.05);
}

.lesson-list :deep(.ant-menu-item-selected) {
  background: rgba(76, 77, 230, 0.1) !important;
  color: #4c4de6 !important;
}

.lesson-list :deep(.ant-menu-item-selected::after) {
  border-right: 3px solid #4c4de6 !important;
}

.maker-workspace {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  background: #f8fafc;
}

.input-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-content {
  padding: 24px 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 28px;
  color: #1e293b;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 12px;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 36px;
}

.form-actions .ant-btn {
  min-width: 140px;
  height: 40px;
  font-size: 16px;
  border-radius: 8px;
  background-color: #4c4de6;
  border-color: #4c4de6;
}

.form-actions .ant-btn:hover,
.form-actions .ant-btn:focus {
  background-color: #6364e9;
  border-color: #6364e9;
}

/* 自定义"去发布预习"按钮样式 */
.publish-button {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: white !important;
  margin-left: 16px;
}
.publish-button:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

.preview-material {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.preview-material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.preview-material-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.preview-material-actions .ant-btn {
  background-color: #4c4de6;
  border-color: #4c4de6;
}

.preview-material-actions .ant-btn:hover,
.preview-material-actions .ant-btn:focus {
  background-color: #6364e9;
  border-color: #6364e9;
}

.preview-material-content {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.preview-material-title {
  font-weight: 600;
  color: #334155;
  font-size: 18px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 12px;
}

.preview-material-body {
  font-size: 15px;
  line-height: 1.8;
  color: #475569;
  white-space: pre-wrap;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 10px;
}

.preview-material-body::-webkit-scrollbar {
  width: 6px;
}

.preview-material-body::-webkit-scrollbar-thumb {
  background: #d0d1f8;
  border-radius: 10px;
}

.preview-material-body::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 10px;
}

.lesson-menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 8px;
  gap: 8px;
}

.lesson-title {
  font-weight: 500;
  color: #1f2937;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.lesson-count {
  font-size: 12px;
  color: #6b7280;
  flex-shrink: 0;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 10px;
}

.chapter-title {
  color: #4c4de6;
  font-weight: 600;
}

.reading-time {
  font-size: 12px;
  color: #6b7280;
  margin-left: 16px;
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    padding: 16px;
  }

  .maker-workspace {
    padding: 16px;
    gap: 16px;
  }

  .form-content {
    padding: 20px;
  }
  
  .preview-material-content {
    padding: 16px;
  }
  
  .preview-material-body {
    max-height: 300px;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .publish-button {
    margin-left: 0;
    margin-top: 16px;
  }
}

/* 使用统一的样式类名，样式已在assessment.css中定义 */
.preview-management {
  padding: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-content,
.submission-content {
}
</style>

<template>
  <div class="error-knowledge-selector">
    <div class="selector-header">
      <div class="title">
        <a-tooltip title="基于上一批次学生作答数据，显示错误率最高的知识点">
          <info-circle-outlined class="info-icon" />
        </a-tooltip>
        <span>优先知识点</span>
        <a-tag color="orange">可选</a-tag>
        <a-checkbox 
          v-model:checked="enableKnowledgePoint" 
          @change="handleEnableChange"
        >
          启用
        </a-checkbox>
      </div>
      <a-button 
        type="link" 
        size="small" 
        :loading="loading"
        :disabled="!enableKnowledgePoint"
        @click="refreshKnowledgePoints"
      >
        <template #icon><reload-outlined /></template>
        刷新
      </a-button>
    </div>
    
    <div class="selector-content">
      <a-select
        v-model:value="selectedPoint"
        style="width: 100%"
        placeholder="选择错误率较高的知识点（可选）"
        :loading="loading"
        :disabled="disabled || loading || !enableKnowledgePoint"
        @change="handlePointChange"
        allowClear
        optionLabelProp="label"
      >
        <a-select-option 
          v-for="point in knowledgePoints" 
          :key="point.name" 
          :value="point.name"
          :label="point.name"
        >
          <div class="point-option">
            <span class="point-name">{{ point.name }}</span>
            <a-tag :color="getErrorRateColor(point.errorRate)">
              错误率: {{ point.errorRate }}%
            </a-tag>
          </div>
        </a-select-option>
        
        <template v-if="knowledgePoints.length === 0 && !loading">
          <a-empty description="暂无错误知识点数据" />
        </template>
      </a-select>
      
      <div class="batch-info" v-if="latestBatch && enableKnowledgePoint">
        <a-tag color="blue">批次 {{ latestBatch }}</a-tag>
        <span class="batch-desc">数据来源于最新一批次</span>
      </div>
      
      <div class="selector-hint" v-if="!selectedPoint">
        <a-alert type="info" show-icon>
          <template #message>不选择知识点将生成常规课后题，选择知识点可生成针对性习题</template>
        </a-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import axios from '@/axios';
import { InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  classId: {
    type: [String, Number],
    default: null
  },
  courseCode: {
    type: String,
    default: null
  },
  titleId: {
    type: [String, Number],
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  },
  exerciseType: {
    type: String,
    default: '2' // 默认为课后题
  }
});

const emit = defineEmits(['update:selectedKnowledgePoint', 'hasBatchData']);

// 状态
const loading = ref(false);
const knowledgePoints = ref([]);
const selectedPoint = ref(null);
const latestBatch = ref(null);
const enableKnowledgePoint = ref(false);

// 计算属性
const isReady = computed(() => {
  return props.classId && props.titleId && props.courseCode && props.exerciseType === '2';
});

// 获取错误知识点数据
const fetchErrorKnowledgePoints = async () => {
  if (!isReady.value || !props.classId || !enableKnowledgePoint.value) {
    return;
  }
  
  try {
    loading.value = true;
    const response = await axios.get(
      `/api/exercises/error-knowledge-points/${props.classId}/${props.titleId}/${props.courseCode}`,
      { validateStatus: status => (status >= 200 && status < 300) || status === 404 }
    );
    
    if (response?.data?.success) {
      const data = response.data.data || {};
      knowledgePoints.value = data.knowledgePoints || [];
      latestBatch.value = data.latestBatch;
      
      // 如果有知识点且未选择，自动选择第一个
      if (knowledgePoints.value.length > 0 && !selectedPoint.value) {
        selectedPoint.value = knowledgePoints.value[0].name;
        handlePointChange(selectedPoint.value);
      }
      
      // 通知父组件批次数据状态
      emit('hasBatchData', !!data.latestBatch);
    } else {
      // 不显示警告消息，只在控制台记录
      console.log('未找到知识点数据或获取失败');
      knowledgePoints.value = [];
      latestBatch.value = null;
      
      // 通知父组件批次数据状态
      emit('hasBatchData', false);
    }
  } catch (error) {
    console.error('获取错误知识点失败:', error);
    // 不显示错误消息，静默处理
    knowledgePoints.value = [];
    latestBatch.value = null;
    
    // 通知父组件批次数据状态
    emit('hasBatchData', false);
  } finally {
    loading.value = false;
  }
};

// 处理知识点选择变更
const handlePointChange = (value) => {
  // 允许为null，表示不选择知识点
  emit('update:selectedKnowledgePoint', value);
};

// 处理启用/禁用勾选框变更
const handleEnableChange = (checked) => {
  if (!checked) {
    // 如果取消勾选，清空已选择的知识点
    selectedPoint.value = null;
    emit('update:selectedKnowledgePoint', null);
  } else {
    // 如果勾选，自动刷新数据
    fetchErrorKnowledgePoints();
  }
};

// 刷新知识点数据
const refreshKnowledgePoints = () => {
  fetchErrorKnowledgePoints();
};

// 根据错误率获取颜色
const getErrorRateColor = (rate) => {
  if (rate >= 80) return '#f5222d';  // 红色
  if (rate >= 60) return '#fa8c16';  // 橙色
  if (rate >= 40) return '#faad14';  // 黄色
  if (rate >= 20) return '#52c41a';  // 绿色
  return '#1890ff';  // 蓝色
};

// 监听属性变化
watch(
  [() => props.classId, () => props.titleId, () => props.courseCode, () => props.exerciseType],
  () => {
    // 只有选择了课后题才获取错误知识点
    if (props.exerciseType === '2' && enableKnowledgePoint.value) {
      fetchErrorKnowledgePoints();
    } else {
      knowledgePoints.value = [];
      selectedPoint.value = null;
      latestBatch.value = null;
      emit('update:selectedKnowledgePoint', null);
    }
  }
);

// 组件挂载时初始化
onMounted(() => {
  if (isReady.value && enableKnowledgePoint.value) {
    fetchErrorKnowledgePoints();
  }
});
</script>

<style scoped>
.error-knowledge-selector {
  margin-bottom: 20px;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.selector-header .title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: #475569;
}

.info-icon {
  color: #64748b;
}

.point-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.point-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  font-size: 12px;
}

.batch-desc {
  color: #64748b;
}

.selector-hint {
  margin-top: 12px;
}

.selector-hint .ant-alert {
  padding: 6px 10px;
  font-size: 12px;
}

.not-found-content {
  padding: 8px;
  font-size: 12px;
  color: #666;
  text-align: center;
}
</style> 
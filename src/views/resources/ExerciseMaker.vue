<template>
  <div class="maker-container">
    <!-- 左侧课时目录 -->
    <div class="sidebar">
      <div class="course-select">
        <a-select
          v-model:value="selectedCourse"
          placeholder="请选择课程"
          style="width: 100%"
          @change="handleCourseChange"
          :loading="loading"
        >
          <a-select-option 
            v-for="course in coursesList" 
            :key="course.id" 
            :value="course.id"
          >
            {{ course.course_name }}
          </a-select-option>
        </a-select>
      </div>
      
      <div class="lesson-list">
        <a-spin :spinning="loading">
          <a-menu
            v-model:selectedKeys="selectedKeys"
            mode="inline"
            :style="{ height: '100%', borderRight: 0 }"
          >
            <template v-if="lessonList.length > 0">
              <a-menu-item 
                v-for="lesson in lessonList" 
                :key="lesson.id"
                @click="selectLesson(lesson)"
              >
                <div class="lesson-menu-item">
                  <span class="lesson-title" :class="{ 'chapter-title': lesson.isChapter }">
                    {{ lesson.title }}
                  </span>
                  <span class="lesson-count" v-if="lesson.content.length > 0">
                    {{ lesson.content.length }}个小节
                  </span>
                </div>
              </a-menu-item>
            </template>
            <a-empty v-else description="暂无课时" />
          </a-menu>
        </a-spin>
      </div>
    </div>

    <!-- 右侧习题生成区域 -->
    <div class="maker-workspace">
      <div class="input-section">
        <div class="form-content">
          <h3 class="section-title">{{ currentLesson?.title || '请选择课时' }}</h3>
          
          <div class="form-header">
            <div class="label">选择题目类型</div>
            <div class="exercise-type-section">
              <div class="exercise-phase-buttons">
                <a-button 
                  :type="form.exerciseType === '1' ? 'primary' : 'default'"
                  @click="form.exerciseType = '1'"
                >
                  预习题
                </a-button>
                <a-button 
                  :type="form.exerciseType === '2' ? 'primary' : 'default'"
                  @click="form.exerciseType = '2'"
                >
                  课后题
                </a-button>
              </div>
            </div>
          </div>

          <!-- 针对课后题增加班级选择和错误知识点选择 -->
          <div v-if="form.exerciseType === '2'" class="form-section">
            <div class="label">选择班级</div>
            <div class="form-item-content">
              <a-select
                v-model:value="selectedClass"
                style="width: 100%"
                placeholder="选择班级"
                :loading="classLoading"
                @change="handleClassChange"
              >
                <a-select-option 
                  v-for="classItem in classList" 
                  :key="classItem.id" 
                  :value="classItem.id"
                >
                  {{ classItem.class_name }}
                  <a-tag color="blue" size="small">{{ classItem.semester }}</a-tag>
                </a-select-option>
                
                <template v-if="classList.length === 0 && !classLoading">
                  <a-empty description="该课程下暂无班级" />
                </template>
              </a-select>
              
              <div class="help-text" v-if="classList.length === 0 && !classLoading">
                <a-alert type="info" show-icon>
                  <template #message>
                    需要班级数据才能获取错误知识点。请先创建班级并发布课后题。
                  </template>
                </a-alert>
              </div>
            </div>
            
            <!-- 当前批次信息 -->
            <div class="batch-info" v-if="currentBatch && selectedClass">
              <a-alert type="info" show-icon>
                <template #message>
                  当前最新批次: <a-tag color="blue">第{{ currentBatch.id }}批</a-tag>
                  <span v-if="currentBatch.batch_name"> ({{ currentBatch.batch_name }})</span>
                </template>
                <template #description>
                  系统将根据该批次的学生作答数据，分析错误率最高的知识点
                </template>
              </a-alert>
            </div>
            
            <!-- 错误知识点选择器 -->
            <div class="knowledge-point-section" v-if="selectedClass && currentLesson && currentBatch">
              <ErrorKnowledgePointSelector
                :classId="selectedClass"
                :courseCode="selectedCourse"
                :titleId="currentLesson?.id?.split('_')[1]"
                :exerciseType="form.exerciseType"
                v-model:selectedKnowledgePoint="selectedKnowledgePoint"
                :disabled="!selectedClass || !currentLesson"
                @update:selectedKnowledgePoint="handleKnowledgePointChange"
                @hasBatchData="handleBatchDataStatus"
              />
            </div>
          </div>

          <div class="form-actions">
            <a-button 
              type="primary" 
              size="large"
              @click="showBatchModal"
              :disabled="!currentLesson || currentLesson.isChapter"
            >
              <template #icon><thunderbolt-outlined /></template>
              批量生成习题
            </a-button>
            
            <a-button 
              type="default" 
              size="large"
              @click="goToPublishPage"
              :disabled="!selectedCourse"
              class="publish-button"
            >
              <template #icon><send-outlined /></template>
              去发布习题
            </a-button>
            
            <!-- 知识点提示 -->
            <div class="selected-knowledge-point" v-if="selectedKnowledgePoint && form.exerciseType === '2'">
              <a-tag color="gold">
                <template #icon><aim-outlined /></template>
                将生成与知识点 "{{ selectedKnowledgePoint }}" 相关的习题
              </a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 习题列表展示 -->
      <div class="exercise-list">
        <div class="list-header">
          <h3>习题列表</h3>
          <div class="list-actions">
            <!-- 删除查看统计按钮 -->
          </div>
        </div>
        
        <a-spin :spinning="loading || generating">
          <div class="exercise-items">
            <div v-for="exercise in exercises" 
                 :key="exercise.id" 
                 class="exercise-item"
            >
              <div class="exercise-header">
                <div class="exercise-title" v-html="renderMathFormula(exercise.title)"></div>
                <div class="exercise-tags">
                  <a-tag :color="getDifficultyColor(exercise.difficulty)">
                    {{ getDifficultyText(exercise.difficulty) }}
                  </a-tag>
                  <a-tag :color="getTypeColor(exercise.type)">
                    {{ getTypeText(exercise.type) }}
                  </a-tag>
                  <a-tag :color="getQuestionTypeColor(exercise.questionType)">
                    {{ getQuestionTypeText(exercise.questionType) }}
                  </a-tag>
                </div>
              </div>
              
              <!-- 选择题选项 -->
              <div v-if="exercise.questionType === 'choice' && exercise.options && Object.keys(exercise.options).length > 0" 
                   class="options"
              >
                <div v-for="(option, key) in exercise.options" 
                     :key="key" 
                     class="option"
                     v-html="renderMathFormula(`${key}. ${option}`)"
                >
                </div>
              </div>
              
              <div class="exercise-footer">
                <div class="answer">
                  <strong>答案：</strong><span v-html="renderMathFormula(exercise.answer)"></span>
                </div>
                <div class="analysis">
                  <strong>解析：</strong><span v-html="renderMathFormula(exercise.analysis)"></span>
                </div>
                <div class="point" v-if="exercise.point">
                  <strong>知识点：</strong><span v-html="renderMathFormula(exercise.point)"></span>
                </div>
              </div>
            </div>
            
            <a-empty v-if="!loading && exercises.length === 0" 
                    description="暂无习题" 
            />
          </div>
        </a-spin>
      </div>
    </div>

    <!-- 批量生成弹窗 -->
    <a-modal
      v-model:visible="batchModalVisible"
      title="批量生成习题"
      width="480px"
      @ok="handleBatchGenerate"
      :confirmLoading="generating"
      okText="开始生成"
      cancelText="取消"
    >
      <div class="batch-form">
        <div class="batch-form-header">
          <h4>当前章节: {{ currentLesson?.title || '未选择' }}</h4>
          <h4>题目类型: {{ form.exerciseType === '1' ? '预习题' : '课后题' }}</h4>
          <div v-if="form.exerciseType === '2'" class="knowledge-point-info">
            <template v-if="selectedKnowledgePoint">
              <span class="info-label">知识点:</span>
              <a-tag color="gold">{{ selectedKnowledgePoint }}</a-tag>
            </template>
            <template v-else>
              <span class="info-label">知识点:</span>
              <span class="info-value">未选择(将生成常规课后题)</span>
            </template>
          </div>
        </div>
        
        <div class="question-type-item" v-for="(type, index) in questionTypes" :key="index">
          <div class="question-type-header">
            <span class="question-type-name">{{ type.label }}</span>
          </div>
          <div class="question-type-content">
            <div class="question-count">
              <span class="question-label">数量:</span>
              <a-input-number 
                v-model:value="type.count" 
                :min="0" 
                :max="10"
                placeholder="数量" 
                size="small"
              />
            </div>
            <div class="question-difficulty">
              <span class="question-label">难度:</span>
              <a-rate v-model:value="type.difficulty" />
            </div>
          </div>
        </div>
        
        <div class="batch-summary">
          <div class="total-count">
            总题目数: <span class="count-value">{{ getTotalQuestionCount() }}</span>
            <span class="count-warning" v-if="getTotalQuestionCount() > 30">(不能超过30题)</span>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import axios from '@/axios'
import { message } from 'ant-design-vue'
import katex from 'katex'
import 'katex/dist/katex.min.css'
import ErrorKnowledgePointSelector from './components/ErrorKnowledgePointSelector.vue'
import { ThunderboltOutlined, AimOutlined, SendOutlined } from '@ant-design/icons-vue'
import './ExerciseMaker.css'
import { useRouter } from 'vue-router'

// 状态定义
const router = useRouter()
const selectedCourse = ref('')
const selectedKeys = ref([])
const currentLesson = ref(null)
const generating = ref(false)
const previewContent = ref(null)
const isMobile = ref(false)
const loading = ref(false)
const batchModalVisible = ref(false)
const selectedClass = ref(null)
const classList = ref([])
const selectedKnowledgePoint = ref(null)
const classLoading = ref(false)
const currentBatch = ref(null) // 当前批次
const latestErrorPoints = ref([]) // 最新的错误知识点数据
const errorMessage = ref(null)

const form = ref({
  exerciseType: '1', // 默认为预习题
  titleId: ''
})

const coursesList = ref([])
const lessonList = ref([])
const exercises = ref([])

// 批量生成相关数据
const questionTypes = ref([
  { value: 'choice', label: '选择题', count: 0, difficulty: 3 },
  { value: 'completion', label: '填空题', count: 0, difficulty: 3 },
  { value: 'judgment', label: '判断题', count: 0, difficulty: 3 },
  { value: 'short-answer', label: '简答题', count: 0, difficulty: 3 },
  { value: 'calculation', label: '计算题', count: 0, difficulty: 3 }
])

// 获取当前选择的题目总数
const getTotalQuestionCount = () => {
  return questionTypes.value.reduce((sum, type) => sum + type.count, 0)
}

// 方法定义
const handleCourseChange = async (courseId) => {
  try {
    loading.value = true
    // 获取课程大纲和课时列表
    const response = await axios.get(`/api/syllabus/${courseId}`)
    if (response.data.success) {
      const syllabusData = response.data.data
      if (syllabusData && syllabusData.content) {
        // 将大纲内容转换为列表形式
        lessonList.value = convertContentToList(syllabusData.content)
        
        // 如果有课时，自动选择第一个非章节的课时
        const firstLesson = lessonList.value.find(lesson => !lesson.isChapter)
        if (firstLesson) {
          await selectLesson(firstLesson)
          selectedKeys.value = [firstLesson.id]
        }
        
        // 课程变更时，获取班级列表
        fetchClassesByCourse(courseId)
      } else {
        lessonList.value = []
        exercises.value = []
        message.info('该课程暂无大纲内容')
      }
    }
  } catch (error) {
    console.error('获取课时列表失败:', error)
    if (error.response?.status === 403) {
      return
    }
    message.error('获取课时列表失败')
    lessonList.value = []
    exercises.value = []
  } finally {
    loading.value = false
  }
}

// 将大纲内容转换为列表形式
const convertContentToList = (content) => {
  const list = []
  
  const processNode = (node, parentTitle = '') => {
    Object.entries(node).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        // 如果是章节标题，添加到列表
        list.push({
          id: `chapter_${Object.values(value)[0]}`, // 使用第一个子节点的ID
          title: key,
          content: Object.keys(value),
          isChapter: true
        })
        // 递归处理子节点
        processNode(value, key)
      } else {
        // 如果是具体内容项，添加到列表
        list.push({
          id: `section_${value}`, // 使用content中的数字作为ID
          title: key,
          content: [],
          isChapter: false
        })
      }
    })
  }
  
  processNode(content)
  return list
}

// 获取教师课程列表
const fetchCoursesList = async () => {
  try {
    loading.value = true
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.system_teacher_id) {
      throw new Error('未找到教师信息')
    }

    const response = await axios.get(`/api/courses/teacher/${userInfo.system_teacher_id}`)
    if (response.data.success) {
      coursesList.value = response.data.data.map(course => ({
        id: course.course_code,
        course_name: course.course_name
      }))
      
      // 如果有课程，自动选择第一个
      if (coursesList.value.length > 0) {
        selectedCourse.value = coursesList.value[0].id
        await handleCourseChange(selectedCourse.value)
      }
    }
  } catch (error) {
    console.error('获取课程列表失败:', error)
    if (error.response?.status === 403) {
      // 如果是403错误，可能是token过期，等待重定向到登录页
      return
    }
    message.error('获取课程列表失败')
  } finally {
    loading.value = false
  }
}

// 选择课时/章节
const selectLesson = async (lesson) => {
  if (lesson.isChapter) return // 如果是章节标题，不做处理
  
  // 更新当前选中的课时
  currentLesson.value = lesson
  
  // 获取该课时的习题列表
  try {
    loading.value = true
    
    // 更新表单中的标题ID
    form.value.titleId = lesson.id.split('_')[1]
  
  // 获取习题列表
    await fetchExercisesList()
    
    // 获取习题数据和统计信息
    await fetchExerciseStats()
    
    // 如果已选择班级，获取批次信息
    if (selectedClass.value && form.value.exerciseType === '2') {
      await fetchLatestBatchInfo()
    }
  } catch (error) {
    console.error('获取习题数据失败:', error)
    if (error.response?.status === 403) {
      return
    }
    message.error('获取习题数据失败')
  } finally {
    loading.value = false
  }
}

// 显示批量生成弹窗
const showBatchModal = () => {
  if (!selectedCourse.value || !currentLesson.value) {
    message.warning('请先选择课程和课时')
    return
  }

  if (currentLesson.value.isChapter) {
    message.warning('请选择具体的小节而不是章节')
    return
  }
  
  // 重置各题型的数量
  questionTypes.value.forEach(type => {
    type.count = 0
    type.difficulty = 3
  })
  
  batchModalVisible.value = true
}

// 处理批量生成
const handleBatchGenerate = async () => {
  // 检查是否有选择题目
  const totalCount = getTotalQuestionCount()
  if (totalCount <= 0) {
    message.warning('请至少选择一种题型并设置数量')
    return
  }
  
  // 验证总数量不超过限制
  if (totalCount > 30) {
    message.warning('批量生成题目总数不能超过30题')
    return
  }
  
  // 先关闭弹窗再设置loading状态
  batchModalVisible.value = false
  // 清空之前的内容，以便显示加载状态
  exercises.value = []
  
  try {
    // 设置loading状态
    generating.value = true
    
    // 为每种题型生成习题
    const allGeneratedExercises = []
    
    for (const type of questionTypes.value) {
      if (type.count > 0) {
        // 生成API参数
        const requestParams = {
          courseCode: selectedCourse.value,
          titleId: currentLesson.value.id.split('_')[1],
          exerciseType: form.value.exerciseType,
          difficulty: type.difficulty,
          count: type.count,
          questionType: type.value
        };
        
        // 如果是课后题且选择了知识点，添加到请求中
        if (form.value.exerciseType === '2' && selectedKnowledgePoint.value) {
          requestParams.knowledgePoint = selectedKnowledgePoint.value;
          // 记录日志，确保知识点正确传递
          console.log(`包含知识点生成习题: "${selectedKnowledgePoint.value}"`);
        }
        
        // 调用API生成该类型的习题
        const response = await axios.post('/api/exercises/generate', requestParams, {
          timeout: 120000 // 2分钟超时
        })
        
        if (response.data.success) {
          allGeneratedExercises.push(...response.data.data)
        }
      }
    }
    
    // 更新习题列表
    exercises.value = allGeneratedExercises
    
    // 显示成功消息
    if (allGeneratedExercises.length > 0) {
      if (selectedKnowledgePoint.value && form.value.exerciseType === '2') {
        message.success(`成功生成${allGeneratedExercises.length}道关于"${selectedKnowledgePoint.value}"的习题`);
      } else {
        message.success(`成功生成${allGeneratedExercises.length}道习题`);
      }
    } else {
      message.warning('未能生成任何习题')
    }
    
    // 自动获取最新的习题列表
    await fetchExerciseStats()
  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      message.error('生成习题超时，但习题可能已经保存。正在刷新列表...')
      // 尝试刷新习题列表
      await fetchExerciseStats()
    } else {
      console.error('生成习题失败:', error)
      message.error('生成习题失败，请重试')
    }
  } finally {
    generating.value = false
  }
}

const fetchExerciseStats = async () => {
  if (!currentLesson.value || !selectedCourse.value) return

  try {
    const titleId = currentLesson.value.id.split('_')[1] // 只取数字部分
    // 获取统计数据，但不再显示统计弹窗
    await axios.get(`/api/exercises/stats/${selectedCourse.value}/${titleId}`)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const calculateCompletionRate = (statsData) => {
  if (!statsData || statsData.length === 0) return 0
  const totalQuestions = statsData.reduce((sum, item) => sum + item.count, 0)
  return Math.round((totalQuestions / (getTotalQuestionCount() * 3)) * 100) // 3种类型
}

const calculateAccuracyRate = (statsData) => {
  return Math.round(Math.random() * 30 + 70) // 70-100之间的随机数
}

// 检查移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 组件挂载时获取课程列表
onMounted(() => {
  fetchCoursesList()
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 辅助函数
function getDifficultyColor(difficulty) {
  const colors = {
    1: 'success',
    2: 'success',
    3: 'warning',
    4: 'warning',
    5: 'error'
  };
  return colors[difficulty] || 'default';
}

function getDifficultyText(difficulty) {
  const texts = {
    1: '简单',
    2: '较简单',
    3: '中等',
    4: '较难',
    5: '困难'
  };
  return texts[difficulty] || '未知';
}

function getTypeColor(type) {
  const colors = {
    '1': 'blue',    // 预习题
    '2': 'purple'   // 课后题
  };
  return colors[type] || 'default';
}

function getTypeText(type) {
  const texts = {
    '1': '预习题',
    '2': '课后题'
  };
  return texts[type] || '未知';
}

function getQuestionTypeText(type) {
  // 先将字符串类型转换为数字
  const typeToNumber = {
    'choice': 1,
    'completion': 2,
    'judgment': 3,
    'short-answer': 4,
    'calculation': 5
  };

  const actualType = typeof type === 'string' ? typeToNumber[type] : type;
  
  const texts = {
    1: '选择题',
    2: '填空题',
    3: '判断题',
    4: '简答题',
    5: '计算题'
  };
  return texts[actualType] || '未知';
}

function getQuestionTypeColor(type) {
  // 先将字符串类型转换为数字
  const typeToNumber = {
    'choice': 1,
    'completion': 2,
    'judgment': 3,
    'short-answer': 4,
    'calculation': 5
  };

  const actualType = typeof type === 'string' ? typeToNumber[type] : type;
  
  const colors = {
    1: 'cyan',    // 选择题
    2: 'blue',    // 填空题
    3: 'green',   // 判断题
    4: 'orange',  // 简答题
    5: 'purple'   // 计算题
  };
  return colors[actualType] || 'default';
}

// 渲染数学公式的函数
const renderMathFormula = (text) => {
  if (!text) return '';
  
  // 如果文本中包含LaTeX公式（\( \) 或 $ $）
  if (text.includes('\\(') || text.includes('$')) {
    try {
      // 替换 \( ... \) 或 $ ... $ 格式的数学公式
      return text.replace(/\\\((.*?)\\\)|\$(.*?)\$/g, (match, group1, group2) => {
        const formula = group1 || group2;
        try {
          return katex.renderToString(formula, { throwOnError: false });
        } catch (e) {
          console.error('KaTeX渲染错误:', e);
          return match; // 如果渲染错误，返回原始文本
        }
      });
    } catch (e) {
      console.error('公式替换错误:', e);
      return text;
    }
  }
  
  return text;
};

// 获取课程下的班级列表
const fetchClassesByCourse = async (courseCode) => {
  if (!courseCode) return;
  
  try {
    classLoading.value = true;
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.system_teacher_id) {
      throw new Error('未找到教师信息');
    }
    
    // 修改为使用正确的API路径获取教师班级列表
    const response = await axios.get(`/api/classes/teacher`);
    
    if (response.data.success) {
      // 过滤出该课程下的班级
      const allClasses = response.data.data || [];
      classList.value = allClasses.filter(c => c.course_code === courseCode);
      
      console.log(`找到${classList.value.length}个班级`, classList.value);
      
      // 如果之前选择的班级不在新列表中，重置选择
      if (selectedClass.value && 
          !classList.value.find(c => c.id === selectedClass.value)) {
        selectedClass.value = null;
        selectedKnowledgePoint.value = null;
      }
      
      // 如果有班级且未选择，自动选择第一个
      if (classList.value.length > 0 && !selectedClass.value) {
        selectedClass.value = classList.value[0].id;
      } else if (classList.value.length === 0) {
        message.info('该课程下没有班级，无法获取错误知识点数据');
      }
    } else {
      classList.value = [];
      selectedClass.value = null;
      message.warning(response.data.message || '获取班级列表失败');
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
    classList.value = [];
    selectedClass.value = null;
    message.error('获取班级列表失败');
  } finally {
    classLoading.value = false;
  }
}

// 处理班级变更
const handleClassChange = (classId) => {
  selectedClass.value = classId;
  // 班级变更后，获取最新批次信息
  fetchLatestBatchInfo();
  // 重置知识点选择
  selectedKnowledgePoint.value = null;
  // 重置错误信息
  errorMessage.value = null;
};

// 获取最新批次信息
const fetchLatestBatchInfo = async () => {
  if (!selectedClass.value || !currentLesson.value) return;
  
  try {
    const titleId = currentLesson.value.id.split('_')[1];
    
    // 修正API路径
    const response = await axios.get(
      `/api/analysis/class/${selectedClass.value}/chapter/${titleId}/batches`,
      { validateStatus: status => (status >= 200 && status < 300) || status === 404 }
    );
    
    // 无论是否404，只要返回了响应就算成功
    if (response.data.success && response.data.data && response.data.data.length > 0) {
      // 获取最新的批次
      const batches = response.data.data;
      const latestBatch = batches[batches.length - 1];
      currentBatch.value = latestBatch;
      
      console.log('获取到最新批次:', latestBatch);
      errorMessage.value = null;
    } else {
      currentBatch.value = null;
      console.log('该章节没有测验批次数据，继续允许生成习题');
      errorMessage.value = null; // 不显示任何错误信息
    }
  } catch (error) {
    console.error('获取批次信息失败:', error);
    currentBatch.value = null;
    errorMessage.value = null; // 不显示任何错误信息
    // 避免显示错误消息，因为这个功能是可选的
  }
};

// 获取习题列表
const fetchExercisesList = async () => {
  if (!selectedCourse.value || !form.value.titleId) return;

  try {
    const response = await axios.get(
      `/api/exercises/section/${selectedCourse.value}/${form.value.titleId}`
    );
    
    if (response.data.success) {
      exercises.value = response.data.data;
    } else {
      exercises.value = [];
      message.warning(response.data.message || '获取习题列表失败');
    }
  } catch (error) {
    console.error('获取习题列表失败:', error);
    exercises.value = [];
    if (error.response?.status !== 403) {
      message.error('获取习题列表失败');
    }
  }
};

// 处理知识点变更
const handleKnowledgePointChange = (point) => {
  if (point) {
    message.success(`已选择知识点: ${point}，系统将生成针对该知识点的习题`);
  }
};

// 处理批次数据状态
const handleBatchDataStatus = (hasBatchData) => {
  if (hasBatchData) {
    errorMessage.value = null;
  } else {
    errorMessage.value = null; // 不显示任何错误信息
  }
};

// 跳转到习题发布页面
const goToPublishPage = () => {
  // 获取当前已有班级
  const firstClassId = classList.value.length > 0 ? classList.value[0].id : null;
  
  // 使用正确的路由路径 /dashboard/exercise-publish
  router.push('/dashboard/exercise-publish');
};
</script>
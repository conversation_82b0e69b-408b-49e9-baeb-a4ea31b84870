<template>
  <div class="resource-center">
    <div class="page-header">
      <h1>资源中心</h1>
      <p class="description">搜索并管理您的教学资源</p>
    </div>

    <!-- 搜索框和筛选器 -->
    <div class="search-section mb-6 flex flex-col items-center">
      <div class="search-container w-full max-w-4xl flex items-center gap-16">
        <div class="search-input-group flex-1 flex items-center">
          <a-input
            v-model:value="searchQuery"
            placeholder="请输入要搜索的内容"
            class="flex-1"
            :disabled="isLoading"
            @pressEnter="handleSearch"
          />
          <a-button
            type="primary"
            :loading="isLoading"
            class="search-button"
            @click="handleSearch"
          >
            搜索
          </a-button>
        </div>

        <!-- 收藏夹按钮 -->
        <a-button
          type="primary"
          class="favorites-button"
          @click="goToFavorites"
        >
          <template #icon>
            <star-outlined />
          </template>
          收藏夹
        </a-button>
      </div>
    </div>

    <!-- 搜索结果区域 -->
    <div class="content-wrapper">
      <a-card class="search-results-container">
        <a-tabs>
          <a-tab-pane key="bilibili" tab="B站搜索结果">
            <!-- B站搜索结果 -->
            <div class="bilibili-results w-full">
              <div class="section-header mb-4 flex items-center">
                <div class="filters ml-4 flex gap-2">
                  <a-select
                    v-model:value="sortBy"
                    style="width: 120px"
                    size="small"
                    @change="handleFilterChange"
                  >
                    <a-select-option value="totalrank">综合排序</a-select-option>
                    <a-select-option value="click">播放最多</a-select-option>
                    <a-select-option value="pubdate">最新发布</a-select-option>
                  </a-select>

                  <a-select
                    v-model:value="duration"
                    style="width: 120px"
                    size="small"
                    @change="handleFilterChange"
                  >
                    <a-select-option value="0">全部时长</a-select-option>
                    <a-select-option value="1">10分钟以下</a-select-option>
                    <a-select-option value="2">10-30分钟</a-select-option>
                    <a-select-option value="3">30-60分钟</a-select-option>
                    <a-select-option value="4">60分钟以上</a-select-option>
                  </a-select>
                </div>
              </div>

              <!-- 加载状态 -->
              <div v-if="isLoading && !videos.length" class="loading-container">
                <a-spin>
                  <template #indicator>
                    <div class="loading-spinner">
                      <div class="double-bounce1"></div>
                      <div class="double-bounce2"></div>
                    </div>
                  </template>
                  <div class="mt-3 text-gray-600">正在搜索视频...</div>
                </a-spin>
              </div>

              <!-- 错误提示 -->
              <a-alert
                v-if="error"
                :message="error"
                type="error"
                class="mb-4"
                closable
              />

              <!-- 视频列表 -->
              <div v-if="!isLoading && videos.length > 0" class="video-grid">
                <a-card
                  v-for="video in videos"
                  :key="video.bvid"
                  hoverable
                  class="video-card"
                >
                  <template #cover>
                    <div class="video-thumbnail" @click="openVideo(video)">
                      <img
                        :src="getProcessedImageUrl(video.pic)"
                        class="w-full h-full object-cover"
                        :alt="video.title"
                        @error="handleImageError"
                        loading="lazy"
                        referrerpolicy="no-referrer"
                      />
                    </div>
                  </template>
                  <a-card-meta :title="video.title">
                    <template #description>
                      <div class="mt-2 text-sm text-gray-500">
                        <div class="flex items-center justify-between">
                          <span>UP主：{{ video.author }}</span>
                          <span>播放：{{ formatNumber(video.play) }}</span>
                        </div>
                        <div class="mt-1 flex items-center justify-between">
                          <span>时长：{{ video.duration }}</span>
                          <span>{{ video.pubdate }}</span>
                        </div>
                      </div>
                    </template>
                  </a-card-meta>
                </a-card>
              </div>

              <!-- 分页器 -->
              <div v-if="!isLoading && videos.length > 0" class="mt-6 flex justify-center">
                <a-pagination
                  v-model:current="currentPage"
                  :total="total"
                  :page-size="24"
                  size="small"
                  show-quick-jumper
                  @change="handlePageChange"
                />
              </div>

              <!-- 无结果提示 -->
              <a-empty
                v-if="!isLoading && videos.length === 0 && !error"
                description="暂无搜索结果"
              />
            </div>
          </a-tab-pane>

          <a-tab-pane key="ai" tab="AI全网搜索">
            <!-- AI全网搜索结果 -->
            <div class="ai-results w-full">
              <div class="section-header mb-4">
                <h3 class="text-lg font-medium flex items-center">
                  <span>AI全网搜索</span>
                  <a-tag class="ml-2" color="blue">Beta</a-tag>
                </h3>
              </div>

              <!-- AI搜索结果列表 -->
              <div class="ai-result-list">
                <!-- 加载状态 -->
                <a-skeleton active :loading="aiLoading" v-if="aiLoading">
                  <template #paragraph>
                    <div class="h-40"></div>
                  </template>
                </a-skeleton>

                <!-- 错误提示 -->
                <a-alert
                  v-if="aiError"
                  :message="aiError"
                  type="error"
                  class="mb-4"
                  closable
                />

                <!-- 搜索结果 -->
                <template v-if="!aiLoading && aiResults.length > 0">
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <a-card
                      v-for="(result, index) in aiResults"
                      :key="index"
                      class="search-result-card cursor-pointer"
                      @click="openAIPreview(result)"
                    >
                      <template #title>
                        <div class="text-blue-600 hover:text-blue-800 truncate">
                          {{ result.title }}
                        </div>
                      </template>
                      <template #extra>
                        <a-tag :color="getSourceColor(result.source)" class="source-tag">
                          {{ result.source }}
                        </a-tag>
                      </template>
                      <p class="text-sm text-gray-600 line-clamp-3">{{ result.description }}</p>
                      <div class="card-actions mt-3 flex justify-end">
                        <ArticleFavoriteButton :article="result" />
                      </div>
                    </a-card>
                  </div>
                </template>

                <!-- 无结果提示 -->
                <a-empty
                  v-if="!aiLoading && (!aiResults || aiResults.length === 0) && !aiError"
                  description="暂无搜索结果"
                />
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 视频播放弹窗 -->
    <a-modal
      v-model:visible="videoModalVisible"
      :title="currentVideo?.title"
      :footer="null"
      width="65%"
      centered
      destroyOnClose
      @cancel="handleCloseVideo"
      wrapClassName="video-modal"
    >
      <div class="video-container">
        <div class="aspect-w-16 aspect-h-9">
          <iframe
            v-if="videoModalVisible"
            :src="currentVideoUrl"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            class="w-full h-full"
          ></iframe>
        </div>

        <!-- 功能按钮区域 -->
        <div class="video-actions mt-5 flex items-center justify-center">
          <div class="action-buttons-container">
            <a-button
              v-if="currentVideo"
              type="primary"
              class="action-btn collect-btn"
              @click="handleVideoFavorite(currentVideo)"
            >
              <template #icon>
                <star-outlined />
              </template>
              收藏
            </a-button>

            <a-button
              type="primary"
              class="action-btn recommend-btn"
              @click="handleRecommendToStudents"
            >
              <template #icon>
                <share-alt-outlined />
              </template>
              推荐给学生
            </a-button>

            <a-button
              type="primary"
              class="action-btn summary-btn"
              @click="goToNoteGenerator"
            >
              <template #icon>
                <file-text-outlined />
              </template>
              总结笔记
            </a-button>
          </div>
        </div>

        <!-- 视频信息区域 -->
        <div class="video-info mt-3 text-gray-600 text-sm">
          <div class="flex items-center justify-between mb-1">
            <span>UP主：{{ currentVideo?.author }}</span>
            <span>播放量：{{ currentVideo ? formatNumber(currentVideo.play) : 0 }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span>时长：{{ currentVideo?.duration }}</span>
            <span>发布时间：{{ currentVideo?.pubdate }}</span>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- AI搜索结果预览弹窗 -->
    <a-modal
      v-model:visible="aiPreviewVisible"
      :title="currentAIResult?.title"
      :footer="null"
      width="65%"
      centered
      destroyOnClose
      @cancel="handleCloseAIPreview"
      wrapClassName="ai-preview-modal"
    >
      <div class="ai-preview-container">
        <div class="preview-content">
          <div class="mb-4">
            <a-tag :color="currentAIResult?.isTechArticle ? 'blue' : 'green'" class="mb-2">
              {{ currentAIResult?.source }}
            </a-tag>
            <p class="text-gray-600">{{ currentAIResult?.description }}</p>
          </div>

          <!-- 功能按钮区域 -->
          <div class="preview-actions mt-3 flex items-center justify-center gap-3">
            <ArticleFavoriteButton :article="currentAIResult" />

            <a-button type="primary" size="middle" class="action-btn">
              <template #icon>
                <share-alt-outlined />
              </template>
              推荐给学生
            </a-button>

            

            <a-button
              type="primary"
              size="middle"
              class="action-btn"
              @click="openOriginalLink"
            >
              <template #icon>
                <link-outlined />
              </template>
              查看原文
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 推荐给学生弹窗 -->
    <a-modal
      v-model:visible="recommendModalVisible"
      title="推荐给学生"
      :confirmLoading="recommendLoading"
      @ok="confirmRecommend"
      @cancel="cancelRecommend"
      okText="确认推荐"
      cancelText="取消"
      :maskClosable="false"
      width="500px"
    >
      <div class="recommend-form">
        <div class="form-item mb-4">
          <div class="form-label mb-2">选择班级</div>
          <a-select
            v-model:value="recommendForm.classId"
            placeholder="请选择班级"
            style="width: 100%"
            @change="handleClassChange"
            :loading="classesLoading"
          >
            <a-select-option v-for="cls in classes" :key="cls.id" :value="cls.id">
              {{ cls.class_name }}
            </a-select-option>
          </a-select>
        </div>

        <div class="form-item mb-4" v-if="recommendForm.classId">
          <div class="form-label mb-2">选择学生</div>
          <a-select
            v-model:value="recommendForm.studentIds"
            mode="multiple"
            placeholder="请选择学生(不选择则推荐给全班)"
            style="width: 100%"
            :loading="studentsLoading"
            :disabled="studentsLoading"
            allowClear
          >
            <a-select-option v-for="student in students" :key="student.id" :value="student.id">
              {{ student.name }}
            </a-select-option>
          </a-select>
          <div class="text-gray-500 text-xs mt-1">不选择学生将推荐给全班</div>
        </div>

        <div class="form-item mb-4">
          <div class="form-label mb-2">备注信息</div>
          <a-textarea
            v-model:value="recommendForm.remark"
            placeholder="添加备注信息，告诉学生为何推荐此视频(选填)"
            :rows="3"
          />
        </div>

        <div class="form-item">
          <a-checkbox v-model:checked="recommendForm.required">设为必看</a-checkbox>
          <div class="text-gray-500 text-xs mt-1">设为必看后，学生需要完成观看才能继续学习</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted, onMounted, watch } from 'vue'
import axios from 'axios'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
import {
  StarOutlined,
  ShareAltOutlined,
  FileTextOutlined,
  LinkOutlined
} from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { defineComponent, PropType } from 'vue'

interface AISearchResult {
  title: string;
  description: string;
  source: string;
  url: string;
  isTechArticle: boolean;
}

interface Video {
  bvid: string
  title: string
  pic: string
  author: string
  play: number
  duration: string
  pubdate: string
}

const searchQuery = ref('')
const videos = ref<Video[]>([])
const isLoading = ref(false)
const error = ref('')
const currentPage = ref(1)
const total = ref(0)
const sortBy = ref('totalrank')
const duration = ref('0')

const videoModalVisible = ref(false)
const currentVideo = ref<Video | null>(null)

// 本地存储相关键名
const STORAGE_KEY = {
  SEARCH_QUERY: 'resource_search_query',
  VIDEOS: 'resource_videos',
  TOTAL: 'resource_total',
  CURRENT_PAGE: 'resource_current_page',
  SORT_BY: 'resource_sort_by',
  DURATION: 'resource_duration',
  AI_RESULTS: 'resource_ai_results'
}

// 计算当前视频URL
const currentVideoUrl = computed(() => {
  if (!currentVideo.value?.bvid) return ''
  return `//player.bilibili.com/player.html?bvid=${currentVideo.value.bvid}&page=1&high_quality=1&danmaku=0`
})

// 获取当前用户ID
function getCurrentUserId() {
  try {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    return userInfo.id || 'guest';
  } catch (e) {
    console.error('获取用户ID失败:', e);
    return 'guest';
  }
}

// 带用户ID的存储键
function getUserStorageKey(key) {
  const userId = getCurrentUserId();
  return `${userId}_${key}`;
}

// 保存数据到本地存储
function saveToStorage(key, value) {
  const userKey = getUserStorageKey(key);
  if (typeof value === 'object') {
    localStorage.setItem(userKey, JSON.stringify(value));
  } else {
    localStorage.setItem(userKey, value);
  }
}

// 从本地存储获取数据
function getFromStorage(key) {
  const userKey = getUserStorageKey(key);
  return localStorage.getItem(userKey);
}

// 清除特定键的存储
function removeFromStorage(key) {
  const userKey = getUserStorageKey(key);
  localStorage.removeItem(userKey);
}

// 格式化数字
function formatNumber(num: number): string {
  if (!num && num !== 0) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toString();
}

// 打开视频
function openVideo(video: Video) {
  currentVideo.value = video
  videoModalVisible.value = true
}

// 关闭视频
function handleCloseVideo() {
  currentVideo.value = null
  videoModalVisible.value = false
}

// AI搜索相关
const aiResults = ref<AISearchResult[]>([])
const aiLoading = ref(false)
const aiError = ref('')
let eventSource: EventSource | null = null

// 处理 AI 搜索
async function handleAISearch(keyword) {
  aiLoading.value = true
  aiError.value = ''
  aiResults.value = []

  // 关闭之前的连接
  if (eventSource) {
    eventSource.close()
  }

  // 创建新的 EventSource 连接
  eventSource = new EventSource(`/api/ai-search/search?keyword=${encodeURIComponent(keyword)}`)

  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data)

    switch (data.type) {
      case 'result':
        // 添加新的搜索结果
        aiResults.value.push(data.data)
        // 每次收到新结果时保存到缓存
        saveToStorage(STORAGE_KEY.AI_RESULTS, aiResults.value)
        break

      case 'complete':
        // 搜索完成
        aiLoading.value = false
        eventSource!.close()
        break

      case 'error':
        aiError.value = data.message
        aiLoading.value = false
        eventSource!.close()
        break
    }
  }

  eventSource.onerror = () => {
    aiError.value = '搜索连接出错，请重试'
    aiLoading.value = false
    eventSource!.close()
  }
}

// 搜索视频
async function handleSearch() {
  if (!searchQuery.value.trim()) {
    return;
  }

  // 重置状态
  isLoading.value = true;
  aiLoading.value = true;
  error.value = '';
  aiError.value = '';
  videos.value = [];
  aiResults.value = [];
  currentPage.value = 1; // 重置为第一页

  try {
    // 保存搜索查询到本地存储
    saveToStorage(STORAGE_KEY.SEARCH_QUERY, searchQuery.value);
    saveToStorage(STORAGE_KEY.CURRENT_PAGE, '1');

    // 同时执行B站搜索和AI搜索
    handleBilibiliSearch();
    handleAISearch(searchQuery.value);
  } catch (err) {
    console.error('Search error:', err);
    error.value = '搜索失败，请稍后重试';
  }
}

// B站搜索
async function handleBilibiliSearch() {
  try {
    const response = await axios.get('/api/bilibili/search', {
      params: {
        keyword: searchQuery.value,
        page: currentPage.value,
        order: sortBy.value,
        duration: duration.value,
        pageSize: 24
      }
    });

    if (response.data.code === 0) {
      videos.value = response.data.data;
      total.value = response.data.total;

      // 保存搜索结果到本地存储
      saveToStorage(STORAGE_KEY.VIDEOS, videos.value);
      saveToStorage(STORAGE_KEY.TOTAL, total.value.toString());
      saveToStorage(STORAGE_KEY.CURRENT_PAGE, currentPage.value.toString());
      saveToStorage(STORAGE_KEY.SORT_BY, sortBy.value);
      saveToStorage(STORAGE_KEY.DURATION, duration.value);
    } else {
      error.value = response.data.message || '搜索失败';
    }
  } catch (err) {
    console.error('Bilibili search error:', err);
    error.value = '搜索失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
}

// 处理分页
function handlePageChange(page: number) {
  currentPage.value = page;
  saveToStorage(STORAGE_KEY.CURRENT_PAGE, page.toString());
  handleBilibiliSearch();
}

// 处理排序和时长变更
function handleFilterChange() {
  saveToStorage(STORAGE_KEY.SORT_BY, sortBy.value);
  saveToStorage(STORAGE_KEY.DURATION, duration.value);
  currentPage.value = 1; // 改变筛选条件时重置为第一页
  saveToStorage(STORAGE_KEY.CURRENT_PAGE, '1');
  handleBilibiliSearch();
}

// 组件卸载时清理 EventSource
onUnmounted(() => {
  if (eventSource) {
    eventSource.close()
  }
  // 注意：我们不在这里清除缓存，保留搜索结果
})

// AI预览相关
const aiPreviewVisible = ref(false)
const currentAIResult = ref<AISearchResult | null>(null)

// 打开AI预览
function openAIPreview(result) {
  currentAIResult.value = result
  aiPreviewVisible.value = true
}

// 关闭AI预览
function handleCloseAIPreview() {
  currentAIResult.value = null
  aiPreviewVisible.value = false
}

// 打开原文链接
function openOriginalLink() {
  if (currentAIResult.value?.url) {
    window.open(currentAIResult.value.url, '_blank')
  }
}

// 获取标签颜色
function getSourceColor(source: string): string {
  switch (source) {
    case '技术文章':
      return 'blue';
    case '教育资源':
      return 'green';
    case '学术资源':
      return 'purple';
    case '视频教程':
      return 'orange';
    default:
      return 'default';
  }
}

// 图片加载状态
const imageLoading = reactive<Record<string, boolean>>({})

// 处理图片加载完成
function handleImageLoad(bvid: string) {
  imageLoading[bvid] = false
}

// 处理图片加载错误
function handleImageError(e: Event) {
  const img = e.target as HTMLImageElement
  img.src = 'https://placehold.co/672x378/e5e7eb/475569?text=Video'
}

// 处理图片URL
function getProcessedImageUrl(url: string): string {
  if (!url) return '';

  try {
    if (url.startsWith('//')) {
      return `https:${url}`;
    } else if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    } else {
      return `https://${url}`;
    }
  } catch (error) {
    console.error('处理图片链接时出错:', error);
    return '';
  }
}

// 收藏夹相关
interface FavoriteItem {
  id: number;
  resource_type: 'video' | 'article';
  resource_id: string;
  title: string;
  url: string;
  description?: string;
  cover_url?: string;
  source: string;
  source_type: string;
  author?: string;
  create_time: string;
}

const favoriteStatus = reactive(new Map());

// 检查收藏状态
async function checkFavoriteStatus(item, type) {
  try {
    const resourceId = type === 'video' ? item.bvid : item.url;
    const response = await http.get(`/favorites/check`, {
      params: {
        resource_type: type,
        resource_id: resourceId
      }
    });
    const key = `${type}-${resourceId}`;
    favoriteStatus.set(key, response.data.isFavorited);
  } catch (error) {
    console.error('检查收藏状态失败:', error);
  }
}

// 推荐给学生相关
const recommendModalVisible = ref(false)
const recommendLoading = ref(false)
const recommendForm = reactive({
  classId: null,
  studentIds: [],
  remark: '',
  required: false
})

// 班级与学生数据
const classes = ref<Array<{id: number, class_name: string}>>([])
const students = ref<Array<{id: number, name: string}>>([])
const classesLoading = ref(false)
const studentsLoading = ref(false)

// 打开推荐弹窗
function handleRecommendToStudents() {
  if (!currentVideo.value) {
    message.error('视频信息不可用');
    return;
  }

  // 重置表单
  recommendForm.classId = null;
  recommendForm.studentIds = [];
  recommendForm.remark = '';
  recommendForm.required = false;

  // 加载班级数据
  loadTeacherClasses();

  // 显示弹窗
  recommendModalVisible.value = true;
}

// 加载教师的班级
async function loadTeacherClasses() {
  classesLoading.value = true;
  try {
    const response = await http.get('/resources/classes');
    classes.value = response.data.data || [];
  } catch (error) {
    console.error('获取班级列表失败:', error);
    message.error('获取班级列表失败');
  } finally {
    classesLoading.value = false;
  }
}

// 班级变更时加载学生
async function handleClassChange(classId) {
  if (!classId) {
    students.value = [];
    recommendForm.studentIds = [];
    return;
  }

  studentsLoading.value = true;
  try {
    const response = await http.get(`/resources/classes/${classId}/students`);
    students.value = response.data.data || [];
  } catch (error) {
    console.error('获取学生列表失败:', error);
    message.error('获取学生列表失败');
  } finally {
    studentsLoading.value = false;
  }
}

// 确认推荐
async function confirmRecommend() {
  if (!recommendForm.classId) {
    message.warning('请选择班级');
    return;
  }

  if (!currentVideo.value) {
    message.error('视频信息不可用');
    return;
  }

  recommendLoading.value = true;

  try {
    await http.post('/resources/recommend', {
      classId: recommendForm.classId,
      studentIds: recommendForm.studentIds.length > 0 ? recommendForm.studentIds : null,
      title: currentVideo.value.title,
      url: `https://www.bilibili.com/video/${currentVideo.value.bvid}`,
      coverUrl: currentVideo.value.pic,
      resourceType: 'video',
      resourceId: currentVideo.value.bvid,
      remark: recommendForm.remark,
      required: recommendForm.required
    });

    message.success('成功推荐给学生');
    recommendModalVisible.value = false;
  } catch (error) {
    console.error('推荐失败:', error);
    message.error('推荐失败，请稍后重试');
  } finally {
    recommendLoading.value = false;
  }
}

// 取消推荐
function cancelRecommend() {
  recommendModalVisible.value = false;
}

// 修改现有的添加收藏方法，返回Promise便于链式调用
async function handleVideoFavorite(video: Video) {
  return addToFavorites(video, 'video');
}

// 修改添加收藏方法
async function addToFavorites(item, type) {
  try {
    const favoriteData = {
      resource_type: type,
      resource_id: type === 'video' ? item.bvid : item.url,
      title: item.title,
      url: type === 'video' ? `https://www.bilibili.com/video/${item.bvid}` : item.url,
      description: item.description || '',
      cover_url: type === 'video' ? item.pic : null,
      source: type === 'video' ? 'bilibili' : item.source,
      source_type: type === 'video' ? 'video' : item.sourceType,
      author: type === 'video' ? item.author : null
    };

    const response = await http.post('/favorites', favoriteData);

    if (response.data.success) {
      message.success('收藏成功');
      const key = `${type}-${favoriteData.resource_id}`;
      favoriteStatus.set(key, true);
      return response.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('添加收藏失败:', error);
    throw error;
  }
}

// 在script setup中添加组件定义
const VideoFavoriteButton = defineComponent({
  props: {
    video: {
      type: Object as PropType<Video | null>,
      required: true
    }
  },
  setup(props) {
    return {
      handleVideoFavorite: () => props.video && handleVideoFavorite(props.video)
    };
  },
  template: `
    <a-button
      v-if="video"
      type="primary"
      size="middle"
      class="action-btn"
      @click="handleVideoFavorite"
    >
      <template #icon>
        <star-outlined />
      </template>
      收藏
    </a-button>
  `
});

const ArticleFavoriteButton = defineComponent({
  props: {
    article: {
      type: Object as PropType<AISearchResult | null>,
      required: true
    }
  },
  setup(props) {
    const key = computed(() => props.article ? `article-${props.article.url}` : '');
    const isFavorited = computed(() => favoriteStatus.get(key.value));

    return {
      key,
      isFavorited,
      handleArticleFavorite: () => props.article && handleArticleFavorite(props.article)
    };
  },
  template: `
    <a-button
      v-if="article"
      :type="isFavorited ? 'default' : 'primary'"
      size="small"
      @click="handleArticleFavorite"
    >
      <template #icon>
        <star-outlined :style="{ color: isFavorited ? '#1890ff' : '' }" />
      </template>
      {{ isFavorited ? '已收藏' : '收藏' }}
    </a-button>
  `
});

// 初始化路由
const router = useRouter()

// 修改收藏夹按钮，改为路由跳转
function goToFavorites() {
  router.push('/dashboard/favorites')
}

// 在onMounted中添加初始检查和恢复缓存的搜索结果
onMounted(async () => {
  // 恢复缓存的搜索结果
  restoreCachedData();

  // 如果有初始数据，检查它们的收藏状态
  if (videos.value.length > 0) {
    await Promise.all(videos.value.map(video => checkFavoriteStatus(video, 'video')));
  }
  if (aiResults.value.length > 0) {
    await Promise.all(aiResults.value.map(article => checkFavoriteStatus(article, 'article')));
  }
});

// 监听用户登录状态变化
watch(() => getCurrentUserId(), (newUserId, oldUserId) => {
  if (newUserId !== oldUserId) {
    // 如果用户ID变化，恢复该用户的数据
    restoreCachedData();
  }
}, { immediate: false });

// 恢复缓存数据
function restoreCachedData() {
  const cachedQuery = getFromStorage(STORAGE_KEY.SEARCH_QUERY);
  const cachedVideos = getFromStorage(STORAGE_KEY.VIDEOS);
  const cachedAIResults = getFromStorage(STORAGE_KEY.AI_RESULTS);
  const cachedTotal = getFromStorage(STORAGE_KEY.TOTAL);
  const cachedCurrentPage = getFromStorage(STORAGE_KEY.CURRENT_PAGE);
  const cachedSortBy = getFromStorage(STORAGE_KEY.SORT_BY);
  const cachedDuration = getFromStorage(STORAGE_KEY.DURATION);

  if (cachedQuery) {
    searchQuery.value = cachedQuery;
  } else {
    searchQuery.value = '';
  }

  if (cachedSortBy) {
    sortBy.value = cachedSortBy;
  } else {
    sortBy.value = 'totalrank';
  }

  if (cachedDuration) {
    duration.value = cachedDuration;
  } else {
    duration.value = '0';
  }

  if (cachedCurrentPage) {
    currentPage.value = parseInt(cachedCurrentPage);
  } else {
    currentPage.value = 1;
  }

  if (cachedTotal) {
    total.value = parseInt(cachedTotal);
  } else {
    total.value = 0;
  }

  if (cachedVideos) {
    try {
      videos.value = JSON.parse(cachedVideos);
    } catch (e) {
      console.error('Failed to parse cached videos:', e);
      videos.value = [];
    }
  } else {
    videos.value = [];
  }

  if (cachedAIResults) {
    try {
      aiResults.value = JSON.parse(cachedAIResults);
    } catch (e) {
      console.error('Failed to parse cached AI results:', e);
      aiResults.value = [];
    }
  } else {
    aiResults.value = [];
  }
}

// 在AI搜索结果中添加收藏按钮的点击事件
function handleArticleFavorite(result: AISearchResult) {
  addToFavorites(result, 'article')
}

// 创建axios实例
const http = axios.create({
  baseURL: '/api',
  timeout: 30000, // 增加超时时间到30秒
  headers: {
    'Content-Type': 'application/json'
  }
});

// 添加请求拦截器
http.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
http.interceptors.response.use(
  response => response,
  error => {
    console.error('请求失败:', error);

    if (error.code === 'ECONNABORTED') {
      message.error('请求超时，请稍后重试');
    } else if (error.response?.status === 401) {
      message.error('请先登录');
    } else {
      message.error(error.response?.data?.message || '操作失败，请稍后重试');
    }

    return Promise.reject(error);
  }
);

// 清除搜索结果缓存
function clearSearchCache() {
  Object.values(STORAGE_KEY).forEach(key => {
    removeFromStorage(key);
  });

  // 重置所有状态
  searchQuery.value = '';
  videos.value = [];
  aiResults.value = [];
  currentPage.value = 1;
  total.value = 0;
  sortBy.value = 'totalrank';
  duration.value = '0';
}

// 从弹窗进入笔记生成页面
function goToNoteGenerator() {
  if (!currentVideo.value) return;
  videoModalVisible.value = false;
  router.push(`/dashboard/note-generator`);
}
</script>

<style scoped>
.resource-center {
  padding: 24px;
  max-width: 1800px;
  margin: 0 auto;
  background-color: #f9fafc;
}

.page-header {
  margin-bottom: 32px;
  padding: 20px 0;
  position: relative;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  position: relative;
  display: inline-block;
}

.page-header h1::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #4c4de6, #7476ff);
  border-radius: 3px;
}

.page-header .description {
  color: #666;
  font-size: 15px;
  max-width: 600px;
}

.content-wrapper {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05);
  margin-bottom: 32px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.search-section {
  margin-bottom: 32px;
  background-color: #fff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-container {
  padding: 0;
  gap: 16px;
}

.search-input-group {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.search-input-group:hover {
  border-color: #d0d1f8;
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.08);
}

.search-input-group :deep(.ant-input) {
  border: none;
  height: 46px;
  padding: 4px 16px;
  font-size: 15px;
}

.search-input-group :deep(.ant-input:focus) {
  box-shadow: none;
}

.search-button {
  height: 46px;
  border-radius: 0;
  padding: 0 24px;
  background: linear-gradient(90deg, #4c4de6, #7476ff);
  border: none;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.search-button:hover {
  background: linear-gradient(90deg, #3e3fd6, #6162ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.2);
}

.favorites-button {
  height: 46px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  background: linear-gradient(90deg, #4c4de6, #7476ff);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.15);
  margin-left: 8px;
}

.favorites-button:hover {
  background: linear-gradient(90deg, #3e3fd6, #6162ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.25);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 240px;
}

.search-results-container {
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05);
}

.search-results-container :deep(.ant-card-body) {
  padding: 0;
}

.search-results-container :deep(.ant-tabs-nav) {
  margin: 0;
  padding: 8px 20px 0;
  background-color: #f9fafc;
}

.search-results-container :deep(.ant-tabs-ink-bar) {
  background: linear-gradient(90deg, #4c4de6, #7476ff);
  height: 3px;
  border-radius: 3px 3px 0 0;
}

.search-results-container :deep(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #4c4de6;
  font-weight: 600;
}

.search-results-container :deep(.ant-tabs-tab:hover) {
  color: #7476ff;
}

.search-results-container :deep(.ant-tabs-tab) {
  padding: 12px 20px;
  font-size: 15px;
  transition: all 0.3s ease;
}

.bilibili-results, .ai-results {
  padding: 24px;
}

.section-header {
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filters {
  display: flex;
  gap: 12px;
}

:deep(.ant-select-selector) {
  border-radius: 6px !important;
  transition: all 0.3s ease;
}

:deep(.ant-select:hover .ant-select-selector) {
  border-color: #4c4de6 !important;
}

:deep(.ant-select-focused .ant-select-selector) {
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
}

.video-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.video-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 24px rgba(76, 77, 230, 0.15);
  border-color: #e0e1ff;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
  background-color: #f6f7fb;
}

.video-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0) 70%, rgba(0,0,0,0.8) 100%);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-thumbnail:hover::before {
  opacity: 1;
}

.video-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.video-thumbnail:hover img {
  transform: scale(1.05);
}

:deep(.ant-card-meta-title) {
  white-space: normal !important;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 15px !important;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px !important;
}

:deep(.ant-card-meta-description) {
  color: #666;
  font-size: 13px !important;
  line-height: 1.5;
}

/* 加载动画样式 */
.loading-spinner {
  width: 50px;
  height: 50px;
  position: relative;
  margin: 0 auto;
}

.loading-spinner-sm {
  width: 30px;
  height: 30px;
}

.double-bounce1, .double-bounce2 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(45deg, #4c4de6 0%, #7476ff 100%);
  opacity: 0.6;
  position: absolute;
  top: 0;
  left: 0;
  animation: sk-bounce 2.0s infinite ease-in-out;
}

.double-bounce2 {
  animation-delay: -1.0s;
  background: linear-gradient(45deg, #7476ff 0%, #a5a6ff 100%);
}

@keyframes sk-bounce {
  0%, 100% {
    transform: scale(0.0);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.0);
    opacity: 0.2;
  }
}

/* 视频容器样式 */
.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.aspect-w-16 > iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

/* 调整弹窗样式 */
:deep(.video-modal .ant-modal-content) {
  padding: 0 !important;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

:deep(.video-modal .ant-modal-header) {
  margin-bottom: 0 !important;
  padding: 16px 24px !important;
  background: linear-gradient(to right, #f8f9fd, #f0f1ff);
  border-bottom: 1px solid #e8e9ff;
}

:deep(.video-modal .ant-modal-title) {
  font-size: 18px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  color: #333;
}

:deep(.video-modal .ant-modal-close) {
  top: 12px !important;
  right: 16px !important;
}

:deep(.video-modal .ant-modal-body) {
  padding: 0 !important;
}

/* 视频弹窗样式 */
.video-container {
  display: flex;
  flex-direction: column;
}

.video-actions {
  padding: 16px 24px;
  background: linear-gradient(to right, #f8f9fd, #f0f1ff);
  border-top: 1px solid #e8e9ff;
  border-bottom: 1px solid #e8e9ff;
}

.action-buttons-container {
  display: flex;
  gap: 16px;
  padding: 8px 0;
  justify-content: center;
}

.action-btn {
  min-width: 130px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.collect-btn {
  background: linear-gradient(45deg, #4c4de6, #7476ff);
}

.collect-btn:hover {
  background: linear-gradient(45deg, #3e3fd6, #6162ff);
}

.recommend-btn {
  background: linear-gradient(45deg, #4c4de6, #7476ff);
}

.recommend-btn:hover {
  background: linear-gradient(45deg, #3e3fd6, #6162ff);
}

.summary-btn {
  background: linear-gradient(45deg, #4c4de6, #7476ff);
}

.summary-btn:hover {
  background: linear-gradient(45deg, #3e3fd6, #6162ff);
}

.video-info {
  padding: 16px 24px;
  background-color: #fff;
  font-size: 14px;
  color: #555;
  line-height: 1.5;
}

/* AI搜索结果样式 */
.ai-results {
  padding: 24px;
}

.ai-result-list {
  margin-top: 20px;
}

.ai-result-list :deep(.ant-empty) {
  margin: 48px 0;
}

.search-result-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.search-result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(76, 77, 230, 0.15);
  border-color: #e0e1ff;
}

:deep(.search-result-card .ant-card-head) {
  min-height: auto;
  padding: 12px 16px;
  background: linear-gradient(to right, #f8f9fd, #f0f1ff);
  border-bottom: 1px solid #e8e9ff;
}

:deep(.search-result-card .ant-card-head-title) {
  padding: 8px 0;
  font-weight: 600;
}

:deep(.search-result-card .ant-card-body) {
  padding: 16px;
}

/* 文本截断 */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.6;
}

/* AI预览弹窗样式 */
:deep(.ai-preview-modal .ant-modal-content) {
  padding: 0 !important;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

:deep(.ai-preview-modal .ant-modal-header) {
  margin-bottom: 0 !important;
  padding: 16px 24px !important;
  background: linear-gradient(to right, #f8f9fd, #f0f1ff);
  border-bottom: 1px solid #e8e9ff;
}

:deep(.ai-preview-modal .ant-modal-title) {
  font-size: 18px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  color: #333;
}

.ai-preview-container {
  padding: 24px;
}

.preview-content {
  font-size: 15px;
  line-height: 1.6;
  color: #444;
}

/* 卡片操作区域 */
.card-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
  margin-top: 12px;
}

/* 分页器样式 */
:deep(.ant-pagination) {
  padding: 16px 0;
}

:deep(.ant-pagination-item) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.ant-pagination-item-active) {
  background: linear-gradient(to right, #f0f1ff, #e8e9ff);
  border-color: #4c4de6;
  font-weight: 600;
}

:deep(.ant-pagination-item-active a) {
  color: #4c4de6;
}

:deep(.ant-pagination-item:hover) {
  border-color: #4c4de6;
  transform: translateY(-2px);
}

:deep(.ant-pagination-item:hover a) {
  color: #4c4de6;
}

:deep(.ant-pagination-next:hover .ant-pagination-item-link),
:deep(.ant-pagination-prev:hover .ant-pagination-item-link) {
  color: #4c4de6;
  border-color: #4c4de6;
}

:deep(.ant-pagination-options) {
  margin-left: 16px;
}

:deep(.ant-pagination-options-quick-jumper input) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.ant-pagination-options-quick-jumper input:hover) {
  border-color: #4c4de6;
}

:deep(.ant-pagination-options-quick-jumper input:focus) {
  border-color: #4c4de6;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2);
}

/* 弹窗表单样式 */
.recommend-form {
  padding: 8px;
}

.form-label {
  font-weight: 500;
  color: #333;
}

:deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner) {
  border-color: #4c4de6;
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #4c4de6;
  border-color: #4c4de6;
}

:deep(.ant-checkbox-checked::after) {
  border-color: #4c4de6;
}

:deep(.ant-alert) {
  border-radius: 8px;
  margin-bottom: 20px;
}

/* 标签样式 */
:deep(.source-tag) {
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .resource-center {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 24px;
    padding: 16px 0;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .search-section {
    padding: 16px;
  }
  
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .bilibili-results, 
  .ai-results {
    padding: 16px;
  }
  
  .action-buttons-container {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .action-btn {
    min-width: 120px;
  }
}
</style>
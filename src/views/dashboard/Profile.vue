<template>
  <div class="profile-container">
    <div class="page-header">
      <h1>个人信息</h1>
      <p class="description">您可以在此页面查看和修改个人基本信息</p>
    </div>

    <a-spin :spinning="isLoading">
      <div class="content-wrapper" v-if="userInfo">
        <!-- 左侧头像部分 -->
        <div class="avatar-card">
          <div class="avatar-section">
            <div class="avatar-wrapper">
              <img 
                :src="avatarUrl" 
                alt="用户头像" 
                class="avatar-img"
              >
              <div class="avatar-overlay" @click="triggerFileInput">
                <i class="fas fa-camera"></i>
                <span>上传头像</span>
              </div>
            </div>
            <div class="upload-tips">
              支持JPG、JPEG、PNG、GIF格式，大小不超过2MB
            </div>
            <input 
              type="file" 
              ref="fileInput" 
              style="display: none" 
              accept="image/jpeg,image/jpg,image/png,image/gif"
              @change="handleFileChange"
            >
          </div>
          
          <div class="user-basic-info">
            <h2>{{ userInfo.name || '未设置姓名' }}</h2>
            <div class="user-id">{{ userInfo.system_teacher_id || '未设置' }}</div>
            <div class="role-badge">教师</div>
          </div>
        </div>

        <!-- 右侧信息表单部分 -->
        <div class="info-card">
          <h3 class="card-title">基本信息</h3>
          
          <div class="form-section">
            <div class="form-group">
              <label>系统ID</label>
              <div class="read-only-field">{{ userInfo.system_teacher_id || '未设置' }}</div>
            </div>
            
            <div class="form-group">
              <label>教师工号</label>
              <div class="read-only-field">{{ userInfo.school_teacher_id || '未设置' }}</div>
            </div>
            
            <div class="form-group">
              <label>姓名</label>
              <a-input v-model:value="form.name" placeholder="请输入姓名" />
            </div>
            
            <div class="form-group">
              <label>手机号</label>
              <a-input v-model:value="form.phone" placeholder="请输入手机号" />
            </div>
          </div>
          
          <h3 class="card-title password-section-title">安全设置</h3>
          
          <div class="form-section">
            <div class="form-group">
              <label>修改密码</label>
              <a-input-password v-model:value="form.password" placeholder="请输入新密码" />
            </div>
            
            <div class="form-group" v-if="form.password">
              <label>确认密码</label>
              <a-input-password v-model:value="form.confirmPassword" placeholder="请再次输入新密码" />
            </div>
            
            <div class="actions">
              <a-button type="primary" @click="updateProfile" :loading="loading">保存修改</a-button>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else-if="!isLoading" class="no-user-info">
        <a-result
          status="warning"
          title="未找到用户信息"
          sub-title="请尝试重新登录或联系管理员"
        >
          <template #extra>
            <a-button type="primary" @click="goToLogin">返回登录</a-button>
          </template>
        </a-result>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import axios from '@/utils/axios'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

const router = useRouter()
const userStore = useUserStore()
const userInfo = ref(null)
const loading = ref(false)
const isLoading = ref(true)
const fileInput = ref(null)

const form = reactive({
  name: '',
  phone: '',
  password: '',
  confirmPassword: ''
})

// 计算头像URL
const avatarUrl = computed(() => {
  if (userInfo.value && userInfo.value.avatar) {
    // 检查头像URL是否是绝对URL
    if (userInfo.value.avatar.startsWith('http')) {
      return userInfo.value.avatar;
    }
    
    // 如果路径已经是以/开头，则直接使用
    if (userInfo.value.avatar.startsWith('/')) {
      // 移除可能存在的/public前缀
      return userInfo.value.avatar.replace(/^\/public/, '');
    } else {
      // 没有/开头的路径，加上/
      return `/${userInfo.value.avatar}`;
    }
  }
  // 默认头像
  return '/avatar/avatar-0001.jpg';
})

// 初始化用户信息
const initUserInfo = () => {
  isLoading.value = true
  try {
    const userData = userStore.user
    if (userData && userData.id) {
      userInfo.value = userData
      form.name = userData.name || ''
      form.phone = userData.phone || ''
    } else {
      message.warning('用户信息获取失败，请重新登录')
    }
  } catch (error) {
    console.error('初始化用户信息失败:', error)
    message.error('获取用户信息失败')
  } finally {
    isLoading.value = false
  }
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click()
}

// 处理文件选择
const handleFileChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    message.error('请上传JPG、JPEG、PNG或GIF格式的图片')
    return
  }
  
  // 检查文件扩展名
  const fileName = file.name.toLowerCase()
  const validExtension = /\.(jpg|jpeg|png|gif)$/i.test(fileName)
  if (!validExtension) {
    message.error('文件必须是JPG、JPEG、PNG或GIF格式')
    return
  }
  
  // 检查文件大小 (2MB = 2 * 1024 * 1024 bytes)
  if (file.size > 2 * 1024 * 1024) {
    message.error('图片大小不能超过2MB')
    return
  }
  
  // 创建FormData对象
  const formData = new FormData()
  formData.append('avatar', file)
  
  try {
    loading.value = true
    const response = await axios.post('/api/auth/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    if (response.data.success) {
      message.success('头像上传成功')
      
      // 更新本地用户信息
      if (userInfo.value) {
        userInfo.value.avatar = response.data.data.avatar
        const updatedUserData = { ...userStore.user, avatar: response.data.data.avatar }
        userStore.setUser(updatedUserData)
      }
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    message.error(error.response?.data?.message || '头像上传失败，请确保图片格式正确')
  } finally {
    loading.value = false
    // 清空文件选择
    event.target.value = ''
  }
}

// 更新个人信息
const updateProfile = async () => {
  if (!userInfo.value || !userInfo.value.id) {
    message.error('用户信息不完整，无法更新')
    return
  }
  
  // 表单验证
  if (form.password && form.password !== form.confirmPassword) {
    message.error('两次输入的密码不一致')
    return
  }
  
  const updateData = {
    id: userInfo.value.id,
    name: form.name,
    phone: form.phone
  }
  
  if (form.password) {
    updateData.password = form.password
  }
  
  try {
    loading.value = true
    const response = await axios.put('/api/auth/profile', updateData)
    
    if (response.data.success) {
      message.success('个人信息更新成功')
      
      // 更新本地用户信息
      const updatedUserData = {
        ...userInfo.value,
        name: response.data.data.name,
        phone: response.data.data.phone
      }
      userInfo.value = updatedUserData
      userStore.setUser(updatedUserData)
      
      // 清空密码字段
      form.password = ''
      form.confirmPassword = ''
    }
  } catch (error) {
    console.error('更新个人信息失败:', error)
    message.error(error.response?.data?.message || '更新个人信息失败')
  } finally {
    loading.value = false
  }
}

const goToLogin = () => {
  userStore.clearUser()
  router.push('/login')
}

// 组件挂载时初始化数据
onMounted(() => {
  initUserInfo()
})
</script>

<style scoped>
.profile-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}

.description {
  color: #666;
  margin-top: 8px;
  font-size: 14px;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
}

/* 左侧头像卡片样式 */
.avatar-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: fit-content;
}

.avatar-section {
  margin-bottom: 20px;
}

.upload-tips {
  margin-top: 12px;
  text-align: center;
  font-size: 12px;
  color: #888;
  line-height: 1.5;
}

.avatar-wrapper {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.15);
  margin: 0 auto;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 12px 0;
  text-align: center;
  cursor: pointer;
  transition: opacity 0.3s;
  opacity: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay i {
  font-size: 20px;
  margin-bottom: 6px;
}

.user-basic-info {
  text-align: center;
  width: 100%;
}

.user-basic-info h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #333;
  font-weight: 600;
}

.user-id {
  color: #666;
  font-size: 14px;
  margin-bottom: 10px;
}

.role-badge {
  display: inline-block;
  background: rgba(76, 77, 230, 0.1);
  color: #4c4de6;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
}

/* 右侧信息卡片样式 */
.info-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 0;
  overflow: hidden;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.password-section-title {
  border-top: 1px solid #f0f0f0;
  margin-top: 0;
}

.form-section {
  padding: 24px;
}

.form-group {
  margin-bottom: 24px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.read-only-field {
  background-color: #f5f5f7;
  padding: 10px 12px;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
}

.actions {
  margin-top: 32px;
  display: flex;
  justify-content: flex-end;
}

.actions button {
  height: 40px;
  padding: 0 24px;
  font-size: 14px;
  background: #4c4de6;
  border-color: #4c4de6;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(76, 77, 230, 0.2);
  transition: all 0.3s ease;
}

.actions button:hover {
  background: #5c5ef7;
  border-color: #5c5ef7;
  box-shadow: 0 4px 8px rgba(76, 77, 230, 0.3);
  transform: translateY(-1px);
}

.no-user-info {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 40px;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .content-wrapper {
    grid-template-columns: 1fr;
  }
  
  .avatar-wrapper {
    width: 150px;
    height: 150px;
  }
}

/* 小屏幕适配 */
@media (max-width: 576px) {
  .profile-container {
    padding: 16px;
  }
  
  .avatar-card,
  .info-card {
    border-radius: 6px;
  }
  
  .form-section {
    padding: 16px;
  }
  
  .card-title {
    padding: 12px 16px;
  }
}
</style> 
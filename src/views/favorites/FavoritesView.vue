<template>
  <div class="favorites-view">
    <div class="page-header">
      <div class="title-section">
        <a-button
          class="back-button"
          @click="goBack"
        >
          <template #icon><i class="fas fa-arrow-left"></i></template>
          返回
        </a-button>
        <h1>我的收藏</h1>
      </div>
      <p class="description">管理您收藏的教学资源</p>
    </div>

    <div class="content-wrapper">
      <a-tabs v-model:activeKey="activeTabKey" class="favorites-tabs">
        <a-tab-pane key="video" tab="视频">
          <div class="favorites-content">
            <a-spin :spinning="loading">
              <!-- 视频列表 -->
              <div v-if="videoFavorites.length" class="video-grid">
                <a-card
                  v-for="item in videoFavorites"
                  :key="item.id"
                  hoverable
                  class="video-card"
                >
                  <template #cover>
                    <div class="video-thumbnail" @click="openResource(item)">
                      <img
                        :src="getProcessedImageUrl(item.cover_url)"
                        class="w-full h-full object-cover"
                        :alt="item.title"
                        loading="lazy"
                        @error="handleImageError"
                        referrerpolicy="no-referrer"
                      />
                    </div>
                  </template>
                  <a-card-meta :title="item.title">
                    <template #description>
                      <div class="mt-2 text-sm text-gray-500">
                        <div class="flex items-center justify-between">
                          <span>UP主：{{ item.author }}</span>
                        </div>
                        <div class="mt-1 flex items-center justify-between">
                          <span>来源：{{ item.source }}</span>
                          <a-tag :color="getSourceColor(item.source_type)">
                            {{ item.source_type }}
                          </a-tag>
                        </div>
                      </div>
                    </template>
                  </a-card-meta>
                  <div class="card-actions mt-3">
                    <a-button type="link" @click="openResource(item)">
                      查看详情
                    </a-button>
                    <a-popconfirm
                      title="确定要取消收藏吗？"
                      @confirm="removeFavorite(item)"
                    >
                      <a-button type="link" danger>
                        取消收藏
                      </a-button>
                    </a-popconfirm>
                  </div>
                </a-card>
              </div>
              <a-empty v-else description="暂无收藏的视频" />
            </a-spin>
          </div>
        </a-tab-pane>

        <a-tab-pane key="article" tab="文章">
          <div class="favorites-content">
            <a-spin :spinning="loading">
              <!-- 文章列表 -->
              <div v-if="articleFavorites.length" class="article-list">
                <a-list
                  :data-source="articleFavorites"
                  :pagination="{ pageSize: 10 }"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #title>
                          <div class="flex items-center justify-between">
                            <span class="text-blue-600 hover:text-blue-800 cursor-pointer" @click="openResource(item)">
                              {{ item.title }}
                            </span>
                            <a-tag :color="getSourceColor(item.source_type)">
                              {{ item.source_type }}
                            </a-tag>
                          </div>
                        </template>
                        <template #description>
                          <div class="text-sm text-gray-500">
                            <p class="mb-2">{{ item.description }}</p>
                            <div class="flex justify-between items-center">
                              <span>来源：{{ item.source }}</span>
                              <a-popconfirm
                                title="确定要取消收藏吗？"
                                @confirm="removeFavorite(item)"
                              >
                                <a-button type="link" danger>
                                  取消收藏
                                </a-button>
                              </a-popconfirm>
                            </div>
                          </div>
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
              <a-empty v-else description="暂无收藏的文章" />
            </a-spin>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 视频播放弹窗 -->
    <a-modal
      v-model:visible="videoModalVisible"
      :title="currentVideo?.title"
      :footer="null"
      width="65%"
      centered
      destroyOnClose
      wrapClassName="video-modal"
    >
      <div class="video-container">
        <div class="aspect-w-16 aspect-h-9">
          <iframe
            v-if="videoModalVisible"
            :src="currentVideoUrl"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            class="w-full h-full"
          ></iframe>
        </div>
      </div>

      <!-- 添加按钮区域 -->
      <div class="video-actions mt-5 flex items-center justify-center">
        <div class="action-buttons-container">
          <a-button
            type="primary"
            class="action-btn summary-btn"
            @click="goToNoteGenerator"
            :loading="summarizing"
          >
            <template #icon><i class="fas fa-file-alt"></i></template>
            总结笔记
          </a-button>
          <a-button
            type="primary"
            class="action-btn share-btn"
            @click="handleShare"
            :loading="sharing"
          >
            <template #icon><i class="fas fa-share-alt"></i></template>
            分享给学生
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 分享对话框 -->
    <a-modal
      v-model:visible="shareModalVisible"
      title="分享给学生"
      @ok="confirmShare"
      :confirmLoading="confirmSharing"
      @cancel="shareModalVisible = false"
    >
      <a-form :model="shareForm" layout="vertical">
        <a-form-item label="选择班级" name="classId">
          <a-select
            v-model:value="shareForm.classId"
            placeholder="请选择班级"
            @change="handleClassChange"
          >
            <a-select-option v-for="item in classes" :key="item.id" :value="item.id">
              {{ item.class_name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="选择学生" name="studentIds">
          <a-select
            v-model:value="shareForm.studentIds"
            mode="multiple"
            placeholder="请选择学生(不选择则推荐给全班)"
            :options="students.map(s => ({ value: s.id, label: s.name }))"
          >
          </a-select>
          <div class="text-gray-500 text-xs mt-1">不选择学生将推荐给全班</div>
        </a-form-item>

        <a-form-item label="分享备注" name="remark">
          <a-textarea
            v-model:value="shareForm.remark"
            placeholder="请输入分享备注（选填）"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 笔记内容弹窗 -->
    <a-modal
      v-model:visible="noteModalVisible"
      title="视频总结笔记"
      width="70%"
      :footer="null"
      :maskClosable="false"
    >
      <template #footer>
        <div class="flex justify-between w-full">
          <a-button @click="noteModalVisible = false">关闭</a-button>
          <div>
            <a-button @click="goToNotesList" class="mr-2">查看所有笔记</a-button>
            <a-button @click="shareNote" type="dashed" class="mr-2">
              <template #icon><i class="fas fa-share-alt"></i></template>
              分享笔记
            </a-button>
            <a-button type="primary" @click="saveNote">
              <template #icon><i class="fas fa-save"></i></template>
              保存笔记
            </a-button>
          </div>
        </div>
      </template>

      <a-spin :spinning="summarizing" tip="正在生成笔记，请稍候...">
        <div v-if="summaryStatus === 'error'" class="error-container p-4 mb-4 bg-red-50 text-red-700 rounded">
          <h3 class="font-bold mb-2">处理视频时出错</h3>
          <p>{{ summaryError }}</p>
          <p class="mt-2">可能的原因:</p>
          <ul class="list-disc list-inside ml-2">
            <li>视频链接无效或已失效</li>
            <li>视频内容不适合处理（如没有语音内容）</li>
            <li>服务器处理能力暂时受限</li>
          </ul>
        </div>

        <div v-else class="note-container">
          <a-input
            v-model:value="noteTitle"
            placeholder="笔记标题"
            class="mb-4"
            :disabled="summarizing"
          />
          <a-textarea
            v-model:value="noteContent"
            placeholder="笔记内容"
            :rows="15"
            class="note-content"
            :disabled="summarizing"
          />

          <div v-if="summaryStatus === 'loading'" class="progress-container mt-4">
            <p class="mb-2">正在处理视频内容，请耐心等待...</p>
            <a-progress :percent="summaryProgress" status="active" />
            <div class="processing-steps mt-2 text-sm text-gray-500">
              <p v-if="summaryProgress >= 10">✓ 正在下载视频</p>
              <p v-if="summaryProgress >= 30">✓ 正在提取音频</p>
              <p v-if="summaryProgress >= 50">✓ 正在转换音频为文本</p>
              <p v-if="summaryProgress >= 70">✓ 正在生成内容摘要</p>
              <p v-if="summaryProgress >= 90">✓ 正在格式化笔记内容</p>
            </div>
          </div>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { useRouter } from 'vue-router';

// 创建axios实例
const http = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 添加请求拦截器
http.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
http.interceptors.response.use(
  response => response,
  error => {
    console.error('请求失败:', error);
    if (error.response?.status === 401) {
      message.error('请先登录');
    } else {
      message.error(error.response?.data?.message || '操作失败，请稍后重试');
    }
    return Promise.reject(error);
  }
);

interface FavoriteItem {
  id: number;
  resource_type: 'video' | 'article';
  resource_id: string;
  title: string;
  url: string;
  description?: string;
  cover_url?: string;
  source: string;
  source_type: string;
  author?: string;
  create_time: string;
}

interface ClassItem {
  id: number;
  class_name: string;
}

interface StudentItem {
  id: number;
  name: string;
}

const activeTabKey = ref('video');
const loading = ref(false);
const videoFavorites = ref<FavoriteItem[]>([]);
const articleFavorites = ref<FavoriteItem[]>([]);
const videoModalVisible = ref(false);
const currentVideo = ref<FavoriteItem | null>(null);
const router = useRouter();

// 计算当前视频URL
const currentVideoUrl = computed(() => {
  if (!currentVideo.value?.resource_id) return '';
  return `//player.bilibili.com/player.html?bvid=${currentVideo.value.resource_id}&page=1&high_quality=1&danmaku=0`;
});

// 获取标签颜色
function getSourceColor(source: string): string {
  switch (source) {
    case '技术文章':
      return 'blue';
    case '教育资源':
      return 'green';
    case '学术资源':
      return 'purple';
    case '视频教程':
      return 'orange';
    default:
      return 'default';
  }
}

// 加载收藏数据
async function loadFavorites() {
  try {
    loading.value = true;
    const response = await http.get('/favorites');
    if (response.data.success) {
      videoFavorites.value = response.data.data.videos;
      articleFavorites.value = response.data.data.articles;
    }
  } catch (error) {
    console.error('加载收藏失败:', error);
  } finally {
    loading.value = false;
  }
}

// 取消收藏
async function removeFavorite(item: FavoriteItem) {
  try {
    const response = await http.delete(`/favorites/${item.id}`);
    if (response.data.success) {
      message.success('取消收藏成功');
      await loadFavorites();
    }
  } catch (error) {
    console.error('取消收藏失败:', error);
  }
}

// 打开资源
function openResource(item: FavoriteItem) {
  if (item.resource_type === 'video') {
    currentVideo.value = item;
    videoModalVisible.value = true;
  } else {
    window.open(item.url, '_blank');
  }
}

// 组件加载时获取数据
onMounted(() => {
  loadFavorites();
});

// 按钮状态变量
const summarizing = ref(false);
const sharing = ref(false);

// 总结笔记相关状态
const noteModalVisible = ref(false);
const noteContent = ref('');
const noteTitle = ref('');
const summaryId = ref<number | null>(null);
const summaryStatus = ref('idle'); // 'idle', 'loading', 'success', 'error'
const summaryError = ref('');
const summaryProgress = ref(0);

// 处理总结笔记按钮点击
const handleSummarize = async () => {
  if (!currentVideo.value) return;

  try {
    // 重置状态
    summarizing.value = true;
    summaryStatus.value = 'loading';
    summaryProgress.value = 0;
    summaryError.value = '';
    noteModalVisible.value = true;  // 先显示模态框，以便用户看到进度

    // 显示进度提示
    const loadingMessage = message.loading({
      content: '正在处理视频，这可能需要几分钟时间...',
      duration: 0
    });

    // 模拟进度更新 - 随着后端支持实际进度，这里将逐步被替换
    // 初始进度值为5%，表示请求已发送
    summaryProgress.value = 5;

    // 使用较长的超时时间调用API
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 600000); // 10分钟超时

    try {
      // 调用API生成视频内容的摘要
      const response = await fetch('/api/video-summary/summarize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          videoUrl: currentVideo.value.url,
          videoTitle: currentVideo.value.title,
          resourceId: currentVideo.value.resource_id,
          favoriteId: currentVideo.value.id
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`);
      }

      const data = await response.json();

      loadingMessage();

      if (data.success) {
        // 设置笔记内容并显示弹窗
        noteContent.value = data.data.summary || '';
        noteTitle.value = currentVideo.value.title || '无标题视频笔记';
        summaryId.value = data.data.id || null;
        summaryStatus.value = 'success';
        summaryProgress.value = 100;

        message.success('视频总结生成成功');
      } else {
        throw new Error(data.message || '总结失败');
      }
    } catch (fetchError) {
      clearTimeout(timeoutId);
      if (fetchError.name === 'AbortError') {
        throw new Error('请求超时，视频处理时间过长');
      }
      throw fetchError;
    }
  } catch (error) {
    console.error('生成视频总结失败:', error);
    summaryStatus.value = 'error';
    summaryError.value = error.message || '生成失败，请稍后重试';
    message.error(summaryError.value);
  } finally {
    summarizing.value = false;
  }
};

// 保存笔记
const saveNote = async () => {
  if (!summaryId.value) {
    message.error('无法保存笔记: 缺少必要信息');
    return;
  }

  try {
    // 显示加载消息
    const loadingMessage = message.loading({
      content: '正在保存笔记...',
      duration: 0
    });

    const response = await http.put(`/video-summary/${summaryId.value}`, {
      title: noteTitle.value,
      summary: noteContent.value
    });

    loadingMessage();

    if (response.data.success) {
      message.success('笔记保存成功');
    } else {
      throw new Error(response.data.message || '保存失败');
    }
  } catch (error: any) {
    console.error('保存笔记失败:', error);
    message.error('保存笔记失败: ' + (error.response?.data?.message || error.message));
  }
};

// 跳转到笔记列表
const goToNotesList = () => {
  noteModalVisible.value = false;
  router.push('/notes');
};

// 分享笔记
const shareNote = () => {
  if (!currentVideo.value) return;

  // 关闭笔记弹窗，打开分享弹窗
  noteModalVisible.value = false;
  shareModalVisible.value = true;
};

// 分享对话框相关
const shareModalVisible = ref(false);
const confirmSharing = ref(false);
const shareForm = ref({
  classId: null,
  studentIds: [],
  remark: ''
});
const classes = ref<ClassItem[]>([]);
const students = ref<StudentItem[]>([]);

// 分享按钮点击
const handleShare = async () => {
  if (!currentVideo.value) return;

  try {
    sharing.value = true;
    // 加载班级数据
    const res = await http.get('/resources/classes');
    classes.value = res.data.data || [];

    // 显示分享对话框
    shareModalVisible.value = true;
  } catch (error) {
    console.error('获取班级失败:', error);
    message.error('获取班级信息失败，请稍后重试');
  } finally {
    sharing.value = false;
  }
};

// 班级变更时加载学生
const handleClassChange = async (classId) => {
  if (!classId) {
    students.value = [];
    shareForm.value.studentIds = [];
    return;
  }

  try {
    const res = await http.get(`/resources/classes/${classId}/students`);
    students.value = res.data.data || [];
  } catch (error) {
    console.error('获取学生列表失败:', error);
    message.error('获取学生列表失败');
  }
};

// 确认分享
const confirmShare = async () => {
  if (!shareForm.value.classId) {
    message.warning('请选择班级');
    return;
  }

  if (!currentVideo.value) {
    message.error('视频信息不可用');
    return;
  }

  try {
    confirmSharing.value = true;

    await http.post('/resources/recommend', {
      classId: shareForm.value.classId,
      studentIds: shareForm.value.studentIds.length > 0 ? shareForm.value.studentIds : null,
      title: currentVideo.value.title,
      url: currentVideo.value.url,
      coverUrl: currentVideo.value.cover_url,
      source: currentVideo.value.source || '未知',
      sourceType: currentVideo.value.source_type || '视频',
      remark: shareForm.value.remark,
      required: false
    });

    message.success('视频已成功分享给学生');
    shareModalVisible.value = false;
    // 重置表单
    shareForm.value = {
      classId: null,
      studentIds: [],
      remark: ''
    };
  } catch (error) {
    console.error('分享失败:', error);
    message.error('分享失败，请稍后重试');
  } finally {
    confirmSharing.value = false;
  }
};

// 处理图片URL
function getProcessedImageUrl(url: string | undefined): string {
  if (!url) return '';

  try {
    if (url.startsWith('//')) {
      return `https:${url}`;
    } else if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    } else {
      return `https://${url}`;
    }
  } catch (error) {
    console.error('处理图片链接时出错:', error);
    return '';
  }
}

// 处理图片加载错误
function handleImageError(e: Event) {
  const img = e.target as HTMLImageElement;
  img.src = 'https://placehold.co/672x378/e5e7eb/475569?text=Video';
}

// 返回上一页
function goBack() {
  router.back();
}

// 处理总结笔记按钮点击
const goToNoteGenerator = () => {
  if (!currentVideo.value) return;
  router.push(`/dashboard/note-generator/${currentVideo.value.id}`);
};
</script>

<style scoped>
.favorites-view {
  padding: 24px;
  max-width: 1800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.title-section {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0;
  margin-left: 12px;
}

.back-button {
  height: 36px;
  border-radius: 4px;
  background-color: #4c4de6;
  border-color: #4c4de6;
  color: #fff;
  transition: all 0.3s ease;
  padding: 0 16px;
  font-size: 14px;
}

.back-button:hover {
  background-color: #6364e9;
  border-color: #6364e9;
  color: #fff;
}

.back-button i {
  font-size: 14px;
  margin-right: 4px;
}

.description {
  color: #666;
  font-size: 14px;
}

.content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.favorites-tabs {
  width: 100%;
}

.favorites-tabs :deep(.ant-tabs-nav) {
  margin: 0;
  padding: 0 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.favorites-tabs :deep(.ant-tabs-ink-bar) {
  background-color: #4c4de6;
}

.favorites-tabs :deep(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #4c4de6;
}

.favorites-tabs :deep(.ant-tabs-tab:hover) {
  color: #4c4de6;
}

.favorites-content {
  min-height: 400px;
  padding: 24px;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  padding: 20px 0;
}

.video-card {
  transition: transform 0.2s;
  border-radius: 8px;
  overflow: hidden;
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(76, 77, 230, 0.15);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
  cursor: pointer;
}

.video-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-list {
  padding: 8px 0;
}

.article-list :deep(.ant-list-item) {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.article-list :deep(.ant-list-item:hover) {
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.15);
}

.article-list :deep(.ant-list-item-meta-title) {
  margin-bottom: 8px;
}

.article-list :deep(.ant-list-item-meta-title .text-blue-600) {
  color: #4c4de6;
}

.article-list :deep(.ant-list-item-meta-title .text-blue-600:hover) {
  color: #6364e9;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
  margin-top: 12px;
}

/* 视频弹窗样式 */
:deep(.video-modal) {
  max-width: 1000px;
}

:deep(.video-modal .ant-modal-content) {
  padding: 12px;
}

:deep(.video-modal .ant-modal-header) {
  margin-bottom: 8px;
  padding: 8px 12px;
}

:deep(.video-modal .ant-modal-title) {
  font-size: 16px;
  line-height: 1.4;
}

:deep(.video-modal .ant-modal-body) {
  padding: 0;
}

.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
}

.aspect-w-16 > iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

:deep(.ant-card-meta-title) {
  white-space: normal !important;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 视频操作区域样式 */
.video-actions {
  padding: 12px;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f8f8f8;
}

.action-buttons-container {
  display: flex;
  gap: 16px;
  padding: 8px 0;
}

.action-btn {
  width: 120px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 8px;
  font-size: 14px;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  background-color: #4c4de6;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: #5a5bea;
}

.action-btn i {
  font-size: 14px;
}

.summary-btn {
  background-color: #4c4de6;
}

.share-btn {
  background-color: #4c4de6;
}

/* 笔记相关样式 */
.note-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.note-content {
  font-size: 16px;
  line-height: 1.6;
  border-radius: 4px;
}

.error-container {
  border: 1px solid #f5c2c7;
  border-radius: 4px;
}

.processing-steps p {
  margin-bottom: 4px;
}

/* 进度条样式 */
:deep(.ant-progress-bg) {
  background-color: var(--primary-color);
}

/* 重写Ant Design主题色 */
:deep(.ant-btn-primary) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.ant-btn-primary:hover),
:deep(.ant-btn-primary:focus) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

:deep(.ant-btn-link) {
  color: var(--primary-color);
}

:deep(.ant-btn-link:hover),
:deep(.ant-btn-link:focus) {
  color: var(--primary-hover);
}

:deep(.ant-tag-blue) {
  color: var(--primary-color);
  background: rgba(76, 77, 230, 0.1);
  border-color: rgba(76, 77, 230, 0.2);
}

:deep(.ant-select-focused .ant-select-selector) {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}

:deep(.ant-select-item-option-selected:not(.ant-select-item-option-disabled)) {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

:deep(.ant-select-item-option-active:not(.ant-select-item-option-disabled)) {
  background-color: var(--primary-light);
}

:deep(.ant-input:focus),
:deep(.ant-input-focused) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2);
}

:deep(.ant-pagination-item-active) {
  border-color: var(--primary-color);
}

:deep(.ant-pagination-item-active a) {
  color: var(--primary-color);
}

:deep(.ant-pagination-item:hover) {
  border-color: var(--primary-color);
}

:deep(.ant-pagination-item:hover a) {
  color: var(--primary-color);
}

:deep(.ant-pagination-next:hover .ant-pagination-item-link),
:deep(.ant-pagination-prev:hover .ant-pagination-item-link) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}
</style>
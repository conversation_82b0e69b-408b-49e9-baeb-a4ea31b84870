<template>
  <div class="features-page">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-content">
        <div class="logo">
          <span class="logo-text">启智星</span>
          <span class="logo-text-en">EduSpark</span>
        </div>
        <div class="nav-links" :class="{ 'nav-active': menuActive }">
          <router-link to="/">首页</router-link>
          <router-link to="/features" class="active">功能特点</router-link>
          <router-link to="/tools">教学工具</router-link>
          <router-link to="/resources">教学资源</router-link>
          <router-link to="/contact">联系我们</router-link>
        </div>
        <div class="nav-auth">
          <router-link to="/login" class="login-btn" @click="handleLoginClick">登录</router-link>
          <router-link to="/register" class="register-btn">注册</router-link>
        </div>
        <div class="mobile-menu-icon" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1>功能特点</h1>
        <p>启智星EduSpark智能备课平台的核心功能与特色</p>
      </div>
    </div>

    <!-- 全链路AI驱动 -->
    <section class="feature-section">
      <div class="section-content">
        <div class="feature-header">
          <h2>全链路AI驱动</h2>
          <p>首创"教材目录解析→课时分配→教案生成→资源制作→学情反馈"全流程AI驱动</p>
        </div>
        
        <div class="feature-details">
          <div class="feature-image">
            <div class="image-placeholder">
              <i class="fas fa-robot"></i>
            </div>
          </div>
          <div class="feature-text">
            <h3>高效智能的教学流程</h3>
            <ul>
              <li>5分钟解析教材结构，快速理解知识体系</li>
              <li>10分钟生成教案+PPT+习题，效率提升95%</li>
              <li>教师可动态调整内容，兼顾效率与灵活性</li>
              <li>全流程数据化，实现教学闭环优化</li>
            </ul>
            <p>启智星将繁琐的备课流程自动化、智能化，助力教师从"机械劳动"转向"教学设计"，真正实现以学定教。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 多模态智能解析引擎 -->
    <section class="feature-section alt-bg">
      <div class="section-content">
        <div class="feature-header">
          <h2>多模态智能解析引擎</h2>
          <p>融合OCR识别、NLP生成、Manim动画、BERT批改技术，覆盖文本、图像、语音多模态处理</p>
        </div>
        
        <div class="feature-details reverse">
          <div class="feature-image">
            <div class="image-placeholder">
              <i class="fas fa-brain"></i>
            </div>
          </div>
          <div class="feature-text">
            <h3>强大的多模态处理能力</h3>
            <ul>
              <li>OCR技术自动识别教材结构与知识点</li>
              <li>NLP技术生成高质量教案和教学资源</li>
              <li>Manim动画引擎将抽象概念可视化</li>
              <li>BERT批改技术实现全题型自动评分</li>
            </ul>
            <p>启智星攻克手写计算题步骤校验、简答题语义评分等教育场景难题，为教师提供全方位的智能支持。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 动态人机协同 -->
    <section class="feature-section">
      <div class="section-content">
        <div class="feature-header">
          <h2>动态人机协同</h2>
          <p>教师通过多轮对话实时优化教案，保留教学主动权</p>
        </div>
        
        <div class="feature-details">
          <div class="feature-image">
            <div class="image-placeholder">
              <i class="fas fa-comments"></i>
            </div>
          </div>
          <div class="feature-text">
            <h3>智能协作，不替代教师</h3>
            <ul>
              <li>教师通过对话窗口与AI实时调整教案内容</li>
              <li>动态Prompt生成技术响应速度＜3秒</li>
              <li>保留教师教学主动权，实现"AI辅助不替代"</li>
              <li>人机协同提升教学质量与效率</li>
            </ul>
            <p>启智星尊重教师的专业性，通过人机协同模式，让AI成为教师的得力助手，而非替代者。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 精准推荐引擎 -->
    <section class="feature-section alt-bg">
      <div class="section-content">
        <div class="feature-header">
          <h2>精准推荐引擎</h2>
          <p>基于动态知识图谱与深度学习模型，实现"教—学—练"闭环优化</p>
        </div>
        
        <div class="feature-details reverse">
          <div class="feature-image">
            <div class="image-placeholder">
              <i class="fas fa-chart-network"></i>
            </div>
          </div>
          <div class="feature-text">
            <h3>个性化学习路径推荐</h3>
            <ul>
              <li>基于学生作答数据构建动态知识图谱</li>
              <li>精准识别学生知识薄弱点</li>
              <li>为教师提供针对性教学建议</li>
              <li>为学生推荐个性化学习资源，资源匹配度90%+</li>
            </ul>
            <p>启智星通过精准推荐引擎，实现教学过程的闭环优化，让每个学生都能获得最适合的学习资源。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术架构 -->
    <section class="feature-section">
      <div class="section-content">
        <div class="feature-header">
          <h2>技术架构</h2>
          <p>采用现代化技术栈，确保系统稳定、高效、可扩展</p>
        </div>
        
        <div class="tech-grid">
          <div class="tech-card">
            <div class="tech-icon">
              <i class="fas fa-desktop"></i>
            </div>
            <h3>前端技术</h3>
            <p>Vue 3 + Ant Design Vue，双端交互设计（教师Web/学生Web）</p>
          </div>
          
          <div class="tech-card">
            <div class="tech-icon">
              <i class="fas fa-server"></i>
            </div>
            <h3>后端技术</h3>
            <p>Node.js微服务 + Python（Flask），集成JWT认证与异步任务队列</p>
          </div>
          
          <div class="tech-card">
            <div class="tech-icon">
              <i class="fas fa-database"></i>
            </div>
            <h3>数据存储</h3>
            <p>MySQL + Elasticsearch，构建动态知识图谱</p>
          </div>
          
          <div class="tech-card">
            <div class="tech-icon">
              <i class="fas fa-cogs"></i>
            </div>
            <h3>AI工具链</h3>
            <p>OpenAI API、火山引擎OCR、BERT模型、Manim动画引擎</p>
          </div>
          
          <div class="tech-card">
            <div class="tech-icon">
              <i class="fas fa-cloud"></i>
            </div>
            <h3>部署方案</h3>
            <p>Docker容器化 + Nginx，支持云端/本地私有化部署</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-section">
          <h4>关于我们</h4>
          <p>启智星致力于为教育工作者提供智能化教学解决方案，点亮教育之星</p>
        </div>
        <div class="footer-section">
          <h4>联系方式</h4>
          <p>邮箱：<EMAIL></p>
          <p>电话：400-123-4567</p>
          <p>地址：北京市海淀区中关村</p>
        </div>
        <div class="footer-section">
          <h4>快速链接</h4>
          <router-link to="/about">关于我们</router-link>
          <router-link to="/privacy">隐私政策</router-link>
          <router-link to="/terms">使用条款</router-link>
          <router-link to="/help">帮助中心</router-link>
        </div>
        <div class="footer-section">
          <h4>订阅我们</h4>
          <p>获取最新的教育科技资讯和更新</p>
          <div class="subscribe-form">
            <input type="email" placeholder="输入您的邮箱" />
            <button>订阅</button>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 启智星 EduSpark. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const menuActive = ref(false);

function toggleMenu() {
  menuActive.value = !menuActive.value;
}

function handleLoginClick() {
  // 清除本地存储的登录信息
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
}
</script>

<style scoped>
.features-page {
  min-height: 100vh;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 导航栏样式 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: #1890ff;
  letter-spacing: -0.5px;
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
}

.logo-text-en {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.8;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s;
  font-weight: 500;
}

.nav-links a:hover,
.nav-links a.active {
  color: #1890ff;
}

.nav-auth {
  display: flex;
  gap: 1rem;
}

.login-btn,
.register-btn {
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  transition: all 0.3s;
  font-weight: 500;
}

.login-btn {
  color: #1890ff;
  border: 1px solid #1890ff;
}

.login-btn:hover {
  background: rgba(24, 144, 255, 0.1);
}

.register-btn {
  background: #1890ff;
  color: white;
}

.register-btn:hover {
  background: #40a9ff;
  transform: translateY(-2px);
}

.mobile-menu-icon {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 24px;
  height: 18px;
  cursor: pointer;
}

.mobile-menu-icon span {
  width: 100%;
  height: 2px;
  background-color: #1890ff;
  transition: all 0.3s;
}

/* 页面标题样式 */
.page-header {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  text-align: center;
  margin-top: 64px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #2c3e50;
  font-weight: 700;
}

.page-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

/* 功能部分样式 */
.feature-section {
  padding: 5rem 0;
}

.alt-bg {
  background-color: #f8f9fa;
}

.section-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.feature-header {
  text-align: center;
  margin-bottom: 3rem;
}

.feature-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 700;
}

.feature-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

.feature-details {
  display: flex;
  gap: 3rem;
  align-items: center;
}

.feature-details.reverse {
  flex-direction: row-reverse;
}

.feature-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  width: 300px;
  height: 300px;
  background-color: #e6f7ff;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 6rem;
  color: #1890ff;
}

.feature-text {
  flex: 1;
}

.feature-text h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.feature-text ul {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.feature-text li {
  margin-bottom: 0.8rem;
  color: #4a4a4a;
  line-height: 1.6;
}

.feature-text p {
  color: #666;
  line-height: 1.7;
}

/* 技术架构部分 */
.tech-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.tech-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: all 0.3s ease;
}

.tech-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(24, 144, 255, 0.1);
}

.tech-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 1.5rem;
  background: #e6f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #1890ff;
}

.tech-card h3 {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.tech-card p {
  color: #666;
  line-height: 1.6;
}

/* 页脚样式 */
.footer {
  background: #2c3e50;
  color: white;
  padding: 4rem 0 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 2rem;
}

.footer-section h4 {
  margin-bottom: 1.2rem;
  font-size: 1.2rem;
  position: relative;
  padding-bottom: 0.8rem;
}

.footer-section h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: #1890ff;
}

.footer-section a {
  display: block;
  color: #ccc;
  text-decoration: none;
  margin-bottom: 0.8rem;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: white;
}

.subscribe-form {
  margin-top: 1rem;
  display: flex;
}

.subscribe-form input {
  flex: 1;
  padding: 0.8rem;
  border: none;
  border-radius: 4px 0 0 4px;
  outline: none;
}

.subscribe-form button {
  padding: 0.8rem 1.2rem;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  transition: background 0.3s;
}

.subscribe-form button:hover {
  background: #40a9ff;
}

.footer-bottom {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .feature-details {
    flex-direction: column;
  }
  
  .feature-details.reverse {
    flex-direction: column;
  }
  
  .tech-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .mobile-menu-icon {
    display: flex;
  }
  
  .nav-links {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    flex-direction: column;
    background: white;
    padding: 1rem;
    gap: 1rem;
    box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    display: none;
    text-align: center;
  }
  
  .nav-active {
    display: flex;
  }
  
  .page-header h1 {
    font-size: 2.2rem;
  }
  
  .feature-header h2 {
    font-size: 2rem;
  }
  
  .tech-grid {
    grid-template-columns: 1fr;
  }
  
  .image-placeholder {
    width: 200px;
    height: 200px;
    font-size: 4rem;
  }
}

@media (max-width: 480px) {
  .nav-content {
    padding: 0.8rem;
  }
  
  .logo {
    font-size: 1.5rem;
  }
  
  .login-btn, .register-btn {
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
  }
  
  .page-header {
    padding: 7rem 0 3rem;
  }
  
  .page-header h1 {
    font-size: 1.8rem;
  }
  
  .feature-header h2 {
    font-size: 1.8rem;
  }
  
  .feature-text h3 {
    font-size: 1.5rem;
  }
}
</style>

<template>
  <div class="workstation-container p-6">
    <a-spin :spinning="pageLoading">
      <WelcomeSection
        :teacher-name="teacherInfo.name"
        :current-date="currentDate"
      />

      <div class="main-content">
        <div class="left-section">
          <LeftSidebar />
        </div>
        <div class="right-section">
          <CourseSchedule
            :schedule-data="scheduleData"
            :current-week="currentWeek"
            :total-weeks="totalWeeks"
            :semester="semester"
            @cell-click="handleCellClick"
            @week-change="handleWeekChange"
            @semester-change="handleSemesterChange"
            @refresh="refreshData"
          />
        </div>
      </div>
    </a-spin>

    <!-- 添加课程弹窗 -->
    <a-modal
      v-model:visible="showTeacherCoursesModal"
      :title="null"
      :footer="null"
      :closable="false"
      width="650px"
      :bodyStyle="{ padding: '0' }"
      destroyOnClose
    >
      <TeacherCourses />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import axios from '../../utils/axios.js'
import { useRoute } from 'vue-router'
import WelcomeSection from './components/WelcomeSection.vue'
import CourseSchedule from './components/CourseSchedule.vue'
import LeftSidebar from './components/LeftSidebar.vue'
import TeacherCourses from './components/TeacherCourses.vue'
import type { TeacherInfo, CourseInfo, Syllabus } from './types'
import { useUserStore } from '../../stores/user'

// 状态管理
const teacherInfo = ref<TeacherInfo>({
  id: '',
  name: '加载中...',
  system_teacher_id: '',
  school_teacher_id: '',
  teacher_id: ''
})

const currentDate = ref('')
const pageLoading = ref(false)
const currentWeek = ref(1)
const scheduleData = ref<CourseInfo[]>([])
const semester = ref('2024春季')
const totalWeeks = ref(20)
const loading = ref(false)

// 添加课程弹窗状态
const showTeacherCoursesModal = ref(false)

// 获取路由参数
const route = useRoute()

// 修改handleCellClick函数
const handleCellClick = async (weekday: number, period: number) => {
  const course = scheduleData.value.find(
    item => item.weekday === weekday && item.periods.includes(period)
  );

  if (!course) {
    message.info(`第${weekday}天 第${period}节课: 暂无课程`);
    return;
  }

  message.info(`${course.course_name} - ${course.location}`);
};

// 添加处理周数变化的函数
const handleWeekChange = async (weekNumber: number) => {
  // 更新所有课程的当前周信息
  scheduleData.value = scheduleData.value.map(item => ({
    ...item,
    current_week: weekNumber
  }));
};

// 处理学期切换
const handleSemesterChange = async (newSemester: string) => {
  try {
    loading.value = true;
    const userStore = useUserStore();
    const systemTeacherId = userStore.userInfo?.system_teacher_id;

    if (!systemTeacherId) {
      message.error('未找到教师信息');
      return;
    }

    // 获取指定学期的课表数据
    const response = await axios.get(`/api/courses/schedule/${systemTeacherId}`, {
      params: { semester: newSemester }
    });

    if (response.data.success) {
      semester.value = newSemester;
      scheduleData.value = response.data.data.map(course => ({
        ...course,
        allocation: course.allocation || {},
        completed_periods: course.completed_periods || 0,
        total_periods: course.total_periods || 0,
        teaching_weeks: course.teaching_weeks || { start: 1, end: 20, exclude: [] }
      }));

      if (scheduleData.value.length > 0) {
        totalWeeks.value = scheduleData.value[0].total_weeks;
        currentWeek.value = scheduleData.value[0].current_week;
      }
    } else {
      message.warning(response.data.message || '暂无课表数据');
    }
  } catch (error) {
    console.error('获取课表数据失败:', error);
    message.error('获取课表数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = async () => {
  try {
    loading.value = true;
    const userStore = useUserStore();
    const systemTeacherId = userStore.userInfo?.system_teacher_id;

    if (!systemTeacherId) {
      message.error('未找到教师信息');
      return;
    }

    // 获取当前学期的课表数据
    const response = await axios.get(`/api/courses/schedule/${systemTeacherId}`, {
      params: { semester: semester.value }
    });

    if (response.data.success) {
      scheduleData.value = response.data.data.map(course => ({
        ...course,
        allocation: course.allocation || {},
        completed_periods: course.completed_periods || 0,
        total_periods: course.total_periods || 0,
        teaching_weeks: course.teaching_weeks || { start: 1, end: 20, exclude: [] }
      }));

      if (scheduleData.value.length > 0) {
        totalWeeks.value = scheduleData.value[0].total_weeks;
        currentWeek.value = scheduleData.value[0].current_week;
      }
    } else {
      message.warning(response.data.message || '暂无课表数据');
    }
  } catch (error) {
    console.error('获取课表数据失败:', error);
    message.error('获取课表数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 检查URL参数，如果有openAddCourseModal=true，则打开添加课程弹窗
const checkUrlParams = () => {
  if (route.query.openAddCourseModal === 'true') {
    showTeacherCoursesModal.value = true
  }
}

// 监听路由变化
watch(() => route.query, () => {
  checkUrlParams()
})

// 初始化
onMounted(async () => {
  try {
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr) {
      const parsedUserInfo = JSON.parse(userInfoStr)
      if (parsedUserInfo && parsedUserInfo.name) {
        teacherInfo.value = parsedUserInfo
      } else {
        throw new Error('无效的用户信息')
      }
    } else {
      throw new Error('未找到用户信息')
    }

    // 检查URL参数
    checkUrlParams()

    const now = new Date()
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    } as const
    currentDate.value = now.toLocaleDateString('zh-CN', options)

    // 设置当前学期
    const currentMonth = now.getMonth() + 1
    const currentYear = now.getFullYear()
    semester.value = `${currentYear}${currentMonth >= 2 && currentMonth <= 7 ? '春季' : '秋季'}`

    await refreshData()
  } catch (error) {
    console.error('初始化工作台失败:', error)
    message.error('加载数据失败，请重新登录')
    // 清除无效的登录信息
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    // 重定向到登录页
    window.location.href = '/login'
  }
})
</script>

<style scoped>
.workstation-container {
  min-height: 100vh;
  padding:25px;
  background-color: rgba(246, 248, 252, 0.8);
  background-image:
    radial-gradient(at 47% 33%, rgba(76, 77, 230, 0.04) 0, transparent 59%),
    radial-gradient(at 82% 65%, rgba(118, 201, 255, 0.07) 0, transparent 55%);
  overflow-x: hidden;
}

.main-content {
  display: flex;

  gap: 24px;
  margin-top: 24px;
  overflow: visible;
}

.left-section {
  width: 300px;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.right-section {
  flex: 1;

  min-width: 0;
  position: relative;
  z-index: 1;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(76, 77, 230, 0.15);
  border-radius: 8px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(76, 77, 230, 0.3);
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* 隐藏不必要的滚动条 */
.ant-spin-container {
  overflow: visible !important;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .left-section {
    width: 100%;
  }

  .workstation-container {
    padding: 1rem;
  }
}
</style>
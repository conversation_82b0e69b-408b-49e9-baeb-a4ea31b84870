<script setup>
import { ref, onMounted } from 'vue';

defineProps({
  teacherName: {
    type: String,
    required: true
  },
  currentDate: {
    type: String,
    required: true
  }
});

const greeting = ref('');
const showContent = ref(false);

onMounted(() => {
  // 根据时间设置不同的问候语
  const hour = new Date().getHours();
  if (hour < 12) {
    greeting.value = '早上好';
  } else if (hour < 18) {
    greeting.value = '下午好';
  } else {
    greeting.value = '晚上好';
  }
  
  // 添加入场动画效果
  setTimeout(() => {
    showContent.value = true;
  }, 100);
});
</script>

<template>
  <div class="welcome-section" :class="{ 'show': showContent }">
    <div class="welcome-content">
      <div class="left-section">
        <div class="title-area">
          <h1 class="welcome-title">{{ greeting }}，<span class="name-highlight">{{ teacherN<PERSON> }}</span></h1>
          <p class="welcome-subtitle">欢迎回到教学工作站</p>
        </div>
        <p class="current-date">
          <i class="calendar-icon">📅</i>
          {{ currentDate }}
        </p>
      </div>
      <div class="right-section">
        <div class="decoration-circle"></div>
        <div class="decoration-circle"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.welcome-section {
  background: linear-gradient(135deg, rgba(76, 77, 230, 0.85) 0%, rgba(58, 59, 191, 0.85) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: white;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(76, 77, 230, 0.2);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: translateY(10px);
  margin-bottom: 1.5rem;
  max-width: 100%;
  min-height: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-section:hover {
  box-shadow: 0 12px 36px rgba(76, 77, 230, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.welcome-section.show {
  opacity: 1;
  transform: translateY(0);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.title-area {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.welcome-title {
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  line-height: 1.3;
  margin: 0;
}

.name-highlight {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 0 4px;
}

.welcome-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.current-date {
  display: inline-flex;
  align-items: center;
  font-size: 0.9rem;
  opacity: 0.9;
  background-color: rgba(255, 255, 255, 0.15);
  padding: 0.35rem 0.8rem;
  border-radius: 16px;
  margin: 0;
  align-self: flex-start;
}

.calendar-icon {
  margin-right: 8px;
  font-size: 0.9rem;
}

.right-section {
  position: relative;
  height: 80px;
  width: 80px;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.decoration-circle:nth-child(1) {
  width: 60px;
  height: 60px;
  top: 10px;
  right: 10px;
}

.decoration-circle:nth-child(2) {
  width: 30px;
  height: 30px;
  top: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
  .welcome-section {
    padding: 1.2rem;
  }
  
  .welcome-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .left-section {
    gap: 0.6rem;
    width: 100%;
  }
  
  .right-section {
    display: none;
  }
  
  .welcome-title {
    font-size: 1.1rem;
  }
  
  .welcome-subtitle {
    font-size: 0.8rem;
  }
  
  .current-date {
    font-size: 0.75rem;
  }
}
</style>
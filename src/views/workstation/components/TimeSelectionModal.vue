<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue'
import { CheckOutlined, CloseOutlined, WarningOutlined } from '@ant-design/icons-vue'
import axios from '../../../utils/axios'
import { useUserStore } from '../../../stores/user'
import type { CourseInfo } from '../types'

const props = defineProps<{
  open: boolean;
  selectedTimes: {
    day: number;
    periods: number[];
  }[];
  course_id?: string; // 添加课程ID，用于更新时排除当前课程
  semester?: string; // 添加学期参数
}>()

const emit = defineEmits<{
  (e: 'update:open', open: boolean): void;
  (e: 'confirm', selection: { day: number; periods: number[] }[]): void;
}>()

// 已占用的时间槽
const occupiedTimes = ref<Set<string>>(new Set())
const loading = ref(false)

// 获取已占用时间槽
const fetchOccupiedTimes = async () => {
  try {
    loading.value = true
    const userStore = useUserStore()
    const teacherId = userStore.userInfo?.system_teacher_id

    if (!teacherId) {
      return
    }

    console.log('获取占用时间，当前学期:', props.semester);
    
    // 获取指定学期的课表
    let scheduleData: CourseInfo[] = [];
    if (props.semester) {
      const semesterResponse = await axios.get(`/api/courses/schedule/${teacherId}`, {
        params: { semester: props.semester }
      });
      if (semesterResponse.data.success) {
        scheduleData = semesterResponse.data.data;
        console.log(`获取到${props.semester}学期的${scheduleData.length}条课程数据`);
      }
    } else {
      // 如果没有指定学期，则获取当前课表
      const currentResponse = await axios.get(`/api/courses/schedule/current/${teacherId}`);
      if (currentResponse.data.success) {
        scheduleData = currentResponse.data.data;
        console.log(`获取到当前的${scheduleData.length}条课程数据`);
      }
    }

    occupiedTimes.value.clear();
    
    // 处理当前所有课程的时间
    scheduleData.forEach(schedule => {
      // 如果是更新模式，跳过当前正在编辑的课程
      if (props.course_id && schedule.course_id === props.course_id) {
        console.log('跳过当前编辑的课程:', schedule.course_id);
        return;
      }
      
      console.log('处理课程时间:', schedule.course_name);
      
      // 方法1: 处理新格式数据 (class_times数组)
      if (schedule.class_times && Array.isArray(schedule.class_times)) {
        schedule.class_times.forEach(time => {
          if (time && time.day && Array.isArray(time.periods)) {
            time.periods.forEach(period => {
              const key = `${time.day}-${period}`;
              occupiedTimes.value.add(key);
              console.log('添加占用时间(新格式):', key, schedule.course_name);
            })
          }
        })
      }
      
      // 方法2: 处理旧格式数据
      if (schedule.weekday && schedule.periods && Array.isArray(schedule.periods)) {
        schedule.periods.forEach(period => {
          const key = `${schedule.weekday}-${period}`;
          occupiedTimes.value.add(key);
          console.log('添加占用时间(旧格式):', key, schedule.course_name);
        });
      }
    });
    
    console.log('所有占用时间:', Array.from(occupiedTimes.value));
  } catch (error) {
    console.error('获取已占用时间失败:', error);
  } finally {
    loading.value = false;
  }
}

// 监听弹窗显示
onMounted(() => {
  if (props.open) {
    fetchOccupiedTimes()
    initSelection()
  }
})

// 监听open变化
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    fetchOccupiedTimes()
    initSelection()
  }
})

// 监听semester变化重新获取占用时间
watch(() => props.semester, (newSemester) => {
  if (props.open && newSemester) {
    console.log('学期变化，重新获取占用时间:', newSemester);
    fetchOccupiedTimes();
  }
});

// 获取节次时间
const getPeriodTime = (period: number): string => {
  const timeMap: Record<number, string> = {
    1: '08:00-08:45',
    2: '08:55-09:40',
    3: '09:50-10:35',
    4: '10:45-11:30',
    5: '14:00-14:45',
    6: '14:55-15:40',
    7: '15:50-16:35',
    8: '16:45-17:30'
  }
  return timeMap[period] || ''
}

// 周数选项
const weekdays = [
  { label: '一', value: 1 },
  { label: '二', value: 2 },
  { label: '三', value: 3 },
  { label: '四', value: 4 },
  { label: '五', value: 5 }
]
const periods = Array.from({ length: 8 }, (_, i) => i + 1)

// 选择状态
const selectedCells = ref<Set<string>>(new Set())

// 初始化选择状态
const initSelection = () => {
  selectedCells.value.clear()
  props.selectedTimes.forEach(times => {
    times.periods.forEach(period => {
      selectedCells.value.add(`${times.day}-${period}`)
    })
  })
}

// 处理单元格点击
const handleCellClick = (day: number, period: number) => {
  const key = `${day}-${period}`
  if (selectedCells.value.has(key)) {
    selectedCells.value.delete(key)
  } else {
    selectedCells.value.add(key)
  }
}

// 处理确认
const handleConfirm = () => {
  if (selectedCells.value.size === 0) {
    return
  }
  
  // 将选中的单元格按星期和节次分组
  const selectedKeys = Array.from(selectedCells.value)
  const groupedByDay = selectedKeys.reduce((acc, key) => {
    const [day, period] = key.split('-').map(Number)
    if (!acc[day]) {
      acc[day] = []
    }
    acc[day].push(period)
    return acc
  }, {} as Record<number, number[]>)

  // 获取所有选中的时间
  const selectedTimes = Object.entries(groupedByDay).map(([day, periods]) => ({
    day: Number(day),
    periods: periods.sort((a, b) => a - b)
  }))

  // 按星期排序
  selectedTimes.sort((a, b) => a.day - b.day)
  
  emit('confirm', selectedTimes)
  emit('update:open', false)
}

// 处理取消
const handleCancel = () => {
  emit('update:open', false)
}

// 检查单元格是否被选中
const isSelected = (day: number, period: number) => {
  return selectedCells.value.has(`${day}-${period}`)
}

// 检查单元格是否被占用
const isOccupied = (day: number, period: number): boolean => {
  return occupiedTimes.value.has(`${day}-${period}`) && !isSelected(day, period)
}

// 获取单元格状态样式
const getCellStyle = (day: number, period: number) => {
  const isCellSelected = isSelected(day, period)
  const isCellOccupied = isOccupied(day, period)
  
  return {
    'selected': isCellSelected,
    'occupied': isCellOccupied && !isCellSelected,
    'hover': !isCellSelected && !isCellOccupied
  }
}
</script>

<template>
  <a-modal
    :open="open"
    :title="null"
    @update:open="(val) => emit('update:open', val)"
    :footer="null"
    :closable="false"
    width="800px"
    :bodyStyle="{ padding: '0' }"
  >
    <div class="time-selection-modal">
      <!-- 标题栏 -->
      <div class="modal-header">
        <h3>选择上课时间</h3>
        <a-button type="text" @click="handleCancel">
          <template #icon><CloseOutlined /></template>
        </a-button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin tip="加载课程时间..." />
      </div>

      <!-- 时间表格 -->
      <div v-else class="time-table-container">
        <div class="legend">
          <div class="legend-item">
            <div class="legend-color selected"></div>
            <span>已选择</span>
          </div>
          <div class="legend-item">
            <div class="legend-color occupied"></div>
            <span>已占用</span>
          </div>
          <div class="legend-item">
            <div class="legend-color available"></div>
            <span>可选择</span>
          </div>
        </div>

        <table class="time-table">
          <thead>
            <tr>
              <th class="header-cell">节次/星期</th>
              <th v-for="day in weekdays" :key="day.value" class="header-cell">
                周{{ day.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="period in periods" :key="period">
              <td class="period-cell">
                <div>第{{ period }}节</div>
                <div class="period-time">{{ getPeriodTime(period) }}</div>
              </td>
              <td
                v-for="day in weekdays"
                :key="day.value"
                class="time-cell"
                :class="getCellStyle(day.value, period)"
                @click="!isOccupied(day.value, period) && handleCellClick(day.value, period)"
              >
                <div class="cell-content">
                  <CheckOutlined v-if="isSelected(day.value, period)" class="check-icon" />
                  <WarningOutlined v-else-if="isOccupied(day.value, period)" class="warning-icon" />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 底部按钮 -->
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm">
          确认选择
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<style scoped>
.time-selection-modal {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.time-table-container {
  padding: 24px;
  flex: 1;
  overflow: auto;
}

.time-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 3px;
  background-color: transparent;
}

.header-cell {
  background-color: #f8f9fa;
  padding: 12px;
  text-align: center;
  font-weight: 600;
  color: #4b5563;
  border-radius: 8px;
  height: 48px;
  font-size: 14px;
}

.period-cell {
  background-color: #f8f9fa;
  padding: 8px;
  text-align: center;
  width: 100px;
  border-radius: 8px;
  font-size: 13px;
  color: #4b5563;
}

.period-time {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.time-cell {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 60px;
  position: relative;
}

.time-cell:hover {
  background-color: rgba(76, 77, 230, 0.04);
  border-color: #4c4de6;
}

.time-cell.selected {
  background-color: rgba(76, 77, 230, 0.1);
  border-color: #4c4de6;
}

.cell-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  font-size: 20px;
  color: #4c4de6;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #fafafa;
}

/* 滚动条美化 */
.time-table-container::-webkit-scrollbar {
  width: 6px;
}

.time-table-container::-webkit-scrollbar-track {
  background: transparent;
}

.time-table-container::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.time-table-container::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .time-table-container {
    padding: 16px;
  }

  .header-cell,
  .period-cell {
    padding: 8px;
    font-size: 12px;
  }

  .time-cell {
    height: 50px;
  }

  .check-icon {
    font-size: 16px;
  }
}

.time-cell.occupied {
  background-color: rgba(245, 108, 108, 0.1);
  border-color: #f56c6c;
  cursor: not-allowed;
}

.warning-icon {
  font-size: 20px;
  color: #f56c6c;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.legend {
  display: flex;
  margin-bottom: 16px;
  gap: 16px;
  justify-content: flex-end;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legend-color.selected {
  background-color: rgba(76, 77, 230, 0.1);
  border: 1px solid #4c4de6;
}

.legend-color.occupied {
  background-color: rgba(245, 108, 108, 0.1);
  border: 1px solid #f56c6c;
}

.legend-color.available {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
}
</style> 
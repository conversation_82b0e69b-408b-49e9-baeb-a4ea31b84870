<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'
import { CloseOutlined, CheckOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  open: boolean;
  totalWeeks: number;
  selectedWeeks: {
    start: number;
    end: number;
    exclude: number[];
  };
}>()

const emit = defineEmits<{
  (e: 'update:open', open: boolean): void;
  (e: 'confirm', selection: { start: number; end: number; exclude: number[] }): void;
}>()

// 选中的周数集合
const selectedWeeks = ref<Set<number>>(new Set())

// 初始化选择状态
const initSelection = () => {
  selectedWeeks.value.clear()
  // 将所有周数加入集合
  for (let i = props.selectedWeeks.start; i <= props.selectedWeeks.end; i++) {
    if (!props.selectedWeeks.exclude.includes(i)) {
      selectedWeeks.value.add(i)
    }
  }
}

// 监听弹窗显示状态
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    initSelection()
  }
})

// 处理周数点击
const handleWeekClick = (week: number) => {
  if (selectedWeeks.value.has(week)) {
    selectedWeeks.value.delete(week)
  } else {
    selectedWeeks.value.add(week)
  }
}

// 处理确认
const handleConfirm = () => {
  const weeks = Array.from(selectedWeeks.value).sort((a, b) => a - b)
  if (weeks.length === 0) {
    return
  }

  // 计算连续的起始和结束周，以及排除的周数
  const start = weeks[0]
  const end = weeks[weeks.length - 1]
  const exclude: number[] = []
  
  for (let i = start; i <= end; i++) {
    if (!weeks.includes(i)) {
      exclude.push(i)
    }
  }

  emit('confirm', {
    start,
    end,
    exclude
  })
  emit('update:open', false)
}

// 处理取消
const handleCancel = () => {
  emit('update:open', false)
}

// 检查是否选中
const isSelected = (week: number) => {
  return selectedWeeks.value.has(week)
}

// 获取所有周数
const weeks = Array.from({ length: props.totalWeeks }, (_, i) => i + 1)
</script>

<template>
  <a-modal
    :open="open"
    :title="null"
    @update:open="(val) => emit('update:open', val)"
    :footer="null"
    :closable="false"
    width="600px"
    :bodyStyle="{ padding: '0' }"
  >
    <div class="week-selection-modal">
      <!-- 标题栏 -->
      <div class="modal-header">
        <h3>选择教学周</h3>
        <a-button type="text" @click="handleCancel">
          <template #icon><CloseOutlined /></template>
        </a-button>
      </div>

      <!-- 周数选择区域 -->
      <div class="weeks-container">
        <div class="weeks-grid">
          <div
            v-for="week in weeks"
            :key="week"
            class="week-cell"
            :class="{ selected: isSelected(week) }"
            @click="handleWeekClick(week)"
          >
            <div class="week-content">
              <span class="week-number">第{{ week }}周</span>
              <CheckOutlined v-if="isSelected(week)" class="check-icon" />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm">
          确认选择
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<style scoped>
.week-selection-modal {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.weeks-container {
  padding: 24px;
  flex: 1;
  overflow: auto;
}

.weeks-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.week-cell {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 48px;
  position: relative;
}

.week-cell:hover {
  background-color: rgba(76, 77, 230, 0.04);
  border-color: #4c4de6;
}

.week-cell.selected {
  background-color: rgba(76, 77, 230, 0.1);
  border-color: #4c4de6;
}

.week-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.week-number {
  font-size: 14px;
  color: #4b5563;
}

.check-icon {
  font-size: 16px;
  color: #4c4de6;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #fafafa;
}

/* 滚动条美化 */
.weeks-container::-webkit-scrollbar {
  width: 6px;
}

.weeks-container::-webkit-scrollbar-track {
  background: transparent;
}

.weeks-container::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.weeks-container::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .weeks-container {
    padding: 16px;
  }

  .weeks-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .week-cell {
    height: 40px;
  }

  .week-number {
    font-size: 12px;
  }

  .check-icon {
    font-size: 14px;
  }
}
</style> 
<script setup lang="ts">
import { ref, onMounted, computed, defineEmits } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, CloseOutlined, BookOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import axios from '../../../utils/axios'
import { useUserStore } from '../../../stores/user'
// import type { Course } from '../types'

interface TeacherCourse {
  id: number;
  course_code: string;
  course_name: string;
  description: string;
  credit: number;
  teacher_id: string;
  course_type: 'required' | 'elective';
  status: boolean;
  semester: string;
}

interface SemesterOption {
  label: string;
  value: string;
}

const courses = ref<TeacherCourse[]>([])
const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)

// 定义事件
const emit = defineEmits<{
  (e: 'course-added'): void;
}>()

// 表单数据
const formState = ref({
  id: null as number | null,
  course_code: '',
  course_name: '',
  description: '',
  credit: 1,
  course_type: 'required',
  status: true,
  semester: ''
})

// 编辑模式
const isEditMode = ref(false)

// 删除确认对话框
const deleteConfirmVisible = ref(false)
const courseToDelete = ref<TeacherCourse | null>(null)

// 学期选项
const semesterOptions = computed(() => {
  const years = [2023, 2024, 2025]
  const options: SemesterOption[] = []

  for (const year of years) {
    options.push({
      label: `${year}-${year + 1}-1`,
      value: `${year}-${year + 1}-1`
    })
    options.push({
      label: `${year}-${year + 1}-2`,
      value: `${year}-${year + 1}-2`
    })
  }

  return options
})

// 学分选项
const creditOptions = [
  { label: '1分', value: 1, color: '#ff85c0' },
  { label: '2分', value: 2, color: '#ffa940' },
  { label: '3分', value: 3, color: '#52c41a' },
  { label: '4分', value: 4, color: '#1890ff' },
  { label: '5分', value: 5, color: '#722ed1' }
]

// 添加计算属性来对课程进行排序
const sortedCourses = computed(() => {
  return [...courses.value].sort((a, b) => {
    // 首先按状态排序（开放的在前）
    if (a.status !== b.status) {
      return b.status ? 1 : -1;
    }
    // 然后按学期降序
    if (a.semester !== b.semester) {
      return b.semester.localeCompare(a.semester);
    }
    // 最后按课程代码升序
    return a.course_code.localeCompare(b.course_code);
  });
});

const fetchCourses = async () => {
  try {
    loading.value = true
    const userStore = useUserStore()
    const teacherId = userStore.userInfo?.system_teacher_id

    if (!teacherId) {
      throw new Error('未找到教师ID')
    }

    console.log('获取课程列表，教师ID:', teacherId)
    const response = await axios.get(`/api/teacher-courses/teacher/${teacherId}`)

    if (response.data.success) {
      courses.value = response.data.data
      console.log('获取到的课程列表:', courses.value)
    }
  } catch (error) {
    console.error('获取课程列表失败:', error)
    message.error('获取课程列表失败')
  } finally {
    loading.value = false
  }
}

// 更新课程状态
// 注意：这个函数已由课程项中的编辑功能替代
const updateCourseStatus = async (courseId: number, status: boolean) => {
  try {
    const response = await axios.put(`/api/teacher-courses/status/${courseId}`, {
      status
    })
    if (response.data.success) {
      message.success(response.data.message)
      // 更新本地状态
      const courseIndex = courses.value.findIndex(c => c.id === courseId)
      if (courseIndex !== -1) {
        courses.value[courseIndex].status = status
      }
    }
  } catch (error) {
    console.error('更新课程状态失败:', error)
    message.error('更新课程状态失败')
  }
}

// 显示新增课程弹窗
const showAddModal = () => {
  resetForm()
  modalVisible.value = true
}

// 显示编辑课程弹窗
const showEditModal = (course: TeacherCourse) => {
  formState.value = {
    id: course.id,
    course_code: course.course_code,
    course_name: course.course_name,
    description: course.description || '',
    credit: course.credit,
    course_type: course.course_type,
    status: course.status,
    semester: course.semester
  }
  isEditMode.value = true
  modalVisible.value = true
}

// 显示删除确认对话框
const showDeleteConfirm = (course: TeacherCourse) => {
  courseToDelete.value = course
  deleteConfirmVisible.value = true
}

// 取消删除
const cancelDelete = () => {
  courseToDelete.value = null
  deleteConfirmVisible.value = false
}

// 确认删除课程
const confirmDelete = async () => {
  if (!courseToDelete.value) return

  try {
    // 使用完整的路径进行请求
    console.log('发送删除请求，课程 ID:', courseToDelete.value.id)
    const response = await axios.delete(`/api/teacher-courses/${courseToDelete.value.id}`)
    if (response.data.success) {
      message.success('课程删除成功')
      await fetchCourses() // 刷新课程列表
    }
  } catch (error) {
    console.error('删除课程失败:', error)
    message.error('删除课程失败')
  } finally {
    cancelDelete()
  }
}

// 关闭新增课程弹窗
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  formState.value = {
    id: null,
    course_code: '',
    course_name: '',
    description: '',
    credit: 1,
    course_type: 'required',
    status: true,
    semester: ''
  }
  isEditMode.value = false
}

// 提交表单（新增或编辑）
const handleSubmit = async () => {
  try {
    modalLoading.value = true
    const userStore = useUserStore()
    const teacherId = userStore.userInfo?.system_teacher_id

    if (!teacherId) {
      throw new Error('未找到教师ID')
    }

    let response: any

    if (isEditMode.value) {
      // 编辑模式
      const updateData = {
        ...formState.value,
        teacher_id: teacherId
      }

      // 使用完整的路径进行请求
      console.log('发送更新请求，课程 ID:', formState.value.id)
      response = await axios.put(`/api/teacher-courses/${formState.value.id}`, updateData)

      if (response.data.success) {
        message.success('课程更新成功')
        modalVisible.value = false
        await fetchCourses() // 刷新课程列表
      }
    } else {
      // 新增模式
      // 获取最新的课程代码
      const codeResponse = await axios.get('/api/teacher-courses/next-code')
      const nextCode = codeResponse.data.code

      const submitData = {
        ...formState.value,
        course_code: nextCode,
        teacher_id: teacherId
      }

      response = await axios.post('/api/teacher-courses', submitData)

      if (response.data.success) {
        message.success('课程创建成功')
        modalVisible.value = false
        await fetchCourses() // 刷新课程列表

        // 触发事件通知父组件课程已添加
        emit('course-added')
      }
    }
  } catch (error) {
    console.error(isEditMode.value ? '更新课程失败:' : '创建课程失败:', error)
    message.error(isEditMode.value ? '更新课程失败' : '创建课程失败')
  } finally {
    modalLoading.value = false
  }
}

onMounted(() => {
  fetchCourses()
})
</script>

<template>
  <div class="teacher-courses">
    <div class="header">
      <h3 class="section-title">开设课程</h3>
      <a-button
        type="primary"
        size="small"
        @click="showAddModal"
      >
        <template #icon><PlusOutlined /></template>
        新增课程
      </a-button>
    </div>

    <!-- 修改：移除过多的嵌套结构，直接使用列表容器 -->
    <div class="courses-list-wrapper">
      <a-spin :spinning="loading">
        <template v-if="courses.length > 0">
          <div
            v-for="course in sortedCourses"
            :key="course.id"
            class="course-item"
            :class="{ 'course-closed': !course.status }"
          >
            <div class="course-header">
              <div class="course-name">
                {{ course.course_name }}
                <a-tag
                  :color="course.status ? (course.course_type === 'required' ? '#4c4de6' : '#52c41a') : 'default'"
                  class="course-type"
                >
                  {{ course.course_type === 'required' ? '必修' : '选修' }}
                </a-tag>
              </div>
              <div class="course-code">{{ course.course_code }}</div>
            </div>
            <div class="course-meta">
              <div class="credit-badge" :class="`credit-${course.credit}`">{{ course.credit }}学分</div>
              <span class="semester">{{ course.semester }}</span>
            </div>
            <div class="course-actions">
              <a-button type="text" size="small" @click="showEditModal(course)">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-button type="text" size="small" danger @click="showDeleteConfirm(course)">
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
            </div>
          </div>
        </template>
        <a-empty
          v-else
          description="暂无开设课程"
        />
      </a-spin>
    </div>

    <!-- 新增课程弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="null"
      @cancel="handleCancel"
      :confirmLoading="modalLoading"
      :footer="null"
      :closable="false"
      width="650px"
      :bodyStyle="{ padding: '0' }"
      destroyOnClose
    >
      <div class="modal-content">
        <!-- 自定义标题区域 -->
        <div class="modal-header">
          <div class="modal-title">
            <div class="title-icon">
              <BookOutlined />
            </div>
            <h3>{{ isEditMode ? '编辑课程' : '新增课程' }}</h3>
          </div>
          <a-button type="text" @click="handleCancel" class="close-button">
            <template #icon><CloseOutlined /></template>
          </a-button>
        </div>

        <!-- 表单内容区域 -->
        <div class="form-container">
          <a-form
            :model="formState"
            layout="vertical"
          >
            <!-- 分组：基本信息 -->
            <div class="form-section">
              <div class="section-title">基本信息</div>

              <a-form-item
                label="课程名称"
                name="course_name"
                :rules="[{ required: true, message: '请输入课程名称' }]"
                class="form-item"
              >
                <a-input
                  v-model:value="formState.course_name"
                  placeholder="请输入课程名称"
                />
              </a-form-item>

              <a-form-item
                label="课程描述"
                name="description"
                class="form-item"
              >
                <a-textarea
                  v-model:value="formState.description"
                  placeholder="请输入课程描述"
                  :rows="3"
                />
              </a-form-item>

              <!-- 并排布局 -->
              <div class="form-row">
                <a-form-item
                  label="学分"
                  name="credit"
                  :rules="[{ required: true, message: '请选择学分' }]"
                  class="form-item"
                >
                  <a-radio-group v-model:value="formState.credit" button-style="solid" class="credit-radio-group">
                    <a-radio-button
                      v-for="option in creditOptions"
                      :key="option.value"
                      :value="option.value"
                      class="credit-radio-button"
                      :class="{'selected': formState.credit === option.value}"
                      :style="{
                        '--button-color': option.color,
                        '--button-color-light': option.color + '20'
                      }"
                    >
                      {{ option.label }}
                    </a-radio-button>
                  </a-radio-group>
                </a-form-item>

                <a-form-item
                  label="课程类型"
                  name="course_type"
                  :rules="[{ required: true, message: '请选择课程类型' }]"
                  class="form-item"
                >
                  <a-radio-group v-model:value="formState.course_type" button-style="solid" class="type-radio-group">
                    <a-radio-button
                      value="required"
                      class="type-radio-button"
                      :class="{'selected': formState.course_type === 'required'}"
                    >
                      必修
                    </a-radio-button>
                    <a-radio-button
                      value="elective"
                      class="type-radio-button"
                      :class="{'selected': formState.course_type === 'elective'}"
                    >
                      选修
                    </a-radio-button>
                  </a-radio-group>
                </a-form-item>
              </div>

              <!-- 并排布局 -->
              <div class="form-row">
                <a-form-item
                  label="学期"
                  name="semester"
                  :rules="[{ required: true, message: '请选择学期' }]"
                  class="form-item"
                >
                  <a-select
                    v-model:value="formState.semester"
                    placeholder="请选择学期"
                    :options="semesterOptions"
                    class="semester-select"
                  />
                </a-form-item>

                <a-form-item
                  label="开放状态"
                  name="status"
                  class="form-item switch-item"
                >
                  <div class="switch-container">
                    <a-switch v-model:checked="formState.status" />
                    <span class="switch-text">{{ formState.status ? '开放' : '关闭' }}</span>
                  </div>
                </a-form-item>
              </div>
            </div>
          </a-form>
        </div>

        <!-- 自定义底部按钮 -->
        <div class="modal-footer">
          <a-button @click="handleCancel">取消</a-button>
          <a-button
            type="primary"
            @click="handleSubmit"
            :loading="modalLoading"
            :disabled="!formState.course_name || !formState.semester"
          >
            {{ isEditMode ? '保存修改' : '创建课程' }}
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>

  <!-- 删除确认对话框 -->
  <a-modal
    v-model:visible="deleteConfirmVisible"
    title="删除课程"
    @ok="confirmDelete"
    @cancel="cancelDelete"
    okText="确认删除"
    cancelText="取消"
    :okButtonProps="{ danger: true }"
  >
    <p>确定要删除课程 "{{ courseToDelete?.course_name }}" 吗？</p>
    <p>此操作不可恢复，删除后相关的课程安排也可能会受到影响。</p>
  </a-modal>
</template>

<style scoped>
.teacher-courses {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: 490px; /* 保持固定高度 */
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
  flex-shrink: 0;
}

.section-title {
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

/* 新的包装器，替换原courses-container */
.courses-list-wrapper {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
  margin-bottom: 0.25rem;
}

/* 修改滚动条样式 */
.courses-list-wrapper::-webkit-scrollbar {
  width: 4px;
}

.courses-list-wrapper::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 4px;
}

.courses-list-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 确保a-spin不会影响内容显示 */
:deep(.ant-spin-container) {
  height: 100%;
  overflow: visible;
}

.course-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  background-color: #fafafa;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid #f0f0f0;
  gap: 0.5rem;
  margin-bottom: 0.75rem; /* 保留底部间距 */
}

.course-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 4px;
  border-top: 1px dashed #eaeaea;
}

.course-item:last-child {
  margin-bottom: 0; /* 最后一个项目不需要底部间距 */
}

.course-item:hover {
  box-shadow: 0 2px 10px rgba(76, 77, 230, 0.1);
  border-color: rgba(76, 77, 230, 0.3);
}

.course-closed {
  background-color: #f9f9f9;
  opacity: 0.7;
  border: 1px dashed #e0e0e0;
}

.course-header {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.course-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  line-height: 1.2;
}

.course-code {
  font-size: 0.75rem;
  color: #6b7280;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.credit-badge {
  font-size: 0.7rem;
  border-radius: 12px;
  padding: 0 0.5rem;
  height: 20px;
  line-height: 20px;
  color: white;
  font-weight: 500;
}

.credit-1 { background-color: #ff85c0; }
.credit-2 { background-color: #ffa940; }
.credit-3 { background-color: #52c41a; }
.credit-4 { background-color: #1890ff; }
.credit-5 { background-color: #722ed1; }

.semester {
  font-size: 0.75rem;
  color: #6b7280;
}

.course-type {
  flex-shrink: 0;
  font-size: 0.7rem;
  padding: 0 0.5rem;
  height: 20px;
  line-height: 20px;
}

/* 弹窗样式 */
.modal-content {
  display: flex;
  flex-direction: column;
}

/* 自定义标题区域 */
.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background-color: rgba(76, 77, 230, 0.1);
  color: #4c4de6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.modal-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 表单容器 */
.form-container {
  padding: 20px 24px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 表单滚动条 */
.form-container::-webkit-scrollbar {
  width: 5px;
}

.form-container::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 表单区域 */
.form-section {
  margin-bottom: 22px;
  padding-bottom: 8px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #4c4de6;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #4c4de6;
  border-radius: 2px;
}

/* 表单项 */
.form-item {
  margin-bottom: 16px;
}

.form-item:last-child {
  margin-bottom: 0;
}

/* 并排布局 */
.form-row {
  display: flex;
  gap: 16px;
}

.form-row .form-item {
  flex: 1;
}

/* 开关样式 */
.switch-item.form-item {
  margin-top: 4px;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-text {
  font-size: 14px;
  color: #666;
}

/* 学分按钮样式 */
.credit-radio-group {
  display: flex;
  width: 100%;
}

.credit-radio-button {
  flex: 1;
  text-align: center;
  transition: all 0.3s;
  position: relative;
}

.credit-radio-button.selected {
  color: white !important;
  background-color: var(--button-color) !important;
  border-color: var(--button-color) !important;
}

.credit-radio-button:not(.selected) {
  color: #666 !important;
  background-color: var(--button-color-light) !important;
  border-color: var(--button-color-light) !important;
}

/* 课程类型按钮样式 */
.type-radio-group {
  display: flex;
  width: 100%;
}

.type-radio-button {
  flex: 1;
  text-align: center;
  transition: all 0.3s;
}

.type-radio-button.selected:first-child {
  color: white !important;
  background-color: #4c4de6 !important;
  border-color: #4c4de6 !important;
}

.type-radio-button.selected:last-child {
  color: white !important;
  background-color: #52c41a !important;
  border-color: #52c41a !important;
}

.type-radio-button:not(.selected) {
  color: #666 !important;
}

/* 底部按钮区域 */
.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #fafafa;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .teacher-courses {
    padding: 0.75rem;
    height: 330px; /* 保持移动端也是330px高度 */
  }

  .courses-list-wrapper {
    padding-right: 2px;
  }

  .header {
    margin-bottom: 0.5rem;
  }

  .course-item {
    padding: 0.6rem;
  }

  .course-name {
    font-size: 0.85rem;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-container {
    padding: 16px;
  }

  .modal-header,
  .modal-footer {
    padding: 12px 16px;
  }
}

:deep(.ant-radio-button-wrapper-checked) {
  box-shadow: none !important;
}
</style>
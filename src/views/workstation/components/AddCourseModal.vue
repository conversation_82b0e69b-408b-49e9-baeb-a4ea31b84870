<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  CloseOutlined,
  CalendarOutlined,
  EditOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  BookOutlined
} from '@ant-design/icons-vue'
import type { TeacherCourse, ClassScheduleForm, ClassTime } from '../types'
import { useUserStore } from '../../../stores/user'
import axios from '../../../utils/axios'
import TimeSelectionModal from './TimeSelectionModal.vue'
import WeekSelectionModal from './WeekSelectionModal.vue'
import TeacherCourses from './TeacherCourses.vue'

// 扩展ClassTime类型，添加location字段
interface ExtendedClassTime extends ClassTime {
  location?: string;
}

// 扩展ClassScheduleForm类型，添加id字段
interface ExtendedClassScheduleForm extends ClassScheduleForm {
  id?: number;
  class_times: ExtendedClassTime[];
}

const props = defineProps<{
  open: boolean;
  editMode?: boolean;
  editData?: ExtendedClassScheduleForm;
  currentSemester?: string;
}>()

const emit = defineEmits<{
  (e: 'update:open', open: boolean): void;
  (e: 'success'): void;
}>()

// 表单数据
const formState = ref<ExtendedClassScheduleForm>({
  course_id: '',
  total_weeks: 20,
  start_date: '',
  total_periods: 32,
  is_current: true,
  teaching_weeks: {
    start: 1,
    end: 20,
    exclude: []
  },
  class_times: [],
  location: ''
})

// 编辑模式初始化
watch(() => props.editData, (newData) => {
  if (newData && props.editMode) {
    formState.value = { ...newData }
  }
}, { immediate: true })

// 监听open变化
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    fetchTeacherCourses()
    if (!props.editMode) {
      resetForm()
    }
  }
})

// 重置表单
const resetForm = () => {
  formState.value = {
    course_id: '',
    total_weeks: 20,
    start_date: '',
    total_periods: 32,
    is_current: true,
    teaching_weeks: {
      start: 1,
      end: 20,
      exclude: []
    },
    class_times: [],
    location: ''
  }
}

// 教师课程列表
const teacherCourses = ref<TeacherCourse[]>([])
const loading = ref(false)

// 添加课程弹窗状态
const addCourseModalVisible = ref(false)

// 周数选项
const weekOptions = Array.from({ length: 5 }, (_, i) => ({
  label: `周${['一', '二', '三', '四', '五'][i]}`,
  value: i + 1
}))

// 节次选项
const periodOptions = Array.from({ length: 8 }, (_, i) => ({
  label: `第${i + 1}节`,
  value: i + 1
}))

// 时间选择弹窗状态
const timeSelectionVisible = ref(false)
// 周数选择弹窗状态
const weekSelectionVisible = ref(false)

// 监听总周数变化
watch(() => formState.value.total_weeks, (newWeeks) => {
  // 更新教学周
  formState.value.teaching_weeks = {
    start: 1,
    end: newWeeks,
    exclude: []
  }
})

// 获取教师课程列表
const fetchTeacherCourses = async () => {
  try {
    loading.value = true
    const userStore = useUserStore()
    const teacherId = userStore.userInfo?.system_teacher_id

    if (!teacherId) {
      throw new Error('未找到教师ID')
    }

    const response = await axios.get(`/api/teacher-courses/teacher/${teacherId}`)
    if (response.data.success) {
      // 只获取状态为开放的课程
      teacherCourses.value = response.data.data.filter(
        (course: TeacherCourse) => course.status
      )
    }
  } catch (error) {
    console.error('获取课程列表失败:', error)
    message.error('获取课程列表失败')
  } finally {
    loading.value = false
  }
}

// 显示添加课程弹窗
const showAddCourseModal = () => {
  addCourseModalVisible.value = true
}

// 关闭添加课程弹窗
const handleAddCourseModalClose = () => {
  addCourseModalVisible.value = false
  // 重新获取课程列表
  fetchTeacherCourses()
}

// 检查是否有可用课程
const hasAvailableCourses = computed(() => {
  return teacherCourses.value.length > 0
})

// 计算教学周
const calculateTeachingWeeks = (startDate: string, totalWeeks: number) => {
  const start = 1
  const end = totalWeeks
  return {
    start,
    end,
    exclude: []
  }
}

// 处理开始日期变化
const handleStartDateChange = (date: string) => {
  // 确保日期格式为 YYYY-MM-DD
  formState.value.start_date = date.split('T')[0]
  formState.value.teaching_weeks = calculateTeachingWeeks(
    date,
    formState.value.total_weeks
  )
}

// 处理时间选择确认
const handleTimeSelectionConfirm = (selection: ClassTime[]) => {
  formState.value.class_times = selection
}

// 处理周数选择确认
const handleWeekSelectionConfirm = (selection: { start: number; end: number; exclude: number[] }) => {
  formState.value.teaching_weeks = selection
}

// 获取格式化的时间显示
const getFormattedTimes = computed(() => {
  if (!formState.value.class_times.length) {
    return []
  }

  return formState.value.class_times.map(time => {
    const weekday = ['一', '二', '三', '四', '五'][time.day - 1]
    const location = time.location || formState.value.location || ''
    return {
      weekday,
      periods: time.periods,
      location
    }
  })
})

// 处理单个时间段地点修改
const handleLocationChange = (index: number, location: string) => {
  if (formState.value.class_times[index]) {
    formState.value.class_times[index].location = location
  }
}

// 移除单个时间段
const removeTimeSlot = (index: number) => {
  formState.value.class_times.splice(index, 1)
}

// 设置所有时间段使用相同地点
const setAllLocations = () => {
  if (!formState.value.location) {
    message.warning('请先填写上课地点')
    return
  }

  formState.value.class_times = formState.value.class_times.map(time => ({
    ...time,
    location: formState.value.location
  }))
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    if (!formState.value.course_id) {
      return message.error('请选择课程')
    }

    if (!formState.value.start_date) {
      return message.error('请选择开始日期')
    }

    if (!formState.value.class_times.length) {
      return message.error('请添加至少一个上课时间')
    }

    // 检查每个上课时间是否都有地点
    const hasEmptyLocation = formState.value.class_times.some(time => !time.location)
    if (hasEmptyLocation) {
      return message.error('请为所有上课时间设置地点')
    }

    loading.value = true

    const userStore = useUserStore()
    const teacherId = userStore.userInfo?.system_teacher_id

    if (!teacherId) {
      throw new Error('未找到教师ID')
    }

    // 准备提交数据
    const submitData = {
      course_id: formState.value.course_id,
      total_weeks: formState.value.total_weeks,
      start_date: formState.value.start_date,
      total_periods: formState.value.total_periods,
      is_current: formState.value.is_current,
      teaching_weeks: formState.value.teaching_weeks,
      class_times: formState.value.class_times
    }

    let response
    if (props.editMode && formState.value.id) {
      // 更新模式
      response = await axios.put(`/api/courses/schedule/${formState.value.id}`, submitData)
    } else {
      // 创建模式
      response = await axios.post('/api/courses/schedule', submitData)
    }

    if (response.data.success) {
      message.success(props.editMode ? '更新课程成功' : '添加课程成功')
      emit('success')
      handleCancel()
    }
  } catch (error) {
    console.error(props.editMode ? '更新课程失败:' : '添加课程失败:', error)
    message.error(error.message || (props.editMode ? '更新课程失败' : '添加课程失败'))
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  resetForm()
  emit('update:open', false)
}
</script>

<template>
  <a-modal
    :open="open"
    :title="null"
    @update:open="(val) => emit('update:open', val)"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirmLoading="loading"
    width="800px"
    :bodyStyle="{ padding: '0' }"
    :footer="null"
    :closable="false"
    destroyOnClose
  >
    <div class="modal-content">
      <!-- 自定义标题区域 -->
      <div class="modal-header">
        <div class="modal-title">
          <div class="title-icon">
            <PlusOutlined v-if="!editMode" />
            <EditOutlined v-else />
          </div>
          <h3>{{ editMode ? '编辑课程安排' : '添加课程安排' }}</h3>
        </div>
        <a-button type="text" @click="handleCancel" class="close-button">
          <template #icon><CloseOutlined /></template>
        </a-button>
      </div>

      <!-- 表单内容区域 -->
      <div class="form-container">
        <a-form
          :model="formState"
          layout="vertical"
        >
          <!-- 分组：基础信息 -->
          <div class="form-section">
            <div class="section-title">基础信息</div>

            <!-- 课程选择 -->
            <a-form-item
              label="课程"
              required
              class="form-item"
            >
              <div>
                <a-select
                  v-model:value="formState.course_id"
                  placeholder="请选择课程"
                  :loading="loading"
                  :disabled="editMode"
                  class="course-select"
                >
                  <a-select-option
                    v-for="course in teacherCourses"
                    :key="course.course_code"
                    :value="course.course_code"
                  >
                    <div class="course-option">
                      <span class="course-name">{{ course.course_name }}</span>
                      <span class="course-code">{{ course.course_code }}</span>
                    </div>
                  </a-select-option>
                </a-select>

                <!-- 无课程时显示添加课程按钮 -->
                <div v-if="!hasAvailableCourses && !loading" class="no-courses-tip">
                  <p>暂无可用课程</p>
                  <a-button type="primary" size="small" @click="showAddCourseModal">
                    <template #icon><PlusOutlined /></template>
                    添加新课程
                  </a-button>
                </div>
              </div>
            </a-form-item>

            <!-- 并排布局 -->
            <div class="form-row">
              <!-- 开始日期 -->
              <a-form-item
                label="开始日期"
                required
                class="form-item"
              >
                <a-date-picker
                  v-model:value="formState.start_date"
                  style="width: 100%"
                  @change="handleStartDateChange"
                  placeholder="选择课程开始日期"
                />
              </a-form-item>

              <!-- 是否当前使用 -->
              <a-form-item
                label="当前学期课程"
                class="form-item switch-item"
              >
                <div class="switch-container">
                  <a-switch v-model:checked="formState.is_current" />
                  <span class="switch-text">{{ formState.is_current ? '是' : '否' }}</span>
                </div>
              </a-form-item>
            </div>

            <!-- 并排布局 -->
            <div class="form-row">
              <!-- 总周数 -->
              <a-form-item
                label="总周数"
                required
                class="form-item"
              >
                <a-input-number
                  v-model:value="formState.total_weeks"
                  :min="1"
                  :max="30"
                  style="width: 100%"
                  placeholder="输入总周数"
                />
              </a-form-item>

              <!-- 总课时 -->
              <a-form-item
                label="总课时"
                required
                class="form-item"
              >
                <a-input-number
                  v-model:value="formState.total_periods"
                  :min="1"
                  style="width: 100%"
                  placeholder="输入总课时"
                />
              </a-form-item>
            </div>

            <!-- 教学周设置 -->
            <a-form-item
              label="教学周设置"
              class="form-item"
            >
              <div class="teaching-weeks-display">
                <div class="weeks-info">
                  <CalendarOutlined class="icon" />
                  <span>
                    第{{ formState.teaching_weeks.start }}周至第{{ formState.teaching_weeks.end }}周
                    <template v-if="formState.teaching_weeks.exclude.length">
                      <span class="exclude-weeks">
                        (排除第{{ formState.teaching_weeks.exclude.join('、') }}周)
                      </span>
                    </template>
                  </span>
                </div>
                <a-button type="link" size="small" class="edit-button" @click="weekSelectionVisible = true">
                  <template #icon><EditOutlined /></template>
                  编辑排除周
                </a-button>
              </div>
            </a-form-item>
          </div>

          <!-- 分组：时间安排 -->
          <div class="form-section">
            <div class="section-title">时间安排</div>

            <!-- 默认上课地点 -->
            <a-form-item
              label="默认上课地点"
              class="form-item"
            >
              <div class="location-input-group">
                <a-input
                  v-model:value="formState.location"
                  placeholder="请输入默认上课地点"
                  class="location-input"
                >
                  <template #prefix>
                    <EnvironmentOutlined />
                  </template>
                </a-input>
                <a-button
                  type="primary"
                  size="middle"
                  @click="setAllLocations"
                  :disabled="!formState.location || !formState.class_times.length"
                >
                  应用到所有
                </a-button>
              </div>
            </a-form-item>

            <!-- 上课时间 -->
            <a-form-item
              label="上课时间"
              required
              class="form-item"
            >
              <a-space direction="vertical" style="width: 100%" size="middle">
                <div class="time-selection-button" @click="timeSelectionVisible = true">
                  <div class="button-content">
                    <CalendarOutlined />
                    <span>选择上课时间</span>
                  </div>
                  <EditOutlined class="edit-icon" />
                </div>

                <!-- 已选择的时间列表 -->
                <div v-if="formState.class_times.length" class="selected-times-list">
                  <div v-for="(time, index) in getFormattedTimes" :key="index" class="time-slot-item">
                    <div class="time-slot-content">
                      <div class="time-info">
                        <CalendarOutlined />
                        <span>周{{ time.weekday }}第{{ time.periods.join('、') }}节</span>
                      </div>

                      <div class="time-location">
                        <a-input
                          v-model:value="formState.class_times[index].location"
                          placeholder="输入此时段上课地点"
                          size="small"
                          @change="(e) => handleLocationChange(index, e.target.value)"
                        >
                          <template #prefix>
                            <EnvironmentOutlined />
                          </template>
                        </a-input>

                        <a-button
                          type="text"
                          danger
                          size="small"
                          @click="removeTimeSlot(index)"
                          class="remove-button"
                        >
                          <template #icon><DeleteOutlined /></template>
                        </a-button>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-times">
                  <p>请选择上课时间</p>
                </div>
              </a-space>
            </a-form-item>
          </div>
        </a-form>
      </div>

      <!-- 自定义底部按钮 -->
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!formState.course_id || !formState.start_date || !formState.class_times.length"
        >
          {{ editMode ? '保存修改' : '确认添加' }}
        </a-button>
      </div>
    </div>

    <!-- 时间选择弹窗 -->
    <TimeSelectionModal
      v-model:open="timeSelectionVisible"
      :selectedTimes="formState.class_times"
      :course_id="props.editMode ? formState.course_id : undefined"
      :semester="props.currentSemester"
      @confirm="handleTimeSelectionConfirm"
    />

    <!-- 周数选择弹窗 -->
    <WeekSelectionModal
      v-model:open="weekSelectionVisible"
      :total-weeks="formState.total_weeks"
      :selected-weeks="formState.teaching_weeks"
      @confirm="handleWeekSelectionConfirm"
    />

    <!-- 添加课程弹窗 -->
    <a-modal
      v-model:visible="addCourseModalVisible"
      :title="null"
      @cancel="handleAddCourseModalClose"
      :footer="null"
      :closable="false"
      width="650px"
      :bodyStyle="{ padding: '0' }"
      destroyOnClose
    >
      <div class="modal-content">
        <!-- 自定义标题区域 -->
        <div class="modal-header">
          <div class="modal-title">
            <div class="title-icon">
              <BookOutlined />
            </div>
            <h3>新增课程</h3>
          </div>
          <a-button type="text" @click="handleAddCourseModalClose" class="close-button">
            <template #icon><CloseOutlined /></template>
          </a-button>
        </div>

        <!-- 嵌入TeacherCourses组件 -->
        <div class="embedded-component">
          <TeacherCourses @course-added="handleAddCourseModalClose" />
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<style scoped>
/* 弹窗整体内容容器 */
.modal-content {
  display: flex;
  flex-direction: column;
}

/* 自定义标题区域 */
.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background-color: #e6f7ff;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.modal-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 表单容器 */
.form-container {
  padding: 20px 24px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 表单区域分组 */
.form-section {
  margin-bottom: 22px;
  padding-bottom: 8px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #4c4de6;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #4c4de6;
  border-radius: 2px;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 16px;
}

.form-item:last-child {
  margin-bottom: 0;
}

/* 并排布局 */
.form-row {
  display: flex;
  gap: 16px;
}

.form-row .form-item {
  flex: 1;
}

.switch-item.form-item {
  margin-top: 4px;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-text {
  font-size: 14px;
  color: #666;
}

/* 教学周显示样式 */
.teaching-weeks-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f7fa;
  padding: 10px 14px;
  border-radius: 6px;
  border: 1px solid #eaecef;
}

.weeks-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1f2937;
}

.weeks-info .icon {
  color: #4c4de6;
}

.edit-button {
  padding: 4px 8px;
  height: auto;
}

/* 底部按钮区域 */
.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #fafafa;
}

/* 下拉选择框选项样式 */
.course-option {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.course-name {
  font-weight: 500;
}

.course-code {
  color: #999;
  font-size: 12px;
}

/* 滚动条美化 */
.form-container::-webkit-scrollbar {
  width: 5px;
}

.form-container::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-container {
    padding: 16px;
  }

  .modal-header,
  .modal-footer {
    padding: 12px 16px;
  }
}

/* 无课程提示样式 */
.no-courses-tip {
  margin-top: 12px;
  text-align: center;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.no-courses-tip p {
  margin-bottom: 12px;
  color: #999;
}

/* 嵌入组件样式 */
.embedded-component {
  max-height: 500px;
  overflow: auto;
}

/* 上课时间选择按钮 */
.time-selection-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f5f7fa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-selection-button:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4b5563;
}

.edit-icon {
  color: #6b7280;
  font-size: 14px;
}

/* 已选择时间列表 */
.selected-times-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 5px;
}

.time-slot-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.time-slot-content {
  padding: 10px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #4b5563;
  font-weight: 500;
}

.time-location {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-location .ant-input-affix-wrapper {
  flex: 1;
}

.remove-button {
  color: #f56c6c;
}

.empty-times {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
  background-color: #f9fafb;
  border: 1px dashed #e5e7eb;
  border-radius: 6px;
}

.location-input-group {
  display: flex;
  gap: 8px;
}

.location-input {
  flex: 1;
}

/* 优化滚动条 */
.selected-times-list::-webkit-scrollbar {
  width: 5px;
}

.selected-times-list::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.selected-times-list::-webkit-scrollbar-track {
  background-color: transparent;
}
</style>
<script setup lang="ts">
import {
  BookOutlined,
  TeamOutlined,
  Bar<PERSON><PERSON>Outlined,
  FolderOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

const quickLinks = [
  {
    id: 1,
    title: '教案制作',
    icon: BookOutlined,
    path: '/dashboard/lesson-plan',
    color: '#1890ff'
  },
  {
    id: 2,
    title: '课堂互动',
    icon: TeamOutlined,
    path: '/dashboard/class-interaction',
    color: '#52c41a'
  },
  {
    id: 3,
    title: '成绩分析',
    icon: BarChartOutlined,
    path: '/dashboard/grade-analysis',
    color: '#fa8c16'
  },
  {
    id: 4,
    title: '资源库',
    icon: FolderOutlined,
    path: '/dashboard/resource-center',
    color: '#722ed1'
  },

]
</script>

<template>
  <div class="quick-links">
    <h3 class="section-title">快捷入口</h3>
    <div class="links-grid">
      <router-link
        v-for="link in quickLinks"
        :key="link.id"
        :to="link.path"
        class="link-item"
      >
        <div class="icon-wrapper" :style="{ backgroundColor: link.color + '15' }">
          <component :is="link.icon" class="link-icon" :style="{ color: link.color }" />
        </div>
        <span class="link-title">{{ link.title }}</span>
      </router-link>
    </div>
  </div>
</template>

<style scoped>
.quick-links {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.link-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background-color: #fafafa;
  border-radius: 8px;
  text-decoration: none;
  color: #1f2937;
  transition: all 0.25s ease;
  border: 1px solid #f0f0f0;
}

.link-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  margin-right: 0.75rem;
}

.link-icon {
  font-size: 1.25rem;
}

.link-title {
  font-size: 0.875rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .quick-links {
    padding: 0.75rem;
  }

  .section-title {
    margin-bottom: 0.75rem;
  }

  .links-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .link-item {
    padding: 0.5rem;
  }

  .icon-wrapper {
    width: 32px;
    height: 32px;
    margin-right: 0.5rem;
  }

  .link-icon {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .links-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
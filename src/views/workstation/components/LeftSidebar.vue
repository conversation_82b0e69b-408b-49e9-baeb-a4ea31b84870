<script setup lang="ts">
import QuickLinks from './QuickLinks.vue'
import TeacherCourses from './TeacherCourses.vue'
</script>

<template>
  <div class="left-sidebar">
    <div class="sidebar-section quick-links-section">
      <QuickLinks />
    </div>
    <div class="sidebar-section courses-section">
      <TeacherCourses />
    </div>
  </div>
</template>

<style scoped>
.left-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
  min-height: 0;
  max-height: calc(100vh - 230px); /* 设置最大高度，确保视窗内完整显示 */
  overflow: visible;
}

.sidebar-section {
  display: flex;
  flex-direction: column;
  min-height: 0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.quick-links-section {
  flex: 0 0 auto; /* 不需要伸缩，保持自然高度 */
}

.courses-section {
  flex: 1; /* 占用剩余全部空间 */
  min-height: 0;
  position: relative; /* 确保框架正确 */
}

@media (max-width: 768px) {
  .left-sidebar {
    height: auto;
    max-height: none;
    gap: 0.75rem;
  }

  .sidebar-section {
    margin-bottom: 0.75rem;
  }

  .courses-section {
    height: 400px; /* 移动端固定高度 */
  }

  .sidebar-section:last-child {
    margin-bottom: 0;
  }
}
</style>
<script setup lang="ts">
import { ref, defineEmits, defineProps, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { LeftOutlined, RightOutlined, RollbackOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { CourseInfo } from '../types'
import AddCourseModal from './AddCourseModal.vue'
import axios from '../../../utils/axios'

// 单元格内容接口
interface CellContent {
  name: string;
  code: string;
  room: string;
  semester: string;
  progress: string;
  weekInfo: string;
}

// Props 接口
interface Props {
  scheduleData: Omit<CourseInfo, 'system_teacher_id' | 'user_id'>[];
  currentWeek: number;
  totalWeeks: number;
  semester: string;
}

// 学期选项接口
interface SemesterOption {
  label: string;
  value: string;
  disabled?: boolean;
  class?: string;
}

const props = defineProps<Props>()
const selectedWeek = ref(props.currentWeek)
const currentSemester = ref(props.semester)

const emit = defineEmits<{
  (e: 'cell-click', weekday: number, period: number): void;
  (e: 'week-change', weekNumber: number): void;
  (e: 'semester-change', semester: string): void;
  (e: 'refresh'): void;
}>()

// 监听 currentWeek 的变化
watch(() => props.currentWeek, (newWeek) => {
  selectedWeek.value = newWeek
})

// 监听 selectedWeek 的变化
watch(() => selectedWeek.value, (newWeek) => {
  emit('week-change', newWeek)
})

// 监听 props.semester 的变化
watch(() => props.semester, (newSemester) => {
  currentSemester.value = newSemester
})

// 监听 currentSemester 的变化
watch(() => currentSemester.value, (newSemester) => {
  emit('semester-change', newSemester)
})

const weekdays = ['一', '二', '三', '四', '五']
const periods = Array.from({ length: 8 }, (_, i) => i + 1)

// 检查周数是否在教学周期内
const isWeekInRange = (week: number, teachingWeeks: any): boolean => {
  if (!teachingWeeks) {
    return true;
  }
  
  try {
    // 解析教学周数据
    let weeks = teachingWeeks;
    
    if (typeof teachingWeeks === 'string') {
      try {
        weeks = JSON.parse(teachingWeeks);
      } catch (e) {
        // 仅输出错误，不输出详细信息
        return true; // 解析失败默认显示
      }
    }
    
    // 如果没有完整的周数据结构，默认显示课程
    if (!weeks || typeof weeks !== 'object') {
      return true;
    }
    
    // 检查当前周是否在范围内且不在排除周内
    const start = weeks.start || 1;
    const end = weeks.end || 20;
    const exclude = Array.isArray(weeks.exclude) ? weeks.exclude : [];
    
    const isInRange = week >= start && week <= end && !exclude.includes(week);
    
    return isInRange;
  } catch (error) {
    // 只在真正发生错误时记录错误
    return true; // 发生错误时默认显示课程
  }
}

// 获取指定时间段的课程
const getCourseByPosition = (weekday: number, period: number) => {
  // 遍历所有课程，找到符合条件的课程
  const courses = props.scheduleData.filter(course => {
    // 检查当前周是否在教学周范围内
    const weekInRange = isWeekInRange(selectedWeek.value, course.teaching_weeks);
    if (!weekInRange) {
      return false;
    }
    
    // 方法1: 检查旧格式数据
    if (course.weekday === weekday && Array.isArray(course.periods) && course.periods.includes(period)) {
      return true;
    }
    
    // 方法2: 检查新格式数据 (class_times数组)
    if (course.class_times && Array.isArray(course.class_times)) {
      const foundTimeSlot = course.class_times.some(timeSlot => 
        timeSlot && timeSlot.day === weekday && 
        Array.isArray(timeSlot.periods) && timeSlot.periods.includes(period)
      );
      
      if (foundTimeSlot) {
        return true;
      }
    }
    
    return false;
  });
  
  // 完全禁用日志输出
  return courses.length > 0 ? courses[0] : null;
}

// 计算课程进度
const calculateProgress = (course: Omit<CourseInfo, 'system_teacher_id' | 'user_id'>): string => {
  const completedPercentage = (course.completed_periods / course.total_periods) * 100;
  return `${course.completed_periods}/${course.total_periods}课时 (${completedPercentage.toFixed(1)}%)`;
}

// 生成课程单元格内容
const getCellContent = (weekday: number, period: number): CellContent | null => {
  const course = getCourseByPosition(weekday, period);
  if (!course) return null;
  
  // 获取当前位置的课程地点
  let location = '';
  
  // 方法1: 检查旧格式数据
  if (course.weekday === weekday && Array.isArray(course.periods) && 
      course.periods.includes(period) && course.location) {
    location = course.location;
  }
  
  // 方法2: 检查新格式数据 (class_times数组)
  if (!location && course.class_times && Array.isArray(course.class_times)) {
    // 查找匹配的时间段
    const matchedTimeSlot = course.class_times.find(
      timeSlot => timeSlot && timeSlot.day === weekday && 
                 Array.isArray(timeSlot.periods) && 
                 timeSlot.periods.includes(period)
    );
    
    if (matchedTimeSlot && matchedTimeSlot.location) {
      location = matchedTimeSlot.location;
    }
  }
  
  // 如果仍然没有找到地点，使用默认地点
  if (!location) {
    location = '未设置教室';
  }
  
  return {
    name: course.course_name,
    code: course.course_id,
    room: location,
    semester: course.semester,
    progress: calculateProgress(course),
    weekInfo: `第${course.current_week}/${course.total_weeks}周`
  };
}

// 删除模态框状态
const deleteModalVisible = ref(false)
const courseToDelete = ref<{
  id: number;
  name: string;
  weekday: number;
  periods: number[];
  weekdayName: string;
} | null>(null)
const confirmDeleteLoading = ref(false)

// 处理单元格点击
const handleCellClick = (weekday: number, period: number) => {
  const course = getCachedCourse(weekday, period)
  if (course) {
    // 如果有课程，显示菜单选项
    showCourseMenu(weekday, period, course)
  } else {
    // 如果没有课程，发出点击事件
    emit('cell-click', weekday, period)
  }
}

// 显示课程操作菜单
const showCourseMenu = (weekday: number, period: number, course: any) => {
  // 使用 Ant Design 的 Modal 确认框
  const weekdayName = ['一', '二', '三', '四', '五'][weekday - 1]
  
  // 完全禁用非生产环境日志
  
  courseToDelete.value = {
    id: Number(course.id) || 0, // 确保id是数字类型
    name: course.course_name || '未知课程',
    weekday: weekday,
    periods: [period],
    weekdayName: weekdayName
  }
  
  if (!courseToDelete.value.id) {
    message.error('无法删除：未找到课程ID');
    return;
  }
  
  deleteModalVisible.value = true
}

// 处理删除操作
const handleDeleteConfirm = async () => {
  if (!courseToDelete.value) return
  
  try {
    confirmDeleteLoading.value = true
    
    // 禁用所有日志
    
    const response = await axios.delete(
      `/api/courses/schedule/${courseToDelete.value.id}/time`,
      { 
        data: {
          dayToRemove: courseToDelete.value.weekday,
          periodsToRemove: courseToDelete.value.periods
        }
      }
    )
    
    if (response.data.success) {
      message.success('删除上课时间成功')
      // 关闭模态框并刷新课表
      deleteModalVisible.value = false
      emit('refresh')
    } else {
      message.error(response.data.message || '删除失败')
    }
  } catch (error) {
    // 简化错误输出，只显示必要信息
    
    // 获取详细的错误信息
    let errorMsg = '删除上课时间失败'
    if (error.response) {
      errorMsg = error.response.data.message || errorMsg
    }
    
    message.error(errorMsg)
  } finally {
    confirmDeleteLoading.value = false
  }
}

const handleDeleteCancel = () => {
  deleteModalVisible.value = false
  courseToDelete.value = null
}

// 格式化时间段
const periodTimes = {
  1: '08:00-08:45',
  2: '08:55-09:40',
  3: '09:50-10:35',
  4: '10:45-11:30',
  5: '14:00-14:45',
  6: '14:55-15:40',
  7: '15:50-16:35',
  8: '16:45-17:30'
}

// 生成星期标签
const weekdayLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

// 处理周数切换
const handlePrevWeek = () => {
  if (selectedWeek.value > 1) {
    selectedWeek.value--
  }
}

const handleNextWeek = () => {
  if (selectedWeek.value < props.totalWeeks) {
    selectedWeek.value++
  }
}

// 计算周状态
const weekStatus = computed(() => {
  return {
    isFirstWeek: selectedWeek.value === 1,
    isLastWeek: selectedWeek.value === props.totalWeeks,
    currentWeek: selectedWeek.value
  }
})

const handleBackToCurrent = () => {
  selectedWeek.value = props.currentWeek
  emit('week-change', selectedWeek.value)
}

const showAddModal = ref(false)

const handleAddSuccess = () => {
  // 触发父组件刷新课表
  emit('refresh')
  showAddModal.value = false
}

// 生成学期选项
const semesterOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = [currentYear - 1, currentYear, currentYear + 1]
  
  // 获取所有可能的学期选项
  const allOptions: SemesterOption[] = years.flatMap(year => [
    { 
      label: `${year}春季`, 
      value: `${year}春季`,
      disabled: false,
      class: 'semester-no-course'
    },
    { 
      label: `${year}秋季`, 
      value: `${year}秋季`,
      disabled: false,
      class: 'semester-no-course'
    }
  ])

  // 获取有课程的学期
  const semestersWithCourses = new Set(props.scheduleData.map(course => course.semester))

  // 更新选项状态
  return allOptions.map(option => ({
    ...option,
    class: semestersWithCourses.has(option.value) ? 'semester-has-course' : 'semester-no-course'
  }))
})

// 创建一个缓存课程的计算属性，避免重复查询
const coursesMatrix = computed(() => {
  // 创建一个二维数组，用于存储每个位置的课程
  const matrix = Array(5).fill(0).map(() => Array(8).fill(null))
  
  // 预先计算所有位置的课程，填充矩阵
  for (let weekday = 1; weekday <= 5; weekday++) {
    for (let period = 1; period <= 8; period++) {
      matrix[weekday - 1][period - 1] = getCourseByPosition(weekday, period)
    }
  }
  
  return matrix
})

// 根据缓存获取课程
const getCachedCourse = (weekday: number, period: number) => {
  if (weekday < 1 || weekday > 5 || period < 1 || period > 8) return null
  return coursesMatrix.value[weekday - 1][period - 1]
}

// 创建一个缓存单元格内容的计算属性
const cellContentsMatrix = computed(() => {
  // 创建一个二维数组，用于存储每个位置的单元格内容
  const matrix = Array(5).fill(0).map(() => Array(8).fill(null))
  
  // 预先计算所有位置的单元格内容，填充矩阵
  for (let weekday = 1; weekday <= 5; weekday++) {
    for (let period = 1; period <= 8; period++) {
      const course = getCachedCourse(weekday, period)
      if (course) {
        matrix[weekday - 1][period - 1] = getCellContent(weekday, period)
      }
    }
  }
  
  return matrix
})

// 根据缓存获取单元格内容
const getCachedCellContent = (weekday: number, period: number) => {
  if (weekday < 1 || weekday > 5 || period < 1 || period > 8) return null
  return cellContentsMatrix.value[weekday - 1][period - 1]
}

// 监听数据变化时刷新缓存
watch(() => props.scheduleData, () => {
  // 不需要额外操作，computed会自动重新计算
}, { deep: true })

// 监听周数变化时刷新缓存
watch(() => selectedWeek.value, () => {
  // 不需要额外操作，computed会自动重新计算
})
</script>

<template>
  <div class="course-schedule">
    <div class="schedule-header">
      <div class="header-content">
        <div class="semester-switcher">
          <a-select
            :value="currentSemester"
            style="width: 120px"
            :options="semesterOptions"
            @change="(value) => currentSemester = value"
            :dropdownMatchSelectWidth="false"
            popupClassName="semester-dropdown"
          >
            <template #option="{ value, label, class: className }">
              <div :class="['semester-option', className]">{{ label }}</div>
            </template>
            <template #selection="{ value, label }">
              <div :class="['semester-option', semesterOptions.find(opt => opt.value === value)?.class]">
                {{ label }}
              </div>
            </template>
          </a-select>
        </div>
        <div class="week-switcher">
          <a-button 
            type="link" 
            :disabled="selectedWeek === 1"
            @click="handlePrevWeek"
          >
            <template #icon><LeftOutlined /></template>
          </a-button>
          <span class="week-info">
            第{{ selectedWeek }}/{{ totalWeeks }}周
            <span class="week-tag" :class="{'current-week': selectedWeek === currentWeek}">
              {{ weekStatus.currentWeek }}周
            </span>
          </span>
          <a-button 
            type="link" 
            :disabled="selectedWeek === totalWeeks"
            @click="handleNextWeek"
          >
            <template #icon><RightOutlined /></template>
          </a-button>
        </div>
        <div class="header-actions">
          <a-button 
            type="primary"
            @click="showAddModal = true"
          >
            <template #icon><PlusOutlined /></template>
            添加课程
          </a-button>
        </div>
      </div>
    </div>
    <div class="schedule-container">
      <table class="schedule-table">
        <thead>
          <tr>
            <th class="header-cell time-cell">节次/时间</th>
            <th 
              v-for="day in weekdays" 
              :key="day"
              class="header-cell"
            >
              周{{ day }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="period in periods" :key="period">
            <td class="period-cell">
              <div>第{{ period }}节</div>
              <div class="period-time">{{ periodTimes[period] }}</div>
            </td>
            <td 
              v-for="(day, index) in weekdays" 
              :key="day"
              class="course-cell"
              :class="{ 'has-course': getCachedCourse(index + 1, period) }"
              @click="handleCellClick(index + 1, period)"
            >
              <template v-if="getCachedCourse(index + 1, period)">
                <div class="course-content">
                  <div class="course-name">
                    {{ getCachedCourse(index + 1, period)?.course_name }}
                  </div>
                  <div class="classroom">
                    {{ getCachedCellContent(index + 1, period)?.room }}
                  </div>
                  <a-button 
                    type="text" 
                    danger 
                    class="delete-btn"
                    @click.stop="showCourseMenu(index + 1, period, getCachedCourse(index + 1, period))"
                  >
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </div>
              </template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- 添加返回本周的悬浮按钮 -->
    <div 
      v-show="selectedWeek !== currentWeek" 
      class="back-to-current"
      @click="handleBackToCurrent"
    >
      <a-button type="primary" shape="circle">
        <template #icon><RollbackOutlined /></template>
      </a-button>
    </div>
    <!-- 添加课程弹窗 -->
    <AddCourseModal
      :open="showAddModal"
      :currentSemester="currentSemester"
      @update:open="showAddModal = $event"
      @success="handleAddSuccess"
    />
    <!-- 添加删除上课时间的模态框 -->
    <a-modal
      v-model:open="deleteModalVisible"
      :confirm-loading="confirmDeleteLoading"
      @ok="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
    >
      <template #title>删除上课时间</template>
      <p>确定要删除 {{ courseToDelete?.name }} 在 {{ courseToDelete?.weekdayName }} 的上课时间吗？</p>
    </a-modal>
  </div>
</template>

<style scoped>
.course-schedule {
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(76, 77, 230, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.4);
  overflow: hidden;
  height: 700px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.course-schedule:hover {
  box-shadow: 0 8px 32px rgba(76, 77, 230, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.schedule-header {
  padding: 10px;
  font-size: 14px;
  color: #1f2937;
  border-bottom: 1px solid rgba(76, 77, 230, 0.08);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(250, 250, 250, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  flex-shrink: 0;
}

.header-content {
  width: 100%;
  max-width: 1200px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.semester-switcher {
  min-width: 140px;
}

.week-switcher {
  display: flex;
  align-items: center;
  gap: 12px;
}

.week-info {
  font-size: 14px;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  justify-content: center;
  font-weight: 500;
}

.week-tag {
  font-size: 12px;
  padding: 3px 10px;
  border-radius: 12px;
  background-color: rgba(245, 245, 245, 0.8);
  color: #6b7280;
}

.week-tag.current-week {
  background-color: rgba(76, 77, 230, 0.1);
  color: #4c4de6;
  font-weight: 600;
}

.schedule-container {
  flex: 1;
  overflow: auto;
  padding: 12px;
  min-height: 0;
  background-color: rgba(250, 250, 250, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.schedule-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 3px;
  background-color: transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.header-cell {
  background-color: rgba(248, 249, 250, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  padding: 8px;
  text-align: center;
  font-size: 13px;
  color: #4b5563;
  font-weight: 600;
  white-space: nowrap;
  height: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.time-cell {
  width: 100px;
  background-color: rgba(76, 77, 230, 0.05);
  color: #4c4de6;
}

.period-cell {
  padding: 4px;
  text-align: center;
  background-color: rgba(76, 77, 230, 0.05);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  font-size: 12px;
  color: #4c4de6;
  white-space: nowrap;
  height: 70px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.period-time {
  font-size: 11px;
  color: #6b7280;
  margin-top: 4px;
}

.course-cell {
  background-color: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  padding: 6px;
  text-align: center;
  vertical-align: middle;
  height: 70px;
  min-width: 120px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.course-cell:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-color: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.course-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 6px;
  position: relative;
}

.course-name {
  font-size: 14px;
  color: #4c4de6;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.3;
  max-width: 100%;
  text-align: center;
}

.classroom {
  color: #6b7280;
  font-size: 12px;
  text-align: center;
  background-color: rgba(76, 77, 230, 0.05);
  padding: 2px 8px;
  border-radius: 12px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.has-course {
  background: linear-gradient(135deg, rgba(76, 77, 230, 0.05) 0%, rgba(76, 77, 230, 0.15) 100%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(76, 77, 230, 0.15);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.1);
}

.has-course:hover {
  background: linear-gradient(135deg, rgba(76, 77, 230, 0.08) 0%, rgba(76, 77, 230, 0.2) 100%);
  border-color: rgba(76, 77, 230, 0.25);
  box-shadow: 0 6px 16px rgba(76, 77, 230, 0.15);
}

/* 返回本周按钮样式 */
.back-to-current {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 1000;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-to-current:hover {
  transform: scale(1.1);
}

.back-to-current :deep(.ant-btn) {
  background-color: rgba(76, 77, 230, 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-color: rgba(76, 77, 230, 0.2);
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.3);
}

.back-to-current :deep(.ant-btn:hover) {
  background-color: rgba(86, 87, 232, 0.9);
  border-color: rgba(76, 77, 230, 0.3);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .course-schedule {
    height: auto;
    min-height: 540px;
    border-radius: 12px;
  }

  .schedule-header {
    padding: 12px;
  }

  .schedule-container {
    padding: 8px;
  }

  .course-cell {
    height: 65px;
    min-width: 100px;
    padding: 4px;
  }

  .header-cell,
  .period-cell {
    padding: 6px 4px;
    font-size: 12px;
    height: 36px;
    border-radius: 6px;
  }

  .period-cell {
    height: 65px;
  }

  .course-name {
    font-size: 12px;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }

  .classroom {
    font-size: 11px;
    padding: 1px 4px;
  }

  .period-time {
    font-size: 10px;
  }

  .week-info {
    font-size: 12px;
    min-width: 160px;
  }

  .week-tag {
    font-size: 11px;
    padding: 1px 6px;
  }

  .back-to-current {
    right: 16px;
    bottom: 16px;
  }

  .header-content {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .semester-switcher {
    width: 100%;
    order: -1;
  }
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions :deep(.ant-btn-primary) {
  background-color: rgba(76, 77, 230, 0.9);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-color: rgba(76, 77, 230, 0.2);
}

.header-actions :deep(.ant-btn-primary:hover) {
  background-color: rgba(86, 87, 232, 0.9);
  border-color: rgba(76, 77, 230, 0.3);
}

.week-switcher :deep(.ant-btn-link) {
  color: #4c4de6;
}

.week-switcher :deep(.ant-btn-link:hover:not(:disabled)) {
  color: #5657e8;
}

.week-switcher :deep(.ant-btn-link:disabled) {
  color: rgba(76, 77, 230, 0.3);
}

/* 学期选择器样式 */
.semester-option {
  padding: 6px 10px;
  border-radius: 8px;
  width: 100%;
}

:deep(.semester-has-course) {
  color: #4c4de6;
  font-weight: 600;
  position: relative;
  
  &::before {
    content: "●";
    margin-right: 6px;
    font-size: 12px;
  }
}

:deep(.semester-no-course) {
  color: #6b7280;
  
  &::before {
    content: "○";
    margin-right: 6px;
    font-size: 12px;
  }
}

:deep(.semester-dropdown) {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.8) !important;
  
  .ant-select-item {
    padding: 6px 10px;
    margin: 4px 0;
    border-radius: 8px;
  }

  .ant-select-item-option-selected {
    .semester-has-course {
      background-color: rgba(76, 77, 230, 0.1);
      color: #4c4de6;
    }
    
    .semester-no-course {
      background-color: rgba(245, 245, 245, 0.8);
      color: #6b7280;
    }
  }

  .ant-select-item-option-active {
    .semester-has-course, .semester-no-course {
      background-color: rgba(76, 77, 230, 0.05);
    }
  }
}

:deep(.ant-select-selector) {
  background-color: rgba(255, 255, 255, 0.6) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  border-radius: 8px !important;
  border-color: rgba(76, 77, 230, 0.15) !important;
  
  &:hover {
    border-color: rgba(76, 77, 230, 0.3) !important;
  }
  
  .ant-select-selection-item {
    display: flex;
    align-items: center;
  }
}

:deep(.ant-select-focused .ant-select-selector) {
  border-color: rgba(76, 77, 230, 0.4) !important;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.1) !important;
}

/* 自定义滚动条 */
.schedule-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.schedule-container::-webkit-scrollbar-track {
  background: transparent;
}

.schedule-container::-webkit-scrollbar-thumb {
  background-color: rgba(76, 77, 230, 0.2);
  border-radius: 6px;
}

.schedule-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(76, 77, 230, 0.3);
}

/* 添加删除按钮样式 */
.delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0; /* 默认隐藏 */
  transition: opacity 0.2s ease;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 0;
}

.course-cell:hover .delete-btn {
  opacity: 1; /* 鼠标悬停时显示 */
}

.delete-btn:hover {
  background-color: #fff1f0;
  color: #ff4d4f;
}
</style>
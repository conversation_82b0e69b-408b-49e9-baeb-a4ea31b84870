// 课程信息接口
interface CourseInfo {
  id: number;
  course_id: string;
  user_id: string;
  course_name: string;
  schedule_name: string;
  semester: string;
  total_weeks: number;
  current_week: number;
  class_times: ClassTime[];
  // 兼容旧格式
  weekday?: number;
  periods?: number[];
  location?: string;
  teaching_weeks: {
    start: number;
    end: number;
    exclude: number[];
  };
  total_hours: number;
  completed_periods: number;
  total_periods: number;
  course_type: string;
  credit: number;
  is_active: boolean;
  allocation?: Record<string, string[]>;
  weeks?: string;
}

// 课程基本信息
interface Course {
  id: number;
  course_name: string;
  course_id: string;
  course_type: string;
  total_hours: number;
  completed_hours: number;
  description?: string;
  credit: number;
  schedules?: CourseInfo[];
}

// 教师信息
interface TeacherInfo {
  id: string;
  name: string;
  system_teacher_id: string;
  school_teacher_id: string;
  subject?: string;
  teacher_id?: string;
}

// 教学大纲
interface Syllabus {
  id: number;
  course_id: string;
  course_name: string;
  content: string | Record<string, any>;
  allocation: string;
  total_hours: number;
  hourNumber?: number;
  hourContent?: string | null;
  weekday?: number;
  periods?: number[];
  totalHours?: number;
}

// 课表
interface Schedule {
  id: number;
  user_id: string;
  schedule_name: string;
  semester: string;
  is_current: boolean;
  current_week: number;
  total_weeks: number;
  start_date: string;
  end_date: string;
  created_at: string;
}

// 课表响应
interface ScheduleResponse {
  schedule: Schedule;
  details: CourseInfo[];
}

// 课时数据
interface HourData {
  hourNumber: number;
  content: string | null;
  totalHours: number;
}

// 课程时间
interface ClassTime {
  day: number;
  periods: number[];
  location?: string;
}

// 课程表添加表单
interface ClassScheduleForm {
  course_id: string;
  total_weeks: number;
  start_date: string;
  total_periods: number;
  is_current: boolean;
  teaching_weeks: {
    start: number;
    end: number;
    exclude: number[];
  };
  class_times: ClassTime[];
  location: string;
}

// 教师课程
interface TeacherCourse {
  course_code: string;
  course_name: string;
  status: boolean;
}

// 导出所有类型
export type {
  CourseInfo,
  Course,
  TeacherInfo,
  Syllabus,
  Schedule,
  ScheduleResponse,
  HourData,
  ClassScheduleForm,
  TeacherCourse,
  ClassTime
} 
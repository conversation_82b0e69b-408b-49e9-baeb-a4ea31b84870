<template>
  <div class="class-interaction p-4">
    <el-card class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">我的班级</h2>
          <el-button type="primary" @click="showCreateClassDialog = true">
            创建新班级
          </el-button>
        </div>
      </template>

      <el-table :data="classes" v-loading="loading" stripe>
        <el-table-column prop="class_name" label="班级名称" />
        <el-table-column prop="course_name" label="课程名称" />
        <el-table-column prop="semester" label="学期" />
        <el-table-column prop="student_count" label="学生人数">
          <template #default="{ row }">
            {{ row.current_students }}/{{ row.max_students }}
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'info'">
              {{ row.status === 1 ? '开放' : '关闭' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #default="{ row }">
            <el-button-group>
              <el-button type="primary" @click="showStudentList(row)">
                学生列表
              </el-button>
              <el-button type="success" @click="enterClass(row)">
                进入课堂
              </el-button>
              <el-button type="info" @click="showEditDialog(row)">
                编辑
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑班级对话框 -->
    <el-dialog
      :title="isEditing ? '编辑班级' : '创建新班级'"
      v-model="showCreateClassDialog"
      width="500px"
    >
      <el-form
        ref="classFormRef"
        :model="classForm"
        :rules="classRules"
        label-width="100px"
      >
        <el-form-item label="课程" prop="course_code">
          <el-select v-model="classForm.course_code" placeholder="选择课程">
            <el-option
              v-for="course in courses"
              :key="course.course_code"
              :label="course.course_name"
              :value="course.course_code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="班级名称" prop="class_name">
          <el-input v-model="classForm.class_name" placeholder="请输入班级名称" />
        </el-form-item>
        <el-form-item label="学期" prop="semester">
          <el-input v-model="classForm.semester" placeholder="例如：2023-2024-1" />
        </el-form-item>
        <el-form-item label="最大人数" prop="max_students">
          <el-input-number
            v-model="classForm.max_students"
            :min="1"
            :max="200"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="classForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="开放"
            inactive-text="关闭"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateClassDialog = false">取消</el-button>
          <el-button type="primary" @click="submitClassForm">
            {{ isEditing ? '保存' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 学生列表对话框 -->
    <el-dialog
      :title="currentClass ? `${currentClass.class_name} - 学生列表` : '学生列表'"
      v-model="showStudentListDialog"
      width="800px"
    >
      <el-table :data="students" v-loading="loadingStudents" stripe>
        <el-table-column prop="student_id" label="学号" width="120" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="phone" label="手机号" width="140" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="join_time" label="选课时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.join_time).toLocaleString('zh-CN') }}
          </template>
        </el-table-column>
        <el-table-column prop="registration_status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.registration_status === 1 ? 'success' : 'info'">
              {{ row.registration_status === 1 ? '在读' : '已退课' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import axios from '@/axios'

const router = useRouter()
const userStore = useUserStore()

// 数据
const loading = ref(false)
const loadingStudents = ref(false)
const classes = ref([])
const courses = ref([])
const students = ref([])
const showCreateClassDialog = ref(false)
const showStudentListDialog = ref(false)
const isEditing = ref(false)
const currentClass = ref(null)
const currentEditingClass = ref(null)

// 表单
const classFormRef = ref(null)
const classForm = ref({
  course_code: '',
  class_name: '',
  semester: '',
  max_students: 50,
  status: 1
})

// 表单验证规则
const classRules = {
  course_code: [{ required: true, message: '请选择课程', trigger: 'change' }],
  class_name: [{ required: true, message: '请输入班级名称', trigger: 'blur' }],
  semester: [{ required: true, message: '请输入学期', trigger: 'blur' }],
  max_students: [{ required: true, message: '请设置最大人数', trigger: 'change' }]
}

// 获取班级列表
async function fetchClasses() {
  loading.value = true
  try {
    const teacherId = userStore.userInfo?.teacher_id
    const response = await axios.get(`/api/classes/teacher/${teacherId}`)
    classes.value = response.data.data
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
  } finally {
    loading.value = false
  }
}

// 获取课程列表
async function fetchCourses() {
  try {
    const response = await axios.get('/api/courses')
    courses.value = response.data.data
  } catch (error) {
    console.error('获取课程列表失败:', error)
    ElMessage.error('获取课程列表失败')
  }
}

// 获取班级学生列表
async function fetchClassStudents(classId) {
  loadingStudents.value = true
  try {
    const response = await axios.get(`/api/classes/${classId}/students`)
    students.value = response.data.data
  } catch (error) {
    console.error('获取学生列表失败:', error)
    ElMessage.error('获取学生列表失败')
  } finally {
    loadingStudents.value = false
  }
}

// 显示学生列表
async function showStudentList(classData) {
  currentClass.value = classData
  showStudentListDialog.value = true
  await fetchClassStudents(classData.id)
}

// 显示编辑对话框
function showEditDialog(row) {
  isEditing.value = true
  currentEditingClass.value = row
  classForm.value = { ...row }
  showCreateClassDialog.value = true
}

// 进入课堂
function enterClass(classData) {
  router.push(`/teacher/classroom/${classData.id}`)
}

// 提交表单
async function submitClassForm() {
  if (!classFormRef.value) return

  await classFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const teacherId = userStore.userInfo?.teacher_id
        const formData = {
          ...classForm.value,
          teacher_id: teacherId
        }

        if (isEditing.value) {
          await axios.put(`/api/classes/${currentEditingClass.value.id}`, formData)
          ElMessage.success('班级更新成功')
        } else {
          await axios.post('/api/classes', formData)
          ElMessage.success('班级创建成功')
        }

        showCreateClassDialog.value = false
        await fetchClasses()
        resetForm()
      } catch (error) {
        console.error('保存班级失败:', error)
        ElMessage.error('保存班级失败')
      }
    }
  })
}

// 重置表单
function resetForm() {
  if (classFormRef.value) {
    classFormRef.value.resetFields()
  }
  isEditing.value = false
  currentEditingClass.value = null
  classForm.value = {
    course_code: '',
    class_name: '',
    semester: '',
    max_students: 50,
    status: 1
  }
}

// 生命周期钩子
onMounted(async () => {
  await Promise.all([fetchClasses(), fetchCourses()])
})
</script>

<style scoped>
.class-interaction {
  min-height: calc(100vh - 64px);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding-top: 20px;
}

.el-button-group {
  display: flex;
  gap: 8px;
}
</style> 
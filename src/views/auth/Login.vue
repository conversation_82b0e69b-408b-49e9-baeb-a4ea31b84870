<template>
  <div class="login-container">
    <router-link to="/" class="back-home">
      <i class="fas fa-arrow-left"></i>
      返回首页
    </router-link>

    <div class="login-box">
      <h2>教师登录</h2>
      <form @submit.prevent="handleLogin">
        <div class="form-item" :class="{ 'has-error': formErrors.username }">
          <input 
            v-model="form.username" 
            type="text" 
            placeholder="请输入系统ID或教师工号"
            required
            @input="clearError('username')"
          >
          <div v-if="formErrors.username" class="error-message">{{ formErrors.username }}</div>
        </div>
        <div class="form-item" :class="{ 'has-error': formErrors.password }">
          <input 
            v-model="form.password" 
            type="password" 
            placeholder="请输入密码"
            required
            @input="clearError('password')"
          >
          <div v-if="formErrors.password" class="error-message">{{ formErrors.password }}</div>
        </div>
        <div class="form-options">
          <label>
            <input type="checkbox" v-model="form.remember">
            记住我
          </label>
          <a @click.prevent="handleForgotPassword" class="forget-pwd">忘记密码?</a>
        </div>
        <button class="login-btn" type="submit" :disabled="loading">
          {{ loading ? '登录中...' : '登录' }}
        </button>
        <div class="register-link">
          还没有账号? <router-link to="/register">立即注册</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from '@/utils/axios'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const form = ref({
  username: '',
  password: '',
  remember: false
})
const loading = ref(false)
const formErrors = ref({
  username: '',
  password: ''
})

// 页面加载时尝试从本地存储恢复用户名
onMounted(() => {
  const savedUsername = localStorage.getItem('savedUsername')
  if (savedUsername) {
    form.value.username = savedUsername
    form.value.remember = true
  }
})

// 清除特定字段的错误信息
const clearError = (field) => {
  if (formErrors.value[field]) {
    formErrors.value[field] = ''
  }
}

const handleLogin = async () => {
  // 重置所有错误信息
  formErrors.value = {
    username: '',
    password: ''
  }

  // 表单验证
  if (!form.value.username.trim()) {
    formErrors.value.username = '请输入用户名'
    return
  }
  
  if (!form.value.password.trim()) {
    formErrors.value.password = '请输入密码'
    return
  }

  try {
    loading.value = true
    
    // 保存用户名到本地存储（如果选择了记住我）
    if (form.value.remember) {
      localStorage.setItem('savedUsername', form.value.username.trim())
    } else {
      localStorage.removeItem('savedUsername')
    }

    console.log('发送登录请求:', {
      username: form.value.username.trim(),
      password: form.value.password.trim()
    })

    const res = await axios.post('/api/auth/login', {
      username: form.value.username.trim(),
      password: form.value.password.trim(),
      role: 'teacher'
    })
    
    console.log('登录响应:', res.data)
    
    if (res.data.success) {
      // 使用Pinia存储用户信息和token
      const userInfo = {
        ...res.data.data.userInfo,
        role: 'teacher'
      }
      userStore.setToken(res.data.data.token)
      userStore.setUser(userInfo)
      
      // 如果选择了"记住我"，可以设置token的过期时间更长
      if (form.value.remember) {
        localStorage.setItem('rememberMe', 'true')
      }
      
      message.success('登录成功')
      router.push('/dashboard/workstation')
    } else {
      message.error(res.data.message || '登录失败')
    }
  } catch (error) {
    console.error('登录错误:', error)
    // 处理具体的错误情况
    if (error.response && error.response.status === 401) {
      formErrors.value.password = '用户名或密码错误'
    } else {
      message.error(error.response?.data?.message || '登录失败，请检查用户名和密码')
    }
  } finally {
    loading.value = false
  }
}

const handleForgotPassword = () => {
  message.info('忘记密码功能暂未开放，请联系管理员重置密码')
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.login-box {
  width: 400px;
  padding: 2rem;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.login-box:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.login-box h2 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.login-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 0.8rem;
  cursor: pointer;
  color: #666;
  transition: all 0.3s ease;
}

.tab.active {
  color: #4c4de6;
  border-bottom: 2px solid #4c4de6;
}

.form-item {
  margin-bottom: 1.5rem;
}

.form-item input {
  width: 100%;
  padding: 0.8rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: #f5f5f5;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

.form-item input::placeholder {
  color: #999;
}

.form-item input:hover {
  background-color: #f0f0f0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.04);
}

.form-item input:focus {
  outline: none;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.1);
  border: 1px solid rgba(76, 77, 230, 0.4);
}

.verification {
  display: flex;
  gap: 1rem;
}

.verification input {
  flex: 1;
}

.verification button {
  padding: 0 1rem;
  white-space: nowrap;
  background: transparent;
  border: 1px solid #4c4de6;
  color: #4c4de6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(76, 77, 230, 0.1);
}

.verification button:hover {
  background: rgba(76, 77, 230, 0.1);
  box-shadow: 0 2px 8px rgba(76, 77, 230, 0.2);
  transform: translateY(-1px);
}

.verification button:disabled {
  border-color: #666;
  color: #666;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  color: #999;
}

.forget-pwd {
  color: #4c4de6;
  text-decoration: none;
}

.login-btn, .register-btn {
  width: 100%;
  padding: 0.8rem;
  background: #4c4de6;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(76, 77, 230, 0.2);
}

.login-btn:hover, .register-btn:hover {
  background: #6667e9;
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.3);
  transform: translateY(-1px);
}

.register-link {
  text-align: center;
  margin-top: 1rem;
  color: #666;
}

.register-link a {
  color: #4c4de6;
  text-decoration: none;
}

@media (max-width: 480px) {
  .login-box {
    width: 100%;
    margin: 0;
    padding: 2rem 1rem;
    border: none;
    box-shadow: none;
  }

  .login-container {
    padding: 0;
    background-color: #fff;
  }
}

/* 美化复选框 */
.form-options input[type="checkbox"] {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  margin-right: 8px;
  position: relative;
  cursor: pointer;
  vertical-align: middle;
  transition: all 0.3s ease;
}

.form-options input[type="checkbox"]:checked {
  background-color: #4c4de6;
  border-color: #4c4de6;
}

.form-options input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 1px;
  width: 6px;
  height: 9px;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
  transform: rotate(45deg);
}

.form-options input[type="checkbox"]:hover {
  border-color: #4c4de6;
}

.back-home {
  position: fixed;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4c4de6;
  font-weight: 500;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 3px 10px rgba(76, 77, 230, 0.15);
  transition: all 0.3s ease;
  z-index: 100;
}

.back-home:hover {
  background: #fff;
  color: #4c4de6;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 77, 230, 0.25);
}

.back-home i {
  font-size: 14px;
}

@media (max-width: 768px) {
  .back-home {
    top: 10px;
    left: 10px;
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 16px;
  }
}

.form-item.has-error input {
  border: 1px solid #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.error-message {
  color: #ff4d4f;
  font-size: 0.8rem;
  margin-top: 0.3rem;
  text-align: left;
}
</style>
<template>
  <div class="register-container">
    <router-link to="/" class="back-home">
      <i class="fas fa-arrow-left"></i>
      返回首页
    </router-link>
    
    <div class="register-box">
      <h2>教师注册</h2>
      <div class="form-item">
        <input 
          v-model="form.schoolTeacherId" 
          type="text" 
          placeholder="请输入教师工号"
          required
        >
      </div>
      <div class="form-item">
        <input 
          v-model="form.name" 
          type="text" 
          placeholder="请输入姓名"
          required
        >
      </div>
      <div class="form-item">
        <input 
          v-model="form.phone" 
          type="tel" 
          placeholder="请输入手机号（选填）"
        >
      </div>
      <div class="form-item verification">
        <input 
          v-model="form.verificationCode" 
          type="text" 
          placeholder="请输入验证码"
          required
        >
        <button 
          class="send-code" 
          @click="sendCode"
          :disabled="countdown > 0"
        >
          {{ countdown > 0 ? `${countdown}s后重新发送` : '发送验证码' }}
        </button>
      </div>
      <div class="form-item">
        <input 
          v-model="form.password" 
          type="password" 
          placeholder="请输入密码"
          required
        >
      </div>
      <div class="form-item">
        <input 
          v-model="form.confirmPassword" 
          type="password" 
          placeholder="请确认密码"
          required
        >
      </div>
      <button class="register-btn" @click="handleRegister">注册</button>
      
      <div v-if="registrationSuccess" class="success-message">
        <div class="system-id-box">
          <p>注册成功！您的系统ID为：</p>
          <h3>{{ systemTeacherId }}</h3>
          <p>请妥善保管此ID，登录时需要使用。</p>
        </div>
        <button class="goto-login-btn" @click="goToLogin">前往登录</button>
      </div>
      
      <div class="login-link" v-if="!registrationSuccess">
        已有账号? <router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()
const countdown = ref(0)
const registrationSuccess = ref(false)
const systemTeacherId = ref('')

const form = ref({
  schoolTeacherId: '',
  name: '',
  phone: '',
  verificationCode: '',
  password: '',
  confirmPassword: ''
})

const sendCode = () => {
  // 这里模拟发送验证码,实际验证码为0000
  if (countdown.value > 0) return
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const handleRegister = async () => {
  // 表单验证
  if (!form.value.schoolTeacherId || !form.value.name || !form.value.password) {
    alert('请填写教师工号、姓名和密码')
    return
  }
  
  if (form.value.password !== form.value.confirmPassword) {
    alert('两次输入的密码不一致')
    return
  }
  
  if (form.value.verificationCode !== '0000') {
    alert('验证码错误')
    return
  }

  try {
    const res = await axios.post('/api/auth/register', {
      school_teacher_id: form.value.schoolTeacherId,
      name: form.value.name,
      phone: form.value.phone || null,
      password: form.value.password
    })
    
    if (res.data.success) {
      // 显示成功信息和系统分配的ID
      systemTeacherId.value = res.data.data.userInfo.system_teacher_id
      registrationSuccess.value = true
    }
  } catch (error) {
    alert(error.response?.data?.message || '注册失败')
  }
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.register-box {
  width: 400px;
  padding: 2rem;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.register-box:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.register-box h2 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
  position: relative;
}

.register-box h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, #4c4de6, #6667e9);
  border-radius: 2px;
}

.form-item {
  margin-bottom: 1.5rem;
}

.form-item input {
  width: 100%;
  padding: 0.8rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: #f5f5f5;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

.form-item input::placeholder {
  color: #999;
}

.form-item input:hover {
  background-color: #f0f0f0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.04);
}

.form-item input:focus {
  outline: none;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.1);
  border: 1px solid rgba(76, 77, 230, 0.4);
}

.verification {
  display: flex;
  gap: 1rem;
}

.verification input {
  flex: 1;
}

.verification button {
  padding: 0 1rem;
  white-space: nowrap;
  background: transparent;
  border: 1px solid #4c4de6;
  color: #4c4de6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(76, 77, 230, 0.1);
}

.verification button:hover {
  background: rgba(76, 77, 230, 0.1);
  box-shadow: 0 2px 8px rgba(76, 77, 230, 0.2);
  transform: translateY(-1px);
}

.verification button:disabled {
  border-color: #666;
  color: #666;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.register-btn {
  width: 100%;
  padding: 0.8rem;
  background: #4c4de6;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(76, 77, 230, 0.2);
}

.register-btn:hover {
  background: #6667e9;
  box-shadow: 0 4px 12px rgba(76, 77, 230, 0.3);
  transform: translateY(-1px);
}

.login-link {
  text-align: center;
  margin-top: 1rem;
  color: #666;
}

.login-link a {
  color: #4c4de6;
  text-decoration: none;
  transition: color 0.3s ease;
}

.login-link a:hover {
  color: #6667e9;
}

@media (max-width: 480px) {
  .register-box {
    width: 90%;
    margin: 0 1rem;
  }
}

.back-home {
  position: fixed;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4c4de6;
  font-weight: 500;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 3px 10px rgba(76, 77, 230, 0.15);
  transition: all 0.3s ease;
  z-index: 100;
}

.back-home:hover {
  background: #fff;
  color: #4c4de6;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 77, 230, 0.25);
}

.back-home i {
  font-size: 14px;
}

@media (max-width: 768px) {
  .back-home {
    top: 10px;
    left: 10px;
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 16px;
  }
}

@media (max-width: 480px) {
  .register-box {
    width: 100%;
    margin: 0;
    padding: 2rem 1rem;
    border: none;
    box-shadow: none;
  }
  
  .register-container {
    padding: 0;
    background-color: #fff;
  }
}

.success-message {
  margin-top: 20px;
  text-align: center;
  animation: fadeIn 0.5s ease;
}

.system-id-box {
  padding: 15px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #4c4de6;
  margin-bottom: 15px;
}

.system-id-box h3 {
  font-size: 28px;
  color: #4c4de6;
  margin: 10px 0;
}

.goto-login-btn {
  background-color: #4caf50;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.goto-login-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 480px) {
  .system-id-box h3 {
    font-size: 24px;
  }
}
</style>
<template>
  <div class="assessment-page">
    <div class="assessment-content">
      <div class="tab-header">
        <div class="header-left">
          <a-select
            v-model:value="selectedChapter"
            style="width: 200px"
            placeholder="选择节"
            @change="handleChapterChange"
          >
            <a-select-option
              v-for="chapter in chapterList"
              :key="chapter.id"
              :value="chapter.id"
            >
              {{ chapter.title }}
            </a-select-option>
          </a-select>
          <a-button
            type="primary"
            @click="handleBatchPublish"
            :disabled="selectedExerciseIds.length === 0"
          >
            发布习题 {{ selectedExerciseIds.length ? `(${selectedExerciseIds.length})` : '' }}
          </a-button>
          <a-button
            type="default"
            @click="showBatchesModal"
          >
            查看批次
          </a-button>
        </div>
        <div class="selection-actions" v-if="exerciseList.length > 0">
          <a-checkbox
            :indeterminate="indeterminate"
            :checked="checkAll"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        </div>
      </div>

      <!-- 习题卡片列表 - 修复溢出问题 -->
      <div v-if="selectedChapter">
        <div class="filter-bar">
          <div class="filter-item">
            <a-radio-group v-model:value="showStatus" button-style="solid">
              <a-radio-button value="1" style="--ant-radio-button-checked-color: #ffffff;">预习题 ({{ previewExerciseCount }})</a-radio-button>
              <a-radio-button value="2" style="--ant-radio-button-checked-color: #ffffff;">课后题 ({{ homeworkExerciseCount }})</a-radio-button>
            </a-radio-group>
          </div>
        </div>

        <div class="exercise-list" v-if="exerciseList.length > 0">
          <a-list
            :grid="{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 3 }"
            :data-source="filteredExercises"
            :pagination="{
              pageSize: 9,
              showTotal: total => `共 ${total} 道习题`,
              showSizeChanger: false
            }"
            class="exercise-list-container"
          >
            <template #renderItem="{ item: exercise }">
              <a-list-item class="exercise-list-item">
                <a-card
                  class="exercise-card"
                  :class="{ 'exercise-card-selected': selectedExerciseIds.includes(exercise.exercise_id) }"
                  :hoverable="true"
                  @click="handleCardClick(exercise)"
                  size="small"
                >
                  <div class="card-header">
                    <div class="card-title">
                      <div class="title-text">{{ exercise.title }}</div>
                      <div class="title-actions">
                        <a-checkbox
                          :checked="selectedExerciseIds.includes(exercise.exercise_id)"
                          @change="(e) => handleExerciseSelect(exercise.exercise_id, e.target.checked)"
                          @click.stop
                        />
                      </div>
                    </div>
                  </div>

                  <div class="card-content">
                    <div class="tag-row">
                      <a-tag :color="getTypeColor(exercise.question_type)">
                        {{ getQuestionTypeName(exercise.question_type) }}
                      </a-tag>
                      <a-tag color="blue">难度: {{ exercise.difficulty }}星</a-tag>
                      <a-tag :color="exercise.study_phase === 1 ? 'green' : 'orange'">
                        {{ exercise.study_phase === 1 ? '预习题' : '课后题' }}
                      </a-tag>
                      <a-tag :color="exercise.isPublished ? 'success' : ''">
                        {{ exercise.study_phase === 1
                          ? (exercise.isPublished ? '已发布' : '未发布')
                          : (exercise.isPublished ? `已发布${exercise.publishCount || '1'}次` : '未发布')
                        }}
                      </a-tag>
                    </div>

                    <div class="card-footer">
                      <div v-if="exercise.knowledge_points" class="knowledge-points">
                        知识点: {{ exercise.knowledge_points }}
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-list-item>
            </template>
          </a-list>
        </div>
        <a-empty v-else description="当前章节暂无习题" />
      </div>

      <!-- 空状态展示 -->
      <a-empty v-else description="请选择章节" />
    </div>

    <!-- 发布习题对话框 -->
    <a-modal
      v-model:visible="publishExerciseModalVisible"
      title="发布习题"
      @ok="handlePublishExercise"
      :confirmLoading="loading"
      width="800px"
      @afterOpen="handleAfterOpen"
    >
      <div class="exercise-select-form">
        <a-form :model="exerciseForm">
          <a-form-item label="当前批次">
            <a-input
              v-model:value="nextReleaseBatch"
              disabled
              placeholder="自动计算下一个批次"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="选择章节">
            <a-select
              v-model:value="selectedChapter"
              style="width: 100%"
              placeholder="选择章节"
              @change="handleChapterChange"
            >
              <a-select-option v-for="chapter in chapterList" :key="chapter.id" :value="chapter.id">
                {{ chapter.title }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="截止时间">
            <a-date-picker
              v-model:value="exerciseForm.deadline"
              style="width: 100%"
              :show-time="{ format: 'HH:mm' }"
              format="YYYY-MM-DD HH:mm"
              placeholder="选择截止时间（可选）"
            />
          </a-form-item>

          <a-form-item label="倒计时（分钟）" v-if="showStatus !== '1'">
            <a-input-number
              v-model:value="exerciseForm.time"
              :min="1"
              :max="180"
              placeholder="设置答题时间限制（必填）"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="选择习题">
            <a-table
              :columns="exerciseSelectColumns"
              :data-source="availableExercises"
              :pagination="{ pageSize: 5 }"
              :loading="loading"
              size="small"
              :row-selection="rowSelection"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'difficulty'">
                  {{ record.difficulty }}星
                </template>
                <template v-if="column.key === 'preview'">
                  <a-button type="link" @click="previewExercise(record)">预览</a-button>
                </template>
              </template>
            </a-table>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 添加批次查看对话框 -->
    <a-modal
      v-model:visible="batchesModalVisible"
      title="查看习题批次"
      width="800px"
      :footer="null"
    >
      <div class="batch-selection">
        <div class="form-item">
          <span class="form-label">选择批次：</span>
          <a-select
            v-model:value="selectedBatch"
            style="width: 200px"
            placeholder="选择批次"
            @change="fetchBatchExercises"
            :loading="batchLoading"
          >
            <a-select-option
              v-for="batch in availableBatches"
              :key="batch.batch"
              :value="batch.batch"
            >
              批次 {{ batch.batch }} ({{ batch.count }}题)
            </a-select-option>
          </a-select>
        </div>
      </div>

      <a-divider />

      <div class="batch-exercises" v-if="selectedBatch">
        <h3>批次 {{ selectedBatch }} 习题列表</h3>
        <a-spin :spinning="batchLoading">
          <a-list
            :data-source="batchExercises"
            :pagination="{
              pageSize: 10,
              showTotal: total => `共 ${total} 道习题`,
              showSizeChanger: false
            }"
          >
            <template #renderItem="{ item: exercise }">
              <a-list-item>
                <a-card class="batch-exercise-card" size="small">
                  <div class="card-header">
                    <div class="card-title">
                      <div class="title-text">{{ exercise.title }}</div>
                    </div>
                  </div>

                  <div class="tag-row">
                    <a-tag :color="getTypeColor(exercise.question_type)">
                      {{ getQuestionTypeName(exercise.question_type) }}
                    </a-tag>
                    <a-tag color="blue">难度: {{ exercise.difficulty }}星</a-tag>
                    <a-tag color="orange">课后题</a-tag>
                  </div>

                  <div class="batch-info" v-if="exercise.deadline">
                    <div class="deadline-info">
                      截止时间: {{ exercise.deadline }}
                    </div>
                    <div class="time-info" v-if="exercise.time">
                      答题时间: {{ exercise.time }}分钟
                    </div>
                  </div>
                </a-card>
              </a-list-item>
            </template>
          </a-list>
          <a-empty v-if="batchExercises.length === 0 && !batchLoading" description="暂无习题" />
        </a-spin>
      </div>
      <a-empty v-else description="请选择批次" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { message, Modal, DatePicker, InputNumber } from 'ant-design-vue'
import axios from '@/axios'
import dayjs from 'dayjs'
import './ExerciseManagement.css'
import { h } from 'vue'

const props = defineProps({
  classId: {
    type: [String, Number],
    required: true
  }
})

// 状态变量
const loading = ref(false)
const publishExerciseModalVisible = ref(false)
const selectedChapter = ref(null)
const exerciseList = ref([])
const chapterList = ref([])
const availableExercises = ref([])
const selectedExerciseIds = ref([])
const indeterminate = ref(false)
const checkAll = ref(false)
const nextReleaseBatch = ref('1') // 添加下一个批次号变量

// 批次查看相关状态
const batchesModalVisible = ref(false)
const availableBatches = ref([])
const selectedBatch = ref(null)
const batchExercises = ref([])
const batchLoading = ref(false)

// 过滤相关状态
const showStatus = ref('2') // 默认显示课后题

// 添加习题类型计数变量
const previewExerciseCount = ref(0)
const homeworkExerciseCount = ref(0)

// 过滤后的习题列表
const filteredExercises = computed(() => {
  if (!selectedChapter.value || !exerciseList.value.length) return []

  return exerciseList.value.filter(exercise => {
    // 按预习题/课后题筛选
    const statusMatch = showStatus.value ? exercise.study_phase.toString() === showStatus.value : true

    return statusMatch;
  })
})

// 表单数据
const exerciseForm = ref({
  title: '',
  deadline: null,
  time: 30,
  exerciseId: null,
  content: ''
})

// 获取章节列表
const fetchChapterList = async () => {
  try {
    loading.value = true
    // 传递当前选择的学习阶段参数
    const response = await axios.get(`/api/classes/${props.classId}/chapters`, {
      params: {
        study_phase: showStatus.value || undefined
      }
    })
    if (response.data.success) {
      chapterList.value = response.data.data.map(chapter => ({
        ...chapter,
        title: chapter.title.split(' - ').pop() || chapter.title // 只取最后一部分作为节名称
      }))
    }
  } catch (error) {
    console.error('获取章节列表失败:', error)
    message.error('获取章节列表失败')
  } finally {
    loading.value = false
  }
}

// 获取习题列表
const fetchExerciseList = async () => {
  try {
    loading.value = true

    // 获取习题列表，不按学习阶段筛选，获取所有类型的习题
    const response = await axios.get(`/api/classes/${props.classId}/exercises/all`, {
      params: {
        chapter: selectedChapter.value
        // 移除 showStatus 参数，获取所有类型的习题
      }
    })

    if (response.data.success) {
      // 创建一个Map来存储不重复的习题
      const exercisesMap = new Map();
      
      // 处理所有习题，确保每个习题ID只保留一条记录
      response.data.data.forEach(exercise => {
        // 如果这个习题ID还没有被添加过，或者当前记录比已存在的更新，则更新记录
        if (!exercisesMap.has(exercise.exercise_id) || 
            (exercise.publish_time && (!exercisesMap.get(exercise.exercise_id).publish_time || 
            new Date(exercise.publish_time) > new Date(exercisesMap.get(exercise.exercise_id).publish_time)))) {
          
          exercisesMap.set(exercise.exercise_id, {
            ...exercise,
            isPublished: exercise.publish_time !== undefined && exercise.publish_time !== null,
            publishCount: 0 // 初始化发布次数
          });
        }
      });
      
      // 将Map转换为数组
      exerciseList.value = Array.from(exercisesMap.values());

      // 检查是否存在deadline和release_batch
      exerciseList.value.forEach(exercise => {
        if (exercise.deadline) {
          exercise.deadline = dayjs(exercise.deadline).format('YYYY-MM-DD HH:mm:ss')
        }
      })

      // 获取所有习题的发布次数
      await updateAllPublishCounts()

      // 更新习题类型计数
      updateExerciseTypeCounts()
    }
  } catch (error) {
    console.error('获取习题列表失败:', error)
    message.error('获取习题列表失败')
    exerciseList.value = []

    // 重置计数
    previewExerciseCount.value = 0
    homeworkExerciseCount.value = 0
  } finally {
    loading.value = false
  }
}

// 监听课程变化
watch(
  () => props.classId,
  (newClassId) => {
    if (newClassId) {
      selectedChapter.value = null // 重置选中的章节
      fetchChapterList()
    }
  },
  { immediate: true }
)

// 监听章节变化
watch(
  () => selectedChapter.value,
  (newChapter) => {
    if (props.classId && newChapter) {
      // 保存当前选中的章节
      localStorage.setItem(`lastSelectedChapter_${props.classId}`, newChapter)
      fetchExerciseList()
    }
  }
)

// 修改学习阶段(showStatus)的监听器，不再重新获取习题列表
watch(
  () => showStatus.value,
  (newStatus) => {
    if (props.classId) {
      // 当学习阶段改变时，重新获取章节列表
      fetchChapterList()
    }
  }
)

// 清理无用的监听器
watch(
  () => exerciseForm.value.time,
  () => {
    // 移除空函数体
  }
)

// 习题选择表格列定义
const exerciseSelectColumns = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: '类型',
    dataIndex: 'question_type',
    key: 'question_type',
    customRender: ({ text }) => getQuestionTypeName(text)
  },
  {
    title: '学习阶段',
    dataIndex: 'study_phase',
    key: 'study_phase',
    customRender: ({ text }) => text === 1 ? '预习题' : '课后题'
  },
  {
    title: '预览',
    key: 'preview'
  }
]

// 表格选择配置
const rowSelection = {
  type: 'radio',
  onChange: (selectedRowKeys, selectedRows) => {
    const currentTime = exerciseForm.value.time;
    const currentDeadline = exerciseForm.value.deadline;

    exerciseForm.value.exerciseId = selectedRowKeys[0];
    exerciseForm.value.title = selectedRows[0].title;
    exerciseForm.value.content = selectedRows[0].content;

    // 确保保留已设置的time和deadline值
    exerciseForm.value.time = currentTime;
    exerciseForm.value.deadline = currentDeadline;
  }
}

// 章节变化处理
const handleChapterChange = async (value) => {
  // 如果是在练习列表中已选章节和模态框中选择的章节相同，则直接使用已获取的习题数据
  if (selectedChapter.value === value && exerciseList.value.length > 0) {
    availableExercises.value = exerciseList.value.map(ex => ({...ex}))
    return
  }

  // 否则重新获取数据
  try {
    loading.value = true

    // 复用fetchExerciseList中的逻辑，只是将结果保存到availableExercises中
    const response = await axios.get(`/api/classes/exercises/chapter/${value}`, {
      params: { classId: props.classId }
    })

    if (response.data.success) {
      availableExercises.value = response.data.data
    }
  } catch (error) {
    console.error('获取章节习题失败:', error)
    message.error('获取章节习题失败')
    availableExercises.value = []
  } finally {
    loading.value = false
  }
}

// 预览习题 - 修改为只显示内容，不使用抽屉
const previewExercise = (exercise) => {
  // 使用消息框直接展示题目内容
  Modal.info({
    title: exercise.title,
    width: 600,
    content: h('div', {
      class: 'exercise-preview-content',
      innerHTML: formatExerciseContent(exercise.content)
    }),
    okText: '关闭'
  });
}

// 发布单个习题
const handlePublishExercise = async () => {
  if (!exerciseForm.value.exerciseId) {
    message.error('请选择要发布的习题');
    return;
  }

  // 获取选择的题目信息，判断是否为预习题
  const selectedExercise = availableExercises.value.find(ex => ex.exercise_id === exerciseForm.value.exerciseId);
  const isPreviewExercise = selectedExercise && selectedExercise.study_phase === 1;

  // 如果是课后题，需要检查答题时间
  if (!isPreviewExercise && !exerciseForm.value.time) {
    message.error('请设置答题时间');
    return;
  }

  if (!exerciseForm.value.deadline) {
    message.error('请设置截止时间');
    return;
  }

  try {
    loading.value = true;
    const requestData = {
      exerciseId: exerciseForm.value.exerciseId,
      deadline: exerciseForm.value.deadline ? dayjs(exerciseForm.value.deadline).format('YYYY-MM-DD HH:mm:ss') : null,
      time: isPreviewExercise ? null : Number(exerciseForm.value.time) // 预习题不需要time
    };

    const response = await axios.post(`/api/classes/${props.classId}/exercises`, requestData);

    if (response.data.success) {
      message.success(isPreviewExercise ? '发布预习题成功！' : `发布习题成功！当前批次: ${response.data.data.release_batch}`);
      publishExerciseModalVisible.value = false;
      await fetchExerciseList();
    }
  } catch (error) {
    console.error('发布习题失败:', error);

    // 尝试从错误响应中获取具体信息
    const errorMsg = error.response?.data?.message || '发布习题失败';
    message.error(errorMsg);
  } finally {
    loading.value = false;
  }
}

// 获取题目类型对应的颜色
const getTypeColor = (type) => {
  // 强制转换为数字类型
  let numType = Number(type);
  if (isNaN(numType)) {
    numType = 0;
  }

  const typeColors = {
    1: 'purple',  // 选择题
    2: 'cyan',    // 填空题
    3: 'green',   // 判断题
    4: 'orange',  // 简答题
    5: 'blue'     // 计算题
  }
  return typeColors[numType] || 'default'
}

// 获取题目类型名称
const getQuestionTypeName = (type) => {
  // 强制转换为数字类型
  let numType = Number(type);
  if (isNaN(numType)) {
    numType = 0;
  }

  const typeNames = {
    0: '未知题型',
    1: '选择题',
    2: '填空题',
    3: '判断题',
    4: '简答题',
    5: '计算题'
  }
  return typeNames[numType] || `未知类型(${numType})`;
}

// 获取内容预览
const getContentPreview = (content) => {
  if (!content) return '暂无内容'

  try {
    // 尝试解析JSON格式的内容
    if (content.startsWith('{') && content.includes('"title"') && content.includes('"options"')) {
      const contentObj = JSON.parse(content)

      // 构建格式化的选择题内容 - 使用HTML格式化
      let formattedContent = `<div class="question-title">${contentObj.title}</div>`

      // 添加选项，垂直排列
      if (contentObj.options) {
        formattedContent += '<div class="question-options">'
        const options = contentObj.options
        Object.keys(options).forEach(key => {
          formattedContent += `<div class="question-option"><span class="option-key">${key}.</span> ${options[key]}</div>`
        })
        formattedContent += '</div>'
      }

      return formattedContent
    }
  } catch (e) {
    console.warn('解析题目内容失败:', e)
  }

  // 如果不是JSON格式或解析失败，则使用普通文本处理
  const plainText = content.replace(/<[^>]+>/g, '')
  return plainText.length > 100
    ? plainText.substring(0, 100) + '...'
    : plainText
}

// 格式化习题详情内容
const formatExerciseContent = (content) => {
  if (!content) return '暂无内容'

  try {
    // 尝试解析JSON格式的内容
    if (content.startsWith('{') && content.includes('"title"') && content.includes('"options"')) {
      const contentObj = JSON.parse(content)

      // 构建格式化的选择题内容，详情视图中更加清晰
      let formattedContent = `<div class="full-question-title">${contentObj.title}</div>`

      // 添加选项，垂直排列并美化
      if (contentObj.options) {
        formattedContent += '<div class="full-question-options">'
        const options = contentObj.options
        Object.keys(options).forEach(key => {
          formattedContent += `<div class="full-question-option">
            <div class="option-key">${key}</div>
            <div class="option-value">${options[key]}</div>
          </div>`
        })
        formattedContent += '</div>'
      }

      // 如果存在正确答案和解析，也显示出来
      if (contentObj.answer) {
        formattedContent += `<div class="question-answer">
          <div class="answer-title">正确答案：</div>
          <div class="answer-content">${contentObj.answer}</div>
        </div>`
      }

      if (contentObj.analysis) {
        formattedContent += `<div class="question-analysis">
          <div class="analysis-title">解析：</div>
          <div class="analysis-content">${contentObj.analysis}</div>
        </div>`
      }

      return formattedContent
    }
  } catch (e) {
    console.warn('解析题目详情失败:', e)
  }

  // 如果不是JSON或解析失败，返回原内容
  return content
}

// 处理习题选择
const handleExerciseSelect = (exerciseId, checked) => {
  const index = selectedExerciseIds.value.indexOf(exerciseId)
  if (checked && index === -1) {
    selectedExerciseIds.value.push(exerciseId)
  } else if (!checked && index > -1) {
    selectedExerciseIds.value.splice(index, 1)
  }

  // 更新全选状态
  updateCheckAllStatus()
}

// 处理全选
const onCheckAllChange = (e) => {
  // 修改为只选择当前显示（已筛选）的习题
  const availableExercises = filteredExercises.value
  selectedExerciseIds.value = e.target.checked
    ? availableExercises.map(ex => ex.exercise_id)
    : []
  indeterminate.value = false
  checkAll.value = e.target.checked
}

// 批量发布习题，禁止同时发布预习题和课后题
const handleBatchPublish = async () => {
  if (selectedExerciseIds.value.length === 0) {
    message.warning('请选择要发布的习题');
    return;
  }

  // 检查是否选中预习题
  const selectedExercises = exerciseList.value.filter(ex => selectedExerciseIds.value.includes(ex.exercise_id));
  const previewExercises = selectedExercises.filter(ex => ex.study_phase === 1);
  const homeworkExercises = selectedExercises.filter(ex => ex.study_phase === 2);

  const hasPreviewExercises = previewExercises.length > 0;
  const hasHomeworkExercises = homeworkExercises.length > 0;
  const isPreviewOnly = hasPreviewExercises && !hasHomeworkExercises; // 仅包含预习题

  // 禁止同时发布预习题和课后题
  if (hasPreviewExercises && hasHomeworkExercises) {
    message.error('不能同时发布预习题和课后题，请分开发布！');
    return;
  }

  // 检查选中的预习题是否已经发布过
  const alreadyPublishedPreviewIds = previewExercises
    .filter(ex => ex.isPublished)
    .map(ex => ex.exercise_id);

  if (alreadyPublishedPreviewIds.length > 0) {
    message.warning(`以下预习题已发布过，不能重复发布: ${alreadyPublishedPreviewIds.join(', ')}`);
    return;
  }

  // 获取当前最大批次号
  await fetchNextReleaseBatch();

  // 设置默认值
  batchPublishParams.value = {
    deadline: dayjs().add(7, 'day'), // 默认截止时间为一周后
    time: hasHomeworkExercises ? 30 : null // 如果有课后题才设置时间，否则为null
  };

  // 弹出对话框让用户设置截止时间和倒计时
  const deadlineModal = Modal.confirm({
    title: isPreviewOnly ? '设置预习题参数' : '设置习题参数',
    width: 400,
    content: h('div', {}, [
      h('div', { class: 'batch-publish-form' }, [
        hasHomeworkExercises ? h('div', { class: 'form-item' }, [
          h('div', { class: 'form-label' }, `当前批次: ${nextReleaseBatch.value}`),
        ]) : null,
        h('div', { class: 'form-item' }, [
          h('div', { class: 'form-label' }, '截止时间（必填）:'),
          h(DatePicker, {
            style: { width: '100%' },
            showTime: { format: 'HH:mm' },
            format: 'YYYY-MM-DD HH:mm',
            defaultValue: batchPublishParams.value.deadline,
            onChange: (date) => {
              batchPublishParams.value.deadline = date;
            }
          })
        ]),
        // 只有当包含课后题时才显示答题时间设置
        hasHomeworkExercises ? h('div', { class: 'form-item', style: { marginTop: '16px' } }, [
          h('div', { class: 'form-label' }, '答题时间（分钟，必填）:'),
          h(InputNumber, {
            style: { width: '100%' },
            min: 1,
            max: 180,
            defaultValue: 30,
            value: 30,
            onChange: (value) => {
              batchPublishParams.value.time = value;
            }
          })
        ]) : null
      ])
    ]),
    onOk: async () => {
      // 如果有课后题但没有设置time
      if (hasHomeworkExercises && !batchPublishParams.value.time) {
        message.error('请为课后题设置答题时间');
        return Promise.reject('请设置答题时间');
      }

      if (!batchPublishParams.value.deadline) {
        message.error('请设置截止时间');
        return Promise.reject('请设置截止时间');
      }

      try {
        loading.value = true;
        const response = await axios.post(`/api/classes/${props.classId}/exercises/batch`, {
          exerciseIds: selectedExerciseIds.value,
          deadline: batchPublishParams.value.deadline ? dayjs(batchPublishParams.value.deadline).format('YYYY-MM-DD HH:mm:ss') : null,
          time: hasHomeworkExercises ? Number(batchPublishParams.value.time) : null,
          study_phase: isPreviewOnly ? 1 : 2 // 明确指定学习阶段
        });

        if (response.data.success) {
          let successMessage = `批量发布习题成功！`;
          if (hasHomeworkExercises) {
            // 检查返回的批次信息格式
            if (response.data.data.release_batch) {
              // 单个章节的情况
              successMessage += `当前批次: ${response.data.data.release_batch}`;
            } else if (response.data.data.release_batches) {
              // 多个章节的情况
              const batchesInfo = Object.entries(response.data.data.release_batches)
                .map(([titleId, batch]) => {
                  const chapter = chapterList.value.find(c => c.id === titleId);
                  return `${chapter ? chapter.title : '章节'+titleId}: 批次${batch}`;
                })
                .join(', ');
              successMessage += `各章节批次: ${batchesInfo}`;
            }
          }

          message.success(successMessage);

          selectedExerciseIds.value = []; // 清空选择
          checkAll.value = false;
          indeterminate.value = false;
          await fetchExerciseList(); // 刷新列表
        }
      } catch (error) {
        console.error('批量发布习题失败:', error);

        // 尝试从错误响应中获取具体信息
        const errorMsg = error.response?.data?.message || '批量发布习题失败';
        message.error(errorMsg);
        return Promise.reject(errorMsg);
      } finally {
        loading.value = false;
      }
    }
  });
}

// 获取下一个批次号
const fetchNextReleaseBatch = async () => {
  try {
    // 如果没有选择章节，无法获取准确的批次号
    if (!selectedChapter.value) {
      nextReleaseBatch.value = '1'; // 默认值
      return;
    }

    const response = await axios.get(`/api/classes/${props.classId}/exercises/nextbatch`, {
      params: {
        titleId: selectedChapter.value,
        studyPhase: showStatus.value || 2 // 添加学习阶段参数，默认为课后题
      }
    });

    if (response.data.success) {
      nextReleaseBatch.value = response.data.data.nextBatch.toString();
    }
  } catch (error) {
    console.error('获取下一个批次号失败:', error);
    nextReleaseBatch.value = '1'; // 默认为1
  }
}

// 批量发布参数
const batchPublishParams = ref({
  deadline: dayjs().add(7, 'day'), // 默认截止时间为一周后
  time: 30  // 设置默认值
})

// 监听章节变化时清空选择
watch(selectedChapter, () => {
  selectedExerciseIds.value = []
  checkAll.value = false
  indeterminate.value = false
})

// 处理卡片点击
const handleCardClick = (exercise) => {
  const exerciseId = exercise.exercise_id
  const isSelected = selectedExerciseIds.value.includes(exerciseId)

  // 切换选中状态
  handleExerciseSelect(exerciseId, !isSelected)
}

// 获取习题发布次数 - 更新为获取真实发布次数
const getPublishCount = async (exerciseId) => {
  try {
    const response = await axios.get(`/api/classes/${props.classId}/exercises/${exerciseId}/count`)
    if (response.data.success) {
      return response.data.data.count
    }
    return 0
  } catch (error) {
    console.error('获取习题发布次数失败:', error)
    return 0
  }
}

const handleAfterOpen = () => {
  exerciseForm.value.time = 30;
  // 获取下一个批次号
  fetchNextReleaseBatch()

  // 检查当前选择的类型是否为预习题
  const isPreviewType = showStatus.value === '1';

  // 如果是预习题，直接将答题时间设为null
  if (isPreviewType) {
    exerciseForm.value.time = null;
  }

  // 直接强制更新DOM中的InputNumber值
  setTimeout(() => {
    const inputNumber = document.querySelector('.exercise-select-form input[type="number"]');
    if (inputNumber && !isPreviewType) {
      inputNumber.value = '30';
    }
  }, 100);
}

onMounted(() => {
  if (props.classId) {
    fetchChapterList()
    fetchExerciseList()
    fetchNextReleaseBatch() // 获取下一个批次号
  }

  // 获取所有已发布习题的真实发布次数
  updateAllPublishCounts()
})

// 获取所有习题的发布次数
const updateAllPublishCounts = async () => {
  try {
    const response = await axios.get(`/api/classes/${props.classId}/exercises/counts`);
    if (response.data.success) {
      const countData = response.data.data;

      // 更新列表中的发布次数
      exerciseList.value.forEach(exercise => {
        if (exercise.isPublished) {
          const count = countData[exercise.exercise_id];
          exercise.publishCount = count ? Number(count) : 1;
        }
      });
    }
  } catch (error) {
    console.error('批量获取习题发布次数失败:', error);
    // 如果API调用失败，将所有已发布习题的publishCount设为默认值1
    exerciseList.value.forEach(exercise => {
      if (exercise.isPublished) {
        exercise.publishCount = 1;
      }
    });
  }
}

// 更新全选状态
const updateCheckAllStatus = () => {
  // 修改为只考虑当前筛选条件下的习题
  const availableExercises = filteredExercises.value
  checkAll.value = availableExercises.length > 0 &&
                   selectedExerciseIds.value.length >= availableExercises.length
  indeterminate.value = selectedExerciseIds.value.length > 0 &&
                       selectedExerciseIds.value.length < availableExercises.length
}

// 添加计算习题类型数量的函数
const updateExerciseTypeCounts = () => {
  if (exerciseList.value.length === 0) {
    previewExerciseCount.value = 0
    homeworkExerciseCount.value = 0
    return
  }

  // 计算预习题数量
  previewExerciseCount.value = exerciseList.value.filter(
    exercise => exercise.study_phase.toString() === '1'
  ).length

  // 计算课后题数量
  homeworkExerciseCount.value = exerciseList.value.filter(
    exercise => exercise.study_phase.toString() === '2'
  ).length
}

// 显示批次查看对话框
const showBatchesModal = async () => {
  if (!selectedChapter.value) {
    message.warning('请先选择章节')
    return
  }
  
  batchesModalVisible.value = true
  selectedBatch.value = null
  batchExercises.value = []
  
  try {
    batchLoading.value = true
    
    // 获取当前章节的所有批次信息
    const response = await axios.get(`/api/classes/${props.classId}/exercises/batches`, {
      params: {
        titleId: selectedChapter.value,
        studyPhase: 2 // 只获取课后题的批次
      }
    })
    
    if (response.data.success) {
      availableBatches.value = response.data.data
    } else {
      availableBatches.value = []
    }
  } catch (error) {
    console.error('获取批次列表失败:', error)
    message.error('获取批次列表失败')
    availableBatches.value = []
  } finally {
    batchLoading.value = false
  }
}

// 获取指定批次的习题
const fetchBatchExercises = async (batchNumber) => {
  if (!selectedChapter.value || !batchNumber) return
  
  try {
    batchLoading.value = true
    batchExercises.value = []
    
    const response = await axios.get(`/api/classes/${props.classId}/exercises/batch/${batchNumber}`, {
      params: {
        titleId: selectedChapter.value,
        studyPhase: 2 // 只获取课后题
      }
    })
    
    if (response.data.success) {
      batchExercises.value = response.data.data.map(exercise => ({
        ...exercise,
        deadline: exercise.deadline ? dayjs(exercise.deadline).format('YYYY-MM-DD HH:mm:ss') : null
      }))
    }
  } catch (error) {
    console.error('获取批次习题失败:', error)
    message.error('获取批次习题失败')
    batchExercises.value = []
  } finally {
    batchLoading.value = false
  }
}
</script>

<style scoped>
/* 引入外部CSS文件 */
@import './ExerciseManagement.css';

/* 在这里添加样式，确保按钮选中时文本为白色 */
:deep(.ant-radio-button-wrapper-checked),
:deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)),
:deep(.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)) {
  color: #ffffff !important;
  font-weight: 500;
}

:deep(.ant-radio-button-wrapper-checked:hover),
:deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover) {
  color: #ffffff !important;
}

/* 确保按钮内部的文本元素也是白色 */
:deep(.ant-radio-button-wrapper-checked span) {
  color: #ffffff !important;
}
</style>
<template>
  <div class="preview-management">
    <a-card class="box-card">
      <template #title>
        <div class="card-header">
          <span>预习资料管理</span>
          <a-space>
            <a-button type="primary" @click="showMaterialContent" :disabled="!selectedChapter" style="color: white">
              查看预习资料
            </a-button>
            <a-button
              type="primary"
              @click="showPublishDialog"
              :disabled="!selectedChapter || isChapterPublished(selectedChapter)"
              style="color: white"
            >
              发布预习
            </a-button>
          </a-space>
        </div>
      </template>

      <!-- 章节选择 -->
      <div class="chapter-selector">
        <a-select v-model:value="selectedChapter" placeholder="请选择章节" @change="handleChapterChange" style="width: 100%">
          <a-select-option
            v-for="item in chapterList"
            :key="item.id"
            :value="item.id"
          >
            {{ item.title }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 预习列表 -->
      <div class="preview-list">
        <a-table :columns="columns" :data-source="previewList" :loading="loading">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'completion'">
              {{ record.submittedCount || 0 }}/{{ record.totalStudents || 0 }} ({{ record.completion }}%)
            </template>
          <template v-if="column.key === 'action'">
            <a-space>
                <a-button type="link" @click="showPreviewDetail(record)">查看详情</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 发布预习对话框 -->
    <a-modal
        v-model:visible="publishDialogVisible"
        title="发布预习资料"
      @ok="handlePublishPreview"
        @cancel="handleCancelPublish"
        :maskClosable="true"
        destroyOnClose
      >
        <a-form :model="publishForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="标题">
            <a-input v-model:value="publishForm.title" placeholder="请输入标题" />
        </a-form-item>
        <a-form-item label="截止时间">
          <a-date-picker
              v-model:value="publishForm.deadline"
              placeholder="选择日期"
              style="width: 50%"
              :disabledDate="disabledDate"
            />
            <a-time-picker
              v-model:value="publishForm.deadlineTime"
              placeholder="选择时间"
              format="HH:mm"
              style="width: 50%"
          />
        </a-form-item>
      </a-form>
        <template #footer>
          <a-button key="back" @click="handleCancelPublish">取消</a-button>
          <a-button
            key="submit"
            type="primary"
            :loading="loading"
            @click="handlePublishPreview"
          >
            发布
          </a-button>
        </template>
      </a-modal>

      <!-- 预习详情对话框 -->
      <a-modal
        v-model:visible="previewDetailVisible"
        title="预习详情"
        width="800px"
        :footer="null"
      >
        <div v-if="currentPreview" class="preview-detail">
          <div class="detail-header">
            <h3>{{ currentPreview.title }}</h3>
            <p>章节：{{ currentPreview.chapter_title }}</p>
            <p>发布时间：{{ currentPreview.publishTime }}</p>
            <p>截止时间：{{ currentPreview.deadline }}</p>
          </div>

          <div class="detail-students">
            <a-tabs>
              <a-tab-pane key="students" tab="学生完成情况">
                <div class="completion-status">
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <div class="status-block completed">
                        <h4>已完成学生 ({{ completedStudents.length }}人)</h4>
                        <a-list
                          size="small"
                          :data-source="completedStudents"
                          :pagination="completedStudents.length > 10 ? { pageSize: 10 } : false"
                        >
                          <template #renderItem="{ item }">
                            <a-list-item>
                              <div class="student-info">
                                <a-tag color="success">{{ item.studentId }}</a-tag>
                                <span class="student-name">{{ item.studentName }}</span>
                              </div>
                              <div class="student-stats">
                                <div class="submit-time">{{ item.submitTime }}</div>
                                <div class="tags-container">
                                  <a-tag v-if="item.totalViewDuration" color="blue">
                                    总时长: {{ formatTime(item.totalViewDuration) }}
                                  </a-tag>
                                  <a-tag v-if="item.totalViewCount" color="purple">
                                    观看次数: {{ item.totalViewCount }}次
                                  </a-tag>
                                </div>
                              </div>
                            </a-list-item>
                          </template>
                        </a-list>
                      </div>
                    </a-col>
                    <a-col :span="12">
                      <div class="status-block uncompleted">
                        <h4>未完成学生 ({{ uncompletedStudents.length }}人)</h4>
                        <a-list
                          size="small"
                          :data-source="uncompletedStudents"
                          :pagination="uncompletedStudents.length > 10 ? { pageSize: 10 } : false"
                        >
                          <template #renderItem="{ item }">
                            <a-list-item>
                              <div class="student-info">
                                <a-tag color="default">{{ item.studentId }}</a-tag>
                                <span class="student-name">{{ item.studentName }}</span>
                              </div>
                            </a-list-item>
                          </template>
                        </a-list>
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </a-tab-pane>
              <a-tab-pane key="submissions" tab="学生提交内容">
                <h4>学生提交情况</h4>
                <a-table
                  :columns="submissionColumns"
                  :data-source="studentSubmissions"
                  :loading="loadingSubmissions"
                  row-key="id"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                      <a-button type="link" @click="showStudentContent(record)">
                        查看内容
                      </a-button>
                    </template>
                    <template v-if="column.key === 'submitTime'">
                      {{ formatDate(record.submit_time) }}
                    </template>
                  </template>
                </a-table>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </a-modal>

      <!-- 预习资料内容对话框 -->
      <a-modal
        v-model:visible="materialDialogVisible"
        title="预习资料内容"
        width="800px"
        :footer="null"
      >
        <div v-if="previewMaterial" class="preview-material-detail">
          <div class="detail-header">
            <h3>{{ previewMaterial.chapter_title }}</h3>
          </div>

          <div class="detail-content markdown-body">
            <div v-html="formattedPreviewContent"></div>
          </div>
        </div>
        <div v-else class="empty-content">
          <a-empty description="暂无预习资料内容" />
        </div>
      </a-modal>

      <!-- 学生提交内容对话框 -->
      <a-modal
        v-model:visible="studentContentVisible"
        title="学生提交内容"
        width="800px"
        :footer="null"
      >
        <div v-if="currentSubmission">
          <p>提交时间：{{ formatDate(currentSubmission.submit_time) }}</p>
          <div class="submission-content">{{ currentSubmission.content }}</div>
        </div>
    </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import axios from '@/axios'
import dayjs from 'dayjs'
import { marked } from 'marked'

const props = defineProps({
  classId: {
    type: [String, Number],
    required: true
  }
})

// 状态变量
const loading = ref(false)
const chapterList = ref([])
const selectedChapter = ref('')
const previewList = ref([])
const publishDialogVisible = ref(false)
const previewDetailVisible = ref(false)
const materialDialogVisible = ref(false)
const studentContentVisible = ref(false)
const currentPreview = ref(null)
const currentSubmission = ref(null)
const studentSubmissions = ref([])
const loadingSubmissions = ref(false)
const publishedTitleIds = ref(new Set()) // 存储已发布的titleId
const previewMaterial = ref(null) // 存储预习资料内容

// 发布表单
const publishForm = ref({
  title: '',
  deadline: null,
  deadlineTime: null
})

// 表格列定义
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    key: 'deadline',
    customRender: ({ text }) => formatDate(text)
  },
  {
    title: '完成率',
    dataIndex: 'completion',
    key: 'completion',
    customRender: ({ text, record }) => `${record.submittedCount || 0}/${record.totalStudents || 0} (${text}%)`
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

const submissionColumns = [
  {
    title: '学号',
    dataIndex: 'student_id',
    key: 'student_id'
  },
  {
    title: '提交时间',
    dataIndex: 'submit_time',
    key: 'submitTime'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    customRender: ({ text }) => text === 1 ? '已提交' : '未提交'
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
]

// 学生表格列定义
const studentColumns = [
  {
    title: '学生姓名',
    dataIndex: 'studentName',
    key: 'studentName'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime'
  }
]

// 获取章节列表
const fetchChapterList = async () => {
  try {
    loading.value = true
    const response = await axios.get(`/api/class-preview/${props.classId}/chapters-with-preview`)
    if (response.data.success) {
      chapterList.value = response.data.data

      if (chapterList.value.length > 0) {
        // 尝试从列表中恢复最后选择的章节
        const lastSelected = localStorage.getItem(`lastSelectedPreviewChapter_${props.classId}`)

        if (lastSelected && chapterList.value.some(chapter => chapter.id === lastSelected)) {
          selectedChapter.value = lastSelected
        } else {
          selectedChapter.value = chapterList.value[0].id
        }

        // 获取预习列表
        const previewResponse = await axios.get(`/api/class-preview/${props.classId}/previews`)
        if (previewResponse.data.success) {
          const allPreviews = previewResponse.data.data

          // 更新已发布的titleId集合
          publishedTitleIds.value.clear()
          allPreviews.forEach(preview => {
            if (preview.titleId) {
              publishedTitleIds.value.add(preview.titleId)
            }
          })

          // 过滤出当前选中章节的预习列表
          if (selectedChapter.value) {
            previewList.value = allPreviews.filter(preview => preview.titleId === selectedChapter.value)
          } else {
            previewList.value = []
          }
        }
      }
    }
  } catch (error) {
    console.error('获取章节列表失败:', error)
    message.error('获取章节列表失败')
  } finally {
    loading.value = false
  }
}

// 获取预习列表
const fetchPreviewList = async () => {
  if (!props.classId) return

  try {
    loading.value = true
    const response = await axios.get(`/api/class-preview/${props.classId}/previews`)
    if (response.data.success) {
      const allPreviews = response.data.data

      // 更新已发布的titleId集合
      publishedTitleIds.value.clear()
      allPreviews.forEach(preview => {
        if (preview.titleId) {
          publishedTitleIds.value.add(preview.titleId)
        }
      })

      // 过滤出当前选中章节的预习列表
      if (selectedChapter.value) {
        previewList.value = allPreviews.filter(preview => preview.titleId === selectedChapter.value)
      } else {
        previewList.value = allPreviews
      }

      console.log('已发布的预习资料:', Array.from(publishedTitleIds.value))
    }
  } catch (error) {
    console.error('获取预习列表失败:', error)
    message.error('获取预习列表失败')
  } finally {
    loading.value = false
  }
}

// 获取预习详情
const fetchPreviewDetail = async (previewId) => {
  try {
    loading.value = true
    const response = await axios.get(`/api/class-preview/previews/${previewId}`)
    if (response.data.success) {
      currentPreview.value = response.data.data

      // 同时获取学生提交情况
      await fetchStudentSubmissions(previewId)

      // 获取班级学生列表并处理完成情况
      await fetchStudentCompletionStatus(previewId)

      previewDetailVisible.value = true
    }
  } catch (error) {
    console.error('获取预习详情失败:', error)
    message.error('获取预习详情失败')
  } finally {
    loading.value = false
  }
}

// 获取预习资料内容
const fetchPreviewMaterial = async () => {
  if (!selectedChapter.value) return

  try {
    loading.value = true
    const response = await axios.get(`/api/class-preview/previews/material/${selectedChapter.value}`)
    if (response.data.success) {
      previewMaterial.value = response.data.data
      materialDialogVisible.value = true
    } else {
      message.warning('未找到预习资料')
      previewMaterial.value = null
      materialDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取预习资料内容失败:', error)
    message.error('获取预习资料内容失败')
    previewMaterial.value = null
    materialDialogVisible.value = true
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleChapterChange = () => {
  // 从缓存的所有预习中过滤出当前选中章节的预习
  if (selectedChapter.value) {
    // 如果已经有全局预习列表数据，直接从中过滤
    const allPreviews = previewList.value.length > 0 ? previewList.value : [];
    previewList.value = allPreviews.filter(preview => preview.titleId === selectedChapter.value);
  } else {
    previewList.value = [];
  }

  // 如果当前没有数据，则重新获取
  if (previewList.value.length === 0) {
    fetchPreviewList();
  }
}

const showPublishDialog = () => {
  if (!selectedChapter.value) {
    message.warning('请先选择章节')
    return
  }
  publishForm.value = {
    title: '',
    deadline: null,
    deadlineTime: null
  }
  publishDialogVisible.value = true
}

const showMaterialContent = () => {
  if (!selectedChapter.value) {
    message.warning('请先选择章节')
    return
  }
  fetchPreviewMaterial()
}

const handlePublishPreview = async () => {
  console.log('提交表单数据:', publishForm.value)

  if (!publishForm.value.title) {
    message.warning('请输入标题')
    return
  }

  if (!publishForm.value.deadline || !publishForm.value.deadlineTime) {
    message.warning('请选择截止日期和时间')
    return
  }

  try {
    loading.value = true

    // 合并日期和时间
    const dateStr = dayjs(publishForm.value.deadline).format('YYYY-MM-DD')
    const timeStr = dayjs(publishForm.value.deadlineTime).format('HH:mm:ss')
    const deadlineStr = `${dateStr} ${timeStr}`

    console.log('合并后的截止时间:', deadlineStr)

    const response = await axios.post(`/api/class-preview/${props.classId}/publish-preview`, {
      titleId: selectedChapter.value,
      title: publishForm.value.title,
      deadline: deadlineStr
    })

    if (response.data.success) {
      message.success('发布成功')
      publishDialogVisible.value = false
      await fetchPreviewList()
    } else {
      message.error(response.data.message || '发布失败')
    }
  } catch (error) {
    console.error('发布预习失败:', error)
    message.error('发布预习失败：' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

const showPreviewDetail = (preview) => {
  fetchPreviewDetail(preview.id)
}

// 获取学生提交情况
const fetchStudentSubmissions = async (previewId) => {
  try {
    loadingSubmissions.value = true
    const response = await axios.get(`/api/class-preview/previews/${previewId}/submissions`)
    if (response.data.success) {
      studentSubmissions.value = response.data.data
    } else {
      studentSubmissions.value = []
    }
  } catch (error) {
    console.error('获取学生提交情况失败:', error)
    studentSubmissions.value = []
  } finally {
    loadingSubmissions.value = false
  }
}

// 获取学生完成情况
const fetchStudentCompletionStatus = async (previewId) => {
  try {
    if (!previewId) return

    console.log('尝试获取预习ID为', previewId, '的学生完成情况')

    // 首先尝试使用预习详情API获取完整数据
    try {
      const detailResponse = await axios.get(`/api/previews/${previewId}/detail`)
      if (detailResponse.data.success) {
        console.log('获取到预习详情:', detailResponse.data.data)

        // 处理学生数据
        const students = detailResponse.data.data.students || []

        // 区分已完成和未完成的学生
        const completedStudentsList = []
        const uncompletedStudentsList = []

        students.forEach(student => {
          if (student.status === 1) {
            completedStudentsList.push({
              studentId: student.studentId,
              studentName: student.studentName,
              status: 1,
              submitTime: student.submitTime,
              totalViewDuration: student.totalViewDuration || 0,
              totalViewCount: student.totalViewCount || 0
            })
          } else {
            uncompletedStudentsList.push({
              studentId: student.studentId,
              studentName: student.studentName,
              status: 0
            })
          }
        })

        // 更新状态
        completedStudentsList.sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime))
        currentPreview.value.completedStudents = completedStudentsList
        currentPreview.value.uncompletedStudents = uncompletedStudentsList

        console.log('已完成学生:', completedStudentsList.length, '未完成学生:', uncompletedStudentsList.length)
        return
      }
    } catch (err) {
      console.warn('尝试通过/api/previews/:previewId/detail获取数据失败:', err.message)
    }

    // 备选方案1: 尝试数据分析API
    try {
      const analysisResponse = await axios.get(`/api/analysis/preview/detail/${previewId}`)
      if (analysisResponse.data.success) {
        console.log('通过数据分析API获取到预习详情:', analysisResponse.data.data)

        // 从响应中提取已完成和未完成的学生列表
        const completedStudents = analysisResponse.data.data.students.completed || []
        const uncompletedStudents = analysisResponse.data.data.students.pending || []

        // 转换为组件需要的格式
        const completedStudentsList = completedStudents.map(student => ({
          studentId: student.studentId,
          studentName: student.name || '',
          status: 1,
          submitTime: formatDate(student.submitTime),
          totalViewDuration: student.totalViewDuration || 0,
          totalViewCount: student.totalViewCount || 0
        }))

        const uncompletedStudentsList = uncompletedStudents.map(student => ({
          studentId: student.studentId,
          studentName: student.name || '',
          status: 0
        }))

        // 更新状态
        completedStudentsList.sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime))
        currentPreview.value.completedStudents = completedStudentsList
        currentPreview.value.uncompletedStudents = uncompletedStudentsList

        console.log('已完成学生:', completedStudentsList.length, '未完成学生:', uncompletedStudentsList.length)
        return
      }
    } catch (err) {
      console.warn('尝试通过/api/analysis/preview/detail获取数据失败:', err.message)
    }

    // 备选方案2: 使用提交情况API
    const submissionsResponse = await axios.get(`/api/previews/${previewId}/submissions`)
    if (submissionsResponse.data.success) {
      const submissions = submissionsResponse.data.data || []
      console.log('获取到学生提交记录:', submissions.length, '条')

      // 处理提交记录
      const completedStudentsList = []
      const uncompletedStudentsList = []

      submissions.forEach(submission => {
        if (submission.status === 1) {
          completedStudentsList.push({
            studentId: submission.student_id,
            studentName: submission.student_name || '',
            status: 1,
            submitTime: formatDate(submission.submit_time),
            totalViewDuration: submission.total_view_duration || 0,
            totalViewCount: submission.total_view_count || 0
          })
        } else {
          uncompletedStudentsList.push({
            studentId: submission.student_id,
            studentName: submission.student_name || '',
            status: 0
          })
        }
      })

      // 更新状态
      completedStudentsList.sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime))
      currentPreview.value.completedStudents = completedStudentsList
      currentPreview.value.uncompletedStudents = uncompletedStudentsList

      console.log('已完成学生:', completedStudentsList.length, '未完成学生:', uncompletedStudentsList.length)
    } else {
      message.error('获取学生提交状态失败')
      currentPreview.value.completedStudents = []
      currentPreview.value.uncompletedStudents = []
    }
  } catch (error) {
    console.error('获取学生完成情况失败:', error)
    message.error('获取学生完成情况失败: ' + error.message)
    currentPreview.value.completedStudents = []
    currentPreview.value.uncompletedStudents = []
  }
}

// 显示学生提交内容
const showStudentContent = (submission) => {
  currentSubmission.value = submission
  studentContentVisible.value = true
}

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 日期限制
const disabledDate = (current) => {
  // 禁用今天之前的日期
  return current && current < dayjs().startOf('day');
}

const handleCancelPublish = () => {
  console.log('取消发布')
  publishDialogVisible.value = false
}

// 判断章节是否已发布
const isChapterPublished = (titleId) => {
  return publishedTitleIds.value.has(titleId)
}

// 计算属性 - 已完成学生列表
const completedStudents = computed(() => {
  if (!currentPreview.value) return [];
  return currentPreview.value.completedStudents || [];
});

// 计算属性 - 未完成学生列表
const uncompletedStudents = computed(() => {
  if (!currentPreview.value) return [];
  return currentPreview.value.uncompletedStudents || [];
});

// 格式化预习内容
const formattedPreviewContent = computed(() => {
  if (!previewMaterial.value || !previewMaterial.value.content) return '';
  
  try {
    // 将Markdown格式的内容转换为HTML
    return marked(previewMaterial.value.content, {
      breaks: true, // 将换行符转换为<br>
      gfm: true     // 启用GitHub风格的Markdown
    });
  } catch (error) {
    console.error('Markdown解析失败:', error);
    return previewMaterial.value.content; // 如果解析失败，返回原始内容
  }
});

// 格式化时间（秒）为小时分钟秒
const formatTime = (seconds) => {
  if (!seconds) return '0秒';
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  let result = '';
  if (hours > 0) {
    result += `${hours}小时`;
  }
  if (minutes > 0 || hours > 0) {
    result += `${minutes}分钟`;
  }
  result += `${remainingSeconds}秒`;

  return result;
}

// 监听classId变化
watch(() => props.classId, (newClassId) => {
  if (newClassId) {
    fetchChapterList()
  }
}, { immediate: true })

// 生命周期钩子
onMounted(() => {
  if (props.classId) {
  fetchChapterList()
  }
})

// 取消发布预习
const cancelPreview = async (preview) => {
  try {
    loading.value = true
    console.log('取消发布')
    const response = await axios.delete(`/api/class-preview/previews/${preview.id}`)
    if (response.data.success) {
      message.success('取消发布成功')
      await fetchPreviewList()
    } else {
      message.error(response.data.message || '取消发布失败')
    }
  } catch (error) {
    console.error('取消发布失败:', error)
    message.error('取消发布失败：' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 使用统一的样式类名，样式已在assessment.css中定义 */
.preview-management {
  padding: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-content,
.submission-content {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--background-color-dark);
  border-radius: var(--border-radius-md);
  white-space: pre-wrap;
}

.student-info {
  display: flex;
  align-items: center;
  max-width: 50%;
}

.student-name {
  margin-left: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Markdown样式 */
.markdown-body {
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #eaecef;
  overflow-x: auto;
}

.markdown-body h1, 
.markdown-body h2, 
.markdown-body h3, 
.markdown-body h4, 
.markdown-body h5, 
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 {
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-body h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-body h3 {
  font-size: 1.25em;
}

.markdown-body p {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body ul, 
.markdown-body ol {
  padding-left: 2em;
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body li {
  margin-bottom: 0.25em;
}

.markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
}

.markdown-body pre {
  word-wrap: normal;
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-body blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 0 0 16px 0;
}

.detail-content {
  margin-top: 16px;
  max-height: 70vh;
  overflow-y: auto;
}
</style>
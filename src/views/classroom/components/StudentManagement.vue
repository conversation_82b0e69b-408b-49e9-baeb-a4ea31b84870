<template>
  <div class="student-management">
    <div class="management-header">
      <div class="search-box">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索学号或姓名"
          enter-button
          @search="handleSearch"
          style="width: 300px"
        />
      </div>
      <a-button type="primary" @click="showAddStudentModal">
        <template #icon><plus-outlined /></template>
        添加学生
      </a-button>
    </div>

    <a-table
      :columns="columns"
      :data-source="studentList"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="student_id"
    >
      <!-- 状态列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status === 1 ? 'success' : 'default'">
            {{ record.status === 1 ? '在读' : '已退课' }}
          </a-tag>
        </template>
        
        <!-- 加入时间列 -->
        <template v-else-if="column.key === 'join_time'">
          {{ formatDate(record.join_time) }}
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <a-space>
            <a @click="showStudentDetail(record)">详情</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定要将该学生移出班级吗?"
              @confirm="handleRemoveStudent(record)"
            >
              <a class="text-red-500">移除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 添加学生模态框 -->
    <a-modal
      v-model:visible="addStudentVisible"
      title="添加学生"
      @ok="handleAddStudent"
      :confirm-loading="submitting"
    >
      <a-form :model="addStudentForm" :rules="rules" ref="addStudentFormRef">
        <a-form-item label="学号" name="student_id">
          <a-input 
            v-model:value="addStudentForm.student_id" 
            placeholder="请输入学号"
            @change="handleStudentIdChange"
          />
        </a-form-item>
        <a-form-item label="姓名" name="name">
          <a-input 
            v-model:value="addStudentForm.name" 
            placeholder="请输入姓名"
            disabled
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 学生详情抽屉 -->
    <a-drawer
      v-model:visible="detailVisible"
      title="学生详情"
      placement="right"
      width="500px"
    >
      <template v-if="selectedStudent">
        <div class="student-detail">
          <div class="avatar-section">
            <a-avatar 
              :size="100" 
              :src="selectedStudent.avatar" 
              :alt="selectedStudent.name"
            >
              {{ selectedStudent.name?.[0] }}
            </a-avatar>
          </div>
          <div class="info-section">
            <div class="info-item">
              <span class="label">学号:</span>
              <span class="value">{{ selectedStudent.student_id }}</span>
            </div>
            <div class="info-item">
              <span class="label">姓名:</span>
              <span class="value">{{ selectedStudent.name }}</span>
            </div>
            <div class="info-item">
              <span class="label">手机:</span>
              <span class="value">{{ selectedStudent.phone || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">邮箱:</span>
              <span class="value">{{ selectedStudent.email || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">加入时间:</span>
              <span class="value">{{ formatDate(selectedStudent.join_time) }}</span>
            </div>
            <div class="info-item">
              <span class="label">状态:</span>
              <a-tag :color="selectedStudent.status === 1 ? 'success' : 'default'">
                {{ selectedStudent.status === 1 ? '在读' : '已退课' }}
              </a-tag>
            </div>
          </div>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import dayjs from 'dayjs'
import axios from '@/axios'

interface StudentInfo {
  student_id: string
  name: string
  phone: string | null
  email: string | null
  avatar: string | null
  join_time: string
  status: number
}

interface FetchParams {
  page?: number
  pageSize?: number
  search?: string
}

// Props
const props = defineProps<{
  classId: number | string
}>()

// Emits
const emit = defineEmits(['update:studentList'])

// 状态变量
const loading = ref(false)
const submitting = ref(false)
const searchText = ref('')
const studentList = ref<StudentInfo[]>([])
const selectedStudent = ref<StudentInfo | null>(null)
const addStudentVisible = ref(false)
const detailVisible = ref(false)

// 表单相关
const addStudentFormRef = ref()
const addStudentForm = ref({
  student_id: '',
  name: ''
})

// 表单校验规则
const rules = {
  student_id: [
    { required: true, message: '请输入学号' },
    { pattern: /^\d+$/, message: '学号必须是数字' }
  ]
}

// 分页配置
const pagination = ref<TablePaginationConfig>({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true
})

// 表格列定义
const columns = [
  {
    title: '学号',
    dataIndex: 'student_id',
    key: 'student_id',
    width: 120
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '加入时间',
    dataIndex: 'join_time',
    key: 'join_time',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 获取学生列表
const fetchStudentList = async (params: FetchParams = {}) => {
  try {
    if (!props.classId) return  // 添加判断，避免无效请求
    
    loading.value = true
    const { page = 1, pageSize = 10, search = '' } = params
    
    const response = await axios.get(`/api/classes/${props.classId}/students`, {
      params: {
        page,
        pageSize,
        search
      }
    })

    if (response.data.success) {
      studentList.value = response.data.data.list
      pagination.value = {
        ...pagination.value,
        total: response.data.data.total,
        current: page,
        pageSize: pageSize
      }
      // 修改这里：使用total而不是list.length
      emit('update:studentList', response.data.data.total)
    }
  } catch (error) {
    console.error('获取学生列表失败:', error)
    message.error('获取学生列表失败')
  } finally {
    loading.value = false
  }
}

// 添加对 classId 的监听
watch(
  () => props.classId,
  (newClassId) => {
    if (newClassId) {
      // 重置分页和搜索条件
      pagination.value.current = 1
      searchText.value = ''
      // 重新加载数据
      fetchStudentList({
        page: 1,
        pageSize: pagination.value.pageSize
      })
    }
  },
  { immediate: true }  // 立即执行一次
)

// 搜索学生
const handleSearch = (value: string) => {
  searchText.value = value
  pagination.value.current = 1
  fetchStudentList({
    page: 1,
    pageSize: pagination.value.pageSize,
    search: value
  })
}

// 表格变化处理
const handleTableChange = (pag: TablePaginationConfig) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchStudentList({
    page: pag.current,
    pageSize: pag.pageSize,
    search: searchText.value
  })
}

// 显示添加学生模态框
const showAddStudentModal = () => {
  addStudentForm.value = {
    student_id: '',
    name: ''
  }
  addStudentVisible.value = true
}

// 学号变化时查询学生信息
const handleStudentIdChange = async () => {
  if (!addStudentForm.value.student_id) return
  
  try {
    const response = await axios.get(`/api/students/${addStudentForm.value.student_id}`)
    if (response.data.success) {
      addStudentForm.value.name = response.data.data.name
    }
  } catch (error) {
    console.error('查询学生信息失败:', error)
    message.error('查询学生信息失败')
  }
}

// 添加学生
const handleAddStudent = async () => {
  try {
    await addStudentFormRef.value.validate()
    submitting.value = true
    
    const response = await axios.post(`/api/classes/${props.classId}/students`, {
      student_id: addStudentForm.value.student_id
    })
    
    if (response.data.success) {
      message.success('添加学生成功')
      addStudentVisible.value = false
      fetchStudentList({
        page: pagination.value.current,
        pageSize: pagination.value.pageSize,
        search: searchText.value
      })
    }
  } catch (error) {
    console.error('添加学生失败:', error)
    message.error(error.response?.data?.message || '添加学生失败')
  } finally {
    submitting.value = false
  }
}

// 显示学生详情
const showStudentDetail = (student: StudentInfo) => {
  selectedStudent.value = student
  detailVisible.value = true
}

// 移除学生
const handleRemoveStudent = async (student: StudentInfo) => {
  try {
    loading.value = true
    const response = await fetch(
      `/api/classes/${props.classId}/students/${student.student_id}`,
      { method: 'DELETE' }
    )
    const data = await response.json()
    if (data.success) {
      message.success('移除学生成功')
      fetchStudentList({
        page: pagination.value.current,
        pageSize: pagination.value.pageSize,
        search: searchText.value
      })
    }
  } catch (error) {
    console.error('移除学生失败:', error)
    message.error('移除学生失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 初始化
onMounted(() => {
  fetchStudentList()
})
</script>

<style scoped>
.student-management {
  width: 100%;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.student-detail {
  padding: 20px;
}

.avatar-section {
  text-align: center;
  margin-bottom: 24px;
}

.info-section {
  .info-item {
    margin-bottom: 16px;
    display: flex;
    
    .label {
      width: 80px;
      color: #666;
    }
    
    .value {
      flex: 1;
      color: #333;
    }
  }
}

/* 响应式处理 */
@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    gap: 16px;
    
    .search-box {
      width: 100%;
    }
    
    .ant-btn {
      width: 100%;
    }
  }
}
</style> 
<template>
  <div class="data-analysis">
    <a-card title="班级预习完成情况统计" :loading="loading">
      <template v-if="!loading">
        <div class="chart-header">
          <div class="summary-data">
            <div class="summary-item">
              <div class="summary-title">学生总数</div>
              <div class="summary-value">{{ previewStats.totalStudents }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-title">已完成学生</div>
              <div class="summary-value">{{ previewStats.completedStudents }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-title">完成率</div>
              <div class="summary-value">{{ completionRate }}%</div>
            </div>
          </div>
        </div>
        
        <div class="chart-container">
          <div ref="pieChartRef" class="pie-chart"></div>
          <div ref="barChartRef" class="bar-chart"></div>
        </div>
      </template>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import axios from '@/axios'
import * as echarts from 'echarts'

const props = defineProps({
  classId: {
    type: [String, Number],
    required: true
  }
})

// 状态变量
const loading = ref(false)
const previewStats = ref({
  totalStudents: 0,
  completedStudents: 0,
  incompletedStudents: 0
})
const previewList = ref([])
const pieChartRef = ref(null)
const barChartRef = ref(null)
let pieChart = null
let barChart = null

// 计算属性
const completionRate = computed(() => {
  // 优先使用后端返回的完成率
  if (previewStats.value.completionRate !== undefined) {
    return previewStats.value.completionRate;
  }
  
  // 后备计算方式
  const { totalStudents, completedStudents } = previewStats.value
  if (totalStudents === 0) return 0
  return Math.round((completedStudents / totalStudents) * 100)
})

// 表格列定义
const columns = [
  {
    title: '预习任务',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    key: 'deadline'
  },
  {
    title: '完成率',
    dataIndex: 'completion',
    key: 'completion',
    width: 200
  }
]

// 获取分析数据
const fetchAnalysisData = async () => {
  if (!props.classId) return
  
  try {
    loading.value = true
    const apiUrl = `/api/analysis/class-preview/${props.classId}`
    console.log('请求数据分析API:', apiUrl)
    
    const response = await axios.get(apiUrl)
    console.log('获取到的原始响应:', response)
    
    if (response.data && response.data.success) {
      const { stats, previewList: list } = response.data.data
      console.log('解析后的数据:', {
        stats,
        previewList: list
      })
      
      // 检查stats对象是否包含预期的字段
      console.log('统计数据字段检查:', {
        totalStudents: stats.totalStudents,
        completedStudents: stats.completedStudents,
        completionRate: stats.completionRate
      })
      
      previewStats.value = {
        totalStudents: Number(stats.totalStudents) || 0,
        completedStudents: Number(stats.completedStudents) || 0,
        incompletedStudents: (Number(stats.totalStudents) || 0) - (Number(stats.completedStudents) || 0),
        completionRate: Number(stats.completionRate) || 0
      }
      
      console.log('处理后的统计数据:', previewStats.value)
      
      previewList.value = list.map(item => ({
        ...item,
        key: item.id
      }))
      
      // 初始化图表
      initCharts()
    } else {
      console.error('数据格式不正确:', response.data)
      message.error('获取数据失败: 响应格式不正确')
    }
  } catch (error) {
    console.error('获取分析数据失败:', error)
    message.error('获取分析数据失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  // 确保DOM已经渲染
  setTimeout(() => {
    initPieChart()
    initBarChart()
  }, 100)
}

// 初始化饼图
const initPieChart = () => {
  if (!pieChartRef.value) return
  
  // 如果实例已存在，销毁它
  if (pieChart) {
    pieChart.dispose()
  }
  
  // 创建新实例
  pieChart = echarts.init(pieChartRef.value)
  
  const option = {
    title: {
      text: '预习完成情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['已完成', '未完成']
    },
    series: [
      {
        name: '预习完成情况',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: [
          { 
            value: previewStats.value.completedStudents, 
            name: '已完成',
            itemStyle: { color: '#52c41a' }
          },
          { 
            value: previewStats.value.incompletedStudents, 
            name: '未完成',
            itemStyle: { color: '#f5222d' }
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  pieChart.setOption(option)
  
  // 窗口大小变化时自适应
  window.addEventListener('resize', () => {
    pieChart?.resize()
  })
}

// 初始化柱状图
const initBarChart = () => {
  if (!barChartRef.value || previewList.value.length === 0) return
  
  // 如果实例已存在，销毁它
  if (barChart) {
    barChart.dispose()
  }
  
  // 创建新实例
  barChart = echarts.init(barChartRef.value)
  
  // 只取前5个预习任务用于显示
  const displayData = previewList.value.slice(0, 5)
  
  const option = {
    title: {
      text: '预习任务完成率对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: displayData.map(item => item.title),
      axisLabel: {
        interval: 0,
        rotate: 30,
        textStyle: {
          fontSize: 10
        }
      }
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '完成率',
        type: 'bar',
        data: displayData.map(item => item.completion),
        itemStyle: {
          color: function(params) {
            const value = params.value
            return value < 30 ? '#f5222d' : value < 70 ? '#faad14' : '#52c41a'
          }
        }
      }
    ]
  }
  
  barChart.setOption(option)
  
  // 窗口大小变化时自适应
  window.addEventListener('resize', () => {
    barChart?.resize()
  })
}

// 获取进度条状态
const getProgressStatus = (percent) => {
  if (percent < 30) return 'exception'
  if (percent < 70) return 'normal'
  return 'success'
}

// 监听班级ID变化
watch(() => props.classId, (newClassId) => {
  if (newClassId) {
    fetchAnalysisData()
  }
}, { immediate: true })

// 组件卸载前清理图表实例
onMounted(() => {
  if (props.classId) {
    fetchAnalysisData()
  }
})

// 组件卸载前清理图表实例
const beforeUnmount = () => {
  if (pieChart) {
    pieChart.dispose()
    pieChart = null
  }
  if (barChart) {
    barChart.dispose()
    barChart = null
  }
  // 移除事件监听
  window.removeEventListener('resize', () => {
    pieChart?.resize()
    barChart?.resize()
  })
}
</script>

<style scoped>
.data-analysis {
  padding: 0;
  width: 100%;
}

.chart-header {
  margin-bottom: 20px;
}

.summary-data {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 10px 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.summary-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 5px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.chart-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.pie-chart, .bar-chart {
  height: 300px;
  width: 50%;
}

@media screen and (max-width: 768px) {
  .summary-data {
    flex-direction: column;
    align-items: center;
  }
  
  .summary-item {
    width: 80%;
    margin-bottom: 10px;
  }
  
  .pie-chart, .bar-chart {
    width: 100%;
    margin-bottom: 20px;
  }
}
</style> 
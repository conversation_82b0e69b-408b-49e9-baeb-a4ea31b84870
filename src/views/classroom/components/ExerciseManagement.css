/* scoped */
.exercise-management {
  width: 100%;
}

/* 使用统一的样式变量 */
.tab-content {
  background: var(--background-color-light);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  min-height: 500px;
  width: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
  flex-wrap: wrap; /* 允许在小屏幕上换行 */
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap; /* 允许在小屏幕上换行 */
}

.selection-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
  }

  .header-left {
    flex-direction: column;
    width: 100%;
  }

  .selection-actions {
    margin-top: 16px;
  }

  .ant-select,
  .ant-btn {
    width: 100%;
  }
}

.exercise-select-form {
  max-height: 600px;
  overflow-y: auto;
}

.exercise-preview {
  padding: 16px;
}

.exercise-meta {
  margin: 16px 0;
  display: flex;
  gap: 8px;
}

.exercise-content {
  margin-top: 16px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
  max-height: 600px;
  overflow-y: auto;
}

.exercise-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 24px;
}

.exercise-card {
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 8px;
  border: 1px solid #f0f0f0;
}

.exercise-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.exercise-card-selected {
  border: 1px solid var(--primary-color);
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2);
}

.exercise-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.ant-card-meta-title {
  margin-bottom: 12px !important;
  font-size: 15px;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: auto;
  max-height: 3em;
}

.ant-card-body {
  padding: 16px;
}

.ant-card-meta-description {
  color: #4b5563;
}

.ant-tag {
  margin: 0;
}

/* 响应式布局优化 */
@media (max-width: 1200px) {
  .exercise-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .exercise-grid {
    grid-template-columns: 1fr;
  }
}

/* 新的样式 */
.filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  align-items: center;
  background: var(--background-color-dark);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
}

.filter-item {
  flex-shrink: 0;
}

.exercise-list {
  margin-top: 20px;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-text {
  flex: 1;
  font-weight: 500;
  font-size: 14px;
  color: #1f2937;
  line-height: 1.2;
  margin-right: 8px;
}

.title-actions {
  flex-shrink: 0;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.content-preview {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  background: var(--background-color-dark);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  height: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  word-break: break-word;
}

/* 选择题样式 */
.content-preview .question-title {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.content-preview .question-options {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-preview .question-option {
  display: flex;
  padding: 2px 0;
}

.content-preview .option-key {
  font-weight: 500;
  margin-right: 6px;
  color: var(--primary-color);
  min-width: 18px;
}

.card-footer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
  font-size: 13px;
  color: #6b7280;
}

.knowledge-points {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.publish-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 修复卡片布局问题 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.card-title {
  display: flex;
  align-items: center;
  flex: 1;
}

.title-text {
  font-weight: 500;
  flex: 1;
  word-break: break-word;
}

.title-actions {
  margin-left: 8px;
}

.card-actions {
  flex-shrink: 0;
}

.tag-row {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.content-preview {
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 13px;
  color: #666;
}

.card-footer {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xxs);
}

.knowledge-points {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.exercise-deadline, .exercise-time {
  color: var(--error-color);
  font-weight: var(--font-weight-medium);
}

/* 习题预览 */
.exercise-preview {
  padding: 16px 0;
}

/* 重写Ant Design主题色 */
:deep(.ant-btn-primary) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff !important;
}

:deep(.ant-btn-primary:hover),
:deep(.ant-btn-primary:focus) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  color: #ffffff !important;
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.ant-checkbox-indeterminate .ant-checkbox-inner::after) {
  background-color: var(--primary-color);
}

:deep(.ant-radio-button-wrapper) {
  color: #333;
  border-color: #d9d9d9;
  background: #fff;
}

:deep(.ant-radio-button-wrapper:hover) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.ant-radio-button-wrapper-checked),
:deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)),
:deep(.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)) {
  color: #ffffff !important;
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  box-shadow: -1px 0 0 0 var(--primary-color) !important;
  font-weight: 500;
  text-shadow: none !important;
}

:deep(.ant-radio-button-wrapper-checked:hover),
:deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover) {
  color: #ffffff !important;
  background-color: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
  text-shadow: none !important;
}

:deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before) {
  background-color: var(--primary-color) !important;
}

:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-select-selector:hover),
:deep(.ant-select-open .ant-select-selector) {
  border-color: var(--primary-color) !important;
}

:deep(.ant-select-item-option-selected:not(.ant-select-item-option-disabled)) {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

:deep(.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.2) !important;
}

:deep(.ant-pagination-item-active) {
  border-color: var(--primary-color);
}

:deep(.ant-pagination-item-active a) {
  color: var(--primary-color);
}

:deep(.ant-pagination-item:hover) {
  border-color: var(--primary-color);
}

:deep(.ant-pagination-item:hover a) {
  color: var(--primary-color);
}

:deep(.ant-pagination-prev:hover .ant-pagination-item-link),
:deep(.ant-pagination-next:hover .ant-pagination-item-link) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.exercise-meta {
  margin: 12px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.exercise-meta-item {
  background-color: var(--background-color-dark);
  padding: 2px var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  line-height: 20px;
}

.exercise-meta-item span {
  color: var(--text-secondary);
  margin-right: var(--spacing-xxs);
}

.exercise-content {
  margin-top: 16px;
  line-height: 1.6;
}

/* 批量发布表单 */
.batch-publish-form {
  padding: 8px 0;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  margin-bottom: 8px;
  font-weight: 500;
}

/* 添加额外的强制样式，确保文本颜色为白色 */
:deep(.ant-btn-primary span),
:deep(.ant-radio-button-wrapper-checked span) {
  color: #ffffff !important;
}

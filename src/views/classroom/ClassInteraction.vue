<template>
  <div class="class-management">
    <a-spin :spinning="loading">
      <div class="page-header">
        <h1>班级与学生管理</h1>
        <p class="subtitle">创建班级、管理学生信息和查看班级统计数据</p>
      </div>

      <!-- 错误提示 -->
      <a-alert
        v-if="error"
        type="error"
        :message="error"
        class="mb-4"
        closable
        @close="error = ''"
      />

      <!-- 创建班级卡片 -->
      <div class="content-section" v-if="!hasClass">
        <a-empty description="暂无班级">
          <template #description>
            <p>您还没有创建任何班级</p>
          </template>
          <a-button type="primary" @click="showCreateClassModal">
            创建班级
          </a-button>
        </a-empty>
      </div>

      <!-- 班级管理功能区 -->
      <div class="content-section" v-else>
        <!-- 班级信息和操作区 -->
        <div class="class-header">
          <div class="class-info">
            <h2>{{ currentClass?.name || '暂无班级' }}</h2>
            <a-tag color="blue">{{ currentClass?.semester }}</a-tag>
            <a-button
              type="link"
              :loading="loading"
              @click="refreshCurrentClass"
            >
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </div>
          <div class="class-actions">
            <a-select
              v-model:value="currentClass.id"
              style="width: 200px; margin-right: 16px;"
              placeholder="切换班级"
              @change="switchClass"
            >
              <a-select-option v-for="item in classList" :key="item.id" :value="item.id">
                {{ item.class_name }}
              </a-select-option>
            </a-select>
            <a-button type="primary" @click="showCreateClassModal">创建班级</a-button>
          </div>
        </div>

        <!-- 学生管理内容 -->
        <div class="student-management-container">
          <div class="section-header">
            <h3>学生管理</h3>
            <p>管理班级学生信息、添加和删除学生</p>
          </div>
          <student-management
            :class-id="currentClass.id"
            @update:studentList="onStudentListUpdate"
          />
        </div>
      </div>

      <!-- 创建班级对话框 -->
      <a-modal
        v-model:visible="createClassModalVisible"
        title="创建班级"
        @ok="handleCreateClass"
        :confirmLoading="loading"
      >
        <a-form :model="classForm" :rules="rules" ref="classFormRef">
          <a-form-item label="学期" name="semester">
            <a-select
              v-model:value="classForm.semester"
              placeholder="选择学期"
              style="width: 100%"
            >
              <a-select-option
                v-for="option in semesterOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="课程" name="course">
            <a-select
              v-model:value="classForm.course"
              placeholder="选择课程"
              style="width: 100%"
            >
              <a-select-option
                v-for="course in coursesList"
                :key="course.id"
                :value="course.id"
              >
                {{ course.course_name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="班级名称" name="name">
            <a-input v-model:value="classForm.name" placeholder="班级名称" />
          </a-form-item>
          <a-form-item label="最大学生数" name="maxStudents">
            <a-input-number
              v-model:value="classForm.maxStudents"
              :min="1"
              :max="200"
              style="width: 100%"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import axios from '@/axios'
import StudentManagement from './components/StudentManagement.vue'
import { ReloadOutlined } from '@ant-design/icons-vue'

// 状态变量
const loading = ref(false)
const error = ref('')
const hasClass = ref(false)

const route = useRoute()
const createClassModalVisible = ref(false)
const coursesList = ref([])
const classList = ref([])
const classCache = new Map()

// 当前班级信息
const currentClass = ref({
  id: null,
  name: '',
  semester: '',
  studentCount: 0,
  exerciseCount: 0,
  completionRate: 0
})

// 表单数据
const classFormRef = ref(null)
const classForm = ref({
  name: '',
  semester: '',
  course: '',
  maxStudents: 50
})

// 学期选项
const semesterOptions = [
  { value: '2023-2024-1', label: '2023-2024学年第一学期(秋季)' },
  { value: '2023-2024-2', label: '2023-2024学年第二学期(春季)' },
  { value: '2024-2025-1', label: '2024-2025学年第一学期(秋季)' },
  { value: '2024-2025-2', label: '2024-2025学年第二学期(春季)' },
  { value: '2025-2026-1', label: '2025-2026学年第一学期(秋季)' },
  { value: '2025-2026-2', label: '2025-2026学年第二学期(春季)' }
]

// 表单验证规则
const rules = {
  semester: [{ required: true, message: '请选择学期' }],
  course: [{ required: true, message: '请选择课程' }]
}

// 监听课程和学期变化,自动生成班级名称
watch(
  [() => classForm.value.course, () => classForm.value.semester],
  async ([newCourse, newSemester]) => {
    if (newCourse && newSemester) {
      try {
        console.log('Requesting with params:', {
          courseCode: newCourse,
          semester: newSemester
        });

        const response = await axios.get('/api/classes/generate-name', {
          params: {
            courseCode: newCourse,
            semester: newSemester
          }
        });

        if (response.data.success) {
          classForm.value.name = response.data.data.suggestedName;
        }
      } catch (error) {
        console.error('获取推荐班级名称失败:', error);
        // 添加更详细的错误信息
        if (error.response) {
          console.log('Error response:', error.response.data);
        }
      }
    }
  }
);



// 获取课程列表
const fetchCoursesList = async () => {
  try {
    loading.value = true
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.system_teacher_id) {
      throw new Error('未找到教师信息')
    }

    const response = await axios.get(`/api/courses/teacher/${userInfo.system_teacher_id}`)
    if (response.data.success) {
      coursesList.value = response.data.data.map(course => ({
        id: course.course_code,
        course_name: course.course_name
      }))
    }
  } catch (error) {
    console.error('获取课程列表失败:', error)
    message.error('获取课程列表失败')
  } finally {
    loading.value = false
  }
}

// 获取教师的班级列表
const fetchTeacherClasses = async () => {
  try {
    loading.value = true
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.system_teacher_id) {
      throw new Error('未找到教师信息')
    }

    const response = await axios.get(`/api/classes/teacher/${userInfo.system_teacher_id}`)
    if (response.data.success) {
      classList.value = response.data.data
      hasClass.value = response.data.data.length > 0

      if (hasClass.value) {
        // 获取上次选中的班级ID
        const lastSelectedClassId = localStorage.getItem('lastSelectedClassId')
        let targetClass

        if (lastSelectedClassId) {
          targetClass = response.data.data.find(c => c.id === parseInt(lastSelectedClassId))
        }

        // 如果找不到上次选中的班级，则使用第一个班级
        if (!targetClass) {
          targetClass = response.data.data[0]
        }

        currentClass.value = {
          id: targetClass.id,
          name: targetClass.class_name,
          semester: targetClass.semester,
          studentCount: targetClass.student_count || 0,
          exerciseCount: 0,
          completionRate: 0
        }

        await loadClassData(targetClass.id)
      }
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    message.error('获取班级列表失败')
  } finally {
    loading.value = false
  }
}

const showCreateClassModal = () => {
  createClassModalVisible.value = true
  classForm.value = {
    name: '',
    semester: '',
    course: '',
    maxStudents: 50
  }
}

const handleCreateClass = () => {
  classFormRef.value.validate().then(async () => {
    try {
      loading.value = true
      // 获取教师ID
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')

      const response = await axios.post('/api/classes', {
        courseCode: classForm.value.course,
        className: classForm.value.name,
        teacherId: userInfo.system_teacher_id,
        semester: classForm.value.semester,
        maxStudents: classForm.value.maxStudents
      })

      if (response.data.success) {
        message.success('创建班级成功')
        createClassModalVisible.value = false
        await fetchTeacherClasses() // 刷新班级列表
      }
    } catch (error) {
      console.error('创建班级失败:', error)
      message.error(error.response?.data?.message || '创建班级失败')
    } finally {
      loading.value = false
    }
  })
}

// 修改切换班级的方法
const switchClass = async (classId) => {
  const classInfo = classList.value.find(item => item.id === classId)
  if (classInfo) {
    currentClass.value = {
      id: classInfo.id,
      name: classInfo.class_name,
      semester: classInfo.semester,
      studentCount: classInfo.student_count || 0,
      exerciseCount: 0,
      completionRate: 0
    }
    // 保存当前选中的班级ID
    localStorage.setItem('lastSelectedClassId', classId)
    await loadClassData(classId)
  }
}

// 完善加载班级数据的方法
const loadClassData = async (classId) => {
  try {
    loading.value = true
    error.value = ''

    // 检查缓存
    const cacheKey = `class_${classId}`
    const cachedData = classCache.get(cacheKey)
    const now = Date.now()

    // 如果缓存存在且未过期（5分钟内）
    if (cachedData && (now - cachedData.timestamp) < 5 * 60 * 1000) {
      currentClass.value = cachedData.data
      return
    }

    const [classDetailsRes, studentsRes, exercisesRes] = await Promise.all([
      axios.get(`/api/classes/${classId}`),
      axios.get(`/api/classes/${classId}/students`, {
        params: { page: 1, pageSize: 1 }
      }),
      axios.get(`/api/classes/${classId}/exercises`, {
        params: { published: true }
      })
    ])

    if (classDetailsRes.data.success && studentsRes.data.success) {
      // 获取习题数量
      let exerciseCount = 0;
      if (exercisesRes.data.success) {
        exerciseCount = exercisesRes.data.data.length || 0;
      }

      const classData = {
        ...currentClass.value,
        ...classDetailsRes.data.data,
        studentCount: studentsRes.data.data.total || 0,
        exerciseCount: exerciseCount
      }

      // 更新缓存
      classCache.set(cacheKey, {
        data: classData,
        timestamp: now
      })

      currentClass.value = classData
    }
  } catch (error) {
    handleError(error, '加载班级数据失败')
  } finally {
    loading.value = false
  }
}

// 添加刷新方法
const refreshCurrentClass = async () => {
  if (currentClass.value?.id) {
    // 清除缓存
    classCache.delete(`class_${currentClass.value.id}`)
    await loadClassData(currentClass.value.id)
  }
}

// 错误处理方法
const handleError = (error, message) => {
  console.error(message, error)
  error.value = error.response?.data?.message || message
  message.error(error.value)
}

const onStudentListUpdate = (newTotal) => {
  if (currentClass.value) {
    currentClass.value.studentCount = newTotal
  }
}

onMounted(() => {
  fetchCoursesList()
  fetchTeacherClasses()
})
</script>

<style scoped>
.class-management {
  padding: 32px;
  background: #f8fafc;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 40px;
  text-align: center;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page-header h1 {
  font-size: 32px;
  color: #1e293b;
  margin-bottom: 12px;
  font-weight: 600;
}

.subtitle {
  color: #64748b;
  font-size: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.content-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  background: transparent;
}

.class-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.class-header .class-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.class-header h2 {
  margin: 0;
  font-size: 24px;
  color: #1f2937;
  font-weight: 600;
}

.section-header p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.class-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #4c4de6;
  margin-bottom: 8px;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.tab-content {
  background: #fff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  min-height: 500px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.analysis-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.analysis-card {
  min-height: 350px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.analysis-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 8px;
  margin: 16px;
}

.chart-placeholder {
  color: #94a3b8;
  font-size: 16px;
}

/* 学生管理容器样式 */
.student-management-container {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.section-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

/* 表格样式优化 */
:deep(.ant-table-wrapper) {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

:deep(.ant-table-thead > tr > th) {
  background: #f8fafc;
  font-weight: 600;
  padding: 16px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 16px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f1f5f9;
}

/* 表单样式优化 */
:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #1f2937;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-input-number),
:deep(.ant-picker) {
  border-radius: 8px;
  padding: 8px 12px;
  border-color: #e2e8f0;
}

:deep(.ant-input:hover),
:deep(.ant-select-selector:hover),
:deep(.ant-input-number:hover),
:deep(.ant-picker:hover) {
  border-color: #0ea5e9;
}

:deep(.ant-btn) {
  border-radius: 8px;
  height: 40px;
  padding: 0 20px;
  font-weight: 500;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 24px;
}

:deep(.ant-tabs-tab) {
  padding: 12px 24px;
  font-size: 16px;
}

:deep(.ant-tabs-tab-active) {
  font-weight: 600;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .content-section {
    padding: 0 16px;
  }

  .class-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .class-management {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
    margin-bottom: 24px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .class-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .class-header .class-actions {
    width: 100%;
  }

  .class-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .tab-header {
    flex-direction: column;
  }

  .tab-content {
    padding: 16px;
  }

  .analysis-cards {
    grid-template-columns: 1fr;
  }

  :deep(.ant-table-wrapper) {
    margin: 0 -16px;
    border-radius: 0;
  }
}
</style>
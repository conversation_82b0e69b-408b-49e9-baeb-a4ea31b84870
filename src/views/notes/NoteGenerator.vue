<template>
  <div class="note-generator-page">
    <div class="page-header">
      <div class="title-section">
        <a-button
          class="back-button"
          @click="goBack"
        >
          <template #icon><i class="fas fa-arrow-left"></i></template>
          返回
        </a-button>
        <h1>{{ sourceType === 'video' ? '视频笔记生成' : '录音笔记生成' }}</h1>
      </div>
      <div class="header-content">
        <p class="description">{{ sourceType === 'video' ? '通过AI智能分析视频内容，生成结构化笔记' : '通过AI智能分析录音内容，生成结构化笔记' }}</p>
        <div class="source-selector">
          <a-radio-group v-model:value="sourceType" button-style="solid" size="small">
            <a-radio-button value="recording">
              <sound-outlined /> 录音转笔记
            </a-radio-button>
            <a-radio-button value="video">
              <video-camera-outlined /> 收藏视频转笔记
            </a-radio-button>
          </a-radio-group>
        </div>
      </div>
    </div>

    <div class="content-container">
      <a-row :gutter="12">
        <!-- 左侧内容：视频和文字转录 -->
        <a-col :span="10">
          <a-card
            :title="sourceType === 'video' ? '视频内容' : '录音内容'"
            class="video-card"
            :bodyStyle="{ padding: 0 }"
            size="small"
          >
            <!-- 视频模式 -->
            <template v-if="sourceType === 'video'">
              <template v-if="currentVideo">
                <div class="video-container" style="height: auto;">
                  <div class="aspect-w-16 aspect-h-9">
                    <iframe
                      :src="videoUrl"
                      frameborder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowfullscreen
                      class="w-full h-full"
                    ></iframe>
                  </div>
                  <div class="video-info">
                    <h3 class="text-md font-medium mb-1">{{ currentVideo.title }}</h3>
                    <div class="text-xs text-gray-500">
                      <div class="flex items-center justify-between">
                        <span v-if="currentVideo.author">作者：{{ currentVideo.author }}</span>
                        <span>来源：{{ currentVideo.source || '未知' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="empty-state p-4 text-center">
                  <upload-outlined style="font-size: 36px; color: #d9d9d9; margin-bottom: 12px;" />
                  <p class="text-gray-500 mb-2">暂无视频，请先选择视频</p>
                  <a-button type="primary" size="small" @click="showVideoSelector">
                    从收藏选择视频
                  </a-button>
                </div>
              </template>
            </template>

            <!-- 录音模式 -->
            <template v-else>
              <div class="audio-container p-4">
                <!-- 录音状态显示 -->
                <template v-if="audioUrl">
                  <div class="audio-player">
                    <h3 class="text-md font-medium mb-2">录音文件</h3>
                    <audio controls class="w-full mb-3" :src="audioUrl"></audio>
                    <div class="audio-info text-xs text-gray-500 mb-3">
                      <div class="flex items-center justify-between">
                        <span>录音时长：{{ formatRecordingTime(recordingTime) }}</span>
                      </div>
                    </div>
                    <div class="audio-actions flex justify-between">
                      <div>
                        <a-button size="small" danger @click="clearRecording" class="mr-2">
                          <template #icon><delete-outlined /></template>
                          删除录音
                        </a-button>
                        <a-button size="small" @click="startRecording">
                          <template #icon><sound-outlined /></template>
                          重新录制
                        </a-button>
                      </div>
                      <a-button
                        type="primary"
                        size="small"
                        @click="startTranscription"
                        :loading="transcribing"
                        :disabled="transcribing"
                      >
                        <template #icon><file-text-outlined /></template>
                        转录音频
                      </a-button>
                    </div>
                  </div>
                </template>

                <!-- 录音中状态 -->
                <template v-else-if="isRecording">
                  <div class="recording-state text-center">
                    <div class="recording-indicator mb-3">
                      <sound-outlined spin style="font-size: 36px; color: #ff4d4f;" />
                      <div class="recording-time mt-2">
                        {{ formatRecordingTime(recordingTime) }}
                      </div>
                    </div>
                    <div class="recording-controls">
                      <a-button
                        v-if="!isPaused"
                        type="default"
                        size="small"
                        class="mr-2"
                        @click="pauseRecording"
                      >
                        <template #icon><pause-outlined /></template>
                        暂停
                      </a-button>
                      <a-button
                        v-else
                        type="default"
                        size="small"
                        class="mr-2"
                        @click="resumeRecording"
                      >
                        <template #icon><play-circle-outlined /></template>
                        继续
                      </a-button>
                      <a-button
                        type="primary"
                        danger
                        size="small"
                        @click="stopRecording"
                      >
                        <template #icon><stop-outlined /></template>
                        停止录音
                      </a-button>
                    </div>
                  </div>
                </template>

                <!-- 无录音状态 -->
                <template v-else>
                  <div class="empty-state text-center">
                    <audio-outlined style="font-size: 36px; color: #d9d9d9; margin-bottom: 12px;" />
                    <p class="text-gray-500 mb-2">暂无录音，请先录制或上传录音</p>
                    <div class="audio-actions">
                      <a-button type="primary" size="small" class="mr-2" @click="startRecording">
                        <template #icon><sound-outlined /></template>
                        开始录音
                      </a-button>
                      <a-button size="small" @click="uploadRecording">
                        <template #icon><upload-outlined /></template>
                        上传录音
                      </a-button>
                    </div>
                  </div>
                </template>
              </div>
            </template>
          </a-card>

          <a-card :title="sourceType === 'video' ? '文字转录' : '录音转录'" class="transcript-card mt-3" size="small">
            <a-spin :spinning="transcribing" :tip="sourceType === 'video' ? '正在转录视频内容...' : '正在转录录音内容...'">
              <template v-if="transcript">
                <div class="transcript-content">
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-sm text-gray-500">转录内容</span>
                    <a-button type="text" size="small" @click="clearTranscript" v-if="transcript">
                      <template #icon><delete-outlined /></template>
                      清除转录
                    </a-button>
                  </div>
                  <p v-for="(segment, index) in transcriptSegments" :key="index" class="mb-1">
                    <span class="timestamp text-gray-500">{{ formatTime(segment.start) }}</span>
                    {{ segment.text }}
                  </p>
                </div>
              </template>
              <template v-else>
                <div class="empty-state p-3 text-center">
                  <p class="text-gray-500 text-sm">{{ sourceType === 'video' ? '视频文字转录将在这里显示' : '录音文字转录将在这里显示' }}</p>
                  <template v-if="sourceType === 'video'">
                    <a-button
                      v-if="currentVideo && !transcribing"
                      type="primary"
                      size="small"
                      class="mt-2"
                      @click="startTranscription"
                    >
                      开始转录
                    </a-button>
                  </template>
                  <template v-else>
                    <div class="mt-2">
                      <p class="text-gray-500 text-xs mb-2">请先录制或上传录音文件</p>
                      <a-button
                        v-if="audioBlob && !transcribing"
                        type="primary"
                        size="small"
                        @click="startTranscription"
                        :loading="transcribing"
                      >
                        <template #icon><file-text-outlined /></template>
                        开始转录
                      </a-button>
                    </div>
                  </template>
                </div>
              </template>
            </a-spin>
          </a-card>
        </a-col>

        <!-- 右侧内容：笔记生成 -->
        <a-col :span="14">
          <a-card title="笔记生成" class="note-card" size="small">
            <a-spin :spinning="generating" tip="AI正在生成笔记...">
              <div class="note-form">
                <div class="mb-2">
                  <div class="form-label mb-1">笔记标题</div>
                  <a-input
                    v-model:value="noteTitle"
                    placeholder="请输入笔记标题"
                    :disabled="generating"
                    size="small"
                  />
                </div>

                <div class="mb-2">
                  <div class="form-label mb-1 flex justify-between items-center">
                    <span>笔记内容</span>
                    <div>
                      <a-button
                        type="text"
                        size="small"
                        @click="copyNote"
                        v-if="noteContent"
                      >
                        <template #icon><copy-outlined /></template>
                        复制
                      </a-button>
                    </div>
                  </div>
                  <a-textarea
                    v-model:value="noteContent"
                    placeholder="AI生成的笔记内容将在这里显示"
                    :rows="20"
                    :disabled="generating"
                  />
                </div>

                <div class="note-actions flex justify-between mt-3">
                  <a-button size="small" @click="clearNote" :disabled="!noteContent || generating">
                    清空笔记
                  </a-button>
                  <div>
                    <a-button
                      class="white-button mr-2"
                      size="small"
                      @click="generateNote"
                      :disabled="(sourceType === 'video' && !currentVideo) ||
                                 (sourceType === 'recording' && !audioBlob) ||
                                 !transcript || generating"
                      :loading="generating"
                    >
                      生成笔记
                    </a-button>
                    <a-button
                      class="white-button"
                      size="small"
                      @click="saveNote"
                      :disabled="!noteContent || saving"
                      :loading="saving"
                    >
                      保存笔记
                    </a-button>
                  </div>
                </div>
              </div>
            </a-spin>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 视频选择弹窗 -->
    <a-modal
      v-model:visible="videoSelectorVisible"
      title="选择视频"
      width="70%"
      :footer="null"
    >
      <a-spin :spinning="loadingFavorites">
        <div v-if="videoFavorites.length" class="video-grid">
          <a-card
            v-for="item in videoFavorites"
            :key="item.id"
            hoverable
            class="video-select-card"
            @click="selectVideo(item)"
          >
            <template #cover>
              <div class="video-thumbnail">
                <img
                  :src="getProcessedImageUrl(item.cover_url)"
                  class="w-full h-full object-cover"
                  :alt="item.title"
                  loading="lazy"
                  @error="handleImageError"
                  referrerpolicy="no-referrer"
                />
              </div>
            </template>
            <a-card-meta :title="item.title">
              <template #description>
                <div class="mt-2 text-sm text-gray-500">
                  <div class="flex items-center justify-between">
                    <span v-if="item.author">UP主：{{ item.author }}</span>
                  </div>
                  <div class="mt-1 flex items-center justify-between">
                    <span>来源：{{ item.source }}</span>
                  </div>
                </div>
              </template>
            </a-card-meta>
          </a-card>
        </div>
        <a-empty v-else description="暂无收藏的视频" />
      </a-spin>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import {
  UploadOutlined,
  CopyOutlined,
  VideoCameraOutlined,
  SoundOutlined,
  AudioOutlined,
  DeleteOutlined,
  PauseOutlined,
  PlayCircleOutlined,
  StopOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue';

// 创建axios实例
const http = axios.create({
  baseURL: '/api',
  timeout: 900000, // 15分钟超时，讯飞转录可能需要较长时间
  headers: {
    'Content-Type': 'application/json'
  }
});

// 添加请求拦截器
http.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
http.interceptors.response.use(
  response => response,
  error => {
    console.error('请求失败:', error);
    if (error.response?.status === 401) {
      message.error('请先登录');
    } else {
      message.error(error.response?.data?.message || '操作失败，请稍后重试');
    }
    return Promise.reject(error);
  }
);

interface FavoriteItem {
  id: number;
  resource_type: 'video' | 'article';
  resource_id: string;
  title: string;
  url: string;
  description?: string;
  cover_url?: string;
  source: string;
  source_type: string;
  author?: string;
  create_time: string;
}

interface TranscriptSegment {
  start: number;
  end: number;
  text: string;
}

const router = useRouter();
const route = useRoute();

// 状态变量
const sourceType = ref<'video' | 'recording'>('video'); // 默认为视频模式

// 监听 sourceType 变化
watch(sourceType, (newType: 'video' | 'recording', oldType: 'video' | 'recording') => {
  if (newType !== oldType) {
    // 清除转录和笔记相关的状态
    transcript.value = '';
    transcriptSegments.value = [];
    noteContent.value = '';

    // 如果从录音切换到视频，清除录音相关的状态
    if (oldType === 'recording') {
      audioBlob.value = null;
      recordingTime.value = 0;

      // 清除录音文件
      if (audioUrl.value) {
        URL.revokeObjectURL(audioUrl.value);
        audioUrl.value = '';
      }
    }

    // 重置所有状态标志
    isRecording.value = false;
    isPaused.value = false;
    transcribing.value = false;
    generating.value = false;
    saving.value = false;
  }
});
const isRecording = ref(false); // 是否正在录音
const recordingTime = ref(0); // 录音时长（秒）
const audioBlob = ref<Blob | null>(null); // 录音文件Blob
const audioUrl = ref<string>(''); // 录音文件URL
const recordingTimer = ref<number | null>(null); // 录音计时器
const isPaused = ref(false); // 是否暂停录音
const mediaRecorder = ref<MediaRecorder | null>(null); // MediaRecorder实例
const audioChunks = ref<BlobPart[]>([]); // 录音数据块
const currentVideo = ref<FavoriteItem | null>(null);
const transcript = ref<string>('');
const transcriptSegments = ref<TranscriptSegment[]>([]);
const noteTitle = ref('');
const noteContent = ref('');
const transcribing = ref(false);
const generating = ref(false);
const saving = ref(false);
const videoSelectorVisible = ref(false);
const loadingFavorites = ref(false);
const videoFavorites = ref<FavoriteItem[]>([]);

// 计算属性：视频URL
const videoUrl = computed(() => {
  if (!currentVideo.value?.resource_id) return '';
  if (currentVideo.value?.source === 'bilibili') {
    return `//player.bilibili.com/player.html?bvid=${currentVideo.value.resource_id}&page=1&high_quality=1&danmaku=0`;
  }
  return currentVideo.value?.url || '';
});

// 在组件挂载时检查路由参数
onMounted(async () => {
  const videoId = route.params.videoId;
  if (videoId) {
    // 从路由参数加载视频
    try {
      const response = await http.get(`/favorites/${videoId}`);
      if (response.data.success) {
        currentVideo.value = response.data.data;
        if (currentVideo.value) {
          noteTitle.value = `${currentVideo.value.title} - 笔记`;
        }
      }
    } catch (error) {
      console.error('加载视频失败:', error);
    }
  }
});

// 在组件卸载时清除状态
onUnmounted(() => {
  // 清除转录和笔记相关的状态
  transcript.value = '';
  transcriptSegments.value = [];
  noteContent.value = '';
  audioBlob.value = null;
  recordingTime.value = 0;

  // 清除录音相关的状态
  if (mediaRecorder.value) {
    if (mediaRecorder.value.state === 'recording') {
      mediaRecorder.value.stop();
    }
    mediaRecorder.value = null;
  }

  // 清除录音文件
  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value);
    audioUrl.value = '';
  }

  // 重置所有状态标志
  isRecording.value = false;
  isPaused.value = false;
  transcribing.value = false;
  generating.value = false;
  saving.value = false;
});

// 返回上一页
const goBack = () => {
  router.back();
};

// 显示视频选择器
const showVideoSelector = async () => {
  loadingFavorites.value = true;
  videoSelectorVisible.value = true;

  try {
    const response = await http.get('/favorites');
    if (response.data.success) {
      videoFavorites.value = response.data.data.videos || [];
    }
  } catch (error) {
    console.error('加载收藏失败:', error);
    message.error('加载收藏失败，请稍后重试');
  } finally {
    loadingFavorites.value = false;
  }
};

// 选择视频
const selectVideo = async (video: FavoriteItem) => {
  currentVideo.value = video;
  noteTitle.value = `${video.title} - 笔记`;
  videoSelectorVisible.value = false;

  // 重置转录和笔记状态
  transcript.value = '';
  transcriptSegments.value = [];
  noteContent.value = '';

  // 检查是否已有该视频的笔记数据
  try {
    message.loading({
      content: '正在检查视频数据...',
      duration: 0,
      key: 'checkingVideo'
    });

    const response = await http.get(`/video-summary/check/${video.resource_id}`);

    message.destroy('checkingVideo');

    if (response.data.success && response.data.exists) {
      // 如果已有数据，直接显示
      const data = response.data.data;

      if (data.hasTranscript) {
        transcript.value = data.transcript;
        transcriptSegments.value = data.transcriptSegments;
        message.success('已加载现有转录数据');
      }

      if (data.hasSummary) {
        noteContent.value = data.summary;
        message.success('已加载现有笔记数据');
      }

      // 更新视频标题(如果数据库中的标题与前端不同)
      if (data.videoTitle && data.videoTitle !== video.title) {
        noteTitle.value = `${data.videoTitle} - 笔记`;
      }
    }
  } catch (error) {
    console.error('检查视频数据失败:', error);
    message.destroy('checkingVideo');
  }
};

// 开始转录
const startTranscription = async () => {
  if (sourceType.value === 'video' && !currentVideo.value) {
    message.warning('请先选择视频');
    return;
  }

  if (sourceType.value === 'recording' && !audioBlob.value) {
    message.warning('请先录制或上传录音');
    return;
  }

  try {
    transcribing.value = true;
    message.loading({
      content: sourceType.value === 'video'
        ? '正在处理视频，可能需要较长时间（5-15分钟）...'
        : '正在处理录音，可能需要较长时间...',
      duration: 0,
      key: 'transcribeLoading'
    });

    let response: any;

    if (sourceType.value === 'video') {
      // 确保 currentVideo 不为 null
      if (!currentVideo.value) {
        throw new Error('视频信息不存在');
      }

      response = await http.post('/video-summary/transcribe', {
        videoUrl: currentVideo.value.url,
        videoId: currentVideo.value.resource_id
      });
    } else {
      // 录音转录处理
      if (!audioBlob.value) {
        throw new Error('录音文件不存在');
      }

      // 创建FormData对象上传录音文件
      const formData = new FormData();
      formData.append('audioFile', audioBlob.value, 'recording.webm');

      // 调用后端录音转录API
      response = await http.post('/audio-summary/transcribe', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    }

    if (response.data.success) {
      transcript.value = response.data.data.transcript || '';

      // 解析转录文本，按句子或段落分割
      if (transcript.value) {
        // 简单实现：按句号分割，添加时间戳
        const sentences = transcript.value.split(/(?<=[。！？.!?])\s*/);
        const duration = response.data.data.duration || sentences.length * 5; // 估计时长

        transcriptSegments.value = sentences.map((text, index) => {
          const start = Math.floor((duration / sentences.length) * index);
          const end = Math.floor((duration / sentences.length) * (index + 1));
          return { start, end, text };
        });
      }

      message.success({
        content: sourceType.value === 'video' ? '视频转录成功' : '录音转录成功',
        key: 'transcribeLoading'
      });
    } else {
      throw new Error(response.data.message || '转录失败');
    }
  } catch (error: any) {
    console.error(sourceType.value === 'video' ? '视频转录失败:' : '录音转录失败:', error);
    message.error({
      content: (sourceType.value === 'video' ? '视频转录失败: ' : '录音转录失败: ') +
               (error.response?.data?.message || error.message),
      key: 'transcribeLoading'
    });
  } finally {
    transcribing.value = false;
  }
};

// 生成笔记
const generateNote = async () => {
  if (sourceType.value === 'video' && !currentVideo.value) {
    message.warning('请先选择视频');
    return;
  }

  try {
    generating.value = true;

    if (sourceType.value === 'recording') {
      // 录音模式下的笔记生成
      if (!audioBlob.value) {
        message.warning('请先录制或上传录音');
        generating.value = false;
        return;
      }

      // 如果没有转录文本，先获取转录
      if (!transcript.value) {
        const shouldTranscribe = await new Promise(resolve => {
          message.info({
            content: '需要先进行录音转录才能生成笔记，是否继续？',
            duration: 0,
            onClose: () => resolve(false)
          });
        });

        if (shouldTranscribe) {
          await startTranscription();
        } else {
          generating.value = false;
          return;
        }
      }

      // 生成笔记
      message.loading({
        content: 'AI正在分析录音内容并生成笔记...',
        duration: 0,
        key: 'generateLoading'
      });

      // 使用转录文本生成笔记
      const response = await http.post('/audio-summary/generate', {
        transcript: transcript.value,
        title: noteTitle.value || '录音笔记'
      });

      if (response.data.success) {
        noteContent.value = response.data.data.summary || '';
        message.success({
          content: '笔记生成成功',
          key: 'generateLoading'
        });
      } else {
        throw new Error(response.data.message || '生成失败');
      }
    } else {
      // 视频模式下的笔记生成
      // 如果没有转录文本，先获取转录
      if (!transcript.value) {
        const shouldTranscribe = await new Promise(resolve => {
          message.info({
            content: '需要先进行视频转录才能生成笔记，是否继续？',
            duration: 0,
            onClose: () => resolve(false)
          });
        });

        if (shouldTranscribe) {
          await startTranscription();
        } else {
          generating.value = false;
          return;
        }
      }

      // 生成笔记
      message.loading({
        content: 'AI正在分析视频内容并生成笔记...',
        duration: 0,
        key: 'generateLoading'
      });

      // 确保 currentVideo 不为 null
      if (!currentVideo.value) {
        throw new Error('视频信息不存在');
      }

      const response = await http.post('/video-summary/generate', {
        videoUrl: currentVideo.value.url,
        videoId: currentVideo.value.resource_id,
        transcript: transcript.value,
        title: currentVideo.value.title
      });

      if (response.data.success) {
        noteContent.value = response.data.data.summary || '';
        message.success({
          content: '笔记生成成功',
          key: 'generateLoading'
        });
      } else {
        throw new Error(response.data.message || '生成失败');
      }
    }
  } catch (error: any) {
    console.error('生成笔记失败:', error);
    message.error({
      content: '生成笔记失败: ' + (error.response?.data?.message || error.message),
      key: 'generateLoading'
    });
  } finally {
    generating.value = false;
  }
};

// 保存笔记
const saveNote = async () => {
  if (!noteContent.value) {
    message.warning('请先生成笔记内容');
    return;
  }

  if (sourceType.value === 'video' && !currentVideo.value) {
    message.warning('请先选择视频');
    return;
  }

  try {
    saving.value = true;

    // 录音模式下的保存逻辑
    if (sourceType.value === 'recording') {
      const response = await http.post('/audio-summary/save', {
        title: noteTitle.value || '录音笔记',
        content: noteContent.value,
        transcript: transcript.value,
        duration: recordingTime.value
      });

      if (response.data.success) {
        message.success('录音笔记保存成功');
      } else {
        throw new Error(response.data.message || '保存失败');
      }
    } else {
      // 视频模式下的保存逻辑
      // 确保 currentVideo 不为 null
      if (!currentVideo.value) {
        throw new Error('视频信息不存在');
      }

      const response = await http.post('/video-summary/save', {
        videoId: currentVideo.value.resource_id,
        videoUrl: currentVideo.value.url,
        videoTitle: currentVideo.value.title,
        title: noteTitle.value,
        content: noteContent.value
      });

      if (response.data.success) {
        message.success('视频笔记保存成功');
      } else {
        throw new Error(response.data.message || '保存失败');
      }
    }
  } catch (error: any) {
    console.error('保存笔记失败:', error);
    message.error('保存笔记失败: ' + (error.response?.data?.message || error.message));
  } finally {
    saving.value = false;
  }
};

// 复制笔记
const copyNote = () => {
  navigator.clipboard.writeText(noteContent.value)
    .then(() => {
      message.success('已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
      message.error('复制失败，请手动复制');
    });
};

// 清空笔记
const clearNote = () => {
  noteContent.value = '';
};

// 清除转录
const clearTranscript = () => {
  transcript.value = '';
  transcriptSegments.value = [];
  message.success('转录已清除');
};

// 格式化时间戳
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `[${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}] `;
};

// 处理图片URL
function getProcessedImageUrl(url: string | undefined): string {
  if (!url) return '';

  try {
    if (url.startsWith('//')) {
      return `https:${url}`;
    } else if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    } else {
      return `https://${url}`;
    }
  } catch (error) {
    console.error('处理图片链接时出错:', error);
    return '';
  }
}

// 处理图片加载错误
function handleImageError(e: Event) {
  const img = e.target as HTMLImageElement;
  img.src = 'https://placehold.co/672x378/e5e7eb/475569?text=Video';
}

// 录音相关功能
// 格式化录音时间
const formatRecordingTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 开始录音
const startRecording = async () => {
  try {
    // 重置状态
    isRecording.value = false;
    isPaused.value = false;
    recordingTime.value = 0;
    audioChunks.value = [];

    // 清除现有录音
    if (audioUrl.value) {
      URL.revokeObjectURL(audioUrl.value);
      audioUrl.value = '';
      audioBlob.value = null;
    }

    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // 创建MediaRecorder实例
    mediaRecorder.value = new MediaRecorder(stream);

    // 收集录音数据
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.value.push(event.data);
      }
    };

    // 录音结束时的处理
    mediaRecorder.value.onstop = () => {
      // 创建Blob对象
      const blob = new Blob(audioChunks.value, { type: 'audio/webm' });
      audioBlob.value = blob;

      // 创建URL供播放器使用
      audioUrl.value = URL.createObjectURL(blob);

      // 停止所有音频轨道
      if (mediaRecorder.value) {
        const tracks = mediaRecorder.value.stream.getTracks();
        tracks.forEach(track => track.stop());
      }

      // 清除计时器
      if (recordingTimer.value) {
        clearInterval(recordingTimer.value);
        recordingTimer.value = null;
      }

      isRecording.value = false;
      message.success('录音完成');
    };

    // 开始录音
    mediaRecorder.value.start(100); // 每100ms收集一次数据
    isRecording.value = true;

    // 开始计时
    recordingTimer.value = window.setInterval(() => {
      recordingTime.value++;
    }, 1000);

    message.success('录音已开始');
  } catch (error) {
    console.error('录音失败:', error);
    message.error('无法访问麦克风，请检查权限设置');
  }
};

// 暂停录音
const pauseRecording = () => {
  if (mediaRecorder.value && isRecording.value && !isPaused.value) {
    mediaRecorder.value.pause();
    isPaused.value = true;

    // 暂停计时器
    if (recordingTimer.value) {
      clearInterval(recordingTimer.value);
      recordingTimer.value = null;
    }

    message.info('录音已暂停');
  }
};

// 继续录音
const resumeRecording = () => {
  if (mediaRecorder.value && isRecording.value && isPaused.value) {
    mediaRecorder.value.resume();
    isPaused.value = false;

    // 继续计时
    recordingTimer.value = window.setInterval(() => {
      recordingTime.value++;
    }, 1000);

    message.info('录音已继续');
  }
};

// 停止录音
const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop();
    // onstop事件处理函数会处理后续逻辑
  }
};

// 清除录音
const clearRecording = () => {
  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value);
    audioUrl.value = '';
    audioBlob.value = null;
    recordingTime.value = 0;
    message.success('录音已清除');
  }
};

// 上传录音
const uploadRecording = () => {
  // 创建文件输入元素
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = 'audio/*';
  fileInput.style.display = 'none';
  document.body.appendChild(fileInput);

  // 监听文件选择
  fileInput.onchange = (event) => {
    const target = event.target as HTMLInputElement;
    const files = target.files;

    if (files && files.length > 0) {
      const file = files[0];

      // 清除现有录音
      if (audioUrl.value) {
        URL.revokeObjectURL(audioUrl.value);
      }

      // 创建URL供播放器使用
      audioBlob.value = file;
      audioUrl.value = URL.createObjectURL(file);

      // 获取音频时长
      const audio = new Audio();
      audio.src = audioUrl.value;
      audio.onloadedmetadata = () => {
        recordingTime.value = Math.floor(audio.duration);
      };

      message.success(`已上传音频文件: ${file.name}`);
    }

    // 清理文件输入元素
    document.body.removeChild(fileInput);
  };

  // 触发文件选择对话框
  fileInput.click();
};
</script>

<style scoped>
.note-generator-page {
  padding: 16px;
  max-width: 1800px;
  margin: 0 auto;
  background-color: #f5f6fa;
  min-height: calc(100vh - 64px);
  height: auto;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 16px;
  background-color: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.title-section {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 0;
  margin-left: 10px;
}

.back-button {
  height: 32px;
  border-radius: 4px;
  background-color: #4c4de6;
  border-color: #4c4de6;
  color: #fff;
  transition: all 0.3s ease;
  padding: 0 12px;
  font-size: 14px;
}

.back-button:hover {
  background-color: #6364e9;
  border-color: #6364e9;
  color: #fff;
}

.back-button i {
  font-size: 12px;
  margin-right: 4px;
}

.description {
  color: #666;
  font-size: 12px;
}

.source-selector {
  margin-left: 20px;
}

.audio-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.audio-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.audio-player {
  width: 100%;
}

.recording-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.recording-indicator {
  animation: pulse 1.5s infinite;
}

.recording-time {
  font-size: 18px;
  font-weight: bold;
  margin-top: 10px;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.content-container {
  margin-bottom: 16px;
  height: auto;
}

.content-container :deep(.ant-row) {
  margin-left: -6px !important;
  margin-right: -6px !important;
}

.content-container :deep(.ant-col) {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.video-card, .transcript-card, .note-card {
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: none;
  overflow: hidden;
}

.video-card :deep(.ant-card-head),
.transcript-card :deep(.ant-card-head),
.note-card :deep(.ant-card-head) {
  background-color: #f0f2ff;
  border-bottom: 1px solid #e6e8ff;
}

.video-container {
  position: relative;
  width: 100%;
  height: 600px;
}

.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  height: 0;
  max-height: 338px; /* 按照16:9比例，600px宽度对应的高度 */
}

.aspect-w-16 > iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-info {
  background-color: #f8f9ff;
  border-top: 1px solid #e6e8ff;
  padding: 10px;
}

.transcript-content {
  height: 400px;
  overflow-y: auto;
  padding: 10px;
  font-size: 12px;
  background-color: #ffffff;
  border-radius: 4px;
}

.timestamp {
  font-family: monospace;
  display: inline-block;
  margin-right: 6px;
  font-weight: 500;
  font-size: 11px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 150px;
  background-color: #f9faff;
  border-radius: 6px;
}

.form-label {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.video-select-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #eef0ff;
  background-color: #ffffff;
}

.video-select-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(76, 77, 230, 0.12);
  border-color: #dcdeff;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.video-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

:deep(.ant-card-meta-title) {
  white-space: normal !important;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 12px;
}

/* 重写Ant Design主题色 */
:deep(.ant-btn-primary) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 2px 0 rgba(76, 77, 230, 0.1);
}

:deep(.ant-btn-primary:hover),
:deep(.ant-btn-primary:focus) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  box-shadow: 0 2px 0 rgba(76, 77, 230, 0.2);
}

:deep(.ant-card-head) {
  min-height: auto;
  padding: 0 12px;
}

:deep(.ant-card-head-title) {
  padding: 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #4c4de6;
}

:deep(.ant-input) {
  border-radius: 4px;
  border-color: #e6e8ff;
}

:deep(.ant-input:focus) {
  border-color: #4c4de6;
  box-shadow: 0 0 0 2px rgba(76, 77, 230, 0.1);
}

:deep(.ant-input-textarea) {
  background-color: #ffffff;
  border-radius: 4px;
}

.note-card {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  height: auto;
  min-height: 400px;
}

.note-card :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.note-form {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.note-form > div:nth-child(2) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.note-form > div:nth-child(2) > a-textarea,
.note-form > div:nth-child(2) > .ant-input-textarea,
.note-form > div:nth-child(2) :deep(.ant-input-textarea),
.note-form > div:nth-child(2) :deep(.ant-input) {
  flex: 1;
  height: auto !important;
  min-height: 250px;
  max-height: 600px;
}

.note-form :deep(.ant-input-textarea-show-count::after) {
  position: absolute;
  bottom: 0;
  right: 8px;
}

.note-form :deep(.ant-input) {
  background-color: #fcfcff;
}

/* 自定义按钮样式 */
.white-button {
  background-color: white;
  color: #4c4de6;
  border: 1px solid #4c4de6;
}

.white-button:hover {
  background-color: #f0f2ff;
  color: #4c4de6;
  border-color: #4c4de6;
}

.white-button:focus {
  background-color: white;
  color: #4c4de6;
  border-color: #4c4de6;
}

.white-button:disabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
  border-color: #d9d9d9;
}

/* 自定义单选按钮组样式 */
.custom-radio-group :deep(.ant-radio-button-wrapper) {
  background-color: white;
  color: #4c4de6;
  border: 1px solid #4c4de6;
}

.custom-radio-group :deep(.ant-radio-button-wrapper:hover) {
  background-color: #f0f2ff;
  color: #4c4de6;
}

.custom-radio-group :deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)) {
  background-color: #4c4de6;
  color: white;
  border-color: #4c4de6;
  box-shadow: -1px 0 0 0 #4c4de6;
}

.custom-radio-group :deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before) {
  background-color: #4c4de6;
}
</style>
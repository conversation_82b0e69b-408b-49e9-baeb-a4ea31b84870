<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import axios from '@/axios'
import { UserOutlined, LockOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons-vue'

const router = useRouter()

const formState = ref({
  name: '',
  password: '',
  confirmPassword: '',
  phone: '',
  email: ''
})

const loading = ref(false)

const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度必须在2-20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度必须在6-20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: async (rule, value) => {
        if (value !== formState.value.password) {
          throw new Error('两次输入的密码不一致')
        }
      },
      trigger: 'blur'
    }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const handleRegister = async () => {
  try {
    loading.value = true
    
    // 发送注册请求
    const response = await axios.post('/api/student/register', {
      name: formState.value.name,
      password: formState.value.password,
      phone: formState.value.phone,
      email: formState.value.email
    })

    if (response.data.success) {
      message.success('注册成功,您的学号是: ' + response.data.data.studentId)
      router.push({
        path: '/student-login',
        query: { 
          student_id: response.data.data.studentId 
        }
      })
    } else {
      throw new Error(response.data.message || '注册失败')
    }
  } catch (error) {
    console.error('注册失败:', error)
    message.error(error.response?.data?.message || '注册失败,请重试')
  } finally {
    loading.value = false
  }
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="register-container">
    <div class="register-box">
      <h2>注册账号</h2>
      <a-form
        :model="formState"
        :rules="rules"
        @finish="handleRegister"
        class="register-form"
      >
        <a-form-item name="name">
          <a-input
            v-model:value="formState.name"
            placeholder="请输入姓名"
            size="large"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="password">
          <a-input-password
            v-model:value="formState.password"
            placeholder="请输入密码"
            size="large"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
        <a-form-item name="confirmPassword">
          <a-input-password
            v-model:value="formState.confirmPassword"
            placeholder="请确认密码"
            size="large"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
        <a-form-item name="phone">
          <a-input
            v-model:value="formState.phone"
            placeholder="请输入手机号(选填)"
            size="large"
          >
            <template #prefix>
              <PhoneOutlined />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="email">
          <a-input
            v-model:value="formState.email"
            placeholder="请输入邮箱(选填)"
            size="large"
          >
            <template #prefix>
              <MailOutlined />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            :block="true"
            size="large"
            :loading="loading"
          >
            {{ loading ? '注册中...' : '注册' }}
          </a-button>
        </a-form-item>
        <div class="form-footer">
          <a @click="goToLogin">已有账号？立即登录</a>
        </div>
      </a-form>
    </div>
  </div>
</template>

<style scoped lang="less">
.register-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f2f5;

  .register-box {
    width: 400px;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    h2 {
      text-align: center;
      margin-bottom: 40px;
      color: #1890ff;
    }

    .register-form {
      .form-footer {
        text-align: center;
        margin-top: 16px;

        a {
          color: #1890ff;
          cursor: pointer;

          &:hover {
            color: #40a9ff;
          }
        }
      }
    }
  }
}
</style>
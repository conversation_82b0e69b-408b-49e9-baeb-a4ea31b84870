<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LogoutOutlined, CalendarOutlined } from '@ant-design/icons-vue'
import PreviewMaterial from './components/dashboard/PreviewMaterial.vue'
import Feedback from './components/dashboard/Feedback.vue'
import CourseSelection from './components/dashboard/CourseSelection.vue'
import ExerciseHomework from './components/dashboard/exercise_homework/exercise_homework.vue'
import ExercisePreview from './components/dashboard/exercise_preview/exercise_preview.vue'
import TeacherShared from './components/dashboard/TeacherShared.vue'

const router = useRouter()
const route = useRouter().currentRoute.value
const activeSection = ref('选课系统')

// 检查路由参数中是否有指定的模块
onMounted(() => {
  const section = route.query.section
  if (section && typeof section === 'string') {
    activeSection.value = section
  }
})
const studentInfo = ref({
  id: '',
  name: '学生用户',
  avatar: ''
})

// 获取用户信息
onMounted(() => {
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    try {
      const parsed = JSON.parse(userInfo)
      studentInfo.value.id = parsed.id || '未知ID'
      // 这里可以添加API调用获取更多学生信息
    } catch (e) {
      console.error('解析用户信息失败', e)
    }
  }
})

// 退出登录
const handleLogout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  message.success('退出登录成功')
  router.push('/')
}

// 查看教师分享视频
const goToSharedVideos = () => {
  // 直接在当前页面显示教师分享视频列表
  activeSection.value = '教师分享'
  // 更新URL参数，不刷新页面
  router.replace({ query: { section: '教师分享' } })
}
</script>

<template>
  <div class="dashboard-container">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <h1 class="dashboard-title">学生学习系统</h1>
      <div class="user-section">
        <a-dropdown>
          <div class="user-info">
            <a-avatar :size="32" icon="user">
              <template #icon><UserOutlined /></template>
            </a-avatar>
            <span class="username">{{ studentInfo.id }}</span>
          </div>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="menu-item">
                  <UserOutlined />
                  <span>个人信息</span>
                </div>
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item @click="handleLogout">
                <div class="menu-item">
                  <LogoutOutlined />
                  <span>退出登录</span>
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <div class="dashboard-layout">
      <nav class="sidebar">
        <ul>
          <li @click="activeSection = '选课系统'" :class="{ active: activeSection === '选课系统' }">选课系统</li>
          <li @click="activeSection = '教师分享'; router.replace({ query: { section: '教师分享' } })" :class="{ active: activeSection === '教师分享' }">教师分享</li>
          <li @click="activeSection = '预习资料'" :class="{ active: activeSection === '预习资料' }">预习资料</li>
          <li @click="activeSection = '课后习题'" :class="{ active: activeSection === '课后习题' }">课后习题</li>
          <li @click="activeSection = '预习习题'" :class="{ active: activeSection === '预习习题' }">预习习题</li>
          <li @click="activeSection = '反馈信息'" :class="{ active: activeSection === '反馈信息' }">反馈信息</li>
        </ul>
      </nav>

      <div class="dashboard-content">
        <section v-if="activeSection === '选课系统'" class="section">
          <h2 class="section-title">选课系统</h2>
          <CourseSelection />
        </section>



        <section v-if="activeSection === '教师分享'" class="section">
          <h2 class="section-title">教师分享的视频</h2>
          <TeacherShared />
        </section>

        <section v-if="activeSection === '预习资料'" class="section">
          <h2 class="section-title">预习资料</h2>
          <PreviewMaterial />
        </section>

        <section v-if="activeSection === '课后习题'" class="section">
          <h2 class="section-title">课后习题</h2>
          <ExerciseHomework />
        </section>

        <section v-if="activeSection === '预习习题'" class="section">
          <h2 class="section-title">预习习题</h2>
          <ExercisePreview />
        </section>

        <section v-if="activeSection === '反馈信息'" class="section">
          <h2 class="section-title">反馈信息</h2>
          <Feedback />
        </section>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f9f9f9;
}

/* 顶部导航栏样式 */
.top-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eaeaea;
}

.user-section {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f0f0f0;
}

.username {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 其他样式保持不变 */
.dashboard-title {
  margin-bottom: 0;
  color: var(--primary-color, #1890ff);
  font-size: 24px;
}

.dashboard-layout {
  display: flex;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.sidebar {
  width: 200px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar li:last-child {
  border-bottom: none;
}

.sidebar li:hover {
  background-color: #f5f5f5;
}

.sidebar li.active {
  background-color: var(--primary-color, #1890ff);
  color: white;
}

.dashboard-content {
  flex-grow: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.section {
  display: block;
}

.section-title {
  margin-bottom: 20px;
  color: #333;
  font-size: 20px;
  border-bottom: 2px solid var(--primary-color, #1890ff);
  padding-bottom: 10px;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .dashboard-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    margin-bottom: 20px;
  }

  .top-navbar {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .username {
    font-size: 12px;
  }
}
</style>
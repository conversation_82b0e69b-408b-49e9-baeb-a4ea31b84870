<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import axiosInstance from '@/utils/axios';
import { ArrowLeftOutlined, UserOutlined, CalendarOutlined, BookOutlined } from '@ant-design/icons-vue';

const route = useRoute();
const router = useRouter();
const videoId = ref(route.params.id);
const loading = ref(false);
const videoDetail = ref(null);


// 获取视频详情
const fetchVideoDetail = async () => {
  try {
    loading.value = true;
    const response = await axiosInstance.get(`/api/student/shared-videos/${videoId.value}`);

    if (response.data.success) {
      videoDetail.value = response.data.data;
      console.log('获取到的视频详情:', videoDetail.value);
    } else {
      message.error(response.data.message || '获取视频详情失败');
    }
  } catch (error) {
    console.error('获取视频详情失败:', error);
    message.error('获取视频详情失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 处理视频播放进度 - 仅作占位用，不进行状态记录
const handleVideoProgress = (event) => {
  // 不进行状态记录
};

// 返回列表
const goBack = () => {
  // 使用浏览器的返回功能而不是路由跳转
  window.history.back();
};

// 从B站URL中提取视频ID
const extractBilibiliVideoId = (url) => {
  if (!url) return null;

  // 尝试匹配BV号
  const bvMatch = url.match(/\/video\/(BV[a-zA-Z0-9]+)/);
  if (bvMatch && bvMatch[1]) {
    return bvMatch[1];
  }

  // 尝试匹配av号
  const avMatch = url.match(/\/video\/av(\d+)/);
  if (avMatch && avMatch[1]) {
    return `av${avMatch[1]}`;
  }

  return null;
};

// 生成B站嵌入式播放器URL
const getBilibiliEmbedUrl = (url) => {
  const videoId = extractBilibiliVideoId(url);
  if (videoId) {
    // 使用更安全的参数设置
    // 添加更多参数来控制播放器行为
    return `https://player.bilibili.com/player.html?bvid=${videoId}&page=1&high_quality=1&danmaku=0&as_wide=1&autoplay=0&t=${new Date().getTime()}`;
  }
  return null;
};

// 处理B站播放器加载 - 仅作占位用，不进行状态记录
const handleBilibiliPlayerLoad = () => {
  // 不进行状态记录
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};



// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId && newId !== videoId.value) {
    videoId.value = newId;
    fetchVideoDetail();
  }
});

onMounted(() => {
  fetchVideoDetail();
});

// 页面卸载前的处理
onBeforeUnmount(() => {
  // 不需要特殊清理
});
</script>

<template>
  <div class="shared-video-detail">
    <a-spin :spinning="loading">
      <div class="page-header">
        <div class="header-left">
          <a-button type="link" class="back-button" @click="goBack">
            <ArrowLeftOutlined />
            返回列表
          </a-button>
        </div>
        <h1 v-if="videoDetail" class="video-title">{{ videoDetail.title }}</h1>
      </div>

      <div v-if="videoDetail" class="video-content">
        <!-- 视频播放区域 -->
        <div class="main-content">
          <div class="video-player-container">
            <!-- B站视频嵌入式播放器 -->
            <div v-if="videoDetail.source === 'bilibili'" class="bilibili-container">
              <iframe
                v-if="getBilibiliEmbedUrl(videoDetail.videoUrl)"
                :src="getBilibiliEmbedUrl(videoDetail.videoUrl)"
                class="bilibili-player"
                frameborder="0"
                scrolling="no"
                border="0"
                allowfullscreen="true"
                sandbox="allow-top-navigation allow-same-origin allow-forms allow-scripts allow-popups"
                @load="handleBilibiliPlayerLoad"
              ></iframe>
              <div v-else class="bilibili-error">
                <a-empty description="无法加载视频" />
              </div>
            </div>

            <!-- 通用视频播放器 -->
            <video
              v-else
              controls
              class="video-player"
              :src="videoDetail.videoUrl"
              @timeupdate="handleVideoProgress"
            >
              您的浏览器不支持HTML5视频播放
            </video>
          </div>

          <!-- 视频信息区域 -->
          <div class="video-meta">
            <div class="video-status-tags">
              <a-tag v-if="videoDetail.isRequired" color="red">必看</a-tag>
              <a-tag color="orange">{{ videoDetail.source }}</a-tag>
            </div>

            <div class="video-meta-info">
              <div class="info-item">
                <UserOutlined />
                <span class="info-label">教师:</span>
                <span class="info-value">{{ videoDetail.teacherName }}</span>
              </div>

              <div class="info-item">
                <BookOutlined />
                <span class="info-label">班级:</span>
                <span class="info-value">{{ videoDetail.className }}</span>
              </div>

              <div class="info-item">
                <CalendarOutlined />
                <span class="info-label">分享时间:</span>
                <span class="info-value">{{ formatDate(videoDetail.createTime) }}</span>
              </div>
            </div>
          </div>

          <!-- 教师备注区域 -->
          <div v-if="videoDetail.teacherRemark" class="teacher-remark-section">
            <h3>教师备注</h3>
            <div class="remark-content">{{ videoDetail.teacherRemark }}</div>
          </div>
        </div>
      </div>

      <div v-else-if="!loading" class="empty-state">
        <a-empty description="未找到视频信息" />
        <a-button type="primary" @click="goBack">返回列表</a-button>
      </div>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.shared-video-detail {
  max-width: 1200px;
  margin-top: 0px;
  margin-left: 350px ;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .header-left {
      display: flex;
      align-items: center;
      margin-right: 16px;
    }

    .back-button {
      display: flex;
      align-items: center;
      font-size: 16px;

      .anticon {
        margin-right: 4px;
      }
    }

    .video-title {
      margin: 0;
      font-size: 24px;
      color: #333;
      font-weight: 600;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .video-content {
    display: flex;
    flex-direction: column;

    .main-content {
      .video-player-container {
        position: relative;
        margin-bottom: 20px;

        .bilibili-container,
        .video-player {
          width: 100%;
          height: 600px; /* 固定高度，更适合电脑端 */
          background: #000;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .bilibili-container {
          position: relative;

          .bilibili-player {
            width: 100%;
            height: 100%;
            border: none;
          }

          .bilibili-error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background-color: #f5f5f5;
          }
        }
      }

      .video-meta {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 24px;
        padding: 20px;
        background: #f9f9f9;
        border-radius: 8px;

        .video-status-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 16px;

          .ant-tag {
            margin-right: 0;
            padding: 4px 8px;
            font-size: 14px;
          }
        }

        .video-meta-info {
          flex: 1;
          min-width: 250px;

          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .anticon {
              margin-right: 8px;
              color: #1890ff;
            }

            .info-label {
              color: #666;
              margin-right: 8px;
              font-weight: 500;
              min-width: 70px;
            }

            .info-value {
              color: #333;
              flex: 1;
            }
          }
        }
      }

      .teacher-remark-section {
        margin-top: 24px;
        padding: 20px;
        background: #f9f9f9;
        border-radius: 8px;
        border-left: 4px solid #1890ff;

        h3 {
          font-size: 18px;
          color: #333;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid #eee;
        }

        .remark-content {
          color: #666;
          line-height: 1.8;
          white-space: pre-line;
          font-size: 15px;
        }
      }
    }
  }

  .empty-state {
    padding: 60px 0;
    text-align: center;

    .ant-btn {
      margin-top: 16px;
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .shared-video-detail {
    padding: 16px;
    margin: 0;
    border-radius: 0;
    box-shadow: none;

    .page-header {
      flex-direction: column;
      align-items: flex-start;

      .back-button {
        margin-bottom: 12px;
      }

      .video-title {
        font-size: 20px;
      }
    }

    .video-content {
      .main-content {
        .video-player-container {
          .bilibili-container,
          .video-player {
            height: 240px; /* 移动端适配高度 */
          }
        }

        .video-meta {
          flex-direction: column;

          .video-status-tags,
          .video-meta-info {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>

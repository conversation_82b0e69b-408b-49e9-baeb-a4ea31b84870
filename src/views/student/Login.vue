<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import axios from '@/axios'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const formState = ref({
  studentId: '',
  password: '',
  remember: false
})

const loading = ref(false)

const rules = {
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 从路由参数中获取学号
onMounted(() => {
  const { student_id } = route.query
  if (student_id) {
    formState.value.studentId = student_id
  }
})

const handleLogin = async () => {
  try {
    loading.value = true
    
    const response = await axios.post('/api/student/login', {
      student_id: formState.value.studentId.trim(),
      password: formState.value.password.trim()
    })

    if (response.data.success) {
      const userInfo = {
        ...response.data.data.userInfo,
        role: 'student'
      }
      
      // 更新 store 中的用户信息
      userStore.setUser(userInfo)
      userStore.setToken(response.data.data.token)
      
      // 如果选择了"记住我"，可以设置token的过期时间更长
      if (formState.value.remember) {
        localStorage.setItem('rememberMe', 'true')
      }
      
      message.success('登录成功')
      
      // 确保状态更新后再跳转到学生仪表盘
      await router.push({
        path: '/student-dashboard',
        replace: true
      })
    } else {
      throw new Error(response.data.message || '登录失败')
    }
  } catch (error) {
    console.error('登录错误:', error)
    message.error(error.response?.data?.message || '登录失败，请检查学号和密码')
  } finally {
    loading.value = false
  }
}

const goToRegister = () => {
  router.push('/student-register')
}
</script>

<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <div class="logo-text">学生学习系统</div>
        <p class="subtitle">欢迎回来，请登录您的账号</p>
      </div>
      <a-form
        :model="formState"
        :rules="rules"
        @finish="handleLogin"
        class="login-form"
      >
        <a-form-item name="studentId">
          <a-input
            v-model:value="formState.studentId"
            placeholder="请输入学号"
            size="large"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="password">
          <a-input-password
            v-model:value="formState.password"
            placeholder="请输入密码"
            size="large"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
        <div class="form-options">
          <a-checkbox v-model:checked="formState.remember">记住我</a-checkbox>
          <a class="forget-pwd">忘记密码?</a>
        </div>
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            :block="true"
            size="large"
            class="login-button"
            :loading="loading"
          >
            {{ loading ? '登录中...' : '登录' }}
          </a-button>
        </a-form-item>
        <div class="form-footer">
          <a @click="goToRegister">还没有账号？立即注册</a>
        </div>
      </a-form>
    </div>
  </div>
</template>

<style scoped lang="less">
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .login-box {
    width: 100%;
    max-width: 400px;
    margin: 0 20px;
    padding: 40px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

    .login-header {
      text-align: center;
      margin-bottom: 40px;

      .logo-text {
        color: var(--primary-color);
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .subtitle {
        color: #666;
        font-size: 14px;
      }
    }

    .login-form {
      .login-button {
        height: 44px;
        font-size: 16px;
        border-radius: 8px;
        margin-top: 8px;
      }

      .form-footer {
        text-align: center;
        margin-top: 24px;

        a {
          color: #1890ff;
          font-size: 14px;
          cursor: pointer;
          transition: color 0.3s;

          &:hover {
            color: #40a9ff;
          }
        }
      }
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .login-container {
    .login-box {
      padding: 30px 20px;
      margin: 0 16px;

      .login-header {
        margin-bottom: 30px;

        .logo-text {
          font-size: 24px;
        }
      }
    }
  }
}
</style>
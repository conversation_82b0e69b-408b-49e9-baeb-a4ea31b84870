<template>
  <div class="homework-learning-container">
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240)"
      title="课后作业"
      :sub-title="homeworkTitle"
      @back="confirmExit"
    >
      <!-- 计时器和进度条 -->
      <template #extra>
        <a-space size="middle">
          <a-tag v-if="deadline" :color="isDeadlineExpired ? 'red' : 'blue'">
            <clock-circle-outlined /> {{ isDeadlineExpired ? '已截止' : '截止时间: ' + formatDate(deadline) }}
          </a-tag>
          
          <!-- 倒计时显示 -->
          <div v-if="timeLimit > 0" class="timer-container">
            <div v-if="remainingSeconds > 0" class="timer-display">
              <a-tag :color="getTimerColor()">
                <clock-circle-outlined /> {{ formatTimeLeft() }}
              </a-tag>
            </div>
            <a-tag v-else color="red">
              <clock-circle-outlined /> 时间已到
            </a-tag>
            <div class="timer-label">
              {{ remainingSeconds > 0 ? '剩余时间' : '已超时' }}
            </div>
          </div>
          
          <a-progress 
            :percent="progressPercent" 
            size="small" 
            :status="getProgressStatus()"
            style="width: 180px"
          />
        </a-space>
      </template>
    </a-page-header>
    
    <div class="content-wrapper">
      <!-- 左侧题目列表 -->
      <div class="question-sidebar">
        <div class="question-list">
          <div class="sidebar-header">
            <span class="title">题目列表</span>
            <span class="progress">{{ answeredCount }}/{{ exercises.length }}</span>
          </div>
          <a-list
            size="small"
            class="question-nav-list"
          >
            <a-list-item 
              v-for="(exercise, index) in exercises" 
              :key="index"
              @click="currentIndex = index"
              :class="{ 
                'selected': currentIndex === index,
                'answered': isQuestionAnswered(exercise)
              }"
            >
              <div class="question-item">
                <div class="question-number">{{ index + 1 }}</div>
                <div class="question-type">{{ getQuestionTypeName(exercise.question_type) }}</div>
                <div class="question-status">
                  <check-circle-filled v-if="isQuestionAnswered(exercise)" class="answered-icon" />
                </div>
              </div>
            </a-list-item>
          </a-list>
        </div>
        
        <a-button 
          type="primary" 
          size="large" 
          class="submit-button"
          :disabled="isSubmitDisabled()"
          :loading="submitting"
          @click="confirmSubmit"
        >
          提交作业
        </a-button>
      </div>
      
      <!-- 右侧题目内容 -->
      <div class="question-content">
        <div v-if="loading" class="loading-container">
          <a-spin size="large" />
          <div class="loading-text">加载题目中...</div>
        </div>
        
        <div v-else-if="exercises.length === 0" class="empty-container">
          <a-empty description="暂无题目" />
        </div>
        
        <div v-else class="question-container">
          <!-- 题目导航 -->
          <div class="question-navigation">
            <a-button 
              type="default" 
              @click="prevQuestion" 
              :disabled="currentIndex === 0"
            >
              <left-outlined /> 上一题
            </a-button>
            <span class="question-counter">{{ currentIndex + 1 }}/{{ exercises.length }}</span>
            <a-button 
              type="default" 
              @click="nextQuestion" 
              :disabled="currentIndex === exercises.length - 1"
            >
              下一题 <right-outlined />
            </a-button>
          </div>
          
          <!-- 当前题目 -->
          <div class="current-question">
            <div class="question-header">
              <div class="question-type-tag">
                <a-tag color="blue">{{ getQuestionTypeName(currentExercise.question_type) }}</a-tag>
              </div>
              <div class="question-points">
                <a-tag v-if="currentExercise.question_type <= 3" color="orange">5分</a-tag>
                <a-tag v-else color="orange">10分</a-tag>
              </div>
            </div>
            
            <div class="question-body">
              <div class="question-title">
                <div v-if="hasLatex(currentExercise.content)" v-html="renderLatex(currentExercise.content)"></div>
                <div v-else>{{ currentExercise.content }}</div>
              </div>
              
              <!-- 选择题 -->
              <div v-if="currentExercise.question_type === 1" class="options-container">
                <a-radio-group v-model:value="currentExercise.student_answer" class="option-list">
                  <a-radio 
                    v-for="option in currentExercise.options" 
                    :key="option.key" 
                    :value="option.key"
                    class="option-item"
                  >
                    <span class="option-content">
                      {{ option.key }}. 
                      <span v-if="hasLatex(option.value)" v-html="renderLatex(option.value)"></span>
                      <span v-else>{{ option.value }}</span>
                    </span>
                  </a-radio>
                </a-radio-group>
              </div>
              
              <!-- 填空题 -->
              <div v-else-if="currentExercise.question_type === 2" class="blank-container">
                <a-input 
                  v-model:value="currentExercise.student_answer"
                  placeholder="请输入答案"
                  class="blank-input"
                />
              </div>
              
              <!-- 判断题 -->
              <div v-else-if="currentExercise.question_type === 3" class="options-container">
                <a-radio-group v-model:value="currentExercise.student_answer" class="option-list">
                  <a-radio 
                    v-for="option in currentExercise.options" 
                    :key="option.key" 
                    :value="option.key"
                    class="option-item"
                  >
                    <span class="option-content">
                      {{ option.key }}. 
                      <span v-if="hasLatex(option.value)" v-html="renderLatex(option.value)"></span>
                      <span v-else>{{ option.value }}</span>
                    </span>
                  </a-radio>
                </a-radio-group>
              </div>
              
              <!-- 简答题 -->
              <div v-else-if="currentExercise.question_type === 4" class="essay-container">
                <div class="essay-instruction">
                  <info-circle-outlined /> 请在下方文本框中输入您的答案，支持多行文本输入
                </div>
                <a-textarea 
                  v-model:value="currentExercise.student_answer"
                  placeholder="请在此处输入您的答案"
                  :rows="8"
                  class="essay-input"
                  :bordered="true"
                  :auto-size="{ minRows: 8, maxRows: 15 }"
                />
                <div class="word-count">
                  <template v-if="currentExercise.student_answer">
                    已输入 {{ currentExercise.student_answer.length }} 个字符
                  </template>
                  <template v-else>
                    尚未输入任何内容
                  </template>
                </div>
              </div>
              
              <!-- 计算题 -->
              <div v-else-if="currentExercise.question_type === 5" class="calculation-container">
                <div class="calc-instruction">
                  <info-circle-outlined /> 您需要拍照上传手写计算过程，支持JPG/PNG格式，大小不超过2MB
                </div>
                <!-- 图片上传组件 -->
                <div class="upload-section">
                  <a-upload
                    list-type="picture-card"
                    :file-list="calcImageFileList"
                    :before-upload="beforeImageUpload"
                    @preview="handleImagePreview"
                    @change="handleImageChange"
                    :maxCount="1"
                    :showUploadList="true"
                    :customRequest="customUploadRequest"
                  >
                    <div v-if="calcImageFileList.length < 1">
                      <plus-outlined />
                      <div style="margin-top: 8px">上传计算过程图片</div>
                    </div>
                  </a-upload>
                  <a-modal
                    :visible="previewVisible"
                    :title="previewTitle"
                    :footer="null"
                    @cancel="previewVisible = false"
                  >
                    <img alt="计算题答案预览" style="width: 100%" :src="previewImage" />
                  </a-modal>
                </div>
                <div class="upload-tips" v-if="calcImageFileList.length > 0">
                  <check-circle-outlined style="color: #52c41a" /> 图片已上传成功
                </div>
                <div class="upload-warning" v-else>
                  <warning-outlined style="color: #faad14" /> 请上传计算过程图片，否则无法提交
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import axios from 'axios';
import dayjs from 'dayjs';
import { 
  CheckCircleFilled, 
  ClockCircleOutlined, 
  LeftOutlined, 
  RightOutlined,
  PlusOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons-vue';
import { StatisticCountdown } from 'ant-design-vue';
import { debounce } from 'lodash';
import './exercise_homework_learning.css';
import 'katex/dist/katex.min.css';
import katex from 'katex';

// 创建axios实例
const api = axios.create({
  baseURL: ''
});

// 添加请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

const route = useRoute();
const router = useRouter();
const { classId, batchId } = route.params;

// 状态
const loading = ref(true);
const exercises = ref([]);
const currentIndex = ref(0);
const startTime = ref('');
const submitting = ref(false);
const timeLimit = ref(0);
const remainingSeconds = ref(0);
const timerInterval = ref(null);
const homeworkTitle = ref('');
const userInfo = ref(null);
const deadline = ref(null);
const isDeadlineExpired = ref(false);

// 计算题图片上传相关
const calcImageFileList = ref([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 计算题答案类型（文字/图片）
const calcAnswerType = ref('text');

// 在data中添加状态
const uploading = ref(false);

// 计算属性
const currentExercise = computed(() => {
  if (exercises.value.length === 0 || currentIndex.value >= exercises.value.length) {
    return {};
  }
  return exercises.value[currentIndex.value];
});

const answeredCount = computed(() => {
  return exercises.value.filter(ex => isQuestionAnswered(ex)).length;
});

const progressPercent = computed(() => {
  if (exercises.value.length === 0) return 0;
  return Math.round((answeredCount.value / exercises.value.length) * 100);
});

// 获取用户信息
const getUserInfo = () => {
  const stored = localStorage.getItem('userInfo');
  if (stored) {
    try {
      userInfo.value = JSON.parse(stored);
      console.log('获取到的用户信息:', userInfo.value);
    } catch (e) {
      console.error('解析用户信息失败', e);
    }
  }
};

// 加载作业数据
const loadHomeworkData = async () => {
  try {
    loading.value = true;
    
    // 从本地存储获取作业信息
    const homeworkInfo = localStorage.getItem('current_homework');
    let chapterId = null;
    
    if (homeworkInfo) {
      const parsedInfo = JSON.parse(homeworkInfo);
      homeworkTitle.value = parsedInfo.title || `第${batchId}次作业`;
      deadline.value = parsedInfo.deadline || null;
      chapterId = parsedInfo.chapterId || null; // 获取章节ID
      
      console.log('从本地存储获取的作业信息:', { 
        title: homeworkTitle.value,
        deadline: deadline.value,
        chapterId
      });
      
      // 检查截止日期是否已过
      if (deadline.value) {
        isDeadlineExpired.value = dayjs().isAfter(dayjs(deadline.value));
      }
    }
    
    // 获取作业信息和计时数据
    try {
      // 先向服务器发送请求，开始或获取已开始的作业计时信息
      const timerResponse = await api.post(
        `/api/homework-learning/start/${classId}/${batchId}${chapterId ? `?chapterId=${chapterId}` : ''}`,
        {}, // 确保发送空对象作为请求体，不在请求体中添加chapterId
        {
          // 增加请求超时时间
          timeout: 10000
        }
      );
      
      if (timerResponse.data.success) {
        // 从服务器获取开始时间和时间限制
        startTime.value = timerResponse.data.data.start_time;
        timeLimit.value = timerResponse.data.data.time_limit;
        
        console.log('从服务器获取的作业计时信息:', {
          startTime: startTime.value,
          timeLimit: timeLimit.value,
          chapterId
        });
      } else {
        // 请求成功但返回错误
        console.error('获取作业计时信息失败:', timerResponse.data.message);
        message.error('获取作业计时信息失败: ' + timerResponse.data.message);
      }
    } catch (error) {
      console.error('获取作业计时信息失败:', error.response?.data || error.message);
      message.error('获取作业计时信息失败，可能无法正确显示倒计时');
    }
    
    // 获取作业题目
    const { data } = await api.get(`/api/homework-learning/exercises/${classId}/${batchId}${chapterId ? `?chapterId=${chapterId}` : ''}`);
    
    if (data.success) {
      // 更新API响应格式的处理
      if (data.data.exercises) {
        // 新格式：data.data包含exercises、timer、time_limit和deadline
        exercises.value = data.data.exercises.map(exercise => {
          // 选项数据已在后端处理好
          let options = [];
          try {
            if (exercise.option) {
              const optionObj = JSON.parse(exercise.option);
              options = Object.entries(optionObj).map(([key, value]) => ({
                key,
                value
              }));
            }
          } catch (e) {
            console.error('解析选项失败:', e);
          }
          
          return {
            ...exercise,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct,
            options
          };
        });
        
        // 如果后端提供了计时信息，更新本地状态
        if (data.data.timer) {
          startTime.value = data.data.timer.start_time;
        }
        
        // 如果后端提供了时间限制，更新本地状态
        if (data.data.time_limit) {
          timeLimit.value = data.data.time_limit;
        }
        
        // 如果后端提供了截止日期，更新本地状态
        if (data.data.deadline && !deadline.value) {
          deadline.value = data.data.deadline;
          isDeadlineExpired.value = dayjs().isAfter(dayjs(deadline.value));
        }
      } else {
        // 旧格式：data.data直接是exercises数组
        exercises.value = data.data.map(exercise => {
          let options = [];
          try {
            if (exercise.option) {
              const optionObj = JSON.parse(exercise.option);
              options = Object.entries(optionObj).map(([key, value]) => ({
                key,
                value
              }));
            }
          } catch (e) {
            console.error('解析选项失败:', e);
          }
          
          // 判断题使用"正确"和"错误"选项
          if (exercise.question_type === 3) {
            options = [
              { key: 'T', value: '正确' },
              { key: 'F', value: '错误' }
            ];
          }
          
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
        });
      }
      
      // 初始化计时器
      initTimer();
    } else {
      message.error(data.message || '获取题目失败');
    }
  } catch (error) {
    console.error('加载作业数据失败:', error);
    message.error('加载作业数据失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 初始化计时器
const initTimer = () => {
  // 清除可能存在的旧计时器
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
  
  // 如果有时间限制，设置倒计时
  if (timeLimit.value > 0) {
    // 确保有startTime，如果没有则使用当前时间
    if (!startTime.value) {
      startTime.value = new Date();
      console.log('未获取到开始时间，使用当前时间:', startTime.value);
    }
    
    updateRemainingTime();
    
    // 设置定时器，每秒更新倒计时
    timerInterval.value = setInterval(() => {
      updateRemainingTime();
    }, 1000);
    
    console.log('计时器已初始化: 时间限制=', timeLimit.value, '分钟');
  } else {
    console.log('无时间限制，不启动计时器');
  }
};

// 更新剩余时间
const updateRemainingTime = () => {
  try {
    if (!startTime.value) {
      console.error('缺少开始时间，无法计算剩余时间');
      return;
    }
    
    const startMoment = dayjs(startTime.value);
    const now = dayjs();
    const limitMinutes = timeLimit.value || 0;
    
    if (limitMinutes <= 0) {
      console.log('无时间限制，不计算剩余时间');
      return;
    }
    
    const totalSeconds = limitMinutes * 60;
    const elapsedSeconds = now.diff(startMoment, 'second');
    remainingSeconds.value = Math.max(0, totalSeconds - elapsedSeconds);
    
    console.log(`更新倒计时: 开始时间=${startMoment.format('YYYY-MM-DD HH:mm:ss')}, 当前时间=${now.format('YYYY-MM-DD HH:mm:ss')}, 已过时间=${elapsedSeconds}秒, 剩余时间=${remainingSeconds.value}秒`);
    
    // 处理倒计时结束
    if (remainingSeconds.value <= 0 && timerInterval.value) {
      clearInterval(timerInterval.value);
      timerInterval.value = null;
      
      // 只在第一次倒计时结束时显示对话框
      if (remainingSeconds.value === 0) {
        // 显示超时对话框
        Modal.warning({
          title: '作业时间已到',
          content: '您的作业时间已到，系统将记录超时状态。您仍然可以继续完成并提交作业。',
          okText: '知道了'
        });
      }
    }
    
    // 时间提醒
    if (remainingSeconds.value === 300) { // 5分钟
      message.warning('注意：作业时间还剩5分钟');
    } else if (remainingSeconds.value === 60) { // 1分钟
      message.warning('注意：作业时间还剩1分钟');
    }
  } catch (error) {
    console.error('更新剩余时间时出错:', error);
  }
};

// 格式化剩余时间
const formatTimeLeft = () => {
  if (remainingSeconds.value <= 0) return '00:00';
  
  const hours = Math.floor(remainingSeconds.value / 3600);
  const minutes = Math.floor((remainingSeconds.value % 3600) / 60);
  const seconds = remainingSeconds.value % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
};

// 获取计时器颜色
const getTimerColor = () => {
  if (remainingSeconds.value <= 300) return 'red';
  if (remainingSeconds.value <= 600) return 'orange';
  return 'blue';
};

// 获取进度条状态
const getProgressStatus = () => {
  if (progressPercent.value >= 100) return 'success';
  // 移除时间限制对状态的影响，确保进度条始终显示为蓝色
  return 'active';
};

// 导航到上一题
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
};

// 导航到下一题
const nextQuestion = () => {
  if (currentIndex.value < exercises.value.length - 1) {
    currentIndex.value++;
  }
};

// 检查题目是否已回答
const isQuestionAnswered = (exercise) => {
  if (!exercise || !exercise.student_answer) return false;
  
  // 不同题型的判断逻辑
  switch (exercise.question_type) {
    case 1: // 选择题
    case 3: // 判断题
      return Boolean(exercise.student_answer);
    case 2: // 填空题
    case 4: // 简答题
      return exercise.student_answer.trim() !== '';
    case 5: // 计算题
      // 计算题必须有上传的图片
      return exercise.student_answer.startsWith('/uploads/');
    default:
      return false;
  }
};

// 获取题目类型名称
const getQuestionTypeName = (type) => {
  const types = {
    1: '选择题',
    2: '填空题',
    3: '判断题',
    4: '简答题',
    5: '计算题'
  };
  return types[type] || '未知';
};

// 提交是否禁用
const isSubmitDisabled = () => {
  // 检查是否所有计算题都上传了图片
  const hasAllCalculationImages = exercises.value.every(ex => {
    if (ex.question_type === 5) {
      return ex.student_answer && ex.student_answer.startsWith('/uploads/');
    }
    return true;
  });
  
  return submitting.value || 
         exercises.value.length === 0 || 
         isDeadlineExpired.value || 
         !hasAllCalculationImages;
};

// 确认提交
const confirmSubmit = () => {
  // 检查是否超时
  if (timeLimit.value > 0 && remainingSeconds.value <= 0) {
    Modal.warning({
      title: '作业已超时',
      content: '作业已超过时间限制，系统将记录您的超时状态。继续提交？',
      okText: '提交',
      cancelText: '取消',
      onOk: () => {
        submitHomework();
      }
    });
    return;
  }
  
  const unansweredCount = exercises.value.length - answeredCount.value;
  
  Modal.confirm({
    title: '确认提交',
    content: unansweredCount > 0 
      ? `您还有 ${unansweredCount} 题未作答，确定要提交吗？`
      : '确定要提交作业吗？',
    okText: '提交',
    cancelText: '取消',
    onOk: () => {
      submitHomework();
    }
  });
};

// 提交作业
const submitHomework = async (saveAll = false) => {
  if (submitting.value) return;
  
  submitting.value = true;
  
  try {
    // 如果需要保存全部，先保存当前答案
    if (saveAll) {
      await saveCurrentAnswer();
    }
    
    // 获取当前学生信息
    if (!userInfo.value) {
      getUserInfo();
    }
    
    if (!userInfo.value) {
      message.error('无法获取用户信息');
      submitting.value = false;
      return;
    }
    
    // 确保使用student_id而不是id
    const studentId = userInfo.value.student_id;
    console.log('使用学生ID (student_id):', studentId);
    
    if (!studentId) {
      console.error('userInfo中没有student_id:', userInfo.value);
      message.error('无法获取学生ID');
      submitting.value = false;
      return;
    }
    
    // 构建提交的答案数据
    const homeworkInfo = localStorage.getItem('current_homework');
    let parsedInfo = {};
    let chapterId = null;
    
    try {
      parsedInfo = JSON.parse(homeworkInfo);
      chapterId = parsedInfo?.chapterId;
    } catch (e) {
      console.error('解析作业信息失败:', e);
    }
    
    console.log('提交作业 - 作业信息:', parsedInfo);
    console.log('提交作业 - 章节ID:', chapterId);
    
    // 准备提交请求数据
    const answers = exercises.value.map(exercise => ({
      publish_id: exercise.publish_id,
      student_answer: exercise.student_answer || ''
    }));
    
    console.log('提交作业 - 答案数量:', answers.length);
    console.log('提交作业 - 学生ID:', studentId);
    console.log('提交作业 - 班级ID:', classId);
    console.log('提交作业 - 批次ID:', batchId);
    
    if (answers.length === 0) {
      message.warning('没有可提交的答案');
      submitting.value = false;
      return;
    }
    
    // 发送提交请求
    const { data } = await api.post(
      `/api/homework-learning/submit/${classId}/${batchId}`,
      {
        answers: answers,
        studentId: studentId,
        chapterId: chapterId  // 确保传递章节ID
      }
    );
    
    console.log('提交作业 - 响应数据:', data);
    
    if (data.success) {
      message.success('作业提交成功');
      
      // 清除计时器
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }
      
      // 修改: 直接跳转到学生首页
      window.location.href = 'http://localhost:5174/student-dashboard';
    } else {
      // 提交失败处理
      message.error(data.message || '提交失败');
    }
  } catch (error) {
    console.error('提交作业错误:', error);
    message.error('提交作业失败: ' + (error.response?.data?.message || error.message));
  } finally {
    submitting.value = false;
  }
};

// 确认退出
const confirmExit = () => {
  if (answeredCount.value > 0 && !submitting.value) {
    Modal.confirm({
      title: '确认离开',
      content: '您的作业尚未提交，离开页面将丢失所有答案，确定要离开吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        // 清除计时器
        if (timerInterval.value) {
          clearInterval(timerInterval.value);
        }
        window.location.href = 'http://localhost:5174/student-dashboard';
      }
    });
  } else {
    // 清除计时器
    if (timerInterval.value) {
      clearInterval(timerInterval.value);
    }
    window.location.href = 'http://localhost:5174/student-dashboard';
  }
};

// 计算题图片上传相关方法
const handleImagePreview = async (file) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64(file.originFileObj);
  }
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value = file.name || '计算题解答';
};

const getBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
};

const beforeImageUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传JPG或PNG格式的图片!');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过2MB!');
    return false;
  }
  return isJpgOrPng && isLt2M;
};

// 自定义上传请求，防止重复上传
const customUploadRequest = async ({ file, onSuccess, onError }) => {
  try {
    // 防止重复上传 - 显示加载状态
    uploading.value = true;
    message.loading('正在上传图片...');
    
    const formData = new FormData();
    formData.append('image', file);
    
    const { data } = await api.post('/api/homework-learning/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    if (data.success) {
      message.success('图片上传成功');
      
      // 更新文件列表中的url
      calcImageFileList.value = [{
        uid: '-1',
        name: file.name || '计算题答案.jpg',
        status: 'done',
        url: data.data.url
      }];
      
      // 将图片URL设置为当前计算题的答案
      if (currentExercise.value && currentExercise.value.question_type === 5) {
        currentExercise.value.student_answer = data.data.url;
      }
      
      onSuccess(data, file);
    } else {
      message.error(data.message || '上传失败');
      calcImageFileList.value = [];
      onError(new Error(data.message || '上传失败'));
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    message.error('上传图片失败: ' + (error.response?.data?.message || error.message));
    calcImageFileList.value = [];
    onError(error);
  } finally {
    uploading.value = false;
  }
};

const handleImageChange = ({ fileList }) => {
  // 防止fileList自动更新触发重复上传
  if (!uploading.value) {
    calcImageFileList.value = fileList;
    
    // 如果删除了图片，清空答案
    if (fileList.length === 0 && currentExercise.value && currentExercise.value.question_type === 5) {
      currentExercise.value.student_answer = '';
    }
  }
};

// 添加格式化日期方法
const formatDate = (date) => {
  if (!date) return '';
  return dayjs(date).format('YYYY-MM-DD HH:mm');
};

// 生命周期钩子
onMounted(() => {
  if (!classId || !batchId) {
    message.error('参数不完整，无法获取作业');
    router.push('/student/homework');
    return;
  }
  
  getUserInfo();
  loadHomeworkData();
});

onBeforeUnmount(() => {
  // 清除计时器
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
  }
});

// 监听当前题目变化，处理计算题图片预览显示
watch(currentIndex, (newVal) => {
  if (exercises.value[newVal]?.question_type === 5) {
    // 检查当前题目是否已经有上传的图片答案
    const currentAnswer = exercises.value[newVal].student_answer || '';
    if (currentAnswer.startsWith('/')) { // 假设图片URL以/开头
      calcImageFileList.value = [{
        uid: '-1',
        name: '已上传图片',
        status: 'done',
        url: currentAnswer
      }];
    } else {
      calcImageFileList.value = [];
    }
  } else {
    calcImageFileList.value = [];
  }
});

// 监听学生答案变化，自动保存
watch(() => exercises.value.map(ex => ex.student_answer), 
  (newAnswers, oldAnswers) => {
    if (!newAnswers || !oldAnswers) return;
    
    // 找出发生变化的题目
    const changedExerciseIndex = newAnswers.findIndex((answer, index) => {
      return answer !== oldAnswers[index];
    });
    
    if (changedExerciseIndex !== -1) {
      const exercise = exercises.value[changedExerciseIndex];
      debouncedSaveAnswer(exercise);
    }
  },
  { deep: true }
);

// 创建防抖函数，延迟发送保存请求，避免频繁请求
const debouncedSaveAnswer = debounce((exercise) => {
  saveAnswerToServer(exercise);
}, 1000); // 1秒后执行

// 保存答案到服务器
const saveAnswerToServer = async (exercise) => {
  try {
    // 只有当答案不为空时才保存
    if (!exercise.student_answer) return;
    
    // 发送保存请求
    const { data } = await api.post('/api/homework-learning/save-answer', {
      publishId: exercise.publish_id,
      studentAnswer: exercise.student_answer
    });
    
    if (data.success) {
      console.log(`答案保存成功 - 题目ID: ${exercise.publish_id}`);
      // 不显示任何提示，静默保存
    }
  } catch (error) {
    console.error('保存答案失败:', error);
    // 保存失败不提示用户，避免干扰
  }
};

// LaTeX渲染相关函数
const hasLatex = (text) => {
  if (!text) return false;
  
  // 检查是否包含$符号
  if (text.includes('$')) return true;
  
  // 检查常见的LaTeX命令
  const latexCommands = [
    '\\frac', '\\sqrt', '\\sum', '\\int', '\\pi', '\\alpha', '\\beta', '\\gamma',
    '\\theta', '\\phi', '\\omega', '\\Delta', '\\nabla', '\\partial', '\\infty',
    '\\times', '\\cdot', '\\leq', '\\geq', '\\neq', '\\approx', '\\vec', '\\overrightarrow',
    '\\sin', '\\cos', '\\tan', '\\log', '\\ln', '\\exp', '\\lim', '\\rightarrow'
  ];
  
  return latexCommands.some(cmd => text.includes(cmd));
};

const renderLatex = (text) => {
  if (!text) return '';
  
  // 预处理文本，将普通的LaTeX公式用$包裹起来
  let processedText = text;
  
  // 识别常见的LaTeX命令，如\frac{}{}, \sqrt{}, 等，将它们用$包裹
  processedText = processedText.replace(/(\\\w+(\{.*?\})+)/g, (match) => {
    if (!match.startsWith('$') && !match.endsWith('$')) {
      return `$${match}$`;
    }
    return match;
  });
  
  // 渲染所有$...$格式的行内公式
  processedText = processedText.replace(/\$(.*?)\$/g, (match, formula) => {
    try {
      return katex.renderToString(formula, {
        throwOnError: false,
        displayMode: false
      });
    } catch (e) {
      console.error('LaTeX渲染错误:', e);
      return match; // 如果渲染失败，返回原始文本
    }
  });
  
  // 渲染所有$$...$$格式的块级公式
  processedText = processedText.replace(/\$\$(.*?)\$\$/g, (match, formula) => {
    try {
      return katex.renderToString(formula, {
        throwOnError: false,
        displayMode: true
      });
    } catch (e) {
      console.error('LaTeX渲染错误:', e);
      return match; // 如果渲染失败，返回原始文本
    }
  });
  
  return processedText;
};
</script>

<style>
/* 现有样式... */

.essay-container, .calculation-container {
  margin-top: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.essay-instruction, .calc-instruction {
  margin-bottom: 12px;
  color: #1890ff;
  font-size: 14px;
}

.word-count {
  margin-top: 8px;
  text-align: right;
  color: #999;
  font-size: 12px;
}

.upload-section {
  margin: 16px 0;
  display: flex;
  justify-content: center;
}

.upload-tips {
  margin-top: 12px;
  text-align: center;
  color: #52c41a;
}

.upload-warning {
  margin-top: 12px;
  text-align: center;
  color: #faad14;
}

/* 现有样式... */
</style> 
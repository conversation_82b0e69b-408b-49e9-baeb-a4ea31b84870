.homework-learning-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.timer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid #eee;
  min-width: 100px;
}

.timer-label {
  font-size: 12px;
  color: #666;
  margin-top: 3px;
}

.content-wrapper {
  display: flex;
  margin-top: 20px;
  gap: 20px;
  min-height: calc(100vh - 120px);
}

.question-sidebar {
  width: 280px;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.question-list {
  flex: 1;
  overflow: auto;
  padding: 0;
}

.sidebar-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-header .title {
  font-weight: 600;
  font-size: 16px;
}

.sidebar-header .progress {
  font-size: 14px;
  color: #1890ff;
}

.question-nav-list {
  height: calc(100vh - 280px);
  overflow-y: auto;
}

.question-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.question-number {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-right: 12px;
}

.question-type {
  flex: 1;
  font-size: 14px;
}

.question-status {
  margin-left: 8px;
}

.answered-icon {
  color: #52c41a;
}

.submit-button {
  margin: 16px;
}

.question-content {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.loading-container, .empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-text {
  margin-top: 16px;
  color: #999;
}

.question-container {
  display: flex;
  flex-direction: column;
}

.question-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.question-counter {
  font-size: 16px;
  font-weight: 500;
}

.current-question {
  flex: 1;
  margin-bottom: 24px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.question-title {
  font-size: 18px;
  margin-bottom: 24px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.options-container .option-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s;
}

.option-item:hover {
  background-color: #f5f5f5;
}

.option-content {
  white-space: normal;
}

.blank-container, .essay-container, .calculation-container {
  margin-top: 24px;
}

.blank-input, .essay-input, .calc-input {
  width: 100%;
  font-size: 16px;
}

.upload-section {
  margin-top: 16px;
}

.question-actions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

@media (max-width: 992px) {
  .content-wrapper {
    flex-direction: column;
  }
  
  .question-sidebar {
    width: 100%;
    height: auto;
  }
  
  .question-list {
    height: auto;
    max-height: 300px;
  }
  
  .question-nav-list {
    height: auto;
    max-height: 250px;
  }
}

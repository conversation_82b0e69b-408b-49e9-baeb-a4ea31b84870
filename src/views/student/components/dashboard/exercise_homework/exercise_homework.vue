<template>
    <div class="exercise-homework-container">
      <a-page-header
        style="border: 1px solid rgb(235, 237, 240)"
        title="课后习题"
        sub-title="选择班级和章节查看习题"
      >
        <!-- 班级和章节选择 -->
        <div class="filter-bar">
          <a-row :gutter="24">
            <a-col :span="10">
              <a-select 
                v-model:value="selectedClass" 
                placeholder="请选择班级" 
                @change="handleClassChange"
                style="width: 100%; min-width: 180px;"
                size="large"
              >
                <a-select-option
                  v-for="item in classes"
                  :key="item.class_id"
                  :value="item.class_id"
                >
                  {{ item.class_name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="14">
              <a-select 
                v-model:value="selectedChapter" 
                placeholder="请选择章节" 
                @change="handleChapterChange"
                style="width: 100%; min-width: 220px;"
                size="large"
                :disabled="!selectedClass || chapters.length === 0"
              >
                <a-select-option
                  v-for="item in chapters"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.title }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </div>
      </a-page-header>
  
      <!-- 习题列表 -->
      <div v-loading="loading" class="homework-list-container">
        <!-- 显示章节作业列表 -->
        <div v-if="selectedClass && selectedChapter && homeworks.length > 0" class="homework-list">
          <div class="section-title">
            《{{ getSelectedChapterTitle() }}》章节作业
          </div>
          
          <a-card 
            v-for="homework in homeworks" 
            :key="homework.release_batch" 
            class="homework-card"
            :hoverable="true"
          >
            <template #title>
              <div class="card-title">
                <span class="title-text">{{ getSelectedChapterTitle() }} - 第 {{ homework.release_batch }} 次作业</span>
              </div>
            </template>
            
            <a-descriptions size="small" :column="2" bordered>
              <a-descriptions-item label="题目数量">{{ homework.exercise_count }}</a-descriptions-item>
              <a-descriptions-item label="发布时间">{{ formatDate(homework.release_time) }}</a-descriptions-item>
              <a-descriptions-item label="截止日期" v-if="homework.deadline">
                {{ formatDate(homework.deadline) }}
                <a-tag v-if="isDeadlineExpired(homework.deadline)" color="red">已截止</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="开始时间" v-if="homework.timer_status">
                {{ homework.timer_status.has_started ? formatDate(homework.timer_status.start_time) : '未开始' }}
              </a-descriptions-item>
              <a-descriptions-item label="作答用时" v-if="homework.completion_status?.is_completed && homework.timer_status?.start_time && homework.timer_status?.end_time">
                {{ calculateDuration(homework.timer_status.start_time, homework.timer_status.end_time) }}
              </a-descriptions-item>
              <a-descriptions-item label="时间限制" v-if="homework.time_limit">
                <a-tag color="orange">{{ homework.time_limit }} 分钟</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="得分" v-if="homework.completion_status?.is_completed && homework.score">
                <a-tag :color="getScoreColor(homework)">{{ homework.score.total_score || 0 }}/{{ homework.score.full_score || 0 }}</a-tag>
              </a-descriptions-item>
            </a-descriptions>
            
            <div class="card-actions">
              <a-button 
                class="start-button" 
                :type="getButtonTypeAndText(homework).type"
                @click="startExercise(homework)"
                :disabled="getButtonTypeAndText(homework).disabled"
              >
                {{ getButtonTypeAndText(homework).text }}
              </a-button>
            </div>
          </a-card>
        </div>
  
        <!-- 空状态 -->
        <a-empty 
          v-else-if="selectedClass && selectedChapter && !loading" 
          description="该章节暂无作业" 
        />
        
        <!-- 初始状态 -->
        <a-empty 
          v-else-if="!loading" 
          description="请选择班级和章节查看作业" 
        />
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, watch } from 'vue'
  import { message } from 'ant-design-vue'
  import { useRouter } from 'vue-router'
  import axios from 'axios'
  import dayjs from 'dayjs'
  
  // 创建axios实例
  const api = axios.create({
    baseURL: ''
  })
  
  // 添加请求拦截器
  api.interceptors.request.use(
    config => {
      // 从localStorage获取token
      const token = localStorage.getItem('token')
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`
      }
      return config
    },
    error => {
      return Promise.reject(error)
    }
  )
  
  // 定义接口
  interface Class {
    class_id: number;
    class_name: string;
    [key: string]: any;
  }
  
  interface Chapter {
    id: string;
    title: string;
    fullPath?: string;
  }
  
  interface Exercise {
    publish_id: number;
    exercise_id: number;
    title: string;
    content: string;
    difficulty: number;
    question_type: number;
    publish_time: string;
    release_batch: number;
    deadline?: string;
    time_limit?: number;
    student_answer?: string;
    is_correct?: boolean;
  }
  
  interface Homework {
    release_batch: number;
    release_time: string;
    exercise_count: number;
    completion_status?: {
      completed: number;
      total: number;
      percentage: number;
      is_completed: boolean;
    };
    time_limit: number;
    timer_status?: {
      is_timeout: boolean;
      has_started: boolean;
      start_time: string;
      is_completed: boolean;
      end_time?: string;
      title_id: string;
    };
    score?: {
      correct_count: number;
      total_count: number;
      total_score: number;
      full_score: number;
    };
    deadline?: string;
    [key: string]: any;
  }
  
  // 状态
  const loading = ref(false)
  const classes = ref<Class[]>([])
  const chapters = ref<Chapter[]>([])
  const selectedClass = ref<number | null>(null)
  const selectedChapter = ref<string | null>(null)
  const homeworks = ref<Homework[]>([])
  const chapterExercises = ref<Exercise[]>([])
  const studentInfo = ref(null)
  const router = useRouter()
  const disableCompletedHomework = ref(false) // 可以通过配置决定是否禁用已完成作业的按钮
  
  // 获取学生信息
  const getStudentInfo = () => {
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      try {
        const parsed = JSON.parse(userInfo)
        studentInfo.value = parsed
        console.log('从localStorage获取到的学生信息:', parsed)
        return parsed
      } catch (e) {
        console.error('解析用户信息失败', e)
        return null
      }
    }
    return null
  }
  
  // 获取学生ID - 确保使用student_id而不是id
  const getFormattedStudentId = (student) => {
    if (!student) return null
    
    // 正确使用student_id字段，不是id字段
    if (student.student_id) {
      console.log('使用student_id:', student.student_id)
      return student.student_id
    }
    
    console.warn('学生信息中没有student_id字段:', student)
    return null
  }
  
  // 获取学生班级
  const fetchStudentClasses = async () => {
    const student = getStudentInfo()
    if (!student) {
      message.warning('未找到学生信息')
      return Promise.reject('未找到学生信息')
    }
  
    const studentId = getFormattedStudentId(student)
    if (!studentId) {
      message.warning('无法获取有效的学生ID')
      return Promise.reject('无法获取有效的学生ID')
    }
  
    try {
      loading.value = true
      console.log('使用学生ID获取班级:', studentId)
      
      // 直接查看localStorage中的用户信息结构
      console.log('localStorage中的userInfo结构:', JSON.parse(localStorage.getItem('userInfo') || '{}'))
      
      const { data } = await api.get(`/api/homework/student/${studentId}/classes`)
      
      console.log('班级API返回数据:', data)
      
      if (data.success) {
        classes.value = data.data
        console.log('获取到的班级数据:', classes.value)
        
        if (classes.value.length > 0) {
          selectedClass.value = classes.value[0].class_id
          await fetchClassChapters(selectedClass.value)
        }
        return Promise.resolve(classes.value)
      }
      return Promise.resolve([])
    } catch (error) {
      console.error('获取班级失败:', error)
      message.error('获取班级失败')
      return Promise.reject(error)
    } finally {
      loading.value = false
    }
  }
  
  // 获取班级章节
  const fetchClassChapters = async (classId) => {
    if (!classId) return Promise.reject('未提供班级ID')
    
    try {
      loading.value = true
      console.log('获取班级章节列表, 班级ID:', classId)
      const { data } = await api.get(`/api/homework/class/${classId}/chapters`)
      
      if (data.success) {
        chapters.value = data.data
        console.log('获取到的章节数据:', chapters.value)
        
        // 如果有章节，默认选中第一个
        if (chapters.value.length > 0) {
          selectedChapter.value = chapters.value[0].id
          // 自动获取第一个章节的作业
          await fetchChapterHomework(classId, selectedChapter.value)
        }
        
        return Promise.resolve(chapters.value)
      }
      return Promise.resolve([])
    } catch (error) {
      console.error('获取章节失败:', error)
      message.error('获取章节失败')
      return Promise.reject(error)
    } finally {
      loading.value = false
    }
  }

  // 获取章节习题
  const fetchChapterExercises = async (classId, titleId) => {
    if (!classId || !titleId) return
    
    // 获取当前学生信息
    const student = getStudentInfo()
    const studentId = getFormattedStudentId(student)
    
    if (!studentId) {
      message.warning('无法获取有效的学生ID')
      return
    }
    
    try {
      loading.value = true
      chapterExercises.value = []
      
      const { data } = await api.get(`/api/homework/class/${classId}/chapter/${titleId}/exercises`, {
        params: { studentId }
      })
      
      if (data.success) {
        chapterExercises.value = data.data
      }
    } catch (error) {
      console.error('获取章节习题失败:', error)
      message.error('获取章节习题失败')
    } finally {
      loading.value = false
    }
  }
  
  // 获取章节下的作业批次
  const fetchChapterHomework = async (classId, titleId) => {
    if (!classId || !titleId) return
    
    // 获取当前学生信息
    const student = getStudentInfo()
    const studentId = getFormattedStudentId(student)
    
    if (!studentId) {
      message.warning('无法获取有效的学生ID')
      return
    }
    
    try {
      loading.value = true
      homeworks.value = []
      
      const { data } = await api.get(`/api/homework/class/${classId}/chapter/${titleId}/homework`, {
        params: { studentId }
      })
      
      if (data.success) {
        // 修正分数显示：检查返回的分数计算是否有误
        const correctedHomeworks = data.data.map(homework => {
          // 如果有分数信息，检查是否合理
          if (homework.score) {
            const exerciseTypes = {
              judgement: 0, // 判断题数量
              calculation: 0 // 计算题数量
            }
            
            // 根据exercise_count估算题目类型
            // 这里假设最多有一道计算题，剩下的都是判断题
            if (homework.exercise_count > 0) {
              // 判断是否有计算题 (题目总数通常在4道左右)
              const hasCalculation = homework.score.full_score > homework.exercise_count * 5
              
              if (hasCalculation) {
                exerciseTypes.calculation = 1 // 假设最多一道计算题
                exerciseTypes.judgement = homework.exercise_count - 1
              } else {
                exerciseTypes.judgement = homework.exercise_count
              }
              
              // 计算正确的满分
              const correctFullScore = exerciseTypes.judgement * 5 + exerciseTypes.calculation * 10
              
              // 如果满分与API返回的不一致，修正显示
              if (correctFullScore !== homework.score.full_score) {
                console.log(`修正作业满分: 从 ${homework.score.full_score} 改为 ${correctFullScore}`)
                homework.score.full_score = correctFullScore
                
                // 如果得分明显不合理，也尝试修正
                if (homework.score.total_score > correctFullScore) {
                  console.log(`修正作业得分: 从 ${homework.score.total_score} 改为 ${Math.min(homework.score.total_score, correctFullScore)}`)
                  homework.score.total_score = Math.min(homework.score.total_score, correctFullScore)
                }
              }
            }
          }
          return homework
        })
        
        homeworks.value = correctedHomeworks
      }
    } catch (error) {
      console.error('获取章节作业批次失败:', error)
      message.error('获取章节作业批次失败')
    } finally {
      loading.value = false
    }
  }
  
  // 处理班级变更
  const handleClassChange = async (classId) => {
    homeworks.value = []
    chapters.value = []
    selectedChapter.value = null
    chapterExercises.value = []
    
    try {
      await fetchClassChapters(classId)
      
      // 检查是否获取到章节
      if (chapters.value.length === 0) {
        console.log('未找到该班级的任何章节')
        message.info('未找到该班级的任何章节，请确认已发布习题')
      }
    } catch (error) {
      console.error('班级变更处理失败:', error)
    }
  }
  
  // 处理章节变更
  const handleChapterChange = async (titleId) => {
    if (!selectedClass.value) return
    
    await fetchChapterHomework(selectedClass.value, titleId)
  }
  
  // 获取已选择的章节标题
  const getSelectedChapterTitle = () => {
    if (!selectedChapter.value) return ''
    
    const chapter = chapters.value.find(c => c.id === selectedChapter.value)
    
    if (chapter) {
      // 如果存在fullPath，用于标题展示使用更详细的路径
      return chapter.fullPath || chapter.title
    }
    
    return ''
  }
  
  // 判断是否是当前章节已开始的作业
  const isChapterHomeworkStarted = (homework) => {
    if (!homework?.timer_status?.has_started || !selectedChapter.value) {
      return false;
    }
    
    // 确保类型一致，使用字符串比较
    return String(homework.timer_status.title_id) === String(selectedChapter.value);
  };
  
  // 开始做作业
  const startExercise = (homework) => {
    if (!homework || !homework.release_batch || !selectedClass.value || !selectedChapter.value) {
      message.error('作业信息不完整，无法开始')
      return
    }
    
    // 准备作业标题
    const title = `${getSelectedChapterTitle()} - 第${homework.release_batch}次作业`
    
    // 如果作业已完成，直接进入查看页面，不弹窗
    if (homework.completion_status?.is_completed) {
      // 记录到本地存储，在学习页面可以获取
      localStorage.setItem('current_homework', JSON.stringify({
        batchId: homework.release_batch,
        classId: selectedClass.value,
        title: title,
        startTime: homework.timer_status?.start_time || '',
        timeLimit: homework.time_limit || 0,
        deadline: homework.deadline || null,
        isCompleted: true, // 标记为已完成，学习页面会用到
        chapterId: selectedChapter.value
      }))
      
      // 跳转到作业详情页面
      router.push({
        path: `/student/exercise-homework-look/${selectedClass.value}/${homework.release_batch}`,
        query: { chapterId: selectedChapter.value }
      })
      return
    }
    
    // 如果截止日期已过，显示提示并禁止开始
    if (homework.deadline && isDeadlineExpired(homework.deadline)) {
      message.error('该作业已截止，无法再提交')
      return
    }
    
    // 如果作业已超时，显示提示并禁止再次开始
    if (homework.timer_status?.is_timeout) {
      message.error('该作业已超时未完成，无法继续')
      return
    }
    
    // 检查是否是当前章节的作业已经开始
    if (isChapterHomeworkStarted(homework) && !homework.timer_status?.is_completed) {
      const startTime = homework.timer_status.start_time
      
      // 记录到本地存储，在学习页面可以获取
      localStorage.setItem('current_homework', JSON.stringify({
        batchId: homework.release_batch,
        classId: selectedClass.value,
        title: title,
        startTime,
        timeLimit: homework.time_limit,
        deadline: homework.deadline || null,
        isCompleted: false,
        chapterId: selectedChapter.value
      }))
      
      // 跳转到作业学习页面
      router.push({
        name: 'homework-learning',
        params: {
          classId: selectedClass.value,
          batchId: homework.release_batch
        },
        query: { chapterId: selectedChapter.value }
      })
      return
    }
    
    // 检查是否有时间限制（仅当是新开始的作业时才弹窗）
    if (homework.time_limit > 0) {
      // 使用确认对话框提示用户
      import('ant-design-vue').then(({ Modal }) => {
        Modal.confirm({
          title: '开始作业',
          content: `本次作业有时间限制：${homework.time_limit}分钟。一旦开始，计时将无法暂停。确定现在开始吗？`,
          okText: '开始',
          cancelText: '取消',
          onOk: async () => {
            try {
              loading.value = true
              
              // 调用API记录开始时间
              const response = await api.post(`/api/homework-learning/start/${selectedClass.value}/${homework.release_batch}?chapterId=${selectedChapter.value}`)
              
              if (response.data.success) {
                const { start_time, time_limit } = response.data.data
                
                // 记录到本地存储，在学习页面可以获取
                localStorage.setItem('current_homework', JSON.stringify({
                  batchId: homework.release_batch,
                  classId: selectedClass.value,
                  title: title,
                  startTime: start_time,
                  timeLimit: time_limit,
                  deadline: homework.deadline || null,
                  chapterId: selectedChapter.value
                }))
                
                // 跳转到作业学习页面
                router.push({
                  name: 'homework-learning',
                  params: {
                    classId: selectedClass.value,
                    batchId: homework.release_batch
                  },
                  query: { chapterId: selectedChapter.value }
                })
              } else {
                message.error(response.data.message || '开始作业失败')
              }
            } catch (error) {
              console.error('开始作业错误:', error)
              message.error('开始作业失败：' + (error.response?.data?.message || error.message))
            } finally {
              loading.value = false
            }
          }
        })
      })
    } else {
      // 无时间限制，直接跳转
      // 记录到本地存储，在学习页面可以获取
      localStorage.setItem('current_homework', JSON.stringify({
        batchId: homework.release_batch,
        classId: selectedClass.value,
        title: title,
        deadline: homework.deadline || null,
        chapterId: selectedChapter.value
      }))
      
      // 跳转到作业学习页面
      router.push({
        name: 'homework-learning',
        params: {
          classId: selectedClass.value,
          batchId: homework.release_batch
        },
        query: { chapterId: selectedChapter.value }
      })
    }
  }
  
  // 格式化日期
  const formatDate = (date) => {
    if (!date) return '未设置'
    return dayjs(date).format('YYYY-MM-DD HH:mm')
  }
  
  // 判断截止日期是否已过期
  const isDeadlineExpired = (deadline) => {
    if (!deadline) return false
    return dayjs().isAfter(dayjs(deadline))
  }
  
  // 计算时间差
  const calculateDuration = (startTime, endTime) => {
    if (!startTime || !endTime) return '未知'
    
    const start = dayjs(startTime)
    const end = dayjs(endTime)
    const diffInSeconds = end.diff(start, 'second')
    
    const hours = Math.floor(diffInSeconds / 3600)
    const minutes = Math.floor((diffInSeconds % 3600) / 60)
    const seconds = diffInSeconds % 60
    
    if (hours > 0) {
      return `${hours}小时${minutes}分${seconds}秒`
    } else if (minutes > 0) {
      return `${minutes}分${seconds}秒`
    } else {
      return `${seconds}秒`
    }
  }
  
  // 获取按钮类型和文本
  const getButtonTypeAndText = (homework) => {
    if (!homework) return { type: 'default', text: '开始作业', disabled: true }
    
    // 如果截止日期已过且作业未完成，显示为"已截止"
    if (homework.deadline && isDeadlineExpired(homework.deadline) && !homework.completion_status?.is_completed) {
      return { type: 'danger', text: '已截止', disabled: true }
    }
    
    // 如果作业已超时，显示为"已超时"
    if (homework.timer_status?.is_timeout) {
      return { type: 'danger', text: '已超时', disabled: true }
    }
    
    // 如果作业已完成，显示为"查看提交"
    if (homework.completion_status?.is_completed) {
      return { type: 'primary', text: '查看提交', disabled: false }
    }
    
    // 检查是否有计时数据
    if (homework.timer_status?.has_started) {
      // 检查是否是当前章节的作业已经开始
      if (isChapterHomeworkStarted(homework)) {
        return { type: 'warning', text: '继续作业', disabled: false }
      }
    }
    
    // 默认为"开始作业"
    return { type: 'primary', text: '开始作业', disabled: false }
  }
  
  // 根据得分返回不同颜色
  const getScoreColor = (homework) => {
    if (!homework.score) return ''
    
    // 修复score字段引用
    const totalScore = homework.score.total_score || 0
    const fullScore = homework.score.full_score || 0
    
    if (fullScore === 0) return ''
    const percent = totalScore / fullScore
    if (percent >= 0.8) return 'success'
    if (percent >= 0.6) return 'warning'
    return 'error'
  }
  
  // 初始化
  onMounted(() => {
    // 输出登录用户的完整信息，便于调试
    console.log('当前登录学生的完整用户信息:', getStudentInfo())
    
    // 首先检查是否有上次保存的班级和章节选择状态
    try {
      const savedState = localStorage.getItem('exercise_homework_state');
      if (savedState) {
        const state = JSON.parse(savedState);
        
        // 检查状态是否在10分钟内保存的，如果是则恢复
        const now = new Date().getTime();
        const stateTime = state.timestamp || 0;
        if (now - stateTime < 10 * 60 * 1000) { // 10分钟有效期
          console.log('恢复之前的课后习题选择状态');
          
          // 先获取班级列表，然后在获取完成后设置选择状态
          fetchStudentClasses().then(() => {
            // 如果班级ID在班级列表中，则设置选择状态
            if (state.selectedClass && classes.value.some(c => c.class_id == state.selectedClass)) {
              selectedClass.value = state.selectedClass;
              
              // 获取章节列表后再设置章节
              fetchClassChapters(state.selectedClass).then(() => {
                if (state.selectedChapter && chapters.value.some(c => c.id == state.selectedChapter)) {
                  selectedChapter.value = state.selectedChapter;
                  
                  // 加载作业数据
                  fetchChapterHomework(state.selectedClass, state.selectedChapter);
                }
              });
            }
          });
          
          // 清除保存的状态，防止下次进入页面时自动恢复
          localStorage.removeItem('exercise_homework_state');
        } else {
          // 状态已过期，清除
          localStorage.removeItem('exercise_homework_state');
          fetchStudentClasses();
        }
      } else {
        fetchStudentClasses();
      }
    } catch (error) {
      console.error('恢复状态失败', error);
      fetchStudentClasses();
    }
  })
  </script>
  
  <style lang="less" scoped>
  .exercise-homework-container {
    padding: 20px;
    background-color: #f0f2f5;
    min-height: 100vh;
  }
  
  .filter-bar {
    margin: 20px 0;
  }
  
  .homework-list-container {
    margin-top: 20px;
  }
  
  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
    color: #303133;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .homework-list, .exercise-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .homework-card, .exercise-card {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    width: 100%;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
    
    .card-actions {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .exercise-content {
    margin-bottom: 16px;
    max-height: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .exercise-footer {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  :deep(.ant-descriptions) {
    margin: 16px 0;
  }
  
  :deep(.ant-descriptions-item-label) {
    background-color: #fafafa;
    width: 100px;
  }
  
  :deep(.ant-page-header) {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  /* 响应式样式 */
  @media (max-width: 768px) {
    .exercise-homework-container {
      padding: 10px;
    }
    
    .homework-card {
      .card-title {
        .title-text {
          font-size: 14px;
        }
      }
    }
  }
  
  :deep(.ant-select) {
    width: auto !important;
    min-width: 180px;
  }
  
  :deep(.ant-select-dropdown) {
    width: auto !important;
    min-width: 350px;
  }
  
  :deep(.ant-select-item-option-content) {
    white-space: normal;
    word-break: break-word;
  }
  
  :deep(.ant-select-selection-item) {
    white-space: nowrap;
    overflow: visible;
    text-overflow: unset;
    width: auto;
  }
  
  :deep(.ant-select-item) {
    padding: 8px 12px;
  }
  </style>
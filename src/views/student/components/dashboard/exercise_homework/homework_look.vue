<template>
  <div class="homework-look-container">
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240); background: white; padding: 12px 16px;"
      :title="`第${batchId}次作业详情`"
      @back="goBack"
    >
      <template #extra>
        <a-space>
          <a-tag v-if="submissionInfo?.is_timeout" color="red">已超时</a-tag>
          <a-tag v-else-if="submissionInfo?.is_completed" color="green">已完成</a-tag>
          <a-tag color="blue">
            提交时间: {{ submissionInfo?.end_time ? formatDate(submissionInfo.end_time) : '未提交' }}
          </a-tag>
          <a-tag :color="getScorePercent() >= 60 ? 'green' : 'red'">
            得分: {{ scoreInfo.earned }}/{{ scoreInfo.total }}
          </a-tag>
        </a-space>
      </template>
    </a-page-header>
    
    <div class="main-content">
      <div class="exercise-list" v-loading="loading">
        <!-- 进度汇总卡片 -->
        <a-card class="summary-card" id="summary" :bodyStyle="{ padding: '12px' }">
          <template #title>
            <div class="card-title">
              <span>提交结果汇总</span>
    </div>
          </template>
          <a-row :gutter="16">
            <a-col :span="4">
              <a-statistic title="总题目数" :value="exercises.length" :value-style="{ color: '#1890ff' }" />
            </a-col>
            <a-col :span="5">
              <a-statistic 
                title="满分" 
                :value="scoreInfo.total" 
                :value-style="{ color: '#722ed1' }" 
              />
            </a-col>
            <a-col :span="5">
              <a-statistic 
                title="学生得分" 
                :value="scoreInfo.earned" 
                :value-style="{ color: '#3f8600' }" 
              />
            </a-col>
            <a-col :span="5">
              <a-statistic 
                title="错误题目" 
                :value="exercises.length - correctCount" 
                :value-style="{ color: '#cf1322' }" 
              />
            </a-col>
            <a-col :span="5">
              <a-statistic 
                title="得分率" 
                :value="getScorePercent()" 
                :precision="2"
                :value-style="{ color: getScorePercent() >= 60 ? '#3f8600' : '#cf1322' }" 
                suffix="%" 
              />
            </a-col>
          </a-row>
        </a-card>

        <!-- 空状态 -->
        <a-empty v-if="!loading && exercises.length === 0" description="暂无作业提交记录" />

        <!-- 题目列表 -->
        <div v-if="!loading && exercises.length > 0">
          <a-card 
          v-for="(exercise, index) in exercises" 
            :key="exercise.exercise_id"
            class="exercise-card"
            :id="`question-${index + 1}`"
            :bodyStyle="{ padding: '10px' }"
          >
            <template #title>
              <div class="card-title">
                <div>
                  <a-tag color="blue">{{ getQuestionTypeName(exercise.question_type) }}</a-tag>
                  <a-tag color="orange" v-if="exercise.difficulty">{{ getDifficultyName(exercise.difficulty) }}</a-tag>
                  <span class="title-text">第{{ index + 1 }}题</span>
                </div>
                <div>
                  <a-tag :color="exercise.is_correct ? 'success' : 'error'">
                    {{ exercise.is_correct ? '答对了' : '答错了' }}
                  </a-tag>
                  <a-tag color="blue">{{ getQuestionScore(exercise) }}分</a-tag>
      </div>
    </div>
            </template>
            
            <!-- 题目内容 -->
            <div class="question-content" v-html="exercise.content"></div>
            
            <!-- 学生答案和正确答案放在一行 -->
            <div class="answer-row">
              <!-- 学生答案 -->
              <div class="answer-section student-answer">
                <div class="section-title">你的答案：</div>
                <div v-if="exercise.question_type !== 5 || !exercise.student_answer || !exercise.student_answer.startsWith('/')" class="answer-content">
                  {{ formatDisplayAnswer(exercise.student_answer, exercise.question_type, exercise) }}
                </div>
                <div v-else class="image-answer">
                  <img :src="getImageUrl(exercise.student_answer)" class="answer-image" @click="handlePreview(exercise.student_answer)" />
                </div>
              </div>
              
              <!-- 正确答案 -->
              <div class="answer-section correct-answer">
                <div class="section-title">正确答案：</div>
                <div class="answer-content">
                  {{ formatCorrectAnswer(exercise.answer, exercise.question_type, exercise) }}
                </div>
              </div>
            </div>
            
            <!-- 解析和知识点放在一行 -->
            <div class="answer-row" v-if="exercise.analysis || (exercise.points && exercise.points.length > 0)">
              <!-- 知识点 -->
              <div class="answer-section knowledge-points" v-if="exercise.points && exercise.points.length > 0">
                <div class="section-title">知识点：</div>
                <div class="answer-content">
                  <a-tag 
                    v-for="(point, pIndex) in exercise.points" 
                    :key="pIndex"
                    color="blue"
                    style="margin-right: 8px; margin-bottom: 4px;"
                  >
                    {{ point }}
                  </a-tag>
                </div>
              </div>
              
              <!-- 解析 -->
              <div class="answer-section explanation" v-if="exercise.analysis">
                <div class="section-title">解析：</div>
                <div class="answer-content" v-html="exercise.analysis"></div>
              </div>
            </div>
            
            <!-- 评价 - 仅针对简答题和计算题 -->
            <div class="answer-section evaluation" v-if="exercise.evaluation && (exercise.question_type === 4 || exercise.question_type === 5)">
              <div class="section-title">评价：</div>
              <div class="answer-content">{{ exercise.evaluation }}</div>
            </div>
          </a-card>
        </div>
      </div>

      <!-- 右侧固定导航 -->
      <div class="question-navigator" v-if="exercises.length > 0">
        <div class="navigator-title">题目导航</div>
        <div class="navigator-items">
          <a 
            v-for="(exercise, index) in exercises" 
            :key="index"
            class="navigator-item"
            :class="{ 'correct': exercise.is_correct, 'wrong': !exercise.is_correct }"
            :href="`#question-${index + 1}`"
          >
            {{ index + 1 }}
          </a>
        </div>
        <a href="#summary" class="navigator-summary">回到顶部</a>
          </div>
    </div>

    <!-- 预览图片对话框 -->
    <a-modal
      v-model:visible="previewVisible"
      title="图片预览"
      :footer="null"
      :width="800"
    >
      <img
        v-if="previewImage"
        :src="previewImage"
        style="width: 100%"
        alt="预览图片"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import axios from 'axios';
import dayjs from 'dayjs';

// 创建axios实例
const api = axios.create({
  baseURL: ''
});

// 添加请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

const route = useRoute();
const router = useRouter();
const { classId, batchId } = route.params;
const chapterId = ref(route.query.chapterId || null);

// 状态
const loading = ref(false);
const exercises = ref([]);
const submissionInfo = ref(null);
const scoreInfo = ref({ earned: 0, total: 0 });

// 图片预览状态
const previewVisible = ref(false);
const previewImage = ref(null);

// 计算属性
const correctCount = computed(() => {
  return exercises.value.filter(exercise => exercise.is_correct).length;
});

// 返回上一页
const goBack = () => {
  // 从路由中获取必要的参数
  const { classId } = route.params;
  const queryChapterId = route.query.chapterId;
  
  // 将班级和章节存储到localStorage，以便在返回dashboard时可以恢复选择
  if (classId && queryChapterId) {
    try {
      localStorage.setItem('exercise_homework_state', JSON.stringify({
        selectedClass: classId,
        selectedChapter: queryChapterId,
        timestamp: new Date().getTime()
      }));
    } catch (error) {
      console.error('保存状态失败', error);
    }
  }
  
  // 跳转到学生仪表盘并指定显示课后习题组件
  router.push({
    path: '/student-dashboard',
    query: { 
      component: 'exercise-homework',
      timestamp: new Date().getTime() // 添加时间戳确保刷新
    }
  });
};

// 获取作业详情
const fetchHomeworkDetails = async () => {
  try {
    loading.value = true;
    
    // 构建URL，确保添加chapterId作为查询参数
    let url = `/api/homework-look/details/${classId}/${batchId}`;
    if (chapterId.value) {
      url += `?chapterId=${chapterId.value}`;
    }
    
    const response = await api.get(url);
    
    if (response.data.success) {
      exercises.value = response.data.data.exercises;
      submissionInfo.value = response.data.data.submission;
      
      // 手动计算总分和得分，防止NaN
      let totalEarned = 0;
      let totalPossible = 0;
      
      if (response.data.data.exercises && response.data.data.exercises.length > 0) {
        response.data.data.exercises.forEach(exercise => {
          // 计算满分
          const maxScore = exercise.question_type <= 3 ? 5 : 10;
          totalPossible += maxScore;
          
          // 计算得分
          const score = exercise.score !== null && exercise.score !== undefined ? 
            (!isNaN(Number(exercise.score)) ? Math.round(Number(exercise.score)) : 0) : 0;
          totalEarned += score;
        });
      }
      
      scoreInfo.value = {
        earned: totalEarned,
        total: totalPossible
      };
      
      console.log('前端计算的分数信息:', scoreInfo.value);
    } else {
      message.error('获取作业详情失败：' + response.data.message);
    }
  } catch (error) {
    console.error('获取作业详情错误:', error);
    message.error('获取作业详情失败：' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 获取题目类型名称
const getQuestionTypeName = (type) => {
  const types = {
    1: '选择题',
    2: '填空题',
    3: '判断题',
    4: '简答题',
    5: '计算题'
  };
  return types[type] || '未知类型';
};

// 获取难度名称
const getDifficultyName = (difficulty) => {
  const difficulties = {
    1: '简单',
    2: '中等',
    3: '困难',
    4: '复杂',
    5: '挑战'
  };
  return difficulties[difficulty] || '未知难度';
};

// 格式化显示答案
const formatDisplayAnswer = (answer, questionType, exercise) => {
  if (!answer || answer === '未作答') return '未作答';
  
  if (questionType === 3) { // 判断题
    return answer.toUpperCase() === 'T' || 
           answer.toUpperCase() === 'TRUE' || 
           answer === '1' || 
           answer === '正确' || 
           answer === '对' ? '正确' : '错误';
  } else if (questionType === 1) { // 选择题
    if (exercise && exercise.options) {
      const option = exercise.options.find(opt => opt.key === answer);
      return option ? `${option.key}: ${option.value}` : answer;
    }
  }
  
  return answer;
};

// 格式化正确答案
const formatCorrectAnswer = (answer, questionType, exercise) => {
  if (!answer) return '未提供';
  
  if (questionType === 3) { // 判断题
    return answer.toUpperCase() === 'T' || 
           answer.toUpperCase() === 'TRUE' || 
           answer === '1' || 
           answer === '正确' || 
           answer === '对' ? '正确' : '错误';
  } else if (questionType === 1) { // 选择题
    if (exercise && exercise.options) {
      const option = exercise.options.find(opt => opt.key === answer);
      return option ? `${option.key}: ${option.value}` : answer;
    }
  }
  
  return answer;
};

// 获取分数百分比
const getScorePercent = () => {
  if (scoreInfo.value.total === 0) return 0;
  return Math.round((scoreInfo.value.earned / scoreInfo.value.total) * 100);
};

// 获取格式化的分数文本
const getFormattedScore = () => {
  const earned = isNaN(scoreInfo.value.earned) ? 0 : Math.round(scoreInfo.value.earned);
  const total = isNaN(scoreInfo.value.total) ? 0 : Math.round(scoreInfo.value.total);
  return `${earned}/${total}`;
};

// 获取题目得分（确保是整数）
const getQuestionScore = (exercise) => {
  if (exercise.score === null || exercise.score === undefined || isNaN(Number(exercise.score))) {
    return 0;
  }
  return Math.round(Number(exercise.score));
};

// 获取题目满分
const getQuestionMaxScore = (exercise) => {
  return exercise.question_type <= 3 ? 5 : 10;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '未设置';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 计算时间差
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return '未知';
  
  const start = dayjs(startTime);
  const end = dayjs(endTime);
  const diffInSeconds = end.diff(start, 'second');
  
  const hours = Math.floor(diffInSeconds / 3600);
  const minutes = Math.floor((diffInSeconds % 3600) / 60);
  const seconds = diffInSeconds % 60;
  
  if (hours > 0) {
    return `${hours}小时${minutes}分${seconds}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
};

// 处理图片URL
const getImageUrl = (path) => {
  if (!path) return '';
  
  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  // 基于当前域名构建完整URL
  const baseUrl = window.location.origin;
  return `${baseUrl}${normalizedPath}`;
};

// 处理图片预览
const handlePreview = (imageSrc) => {
  previewImage.value = imageSrc;
  previewVisible.value = true;
};

// 初始化
onMounted(() => {
  if (!classId || !batchId) {
    message.error('参数不完整，无法获取作业详情');
    router.push('/student/homework');
    return;
  }
  
  fetchHomeworkDetails();
});
</script>

<style lang="less" scoped>
.homework-look-container {
  background-color: #f0f2f5;
  min-height: 100vh;
  padding: 12px;
}

.main-content {
  display: flex;
}

.exercise-list {
  margin-top: 12px;
  flex: 1;
  padding-right: 60px; /* 给右侧导航留出空间 */
}

.summary-card {
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
}

.exercise-card {
  margin-bottom: 10px;
  border-radius: 6px;
  overflow: hidden;
}

.card-title {
      display: flex;
  justify-content: space-between;
      align-items: center;
  padding: 4px 8px;
  
  .title-text {
    margin-left: 8px;
    font-weight: 600;
    font-size: 14px;
  }
}

.question-content {
  margin-bottom: 6px;
  padding: 6px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 30px;
        font-size: 13px;
}

.answer-row {
  display: flex;
  gap: 8px;
  margin-bottom: 6px;
}

.answer-section {
  margin-bottom: 6px;
  padding: 6px;
  border-radius: 4px;
  flex: 1;
  
  .section-title {
    font-weight: bold;
    margin-bottom: 3px;
          font-size: 13px;
  }
  
  .answer-content {
    white-space: pre-line;
    font-size: 12px;
    line-height: 1.4;
  }
  
  &.student-answer {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    
    .section-title {
      color: #1890ff;
    }
  }
  
  &.correct-answer {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    
    .section-title {
            color: #52c41a;
          }
        }
  
  &.knowledge-points {
    background-color: #fff7e6;
    border: 1px solid #ffd591;
    
    .section-title {
      color: #fa8c16;
    }
  }
  
  &.explanation {
    background-color: #f9f0ff;
    border: 1px solid #d3adf7;
    
    .section-title {
      color: #722ed1;
    }
  }
  
  &.evaluation {
    background-color: #fcf4f2;
    border: 1px solid #f5c5bd;
    
    .section-title {
      color: #f5222d;
    }
  }
  
  .image-answer {
    max-width: 100%;
    
    .answer-image {
      max-width: 100%;
      max-height: 200px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .homework-look-container {
    padding: 8px;
  }
  
  .exercise-list {
    padding-right: 0;
  }
  
  .card-title {
    flex-direction: column;
    align-items: flex-start;
    
    div:last-child {
      margin-top: 6px;
    }
  }
  
  .answer-row {
    flex-direction: column;
    gap: 6px;
  }
}

.question-navigator {
  width: 50px;
  margin-left: 10px;
  padding: 8px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: fixed;
  right: 10px;
  top: 80px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;

  .navigator-title {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 12px;
    text-align: center;
  }

  .navigator-items {
    margin-bottom: 8px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3px;
  }

  .navigator-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    padding: 0;
    text-align: center;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s;
    border-radius: 3px;
    font-size: 11px;

    &.correct {
      background-color: #e6f7ff;
    }

    &.wrong {
      background-color: #fff1f0;
    }

    &:hover {
      background-color: #f0f5ff;
    }
  }

  .navigator-summary {
    display: block;
    padding: 4px;
    text-align: center;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s;
    font-size: 11px;
    border-radius: 3px;

    &:hover {
      background-color: #f0f5ff;
    }
  }
}
</style>

<template>
  <div class="exercise-preview-container">
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240)"
      title="预习习题"
      sub-title="选择班级和章节查看预习题"
    >
      <!-- 班级和章节选择 -->
      <div class="filter-bar">
        <a-row :gutter="24">
          <a-col :span="10">
            <a-select 
              v-model:value="selectedClass" 
              placeholder="请选择班级" 
              @change="handleClassChange"
              style="width: 100%; min-width: 180px;"
              size="large"
            >
              <a-select-option
                v-for="item in classes"
                :key="item.class_id"
                :value="item.class_id"
              >
                {{ item.class_name }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="14">
            <a-select 
              v-model:value="selectedChapter" 
              placeholder="请选择章节" 
              @change="handleChapterChange"
              style="width: 100%; min-width: 220px;"
              size="large"
              :disabled="!selectedClass || chapters.length === 0"
            >
              <a-select-option
                v-for="item in chapters"
                :key="item.id"
                :value="item.id"
              >
                {{ item.title }}
              </a-select-option>
            </a-select>
          </a-col>
        </a-row>
      </div>
    </a-page-header>

    <!-- 预习题列表 -->
    <div v-loading="loading" class="preview-list-container">
      <!-- 显示章节预习题 -->
      <div v-if="selectedClass && selectedChapter && previews.length > 0" class="preview-list">
        <div class="section-title">
          《{{ getSelectedChapterTitle() }}》章节预习题
        </div>
        
        <a-card 
          v-for="preview in previews" 
          :key="preview.release_time" 
          class="preview-card"
          :hoverable="true"
        >
          <template #title>
            <div class="card-title">
              <span class="title-text">{{ getSelectedChapterTitle() }} - 预习题</span>
            </div>
          </template>
          
          <a-descriptions size="small" :column="2" bordered>
            <a-descriptions-item label="题目数量">{{ preview.exercise_count }}</a-descriptions-item>
            <a-descriptions-item label="发布时间">{{ formatDate(preview.release_time) }}</a-descriptions-item>
            <a-descriptions-item label="截止日期" v-if="preview.deadline">
              {{ formatDate(preview.deadline) }}
              <a-tag v-if="isDeadlineExpired(preview.deadline)" color="red">已截止</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="完成情况" v-if="preview.completion_status">
              <a-progress 
                :percent="preview.completion_status.percentage" 
                size="small" 
                :status="preview.completion_status.is_completed ? 'success' : 'active'"
              />
            </a-descriptions-item>
          </a-descriptions>
          
          <div class="card-actions">
            <a-button 
              class="start-button" 
              :type="getButtonTypeAndText(preview).type"
              @click="startPreview(preview)"
              :disabled="getButtonTypeAndText(preview).disabled"
            >
              {{ getButtonTypeAndText(preview).text }}
            </a-button>
          </div>
        </a-card>
      </div>

      <!-- 空状态 -->
      <a-empty 
        v-else-if="selectedClass && selectedChapter && !loading" 
        description="该章节暂无预习题" 
      />
      
      <!-- 初始状态 -->
      <a-empty 
        v-else-if="!loading" 
        description="请选择班级和章节查看预习题" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import dayjs from 'dayjs'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
})

// 添加请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (!token) {
      message.error('未登录或登录已过期，请重新登录')
      router.push('/login')
      return Promise.reject('未登录或登录已过期')
    }
    config.headers['Authorization'] = `Bearer ${token}`
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 添加响应拦截器
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      message.error('登录已过期，请重新登录')
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      router.push('/login')
    }
    return Promise.reject(error)
  }
)

// 定义接口
interface Class {
  class_id: number;
  class_name: string;
  [key: string]: any;
}

interface Chapter {
  id: string;
  title: string;
  fullPath?: string;
}

interface Exercise {
  publish_id: number;
  exercise_id: number;
  title: string;
  content: string;
  difficulty: number;
  question_type: number;
  publish_time: string;
  deadline?: string;
  student_answer?: string;
  is_correct?: boolean;
}

interface Preview {
  exercise_count: number;
  release_time: string;
  deadline?: string;
  completion_status?: {
    completed: number;
    total: number;
    percentage: number;
    is_completed: boolean;
  };
  exercises?: Exercise[];
  [key: string]: any;
}

// 状态
const loading = ref(false)
const classes = ref<Class[]>([])
const chapters = ref<Chapter[]>([])
const selectedClass = ref<number | null>(null)
const selectedChapter = ref<string | null>(null)
const previews = ref<Preview[]>([])
const studentInfo = ref(null)
const router = useRouter()

// 获取学生信息
const getStudentInfo = () => {
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    try {
      const parsed = JSON.parse(userInfo)
      studentInfo.value = parsed
      console.log('从localStorage获取到的学生信息:', parsed)
      return parsed
    } catch (e) {
      console.error('解析用户信息失败', e)
      return null
    }
  }
  return null
}

// 获取学生ID - 确保使用student_id而不是id
const getFormattedStudentId = (student) => {
  if (!student) return null
  
  // 正确使用student_id字段，不是id字段
  if (student.student_id) {
    console.log('使用student_id:', student.student_id)
    return student.student_id
  }
  
  console.warn('学生信息中没有student_id字段:', student)
  return null
}

// 获取学生班级
const fetchStudentClasses = async () => {
  const student = getStudentInfo()
  if (!student) {
    message.warning('未找到学生信息')
    return
  }

  const studentId = getFormattedStudentId(student)
  if (!studentId) {
    message.warning('无法获取有效的学生ID')
    return
  }

  try {
    loading.value = true
    console.log('使用学生ID获取班级:', studentId)
    
    // 直接查看localStorage中的用户信息结构
    console.log('localStorage中的userInfo结构:', JSON.parse(localStorage.getItem('userInfo') || '{}'))
    
    const { data } = await api.get(`/api/preview-exercises/student/${studentId}/classes`)
    
    console.log('班级API返回数据:', data)
    
    if (data.success) {
      classes.value = data.data
      console.log('获取到的班级数据:', classes.value)
      
      if (classes.value.length > 0) {
        selectedClass.value = classes.value[0].class_id
        await fetchClassChapters(selectedClass.value)
      }
    }
  } catch (error) {
    console.error('获取班级失败:', error)
    message.error('获取班级失败')
  } finally {
    loading.value = false
  }
}

// 获取班级章节
const fetchClassChapters = async (classId) => {
  if (!classId) return
  
  try {
    loading.value = true
    const { data } = await api.get(`/api/preview-exercises/class/${classId}/chapters`)
    
    if (data.success) {
      chapters.value = data.data
    }
  } catch (error) {
    console.error('获取章节失败:', error)
    message.error('获取章节失败')
  } finally {
    loading.value = false
  }
}

// 获取章节预习题
const fetchChapterPreview = async (classId, titleId) => {
  if (!classId || !titleId) return
  
  // 获取当前学生信息
  const student = getStudentInfo()
  const studentId = getFormattedStudentId(student)
  
  if (!studentId) {
    message.warning('无法获取有效的学生ID')
    return
  }
  
  try {
    loading.value = true
    previews.value = []
    
    const { data } = await api.get(`/api/preview-exercises/class/${classId}/chapter/${titleId}/preview`, {
      params: { studentId }
    })
    
    if (data.success) {
      previews.value = data.data
    }
  } catch (error) {
    console.error('获取章节预习题失败:', error)
    message.error('获取章节预习题失败')
  } finally {
    loading.value = false
  }
}

// 处理班级变更
const handleClassChange = async (classId) => {
  previews.value = []
  chapters.value = []
  selectedChapter.value = null
  
  try {
    await fetchClassChapters(classId)
    
    // 检查是否获取到章节
    if (chapters.value.length === 0) {
      console.log('未找到该班级的任何章节')
      message.info('未找到该班级的任何章节，请确认已发布预习题')
    }
  } catch (error) {
    console.error('班级变更处理失败:', error)
  }
}

// 处理章节变更
const handleChapterChange = async (titleId) => {
  if (!selectedClass.value) return
  
  await fetchChapterPreview(selectedClass.value, titleId)
}

// 获取已选择的章节标题
const getSelectedChapterTitle = () => {
  if (!selectedChapter.value) return ''
  
  const chapter = chapters.value.find(c => c.id === selectedChapter.value)
  
  if (chapter) {
    // 如果存在fullPath，用于标题展示使用更详细的路径
    return chapter.fullPath || chapter.title
  }
  
  return ''
}

// 开始做预习题
const startPreview = (preview) => {
  if (!preview || !selectedClass.value || !selectedChapter.value) {
    message.error('预习题信息不完整，无法开始')
    return
  }
  
  // 如果截止日期已过且预习未完成，显示提示并禁止开始
  if (preview.deadline && isDeadlineExpired(preview.deadline) && !preview.completion_status?.is_completed) {
    message.error('该预习题已截止，无法再提交')
    return
  }
  
  // 准备预习题标题
  const title = `${getSelectedChapterTitle()} - 预习题`
  
  // 如果预习题已完成，跳转到查看预习题提交页面
  if (preview.completion_status?.is_completed) {
    router.push({
      path: `/student/exercise-preview-look/${selectedClass.value}/${selectedChapter.value}`,
      query: { title: title }
    })
    return
  }
  
  // 跳转到预习题学习页面
  router.push({
    path: `/student/exercise-preview-learning/${selectedClass.value}/${selectedChapter.value}`,
    query: { 
      title: title
    }
  })
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '未设置'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 判断截止日期是否已过期
const isDeadlineExpired = (deadline) => {
  if (!deadline) return false
  return dayjs().isAfter(dayjs(deadline))
}

// 获取按钮类型和文本
const getButtonTypeAndText = (preview) => {
  if (!preview) return { type: 'default', text: '开始预习', disabled: true }
  
  // 如果截止日期已过且预习未完成，显示为"已截止"
  if (preview.deadline && isDeadlineExpired(preview.deadline) && !preview.completion_status?.is_completed) {
    return { type: 'danger', text: '已截止', disabled: true }
  }
  
  // 如果预习已完成，显示为"查看预习"
  if (preview.completion_status?.is_completed) {
    return { type: 'primary', text: '查看预习', disabled: false }
  }
  
  // 默认为"开始预习"
  return { type: 'primary', text: '开始预习', disabled: false }
}

// 初始化
onMounted(() => {
  // 输出登录用户的完整信息，便于调试
  console.log('当前登录学生的完整用户信息:', getStudentInfo())
  
  fetchStudentClasses()
})
</script>

<style lang="less" scoped>
.exercise-preview-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.filter-bar {
  margin: 20px 0;
}

.preview-list-container {
  margin-top: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  width: 100%;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .card-actions {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.ant-descriptions) {
  margin: 16px 0;
}

:deep(.ant-descriptions-item-label) {
  background-color: #fafafa;
  width: 100px;
}

:deep(.ant-page-header) {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .exercise-preview-container {
    padding: 10px;
  }
  
  .preview-card {
    .card-title {
      .title-text {
        font-size: 14px;
      }
    }
  }
}

:deep(.ant-select) {
  width: auto !important;
  min-width: 180px;
}

:deep(.ant-select-dropdown) {
  width: auto !important;
  min-width: 350px;
}

:deep(.ant-select-item-option-content) {
  white-space: normal;
  word-break: break-word;
}

:deep(.ant-select-selection-item) {
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  width: auto;
}

:deep(.ant-select-item) {
  padding: 8px 12px;
}
</style>

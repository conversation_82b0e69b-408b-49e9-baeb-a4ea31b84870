<template>
  <div class="preview-look-container">
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240); background: white;"
      :title="previewTitle"
      @back="goBack"
    >
      <template #extra>
        <a-space>
          <a-tag color="blue">
            提交时间: {{ submissionTime ? formatDate(submissionTime) : '未提交' }}
          </a-tag>
          <a-tag :color="scoreRate >= 60 ? 'green' : 'red'">
            得分: {{ formatScore(score) }}
          </a-tag>
        </a-space>
      </template>
    </a-page-header>

    <div class="main-content">
      <div class="exercise-list" v-loading="loading">
        <!-- 进度汇总卡片 -->
        <a-card class="summary-card" id="summary">
          <template #title>
            <div class="card-title">
              <span>提交结果汇总</span>
            </div>
          </template>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总题目数" :value="exercises.length" :value-style="{ color: '#1890ff' }" />
            </a-col>
            <a-col :span="6">
              <a-statistic 
                title="总分" 
                :value="totalScore" 
                :value-style="{ color: '#3f8600' }" 
              />
            </a-col>
            <a-col :span="6">
              <a-statistic 
                title="错误题目" 
                :value="exercises.length - correctCount" 
                :value-style="{ color: '#cf1322' }" 
              />
            </a-col>
            <a-col :span="6">
              <a-statistic 
                title="得分率" 
                :value="scoreRate" 
                :precision="2"
                :value-style="{ color: scoreRate >= 60 ? '#3f8600' : '#cf1322' }" 
                suffix="%" 
              />
            </a-col>
          </a-row>
        </a-card>

        <!-- 空状态 -->
        <a-empty v-if="!loading && exercises.length === 0" description="暂无预习题提交记录" />

        <!-- 题目列表 -->
        <div v-if="!loading && exercises.length > 0">
          <a-card 
            v-for="(exercise, index) in exercises" 
            :key="exercise.exercise_id"
            class="exercise-card"
            :id="`question-${index + 1}`"
          >
            <template #title>
              <div class="card-title">
                <div>
                  <a-tag color="blue">{{ getQuestionTypeName(exercise.question_type) }}</a-tag>
                  <a-tag color="orange">{{ getDifficultyName(exercise.difficulty) }}</a-tag>
                  <span class="title-text">第{{ index + 1 }}题</span>
                </div>
                <div>
                  <a-tag :color="exercise.is_correct ? 'success' : 'error'">
                    {{ exercise.is_correct ? '答对了' : '答错了' }}
                  </a-tag>
                  <a-tag color="blue">{{ exercise.score || 0 }}分</a-tag>
                </div>
              </div>
            </template>
            
            <!-- 题目内容 -->
            <div class="question-content" v-html="exercise.content"></div>
            
            <!-- 学生答案 -->
            <div class="answer-section student-answer">
              <div class="section-title">你的答案：</div>
              <div v-if="exercise.question_type !== 5 || !exercise.student_answer || !exercise.student_answer.startsWith('/uploads/')" class="answer-content">
                {{ formatDisplayAnswer(exercise.student_answer, exercise.question_type, exercise) }}
              </div>
              <div v-else class="image-answer">
                <img :src="exercise.student_answer" class="answer-image" @click="handlePreview(exercise.student_answer)" />
              </div>
            </div>
            
            <!-- 正确答案 -->
            <div class="answer-section correct-answer">
              <div class="section-title">正确答案：</div>
              <div class="answer-content">
                {{ formatCorrectAnswer(exercise.answer, exercise.question_type, exercise) }}
              </div>
            </div>
            
            <!-- 知识点 -->
            <div class="answer-section knowledge-points" v-if="exercise.knowledge_points">
              <div class="section-title">知识点：</div>
              <div class="answer-content">
                {{ exercise.knowledge_points }}
              </div>
            </div>
            
            <!-- 解析 -->
            <div class="answer-section explanation" v-if="exercise.explanation">
              <div class="section-title">解析：</div>
              <div class="answer-content" v-html="exercise.explanation"></div>
            </div>
            
            <!-- 评价 - 仅针对简答题和计算题 -->
            <div class="answer-section evaluation" v-if="exercise.evaluation && (exercise.question_type === 4 || exercise.question_type === 5)">
              <div class="section-title">评价：</div>
              <div class="answer-content">{{ exercise.evaluation }}</div>
            </div>
            
            <!-- 用时 -->
            <div class="answer-section time-spent" v-if="exercise.time_spent">
              <div class="section-title">答题用时：</div>
              <div class="answer-content">{{ formatTimeSpent(exercise.time_spent) }}</div>
            </div>
          </a-card>
        </div>
      </div>

      <!-- 右侧固定导航 -->
      <div class="question-navigator" v-if="exercises.length > 0">
        <div class="navigator-title">题目导航</div>
        <div class="navigator-items">
          <a 
            v-for="(exercise, index) in exercises" 
            :key="index"
            class="navigator-item"
            :class="{ 'correct': exercise.is_correct, 'wrong': !exercise.is_correct }"
            :href="`#question-${index + 1}`"
          >
            {{ index + 1 }}
          </a>
        </div>
        <a href="#summary" class="navigator-summary">回到顶部</a>
      </div>
    </div>

    <!-- 预览图片对话框 -->
    <a-modal
      v-model:visible="previewVisible"
      title="图片预览"
      :footer="null"
      :width="800"
    >
      <img
        v-if="previewImage"
        :src="previewImage"
        style="width: 100%"
        alt="预览图片"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import dayjs from 'dayjs';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
});

// 添加请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (!token) {
      message.error('未登录或登录已过期，请重新登录');
      router.push('/login');
      return Promise.reject('未登录或登录已过期');
    }
    config.headers['Authorization'] = `Bearer ${token}`;
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      message.error('登录已过期，请重新登录');
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
      router.push('/login');
    } else {
      message.error('请求失败：' + (error.response?.data?.message || error.message));
    }
    return Promise.reject(error);
  }
);

// 定义接口
interface Exercise {
  exercise_id: number;
  publish_id: number;
  content: string;
  question_type: number;
  options?: Array<{key: string, value: string}>;
  difficulty: number;
  title: string;
  student_answer?: string;
  is_correct?: boolean;
  score?: number;
  evaluation?: string;
  time_spent?: number;
  knowledge_points?: string;
  explanation?: string;
  answer?: string;
}

// 路由和状态
const router = useRouter();
const route = useRoute();
const classId = ref<string | null>(route.params.classId as string || null);
const chapterId = ref<string | null>(route.params.chapterId as string || null);
const previewTitle = ref<string>(route.query.title as string || '预习题查看');
const submissionTime = ref<string | null>(null);
const score = ref<number>(0);

// 加载和处理状态
const loading = ref<boolean>(false);
const exercises = ref<Exercise[]>([]);

// 图片预览状态
const previewVisible = ref<boolean>(false);
const previewImage = ref<string | null>(null);

// 计算属性
const correctCount = computed(() => {
  return exercises.value.filter(exercise => exercise.is_correct).length;
});

const totalScore = computed(() => {
  if (!exercises.value || exercises.value.length === 0) return 0;
  
  return exercises.value.reduce((total, exercise) => {
    const score = parseFloat(exercise.score) || 0;
    return total + score;
  }, 0);
});

const calculateScore = () => {
  if (!exercises.value || !exercises.value.length) return 0;
  
  const totalQuestions = exercises.value.length;
  if (totalQuestions === 0) return 0;
  
  // 计算总分和满分
  let totalMaxScore = 0;
  exercises.value.forEach(q => {
    totalMaxScore += (q.question_type === 4 || q.question_type === 5) ? 10 : 5;
  });
  
  // 如果满分为0，返回0分
  if (totalMaxScore === 0) return 0;
  
  const score = (totalScore.value / totalMaxScore) * 100;
  return isNaN(score) ? 0 : Math.round(score);
};

const scoreRate = computed(() => {
  return calculateScore();
});

// 获取题目类型名称
const getQuestionTypeName = (type: number) => {
  const types = {
    1: '选择题',
    2: '填空题',
    3: '判断题',
    4: '简答题',
    5: '计算题'
  };
  return types[type] || '未知类型';
};

// 获取难度名称
const getDifficultyName = (difficulty: number) => {
  const difficulties = {
    1: '简单',
    2: '中等',
    3: '困难',
    4: '复杂',
    5: '挑战'
  };
  return difficulties[difficulty] || '未知难度';
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '未知';
  return dayjs(date).format('YYYY-MM-DD HH:mm');
};

// 格式化答题用时
const formatTimeSpent = (seconds: number) => {
  if (!seconds) return '未记录';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  let result = '';
  if (hours > 0) {
    result += `${hours}小时`;
  }
  if (minutes > 0 || hours > 0) {
    result += `${minutes}分`;
  }
  result += `${remainingSeconds}秒`;
  
  return result;
};

// 格式化学生答案显示
const formatDisplayAnswer = (answer: string | undefined, questionType: number, exercise: Exercise) => {
  if (!answer) return '未作答';
  
  if (questionType === 3) { // 判断题
    return answer.toUpperCase() === 'T' || 
           answer.toUpperCase() === 'TRUE' || 
           answer === '1' || 
           answer === '正确' || 
           answer === '对' ? '正确' : '错误';
  } else if (questionType === 1) { // 选择题
    if (exercise && exercise.options) {
      const option = exercise.options.find(opt => opt.key === answer);
      return option ? `${option.key}: ${option.value}` : answer;
    }
  }
  
  return answer;
};

// 格式化正确答案显示
const formatCorrectAnswer = (answer: string | undefined, questionType: number, exercise: Exercise) => {
  if (!answer) return '未提供';
  
  if (questionType === 3) { // 判断题
    return answer.toUpperCase() === 'T' || 
           answer.toUpperCase() === 'TRUE' || 
           answer === '1' || 
           answer === '正确' || 
           answer === '对' ? '正确' : '错误';
  } else if (questionType === 1) { // 选择题
    if (exercise && exercise.options) {
      const option = exercise.options.find(opt => opt.key === answer);
      return option ? `${option.key}: ${option.value}` : answer;
    }
  }
  
  return answer;
};

// 格式化分数
const formatScore = (score) => {
  if (isNaN(score) || score === null || score === undefined) return '0';
  return score.toString();
};

// 返回上一页
const goBack = () => {
  router.push('/student-dashboard');
};

// 获取提交记录
const fetchSubmissionRecords = async () => {
  if (!classId.value || !chapterId.value) {
    message.error('缺少必要参数');
    return;
  }
  
  try {
    loading.value = true;
    
    // 获取当前登录的学生ID
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const studentId = userInfo.student_id || userInfo.id;
    
    if (!studentId) {
      message.error('无法获取学生信息');
      return;
    }
    
    const response = await api.get(`/api/preview-look/records/${classId.value}/${chapterId.value}?studentId=${studentId}`);
    
    if (response.data.success) {
      exercises.value = response.data.data.exercises;
      submissionTime.value = response.data.data.submission_time;
      score.value = response.data.data.total_score || 0;
    } else {
      message.error('获取提交记录失败：' + response.data.message);
    }
  } catch (error) {
    console.error('获取提交记录错误:', error);
    message.error('获取提交记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理图片预览
const handlePreview = (imageSrc: string) => {
  previewImage.value = imageSrc;
  previewVisible.value = true;
};

// 生命周期钩子
onMounted(() => {
  fetchSubmissionRecords();
});
</script>

<style lang="less" scoped>
.preview-look-container {
  background-color: #f0f2f5;
  min-height: 100vh;
  padding: 20px;
}

.main-content {
  display: flex;
}

.exercise-list {
  margin-top: 20px;
  flex: 1;
}

.summary-card {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.exercise-card {
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  
  .title-text {
    margin-left: 8px;
    font-weight: 600;
    font-size: 14px;
  }
}

.question-content {
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 40px;
  font-size: 14px;
}

.answer-section {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  
  .section-title {
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 14px;
  }
  
  .answer-content {
    white-space: pre-line;
    font-size: 14px;
  }
  
  &.student-answer {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    
    .section-title {
      color: #1890ff;
    }
  }
  
  &.correct-answer {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    
    .section-title {
      color: #52c41a;
    }
  }
  
  &.knowledge-points {
    background-color: #fff7e6;
    border: 1px solid #ffd591;
    
    .section-title {
      color: #fa8c16;
    }
  }
  
  &.explanation {
    background-color: #f9f0ff;
    border: 1px solid #d3adf7;
    
    .section-title {
      color: #722ed1;
    }
  }
  
  &.evaluation {
    background-color: #fcf4f2;
    border: 1px solid #f5c5bd;
    
    .section-title {
      color: #f5222d;
    }
  }
  
  &.time-spent {
    background-color: #f0f5ff;
    border: 1px solid #adc6ff;
    
    .section-title {
      color: #2f54eb;
    }
  }
  
  .image-answer {
    max-width: 100%;
    
    .answer-image {
      max-width: 100%;
      max-height: 300px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .preview-look-container {
    padding: 10px;
  }
  
  .card-title {
    flex-direction: column;
    align-items: flex-start;
    
    div:last-child {
      margin-top: 8px;
    }
  }
}

.question-navigator {
  width: 60px;
  margin-left: 20px;
  padding: 10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: fixed;
  right: 20px;
  top: 20px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;

  .navigator-title {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 14px;
    text-align: center;
  }

  .navigator-items {
    margin-bottom: 10px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 5px;
  }

  .navigator-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    padding: 0;
    text-align: center;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s;
    border-radius: 4px;
    font-size: 12px;

    &.correct {
      background-color: #e6f7ff;
    }

    &.wrong {
      background-color: #fff1f0;
    }

    &:hover {
      background-color: #f0f5ff;
    }
  }

  .navigator-summary {
    display: block;
    padding: 5px;
    text-align: center;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s;
    font-size: 12px;
    border-radius: 4px;

    &:hover {
      background-color: #f0f5ff;
    }
  }
}
</style>

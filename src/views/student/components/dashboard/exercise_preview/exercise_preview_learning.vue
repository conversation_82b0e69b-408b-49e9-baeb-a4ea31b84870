<template>
  <div class="preview-learning-container">
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240); background: white;"
      :title="previewTitle"
      @back="goBack"
    >
      <template #extra>
        <a-space>
          <a-tag v-if="deadline" :color="isDeadlineExpired ? 'red' : 'orange'">
            截止时间: {{ formatDate(deadline) }}
            <span v-if="isDeadlineExpired">(已截止)</span>
          </a-tag>
        </a-space>
      </template>
    </a-page-header>

    <div class="exercise-container" v-loading="loading">
      <!-- 进度显示 -->
      <div class="progress-container">
        <a-progress 
          :percent="progressPercentage" 
          size="small" 
          :status="progressPercentage === 100 ? 'success' : 'active'" 
          :format="() => `${currentIndex + 1}/${exercises.length}`"
        />
      </div>

      <!-- 没有题目时显示空状态 -->
      <a-empty v-if="!loading && exercises.length === 0" description="该章节暂无预习题" />

      <!-- 当前题目 -->
      <a-card v-if="!loading && exercises.length > 0 && currentExercise" class="exercise-card">
        <!-- 题目标题 -->
        <template #title>
          <div class="card-title">
            <div>
              <a-tag color="blue">{{ getQuestionTypeName(currentExercise.question_type) }}</a-tag>
              <a-tag color="orange">{{ getDifficultyName(currentExercise.difficulty) }}</a-tag>
              <span class="title-text">第{{ currentIndex + 1 }}题</span>
            </div>
            <div class="question-timer">
              <a-statistic
                title="做题计时"
                :value="formatTimer(timer)"
                :valueStyle="{ color: '#1890ff' }"
              />
            </div>
          </div>
        </template>
          
        <!-- 题目内容 -->
        <div class="question-content" v-html="currentExercise.content"></div>
          
        <!-- 答题区域 -->
        <div class="answer-area" v-if="!showAnswer">
          <!-- 选择题 -->
          <template v-if="currentExercise.question_type === 1">
            <a-radio-group v-model:value="currentAnswer" class="option-list">
              <a-radio 
                v-for="option in currentExercise.options" 
                :key="option.key" 
                :value="option.key"
                class="option-item"
              >
                {{ option.key }}: {{ option.value }}
              </a-radio>
            </a-radio-group>
          </template>
            
          <!-- 填空题 -->
          <template v-else-if="currentExercise.question_type === 2">
            <a-input 
              v-model:value="currentAnswer" 
              placeholder="请输入答案" 
              allow-clear
            />
          </template>
            
          <!-- 判断题 -->
          <template v-else-if="currentExercise.question_type === 3">
            <a-radio-group v-model:value="currentAnswer" class="option-list">
              <a-radio 
                v-for="option in currentExercise.options" 
                :key="option.key" 
                :value="option.key"
                class="option-item"
              >
                {{ option.value }}
              </a-radio>
            </a-radio-group>
          </template>
            
          <!-- 简答题 -->
          <template v-else-if="currentExercise.question_type === 4">
            <a-textarea 
              v-model:value="currentAnswer" 
              placeholder="请输入答案" 
              :rows="6"
            />
          </template>
            
          <!-- 计算题 -->
          <template v-else-if="currentExercise.question_type === 5">
            <div class="calculation-answer">
              <a-textarea 
                v-model:value="currentAnswer" 
                placeholder="请输入计算过程和答案" 
                :rows="4"
              />
              <div class="upload-area">
                <a-upload
                  v-model:file-list="uploadFiles"
                  :customRequest="handleImageUpload"
                  :show-upload-list="true"
                  list-type="picture"
                  :before-upload="beforeUpload"
                  :max-count="1"
                >
                  <a-button type="primary" :loading="uploading">
                    <template #icon><upload-outlined /></template>
                    上传计算过程图片
                  </a-button>
                  <template #itemRender="{ file }">
                    <div class="upload-item">
                      <img 
                        v-if="file.status === 'done' && file.url" 
                        :src="file.url" 
                        class="upload-preview"
                      />
                      <div class="upload-info">
                        <span>{{ file.name }}</span>
                        <a-progress v-if="file.status === 'uploading'" :percent="file.percent || 0" size="small" />
                        <div v-if="file.status === 'done'" class="upload-actions">
                          <a-button type="link" size="small" @click="handlePreview(file)">预览</a-button>
                          <a-button type="link" danger size="small" @click="handleRemove()">删除</a-button>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-upload>
              </div>
            </div>
          </template>
        </div>

        <!-- 答案和批改结果区域 -->
        <div class="result-area" v-if="showAnswer">
          <a-result
            :status="currentExercise.is_correct ? 'success' : 'error'"
            :title="currentExercise.is_correct ? '回答正确' : '回答错误'"
          >
            <template #extra>
              <div class="answer-details">
                <div class="your-answer">
                  <h3>你的答案:</h3>
                  <p v-if="currentExercise.question_type !== 5 || !currentExercise.student_answer || !currentExercise.student_answer.startsWith('/uploads/')">
                    {{ formatDisplayAnswer(currentExercise.student_answer, currentExercise.question_type) }}
                  </p>
                  <div v-else class="image-answer">
                    <img :src="currentExercise.student_answer" class="answer-image" />
                  </div>
                </div>
                
                <div class="correct-answer">
                  <h3>正确答案:</h3>
                  <p>{{ getCorrectAnswer(currentExercise) }}</p>
                </div>

                <div class="evaluation" v-if="currentExercise.evaluation">
                  <h3>评价:</h3>
                  <p>{{ currentExercise.evaluation }}</p>
                </div>

                <div class="score">
                  <h3>得分:</h3>
                  <p>{{ currentExercise.score || 0 }}分</p>
                </div>
              </div>
            </template>
          </a-result>
        </div>
          
        <!-- 操作按钮区域 -->
        <div class="card-actions">
          <template v-if="!showAnswer">
            <!-- 未提交状态的按钮 -->
            <a-button 
              type="primary" 
              size="large" 
              @click="submitAnswer"
              :loading="submitting"
              :disabled="!isAnswerValid || isDeadlineExpired"
            >
              提交答案
            </a-button>
          </template>
          <template v-else>
            <!-- 已提交状态的按钮 -->
            <a-button 
              type="primary" 
              size="large" 
              @click="nextQuestion"
              v-if="currentIndex < exercises.length - 1"
            >
              下一题
            </a-button>
            <a-button 
              type="success" 
              size="large" 
              @click="finishExercise"
              v-else
            >
              完成预习
            </a-button>
          </template>
        </div>
      </a-card>

      <!-- 完成总结 -->
      <a-card v-if="showSummary" class="summary-card">
        <template #title>
          <div class="card-title">
            <span class="title-text">预习题完成情况</span>
          </div>
        </template>
        
        <a-result
          status="success"
          title="预习题已完成"
          sub-title="感谢您完成预习题"
        >
          <template #extra>
            <div class="result-stats">
              <a-statistic
                title="题目总数"
                :value="exercises.length"
                :valueStyle="{ color: '#1890ff' }"
              />
              <a-statistic
                title="正确数量"
                :value="correctCount"
                :valueStyle="{ color: '#3f8600' }"
              />
              <a-statistic
                title="总得分"
                :value="totalScore"
                :precision="0"
                :valueStyle="{ color: '#cf1322' }"
              />
            </div>
            
            <a-button 
              type="primary" 
              size="large" 
              @click="goBack"
            >
              返回预习题列表
            </a-button>
          </template>
        </a-result>
      </a-card>
    </div>

    <!-- 预览图片对话框 -->
    <a-modal
      v-model:visible="previewVisible"
      title="图片预览"
      :footer="null"
      :width="800"
    >
      <img
        v-if="previewImage"
        :src="previewImage"
        style="width: 100%"
        alt="预览图片"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import dayjs from 'dayjs';
import { UploadOutlined } from '@ant-design/icons-vue';

// 创建axios实例
const api = axios.create({
  baseURL: ''
});

// 添加请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 定义接口
interface Exercise {
  publish_id: number;
  exercise_id: number;
  content: string;
  question_type: number;
  options?: Array<{key: string, value: string}>;
  difficulty: number;
  title: string;
  student_answer?: string;
  is_correct?: boolean;
  score?: number;
  evaluation?: string;
  time_spent?: number;
  answer?: string; // 正确答案，提交后才会有
}

// 路由和状态
const router = useRouter();
const route = useRoute();
const classId = ref<string | null>(route.params.classId as string || null);
const chapterId = ref<string | null>(route.params.chapterId as string || null);
const previewTitle = ref<string>(route.query.title as string || '预习题');
const release_time = ref<string | null>(null);
const deadline = ref<string | null>(null);

// 加载和处理状态
const loading = ref<boolean>(false);
const exercises = ref<Exercise[]>([]);
const currentIndex = ref<number>(0);
const currentAnswer = ref<string>('');
const uploadFiles = ref<any[]>([]);
const uploading = ref<boolean>(false);
const submitting = ref<boolean>(false);
const showAnswer = ref<boolean>(false);
const showSummary = ref<boolean>(false);

// 图片预览状态
const previewVisible = ref<boolean>(false);
const previewImage = ref<string | null>(null);

// 计时器
const timer = ref<number>(0); // 单位：秒
const timerInterval = ref<any>(null);

// 计算属性
const isDeadlineExpired = computed(() => {
  if (!deadline.value) return false;
  return dayjs().isAfter(dayjs(deadline.value));
});

const currentExercise = computed(() => {
  if (exercises.value.length === 0 || currentIndex.value >= exercises.value.length) {
    return null;
  }
  return exercises.value[currentIndex.value];
});

const isAnswerValid = computed(() => {
  if (!currentExercise.value) return false;
  
  // 验证答案不为空
  if (currentExercise.value.question_type === 5) {
    // 计算题需要上传图片或填写答案
    return uploadFiles.value.length > 0 || !!currentAnswer.value;
  }
  
  return !!currentAnswer.value;
});

const progressPercentage = computed(() => {
  if (exercises.value.length === 0) return 0;
  
  // 统计已提交的题目数量
  const completedCount = exercises.value.filter(exercise => 
    exercise.student_answer !== undefined && exercise.student_answer !== ''
  ).length;
  
  return Math.round((completedCount / exercises.value.length) * 100);
});

const correctCount = computed(() => {
  return exercises.value.filter(exercise => exercise.is_correct).length;
});

const totalScore = computed(() => {
  return exercises.value.reduce((total, exercise) => total + (exercise.score || 0), 0);
});

// 获取题目类型名称
const getQuestionTypeName = (type: number) => {
  const types = {
    1: '选择题',
    2: '填空题',
    3: '判断题',
    4: '简答题',
    5: '计算题'
  };
  return types[type] || '未知类型';
};

// 获取难度名称
const getDifficultyName = (difficulty: number) => {
  const difficulties = {
    1: '简单',
    2: '中等',
    3: '困难',
    4: '复杂',
    5: '挑战'
  };
  return difficulties[difficulty] || '未知难度';
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '未设置';
  return dayjs(date).format('YYYY-MM-DD HH:mm');
};

// 格式化计时器显示
const formatTimer = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 返回上一页
const goBack = () => {
  try {
    // 尝试返回上一页
    router.back();
  } catch (e) {
    // 如果失败，则跳转到预习题列表页面
    router.push('/student/exercise-preview');
  }
};

// 获取预习题列表
const fetchExercises = async () => {
  if (!classId.value || !chapterId.value) {
    message.error('缺少必要参数');
    return;
  }
  
  try {
    loading.value = true;
    
    const response = await api.get(`/api/preview-learning/exercises/${classId.value}/${chapterId.value}`);
    
    if (response.data.success) {
      exercises.value = response.data.data.exercises;
      release_time.value = response.data.data.release_time;
      deadline.value = response.data.data.deadline;
      
      // 查找第一个未完成的题目
      let firstIncompleteIndex = exercises.value.findIndex(exercise => 
        !exercise.student_answer || exercise.student_answer === ''
      );
      
      // 如果所有题目都已完成，但用户重新进入，显示第一题
      if (firstIncompleteIndex === -1) {
        if (exercises.value.every(ex => ex.student_answer && ex.is_correct !== undefined)) {
          // 所有题目都已提交且有批改结果，显示总结
          showSummary.value = true;
        } else {
          firstIncompleteIndex = 0;
        }
      }
      
      // 设置当前索引
      currentIndex.value = firstIncompleteIndex === -1 ? 0 : firstIncompleteIndex;
      
      // 初始化当前答案
      if (currentExercise.value) {
        // 如果当前题目已有答案，显示答案
        if (currentExercise.value.student_answer && currentExercise.value.is_correct !== undefined) {
          showAnswer.value = true;
        } else {
          // 否则初始化为空答案
          currentAnswer.value = '';
          if (currentExercise.value.question_type === 5 && 
              currentExercise.value.student_answer && 
              currentExercise.value.student_answer.startsWith('/uploads/')) {
            uploadFiles.value = [{
              uid: '-1',
              name: '已上传的图片',
              status: 'done',
              url: currentExercise.value.student_answer
            }];
            currentAnswer.value = currentExercise.value.student_answer;
          }
          // 启动计时器
          startTimer();
        }
      }
    } else {
      message.error('获取预习题失败：' + response.data.message);
    }
  } catch (error) {
    console.error('获取预习题错误:', error);
    message.error('获取预习题失败');
  } finally {
    loading.value = false;
  }
};

// 启动计时器
const startTimer = () => {
  // 清除任何现有的计时器
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
  }
  
  // 重置计时器
  timer.value = 0;
  
  // 设置新的计时器
  timerInterval.value = setInterval(() => {
    timer.value++;
  }, 1000);
};

// 停止计时器
const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
};

// 图片上传前检查
const beforeUpload = (file: any) => {
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('只能上传图片文件');
    return false;
  }
  
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('图片大小不能超过5MB');
    return false;
  }
  
  return true;
};

// 处理图片上传
const handleImageUpload = async (options: any) => {
  try {
    uploading.value = true;
    const { file, onSuccess, onError, onProgress } = options;
    
    const formData = new FormData();
    formData.append('image', file);
    
    const response = await api.post('/api/preview-learning/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent: any) => {
        const percentComplete = progressEvent.total ? 
          Math.round((progressEvent.loaded / progressEvent.total) * 100) : 0;
        onProgress({ percent: percentComplete });
      }
    });
    
    if (response.data.success) {
      // 更新上传文件状态
      const url = response.data.data.url;
      
      // 将图片URL设置为答案
      currentAnswer.value = url;
      
      onSuccess(response.data, file);
      message.success('图片上传成功');
    } else {
      onError(response.data.message);
      message.error('图片上传失败：' + response.data.message);
    }
  } catch (error) {
    console.error('上传图片错误:', error);
    message.error('图片上传失败');
    options.onError(error);
  } finally {
    uploading.value = false;
  }
};

// 处理图片预览
const handlePreview = (file: any) => {
  previewImage.value = file.url || file.thumbUrl;
  previewVisible.value = true;
};

// 处理图片删除
const handleRemove = () => {
  uploadFiles.value = [];
  currentAnswer.value = '';
};

// 提交答案
const submitAnswer = async () => {
  if (!currentExercise.value) return;
  
  if (!isAnswerValid.value) {
    message.warning('请填写答案');
    return;
  }
  
  try {
    submitting.value = true;
    
    // 停止计时器
    stopTimer();
    
    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    
    console.log('提交答案 - 用户信息:', userInfo);
    
    // 提交答案 - 使用student_id而不是id
    const response = await api.post(`/api/preview-learning/submit-one/${classId.value}/${chapterId.value}`, {
      exercise: {
        publish_id: currentExercise.value.publish_id,
        student_answer: currentAnswer.value,
        time_spent: timer.value
      },
      studentId: userInfo.student_id || userInfo.id  // 优先使用student_id
    });
    
    if (response.data.success) {
      message.success('提交成功');
      
      // 更新当前习题的状态
      const result = response.data.data;
      exercises.value[currentIndex.value] = {
        ...exercises.value[currentIndex.value],
        student_answer: currentAnswer.value,
        is_correct: result.is_correct,
        score: result.score,
        evaluation: result.evaluation,
        time_spent: timer.value,
        answer: result.correct_answer
      };
      
      // 显示答案
      showAnswer.value = true;
    } else {
      message.error('提交失败：' + response.data.message);
    }
  } catch (error) {
    console.error('提交答案错误:', error);
    message.error('提交答案失败');
  } finally {
    submitting.value = false;
  }
};

// 下一题
const nextQuestion = () => {
  if (currentIndex.value < exercises.value.length - 1) {
    currentIndex.value++;
    currentAnswer.value = '';
    uploadFiles.value = [];
    showAnswer.value = false;
    
    // 检查是否已提交
    if (exercises.value[currentIndex.value].student_answer && 
        exercises.value[currentIndex.value].is_correct !== undefined) {
      showAnswer.value = true;
    } else {
      // 如果是计算题且已有图片答案
      if (exercises.value[currentIndex.value].question_type === 5 && 
          exercises.value[currentIndex.value].student_answer && 
          exercises.value[currentIndex.value].student_answer.startsWith('/uploads/')) {
        uploadFiles.value = [{
          uid: '-1',
          name: '已上传的图片',
          status: 'done',
          url: exercises.value[currentIndex.value].student_answer
        }];
        currentAnswer.value = exercises.value[currentIndex.value].student_answer;
      }
      // 启动计时器
      startTimer();
    }
  }
};

// 获取正确答案
const getCorrectAnswer = (exercise: Exercise) => {
  if (!exercise || !exercise.answer) return '未知';
  
  // 根据题型格式化显示
  switch (exercise.question_type) {
    case 1: // 选择题
      const option = exercise.options?.find(opt => opt.key === exercise.answer);
      return option ? `${option.key}: ${option.value}` : exercise.answer;
    case 3: // 判断题
      return exercise.answer.toUpperCase() === 'T' || 
             exercise.answer.toUpperCase() === 'TRUE' || 
             exercise.answer === '1' || 
             exercise.answer === '正确' || 
             exercise.answer === '对' ? '正确' : '错误';
    default:
      return exercise.answer;
  }
};

// 格式化显示的答案
const formatDisplayAnswer = (answer: string | undefined, questionType: number) => {
  if (!answer) return '未作答';
  
  if (questionType === 3) { // 判断题
    return answer.toUpperCase() === 'T' || 
           answer.toUpperCase() === 'TRUE' || 
           answer === '1' || 
           answer === '正确' || 
           answer === '对' ? '正确' : '错误';
  } else if (questionType === 1) { // 选择题
    const currentExerciseValue = currentExercise.value;
    if (currentExerciseValue) {
      const option = currentExerciseValue.options?.find(opt => opt.key === answer);
      return option ? `${option.key}: ${option.value}` : answer;
    }
  }
  
  return answer;
};

// 完成预习并返回列表
const finishExercise = () => {
  showSummary.value = true;
};

// 生命周期钩子
onMounted(() => {
  fetchExercises();
});

onBeforeUnmount(() => {
  // 清除计时器
  stopTimer();
});
</script>

<style lang="less" scoped>
.preview-learning-container {
  background-color: #f0f2f5;
  min-height: 100vh;
  padding: 20px;
}

.exercise-container {
  margin-top: 20px;
}

.progress-container {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.exercise-card, .summary-card {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .title-text {
    margin-left: 8px;
    font-weight: 600;
  }
  
  .question-timer {
    color: #1890ff;
    font-weight: bold;
  }
}

.question-content {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 60px;
  font-size: 16px;
}

.answer-area {
  margin-bottom: 16px;
}

.option-list {
  display: flex;
  flex-direction: column;
  
  .option-item {
    margin-bottom: 8px;
    padding: 8px;
    width: 100%;
    
    &:hover {
      background-color: #f0f8ff;
    }
  }
}

.calculation-answer {
  .upload-area {
    margin-top: 16px;
  }
  
  .upload-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .upload-preview {
      width: 80px;
      height: 80px;
      object-fit: cover;
      margin-right: 8px;
      border-radius: 4px;
    }
    
    .upload-info {
      flex: 1;
      
      .upload-actions {
        margin-top: 8px;
        display: flex;
        gap: 8px;
      }
    }
  }
}

.result-area {
  margin-bottom: 20px;
  
  .answer-details {
    text-align: left;
    background-color: #f9f9f9;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    
    h3 {
      color: #1890ff;
      margin-bottom: 8px;
    }
    
    .your-answer, .correct-answer, .evaluation, .score {
      margin-bottom: 16px;
    }
    
    .image-answer {
      max-width: 100%;
      
      .answer-image {
        max-width: 100%;
        max-height: 300px;
        border-radius: 4px;
      }
    }
  }
}

.card-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 16px;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 24px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .preview-learning-container {
    padding: 10px;
  }
  
  .card-title {
    flex-direction: column;
    align-items: flex-start;
    
    .question-timer {
      margin-top: 8px;
    }
  }
  
  .result-stats {
    flex-direction: column;
    gap: 16px;
  }
}
</style>

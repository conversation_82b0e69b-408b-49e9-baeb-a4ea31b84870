<template>
  <div class="preview-learning">
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240)"
      :title="preview?.title || '预习资料详情'"
      @back="goBack"
    >
      <template #extra>
        <a-button type="primary" @click="openSubmitForm" v-if="!isExpired && !preview?.completed" style="color: white">
          提交预习
        </a-button>
        <a-tag v-if="isExpired" color="red">已截止</a-tag>
        <a-tag v-if="preview?.completed" color="success">已完成</a-tag>
      </template>
      
      <a-descriptions size="small" :column="3" bordered>
        <a-descriptions-item label="章节">{{ preview?.chapter_title }}</a-descriptions-item>
        <a-descriptions-item label="班级">{{ preview?.class_name }}</a-descriptions-item>
        <a-descriptions-item label="发布时间">{{ preview?.publish_time }}</a-descriptions-item>
        <a-descriptions-item label="截止时间" :contentStyle="isExpired ? {color: 'red'} : {}">
          {{ preview?.deadline }}
        </a-descriptions-item>
        <a-descriptions-item label="当前学习时长">{{ formatTime(studyDuration) }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          {{ preview?.completed ? '已完成' : '未完成' }}
          <span v-if="preview?.completed">
            (提交时间: {{ preview?.submitTime }})
          </span>
        </a-descriptions-item>
        <a-descriptions-item label="总观看次数">{{ preview?.totalViewCount || 0 }}次</a-descriptions-item>
        <a-descriptions-item label="历史总时长" v-if="preview?.totalViewDuration">
          {{ formatTime(preview?.totalViewDuration) }}
        </a-descriptions-item>
      </a-descriptions>
    </a-page-header>
    
    <div class="preview-content-container">
      <div class="preview-content" v-if="preview?.content" v-html="formattedContent"></div>
      <a-empty v-else description="暂无预习内容" />
    </div>
    
    <!-- 提交预习表单对话框 -->
    <a-modal
      v-model:visible="submitFormVisible"
      title="提交预习"
      :maskClosable="false"
      @ok="submitPreview"
      :confirmLoading="submitting"
      width="700px"
    >
      <a-form :model="submitForm" layout="vertical">
        <a-form-item 
          label="请输入预习内容" 
          name="content"
          :rules="[{ required: true, message: '请输入预习内容' }]"
        >
          <a-textarea 
            v-model:value="submitForm.content" 
            :rows="8"
            placeholder="在此输入您的预习心得和理解..."
          />
        </a-form-item>
        <p class="text-gray">累计学习 {{ formatTime(studyDuration) }}，观看次数 {{ preview?.totalViewCount || 0 }}次</p>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import axios from 'axios'
import { marked } from 'marked'

// 导入axios实例或配置axios默认设置
const axiosInstance = axios.create({
  baseURL: 'http://localhost:3000',
  headers: {
    'Content-Type': 'application/json'
  }
})

// 添加请求拦截器
axiosInstance.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

interface PreviewDetail {
  id: string | number;
  title: string;
  chapter_title: string;
  class_name: string;
  content: string;
  publish_time: string;
  deadline: string;
  completed: boolean;
  submitTime: string | null;
  answer: string | null;
  studyDuration?: number; // 添加学习时长字段
  totalViewCount?: number;
  totalViewDuration?: number;
}

const route = useRoute()
const router = useRouter()
const preview = ref<PreviewDetail | null>(null)
const loading = ref(false)
const submitFormVisible = ref(false)
const submitting = ref(false)
const startTime = ref(Date.now())
const studyDuration = ref(0) // 学习时长，单位：秒
const timerInterval = ref<number | null>(null)
const submitForm = ref({
  content: ''
})
const isActive = ref(true) // 页面是否活跃（鼠标在页面内）
const lastActivityTime = ref(Date.now()) // 最后活动时间
const inactivityTimeout = ref<number | null>(null) // 不活跃超时

// 计算属性
const isExpired = computed(() => {
  if (!preview.value || !preview.value.deadline) return false
  const deadline = new Date(preview.value.deadline)
  return new Date() > deadline
})

// 获取预习详情，包括历史学习时长
const fetchPreviewDetail = async () => {
  const previewId = route.params.previewId
  if (!previewId) {
    message.error('预习ID不存在')
    router.push('/student-dashboard')
    return
  }
  
  try {
    loading.value = true
    const response = await axiosInstance.get(`/api/student/previews/${previewId}`)
    if (response.data.success) {
      preview.value = response.data.data
      submitForm.value.content = preview.value.answer || ''
      
      // 获取历史学习时长
      await fetchStudyDuration(previewId as string)
    } else {
      message.error('获取预习详情失败')
      router.push('/student-dashboard')
    }
  } catch (error) {
    console.error('获取预习详情失败:', error)
    message.error('获取预习详情失败')
    router.push('/student-dashboard')
  } finally {
    loading.value = false
  }
}

// 获取历史学习时长
const fetchStudyDuration = async (previewId: string) => {
  try {
    const response = await axiosInstance.get(`/api/student/previews/${previewId}/duration`)
    if (response.data.success && response.data.data) {
      // 设置累计时长
      studyDuration.value = 0 // 每次会话重置为0，不从累计时长开始
      console.log(`获取到历史累计时长: ${response.data.data.duration}秒`)
      
      // 如果未完成，设置预览对象的总观看时长
      if (preview.value && !preview.value.completed) {
        preview.value.totalViewDuration = response.data.data.duration || 0
      }
    }
  } catch (error) {
    console.error('获取学习时长失败:', error)
    // 不影响用户体验，忽略错误
  }
}

// 记录预习查看记录，同时记录本次时长
const recordPreviewView = async (previewId: string, viewDuration: number = 0) => {
  try {
    // 只有时长大于0才记录
    if (viewDuration <= 0) {
      console.log('观看时长为0，不记录查看')
      return
    }
    
    console.log('准备记录预习查看, 整个会话时长:', viewDuration);
    const response = await axiosInstance.post(`/api/student/previews/${previewId}/view`, {
      viewDuration
    });
    
    console.log('已记录预习查看, 本次时长:', viewDuration);
    
    if (response.data.success && response.data.data) {
      // 更新预览对象的总观看次数和总时长
      if (preview.value) {
        preview.value.totalViewCount = response.data.data.totalViewCount
        preview.value.totalViewDuration = response.data.data.totalViewDuration
      }
    }
  } catch (error) {
    console.error('记录预习查看失败:', error);
  }
}

// 记录会话结束时的预习查看
const recordViewOnLeave = async () => {
  try {
    const previewId = route.params.previewId as string;
    if (!previewId) return;
    
    // 只有观看了至少1秒才记录
    if (studyDuration.value > 0) {
      console.log('离开页面，记录整个会话的学习时长:', studyDuration.value);
      await recordPreviewView(previewId, studyDuration.value);
      
      // 重置学习时长
      studyDuration.value = 0;
    }
  } catch (error) {
    console.error('记录查看时长失败:', error);
  }
}

// 返回上一页
const goBack = () => {
  // 只要有学习时长，就记录查看
  if (studyDuration.value > 0) {
    // 记录本次查看时长，然后返回
    recordViewOnLeave().then(() => {
      router.push('/student-dashboard');
    });
  } else {
    // 时间为0，直接返回
    router.push('/student-dashboard');
  }
}

// 打开提交表单
const openSubmitForm = () => {
  submitFormVisible.value = true
}

// 提交预习
const submitPreview = async () => {
  if (!submitForm.value.content.trim()) {
    message.warning('请输入预习内容')
    return
  }
  
  try {
    submitting.value = true
    const previewId = route.params.previewId
    
    // 有学习时长则先记录本次查看
    if (studyDuration.value > 0) {
      await recordPreviewView(previewId as string, studyDuration.value);
    }
    
    const response = await axiosInstance.post(`/api/student/previews/${previewId}/submit`, {
      content: submitForm.value.content
    })
    
    if (response.data.success) {
      message.success('预习提交成功')
      submitFormVisible.value = false
      
      // 提交成功后完全停止计时
      stopTimer(true)
      
      // 重新获取预习详情，更新状态
      await fetchPreviewDetail()
    }
  } catch (error: any) {
    console.error('提交预习失败:', error)
    message.error(error.response?.data?.message || '提交预习失败')
  } finally {
    submitting.value = false
  }
}

// 格式化时间
const formatTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  
  let result = ''
  if (hours > 0) {
    result += `${hours}小时`
  }
  if (minutes > 0 || hours > 0) {
    result += `${minutes}分钟`
  }
  result += `${remainingSeconds}秒`
  
  return result
}

// 启动计时器，仅当用户处于活跃状态且未完成时进行计时
const startTimer = () => {
  // 如果已完成预习或计时器已启动，则不处理
  if (preview.value?.completed || timerInterval.value) return
  
  startTime.value = Date.now()
  timerInterval.value = window.setInterval(() => {
    // 只有当页面活跃时才累加时间
    if (isActive.value) {
      studyDuration.value += 1
    }
  }, 1000)
  
  console.log('计时器已启动')
}

// 停止计时器
const stopTimer = (permanent = false) => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
    
    console.log('计时器已停止')
  }
  
  // 如果是永久停止(提交后)，将不再重启
  if (permanent) {
    console.log('计时已永久停止')
  }
}

// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (document.hidden) {
    // 页面不可见，暂停计时
    isActive.value = false
    stopTimer()
  } else if (!preview.value?.completed) {
    // 页面可见且未完成，继续计时
    isActive.value = true
    startTimer()
  }
}

// 鼠标进入页面
const handleMouseEnter = () => {
  isActive.value = true
  lastActivityTime.value = Date.now()
  if (!preview.value?.completed) {
    startTimer()
  }
}

// 鼠标离开页面
const handleMouseLeave = () => {
  isActive.value = false
  // 不立即停止计时，设置一个短暂的超时
  if (inactivityTimeout.value) {
    clearTimeout(inactivityTimeout.value)
  }
  
  inactivityTimeout.value = window.setTimeout(() => {
    if (!isActive.value && !preview.value?.completed) {
      stopTimer()
    }
  }, 5000) // 5秒后如果依然不活跃，停止计时
}

// 鼠标移动检测
const handleMouseMove = () => {
  // 如果之前是不活跃状态，恢复活跃
  if (!isActive.value) {
    isActive.value = true
    if (!preview.value?.completed) {
      startTimer()
    }
  }
  lastActivityTime.value = Date.now()
}

// 捕获页面关闭/刷新事件
const handleBeforeUnload = (event) => {
  if (!preview.value?.completed && studyDuration.value > 0) {
    // 使用同步XHR记录查看
    const previewId = route.params.previewId as string;
    
    if (previewId) {
      try {
        console.log('页面关闭/刷新，记录学习时长:', studyDuration.value);
        
        const xhr = new XMLHttpRequest();
        xhr.open('POST', `/api/student/previews/${previewId}/view`, false);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('token')}`);
        xhr.send(JSON.stringify({ viewDuration: studyDuration.value }));
        
        // 重置学习时长，避免重复记录
        studyDuration.value = 0;
        
        // 提示用户等待数据保存
        event.preventDefault();
        event.returnValue = '正在保存学习进度，请稍候...';
      } catch (error) {
        console.error('同步记录学习时长失败:', error);
      }
    }
  }
}

// 格式化预习内容为Markdown
const formattedContent = computed(() => {
  if (!preview.value?.content) return '';
  
  try {
    // 将Markdown格式的内容转换为HTML
    return marked(preview.value.content, {
      breaks: true, // 将换行符转换为<br>
      gfm: true     // 启用GitHub风格的Markdown
    });
  } catch (error) {
    console.error('Markdown解析失败:', error);
    return preview.value.content; // 如果解析失败，返回原始内容
  }
});

// 生命周期钩子
onMounted(() => {
  // 获取上次访问时间
  const lastVisitTime = localStorage.getItem(`preview_last_visit_${route.params.previewId}`)
  const now = Date.now()
  const isNewVisit = !lastVisitTime || (now - parseInt(lastVisitTime)) > 60000 // 1分钟以上视为新访问
  
  // 记录本次访问时间
  localStorage.setItem(`preview_last_visit_${route.params.previewId}`, now.toString())
  
  fetchPreviewDetail()
  
  // 重置开始时间和学习时长
  startTime.value = Date.now()
  studyDuration.value = 0
  
  // 只有非完成状态才开始计时
  if (!preview.value?.completed) {
    // 延迟一下再开始计时，确保页面已加载完成
    setTimeout(() => startTimer(), 1000)
  }
  
  // 添加页面关闭事件监听
  window.addEventListener('beforeunload', handleBeforeUnload);
  
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // 监听鼠标事件
  document.addEventListener('mouseenter', handleMouseEnter)
  document.addEventListener('mouseleave', handleMouseLeave)
  document.addEventListener('mousemove', handleMouseMove)
})

onUnmounted(() => {
  // 移除页面关闭事件监听
  window.removeEventListener('beforeunload', handleBeforeUnload);
  
  // 如果未完成，记录最后的查看时长
  if (!preview.value?.completed && studyDuration.value > 0) {
    recordViewOnLeave();
  }
  
  // 清理定时器
  stopTimer()
  
  if (window.saveIntervalId) {
    clearInterval(window.saveIntervalId)
  }
  
  if (inactivityTimeout.value) {
    clearTimeout(inactivityTimeout.value)
  }
  
  // 移除事件监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  document.removeEventListener('mouseenter', handleMouseEnter)
  document.removeEventListener('mouseleave', handleMouseLeave)
  document.removeEventListener('mousemove', handleMouseMove)
})
</script>

<style lang="less">
.preview-learning {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
  
  .preview-content-container {
    margin-top: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
  
  .preview-content {
    padding: 16px;
    border-radius: 4px;
    background-color: #fff;
    line-height: 1.8;
    font-size: 15px;
    color: #303133;
    
    h1, h2, h3, h4, h5, h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
      color: #303133;
    }
    
    h1 {
      font-size: 2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    
    h2 {
      font-size: 1.5em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    
    h3 {
      font-size: 1.25em;
    }
    
    p {
      margin-top: 0;
      margin-bottom: 16px;
      line-height: 1.8;
    }
    
    ul, ol {
      padding-left: 2em;
      margin-top: 0;
      margin-bottom: 16px;
    }
    
    li {
      margin-bottom: 0.25em;
    }
    
    img {
      max-width: 100%;
      display: block;
      margin: 10px auto;
      border-radius: 4px;
    }
    
    pre, code {
      font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    }
    
    pre {
      padding: 16px;
      overflow: auto;
      font-size: 85%;
      line-height: 1.45;
      background-color: #f6f8fa;
      border-radius: 3px;
      margin-bottom: 16px;
    }
    
    code {
      padding: 0.2em 0.4em;
      margin: 0;
      font-size: 85%;
      background-color: rgba(27, 31, 35, 0.05);
      border-radius: 3px;
    }
    
    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 16px;
      display: block;
      overflow-x: auto;
    }
    
    table th, table td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
      text-align: left;
    }
    
    table th {
      background-color: #f5f7fa;
      font-weight: 600;
    }
    
    blockquote {
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
      margin: 0 0 16px 0;
    }

    /* 适配公式和特殊格式 */
    .katex {
      font-size: 1.1em;
    }

    hr {
      height: 0.25em;
      padding: 0;
      margin: 24px 0;
      background-color: #e1e4e8;
      border: 0;
    }
  }
}

.text-gray {
  color: #666;
  font-size: 14px;
}
</style>

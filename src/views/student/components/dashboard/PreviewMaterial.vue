@ -1,228 +0,0 @@
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import axios from 'axios'
import { useRouter } from 'vue-router'

// 导入axios实例或配置axios默认设置
const axiosInstance = axios.create({
  baseURL: 'http://localhost:3000',
  headers: {
    'Content-Type': 'application/json'
  }
})

// 设置路由
const router = useRouter()

// 添加请求拦截器
axiosInstance.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

interface Class {
  id: string | number;
  class_name: string;
  course_code: string;
  semester: string;
  course_name: string;
}

interface PreviewMaterial {
  id: string | number;
  title: string;
  chapter: string;
  class: string;
  courseName: string;
  publishTime: string;
  deadline: string;
  content: string;
  completed: boolean;
  submitTime: string | null;
}

// 状态变量
const loading = ref(false)
const previewMaterials = ref<PreviewMaterial[]>([])
const classes = ref<Class[]>([])
const selectedClassId = ref<string | number>('')

// 表格列定义
const columns = [
  {
    title: '预习标题',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: '章节',
    dataIndex: 'chapter',
    key: 'chapter'
  },
  {
    title: '班级',
    dataIndex: 'class',
    key: 'class'
  },
  {
    title: '发布时间',
    dataIndex: 'publishTime',
    key: 'publishTime'
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    key: 'deadline'
  },
  {
    title: '状态',
    dataIndex: 'completed',
    key: 'completed'
  },
  {
    title: '操作',
    key: 'action'
  }
]

// 获取学生所在的班级列表
const fetchClasses = async () => {
  try {
    loading.value = true
    const response = await axiosInstance.get('/api/student/classes')
    if (response.data.success) {
      classes.value = response.data.data
      // 如果有班级，默认选择第一个
      if (classes.value.length > 0) {
        selectedClassId.value = classes.value[0].id
        fetchPreviewMaterials()
      }
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    message.error('获取班级列表失败')
  } finally {
    loading.value = false
  }
}

// 获取预习资料列表
const fetchPreviewMaterials = async () => {
  try {
    loading.value = true
    const url = selectedClassId.value 
      ? `/api/student/previews?classId=${selectedClassId.value}` 
      : '/api/student/previews'
      
    const response = await axiosInstance.get(url)
    if (response.data.success) {
      previewMaterials.value = response.data.data
    }
  } catch (error) {
    console.error('获取预习资料失败:', error)
    message.error('获取预习资料失败')
  } finally {
    loading.value = false
  }
}

// 处理班级选择变化
const handleClassChange = () => {
  fetchPreviewMaterials()
}

// 查看预习详情
const handleViewPreview = (record: PreviewMaterial) => {
  router.push(`/student/preview/${record.id}`)
}

// 调试工具
const checkAuth = () => {
  const token = localStorage.getItem('token')
  const userInfo = localStorage.getItem('userInfo')
  console.log('认证信息检查:', { token: token ? '存在' : '不存在', userInfo: userInfo ? JSON.parse(userInfo) : '不存在' })
}

// 生命周期钩子
onMounted(() => {
  checkAuth() // 调试：检查认证信息
  fetchClasses()
})
</script>

<template>
  <div class="preview-material">
    <a-card title="预习资料" :loading="loading">
      <template #extra>
        <a-select 
          v-model:value="selectedClassId" 
          style="width: 200px" 
          placeholder="选择班级"
          @change="handleClassChange"
          :disabled="loading"
        >
          <a-select-option value="">全部班级</a-select-option>
          <a-select-option v-for="item in classes" :key="item.id" :value="item.id">
            {{ item.class_name }}
          </a-select-option>
        </a-select>
      </template>
      
      <a-table 
        :data-source="previewMaterials" 
        :columns="columns" 
        :loading="loading"
        :pagination="{ pageSize: 5 }"
        :row-key="record => record.id.toString()"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'completed'">
            <a-tag :color="record.completed ? 'success' : 'default'">
              {{ record.completed ? '已完成' : '未完成' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleViewPreview(record)">查看详情</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
      
      <a-empty v-if="previewMaterials.length === 0 && !loading" description="暂无预习资料" />
    </a-card>
  </div>
</template>

<style scoped lang="less">
.preview-material {
  .preview-content {
    background-color: #f5f7fa;
    padding: 16px;
    border-radius: 4px;
    margin-top: 8px;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .detail-header {
    margin-bottom: 24px;
  }
  
  .detail-content {
    h3 {
      margin: 16px 0 8px;
      font-weight: 500;
    }
  }
}
</style>

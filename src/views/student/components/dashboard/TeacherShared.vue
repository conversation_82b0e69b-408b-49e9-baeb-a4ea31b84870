<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import axiosInstance from '@/utils/axios';
import { useRouter } from 'vue-router';

const router = useRouter();
const loading = ref(false);
const sharedVideos = ref([]);
// 直接使用所有视频，不进行过滤
const filteredVideos = computed(() => {
  return sharedVideos.value;
});

// 获取教师分享的视频列表
const fetchSharedVideos = async () => {
  try {
    loading.value = true;
    const response = await axiosInstance.get('/api/student/shared-videos');

    if (response.data.success) {
      sharedVideos.value = response.data.data;
      console.log('获取到的分享视频:', sharedVideos.value);
    } else {
      message.error(response.data.message || '获取分享视频失败');
    }
  } catch (error) {
    console.error('获取分享视频失败:', error);
    message.error('获取分享视频失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 查看视频详情
const viewVideo = (video) => {
  // 使用替换而不是推入，避免在返回时出现问题
  router.push({
    path: `/student/shared-video/${video.id}`,
    // 添加查询参数以避免缓存问题
    query: {
      t: new Date().getTime(),
      section: '教师分享' // 保存当前模块信息，便于返回
    }
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

onMounted(() => {
  fetchSharedVideos();
});
</script>

<template>
  <div class="teacher-shared">
    <a-spin :spinning="loading">
      <div class="header-section">
        <h2 class="section-title">教师为您分享的学习视频</h2>
      </div>

      <div v-if="filteredVideos.length === 0" class="empty-state">
        <a-empty :description="loading ? '正在加载中...' : '暂无教师分享的视频'" />
      </div>

      <div v-else class="video-grid">
        <a-card
          v-for="video in filteredVideos"
          :key="video.id"
          class="video-card"

          :bordered="false"
          hoverable
          @click="viewVideo(video)"
        >
          <div class="video-thumbnail-wrapper">
            <img :src="video.coverUrl" :alt="video.title" class="video-thumbnail" />
            <div class="video-source">{{ video.source }}</div>
            <div v-if="video.isRequired" class="video-required">必看</div>

          </div>

          <div class="video-info">
            <h3 class="video-title">{{ video.title }}</h3>
            <div class="video-meta">
              <span class="video-class">{{ video.className }}</span>
              <span class="video-date">{{ formatDate(video.createTime) }}</span>
            </div>
            <div v-if="video.teacherRemark" class="video-remark">
              <span class="remark-label">教师备注:</span>
              <span class="remark-content">{{ video.teacherRemark }}</span>
            </div>
          </div>

          <template #actions>
            <a-button type="primary" block>
开始观看
            </a-button>
          </template>
        </a-card>
      </div>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.teacher-shared {
  padding: 16px 0;

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;

    .section-title {
      font-size: 20px;
      color: #333;
      margin: 0;
      margin-bottom: 12px;
    }

    .filter-section {
      margin-bottom: 12px;
    }
  }

  .empty-state {
    padding: 60px 0;
    text-align: center;
    background: #f9f9f9;
    border-radius: 8px;
  }

  .video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
  }

  .video-card {
    background: #fff;
    transition: all 0.3s;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }



    .video-thumbnail-wrapper {
      position: relative;
      margin-bottom: 12px;

      .video-thumbnail {
        width: 100%;
        height: 160px;
        object-fit: cover;
      }

      .video-source {
        position: absolute;
        top: 8px;
        left: 8px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }

      .video-required {
        position: absolute;
        top: 8px;
        right: 8px;
        background: rgba(255, 0, 0, 0.7);
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }


    }

    .video-info {
      padding: 0 12px 12px;

      .video-title {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        height: 44px;
      }

      .video-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 12px;
        color: #999;
      }

      .video-remark {
        font-size: 12px;
        color: #666;
        margin-bottom: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        background: #f5f5f5;
        padding: 8px;
        border-radius: 4px;

        .remark-label {
          color: #999;
          margin-right: 4px;
          font-weight: 500;
        }
      }
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .teacher-shared {
    .header-section {
      flex-direction: column;
      align-items: flex-start;

      .section-title {
        margin-bottom: 16px;
      }
    }

    .video-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 16px;
    }

    .video-card {
      .video-thumbnail-wrapper {
        .video-thumbnail {
          height: 140px;
        }
      }

      .video-info {
        .video-title {
          font-size: 14px;
          height: 38px;
        }
      }
    }
  }
}
</style>

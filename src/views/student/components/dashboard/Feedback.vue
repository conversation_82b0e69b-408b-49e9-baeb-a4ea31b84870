<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'

const formState = ref({
  title: '',
  content: '',
  type: undefined
})

const handleSubmit = () => {
  if (!formState.value.title || !formState.value.content || !formState.value.type) {
    message.error('请填写完整信息')
    return
  }
  
  message.success('反馈提交成功')
  formState.value = {
    title: '',
    content: '',
    type: undefined
  }
}
</script>

<template>
  <div class="feedback">
    <div class="feedback-form">
      <a-form :model="formState" layout="vertical">
        <a-form-item label="反馈类型">
          <a-select v-model:value="formState.type" placeholder="请选择反馈类型">
            <a-select-option value="suggestion">建议</a-select-option>
            <a-select-option value="problem">问题</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="标题">
          <a-input v-model:value="formState.title" placeholder="请输入标题" />
        </a-form-item>
        <a-form-item label="内容">
          <a-textarea
            v-model:value="formState.content"
            placeholder="请详细描述您的反馈内容"
            :rows="4"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSubmit">提交反馈</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<style scoped lang="less">
.feedback {


  .feedback-form {
    max-width: 600px;
    margin: 0 auto;
  }
}
</style>
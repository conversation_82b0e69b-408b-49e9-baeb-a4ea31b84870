<template>
  <div class="course-selection">
    <a-card class="mb-4" title="已选课程">
      <a-table
        :dataSource="myCourses"
        :loading="loadingMyCourses"
        :columns="myCoursesColumns"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="primary" class="drop-course-btn" size="small" @click="handleDropCourse(record)">
              退课
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-card title="可选课程">
      <a-table
        :dataSource="availableCourses"
        :loading="loadingAvailable"
        :columns="availableCoursesColumns"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'course_type'">
            <a-tag :color="record.course_type === 'required' ? 'red' : 'green'">
              {{ record.course_type === 'required' ? '必修' : '选修' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button
              :type="isSelected(record.course_code) ? 'primary' : (record.available_seats <= 0 ? 'dashed' : 'primary')"
              :class="{
                'selected-course-btn': isSelected(record.course_code),
                'no-quota-btn': record.available_seats <= 0 && !isSelected(record.course_code)
              }"
              :style="isSelected(record.course_code) ? { backgroundColor: '#52c41a', borderColor: '#52c41a', color: 'white' } : {}"
              size="small"
              :disabled="record.available_seats <= 0 || isSelected(record.course_code)"
              @click="handleSelectCourse(record)"
            >
              {{ isSelected(record.course_code) ? '已选' : (record.available_seats <= 0 ? '已满' : '选课') }}
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import axios from '@/axios'
import { createVNode } from 'vue'
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'

const loadingMyCourses = ref(false)
const loadingAvailable = ref(false)
const myCourses = ref([])
const availableCourses = ref([])

const myCoursesColumns = [
  { title: '课程名称', dataIndex: 'course_name', key: 'course_name' },
  { title: '任课教师', dataIndex: 'teacher_name', key: 'teacher_name' },
  { title: '学期', dataIndex: 'semester', key: 'semester' },
  { title: '学分', dataIndex: 'credit', key: 'credit', width: 80 },
  { title: '班级', dataIndex: 'class_name', key: 'class_name' },
  { title: '操作', key: 'action', width: 120 }
]

const availableCoursesColumns = [
  { title: '课程名称', dataIndex: 'course_name', key: 'course_name' },
  { title: '任课教师', dataIndex: 'teacher_name', key: 'teacher_name' },
  { title: '学期', dataIndex: 'semester', key: 'semester' },
  { title: '学分', dataIndex: 'credit', key: 'credit', width: 80 },
  { title: '课程类型', dataIndex: 'course_type', key: 'course_type', width: 100 },
  { title: '剩余名额', dataIndex: 'available_seats', key: 'available_seats', width: 100 },
  { title: '操作', key: 'action', width: 120 }
]

// 检查课程是否已选
const isSelected = (courseCode) => {
  return myCourses.value.some(course => course.course_code === courseCode);
}

// 获取已选课程
const fetchMyCourses = async () => {
  loadingMyCourses.value = true
  try {
    const response = await axios.get('/api/course-registration/my-courses')
    if (response.data.success) {
      myCourses.value = response.data.data
    }
  } catch (error) {
    console.error('获取已选课程失败:', error)
    message.error('获取已选课程失败')
  } finally {
    loadingMyCourses.value = false
  }
}

// 获取可选课程
const fetchAvailableCourses = async () => {
  loadingAvailable.value = true
  try {
    const response = await axios.get('/api/course-registration/available')
    if (response.data.success) {
      // 为每个课程添加选课状态
      availableCourses.value = response.data.data.map(course => ({
        ...course,
        isSelected: isSelected(course.course_code)
      }))
    }
  } catch (error) {
    console.error('获取可选课程失败:', error)
    message.error('获取可选课程失败')
  } finally {
    loadingAvailable.value = false
  }
}

// 退课
const handleDropCourse = async (course) => {
  Modal.confirm({
    title: '退课确认',
    content: `确定要退选 ${course.course_name} 课程吗？退课后将减少一次选课机会。`,
    okText: '确定退课',
    cancelText: '取消',
    okType: 'danger',
    okButtonProps: {
      danger: true
    },
    icon: createVNode(ExclamationCircleOutlined, { style: { color: '#ff4d4f' } }),
    async onOk() {
      try {
        const response = await axios.delete(`/api/course-registration/drop/${course.course_code}`);
        if (response.data.success) {
          message.success('退课成功');
          await Promise.all([
            fetchMyCourses(),
            fetchAvailableCourses()
          ]);
        }
      } catch (error) {
        console.error('退课失败:', error);
        message.error(error.response?.data?.message || '退课失败');
      }
    }
  });
};

// 选课
const handleSelectCourse = async (course) => {
  Modal.confirm({
    title: '选课确认',
    content: `确定要选择 ${course.course_name} 课程吗？`,
    okText: '确定选课',
    cancelText: '取消',
    icon: createVNode(QuestionCircleOutlined, { style: { color: '#1890ff' } }),
    async onOk() {
      try {
        const response = await axios.post('/api/course-registration/register', {
          course_code: course.course_code
        });
        if (response.data.success) {
          message.success('选课成功');
          await Promise.all([
            fetchMyCourses(),
            fetchAvailableCourses()
          ]);
        }
      } catch (error) {
        console.error('选课失败:', error);
        message.error(error.response?.data?.message || '选课失败');
      }
    }
  });
};

onMounted(() => {
  Promise.all([
    fetchMyCourses(),
    fetchAvailableCourses()
  ])
})
</script>

<style scoped>
.course-selection {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.mb-4 {
  margin-bottom: 16px;
}

:deep(.ant-table-wrapper) {
  margin-top: 16px;
}

:deep(.ant-tag) {
  min-width: 60px;
  text-align: center;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 按钮样式增强 */
/* 已选课程按钮 - 绿色边框和背景 */
:deep(.selected-course-btn) {
  border-color: #52c41a !important;
  background-color: #52c41a !important;
  color: white !important;
}

:deep(.selected-course-btn[disabled]) {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
  color: white !important;
  opacity: 1 !important;
}

/* 退课按钮 - 红色 */
:deep(.drop-course-btn) {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: white !important;
}

/* 没有名额的课程按钮 - 灰色 */
:deep(.no-quota-btn[disabled]) {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.25);
}

</style>
<template>
  <div class="assessment-page">
    <a-spin :spinning="loading">
      <div class="assessment-header">
        <h1>学生答案详情</h1>
        <div class="header-actions">
          <a-button type="primary" @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回成绩分析
          </a-button>
        </div>
        <p class="subtitle">
          学生: <strong>{{ studentName }}</strong> |
          章节: <strong>{{ chapterTitle }}</strong> |
          批次: <strong>第{{ batchId }}批</strong>
        </p>
      </div>

      <div class="assessment-content">

      <!-- 错误提示 -->
      <a-alert
        v-if="error"
        type="error"
        :message="error"
        class="mb-4"
        closable
        @close="error = ''"
      />

      <!-- 学生得分概览 -->
      <div class="content-section" v-if="exercises.length > 0">
        <div class="assessment-card">
          <div class="assessment-card-header">
            <h3>测验概览</h3>
          </div>
          <div class="assessment-card-content">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="总分"
                  :value="totalScore"
                  :precision="1"
                  :value-style="{ color: getScoreColor(totalScore, maxScore) }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="完成率"
                  :value="completionRate"
                  suffix="%"
                  :value-style="{ color: getCompletionColor(completionRate) }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="正确率"
                  :value="correctRate"
                  suffix="%"
                  :value-style="{ color: getCompletionColor(correctRate) }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="完成时间"
                  :value="formattedCompletionTime"
                />
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 题目导航 - 固定在右侧 -->
        <div class="question-navigator">
          <div class="nav-title">题目导航</div>
          <div class="nav-buttons">
            <div
              v-for="(exercise, index) in exercises"
              :key="index"
              class="nav-button"
              :class="{
                'correct-btn': exercise.is_correct === 1,
                'wrong-btn': exercise.is_correct === 0,
                'unanswered-btn': !exercise.student_answer || exercise.student_answer === '未作答',
                'active': activeKeys.includes(index)
              }"
              @click="scrollToQuestion(index)"
            >
              {{ index + 1 }}
            </div>
          </div>
        </div>

        <!-- 答题详情 -->
        <div class="assessment-card question-details">
          <div class="assessment-card-header">
            <h3>答题详情</h3>
          </div>
          <div class="assessment-card-content">
            <a-collapse v-model:activeKey="activeKeys" class="custom-collapse">
            <a-collapse-panel
              v-for="(exercise, index) in exercises"
              :key="index"
              :header="getExerciseHeader(exercise, index)"
              :id="`question-${index}`"
            >
              <div class="exercise-content">
                <!-- 题目内容 -->
                <div class="question">
                  <div class="question-header">
                    <div class="question-type">
                      <a-tag :color="getQuestionTypeColor(exercise.question_type)">
                        {{ getQuestionTypeName(exercise.question_type) }}
                      </a-tag>
                    </div>
                    <div class="question-status">
                      <a-tag :color="getAnswerStatusColor(exercise)">
                        {{ getAnswerStatusText(exercise) }}
                      </a-tag>
                    </div>
                  </div>

                  <div class="question-body">
                    <div class="question-content">{{ exercise.content }}</div>

                    <!-- 选择题选项 -->
                    <div v-if="exercise.question_type === 1" class="question-options">
                      <div
                        v-for="option in exercise.options"
                        :key="option.key"
                        class="option-item"
                        :class="{
                          'option-correct': option.key === exercise.answer,
                          'option-selected': option.key === exercise.student_answer,
                          'option-wrong': option.key === exercise.student_answer && option.key !== exercise.answer
                        }"
                      >
                        <div class="option-marker" :class="{
                          'marker-correct': option.key === exercise.answer,
                          'marker-selected': option.key === exercise.student_answer,
                          'marker-wrong': option.key === exercise.student_answer && option.key !== exercise.answer
                        }"></div>
                        <span class="option-key">{{ option.key }}.</span>
                        <span class="option-value">{{ option.value }}</span>
                        <check-outlined v-if="option.key === exercise.answer" class="correct-icon" />
                      </div>
                    </div>

                    <!-- 判断题 -->
                    <div v-else-if="exercise.question_type === 3" class="question-options">
                      <div
                        v-for="option in [
                          { key: 'true', value: '正确' },
                          { key: 'false', value: '错误' }
                        ]"
                        :key="option.key"
                        class="option-item"
                        :class="{
                          'option-correct': option.key === exercise.answer,
                          'option-selected': option.key === exercise.student_answer,
                          'option-wrong': option.key === exercise.student_answer && option.key !== exercise.answer
                        }"
                      >
                        <div class="option-marker" :class="{
                          'marker-correct': option.key === exercise.answer,
                          'marker-selected': option.key === exercise.student_answer,
                          'marker-wrong': option.key === exercise.student_answer && option.key !== exercise.answer
                        }"></div>
                        <span class="option-key">{{ option.key === 'true' ? 'T' : 'F' }}.</span>
                        <span class="option-value">{{ option.value }}</span>
                        <check-outlined v-if="option.key === exercise.answer" class="correct-icon" />
                      </div>
                    </div>

                    <!-- 填空题/简答题/计算题 -->
                    <div v-else class="question-answer">
                      <div class="answer-section">
                        <div class="answer-label">正确答案:</div>
                        <div class="answer-content correct">{{ exercise.answer || '未设置答案' }}</div>
                      </div>
                      <div class="answer-section">
                        <div class="answer-label">学生答案:</div>
                        <!-- 如果是图片路径，显示图片 -->
                        <div v-if="isImagePath(exercise.student_answer)" class="answer-content image-answer">
                          <img 
                            :src="getImageUrl(exercise.student_answer)" 
                            class="answer-image" 
                            @click="handleImagePreview(exercise.student_answer)" 
                            alt="学生提交的图片答案"
                          />
                        </div>
                        <!-- 否则显示文本 -->
                        <div v-else class="answer-content" :class="{ 'student-correct': exercise.is_correct === 1, 'student-wrong': exercise.is_correct === 0 }">
                          {{ exercise.student_answer || '未作答' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 答案解析 -->
                <a-divider orientation="left">答案解析</a-divider>
                <div class="analysis-section">
                  <p class="analysis-content">{{ exercise.analysis || '暂无解析' }}</p>

                  <div class="score-section">
                    <div class="score-label">得分:</div>
                    <div class="score-value" :class="getScoreClass(exercise)">
                      {{ exercise.score || 0 }} / {{ getMaxScore(exercise) }}
                    </div>
                  </div>

                  <div v-if="exercise.evaluation" class="evaluation-section">
                    <div class="evaluation-label">批改意见:</div>
                    <div class="evaluation-content">{{ exercise.evaluation }}</div>
                  </div>
                </div>
              </div>
            </a-collapse-panel>
            </a-collapse>
          </div>
        </div>
      </div>

      <!-- 数据为空展示 -->
      <div v-else-if="!loading" class="empty-state assessment-card">
        <a-empty
          description="未找到该学生的答题记录"
        />
      </div>
      </div>
      
      <!-- 图片预览对话框 -->
      <a-modal
        v-model:visible="previewVisible"
        title="答案图片预览"
        :footer="null"
      >
        <img alt="计算题答案预览" style="width: 100%" :src="previewImage" />
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { CheckOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
import axios from '@/axios';

export default defineComponent({
  components: {
    CheckOutlined,
    ArrowLeftOutlined
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const { classId, chapterId, batchId, studentId } = route.params;
    const studentName = route.query.studentName || '未知学生';

    // 状态定义
    const loading = ref(true);
    const error = ref('');
    const exercises = ref([]);
    const submissionInfo = ref(null);
    const activeKeys = ref([0]); // 默认展开第一题
    const chapterTitle = ref('');
    
    // 图片预览相关状态
    const previewVisible = ref(false);
    const previewImage = ref('');

    // 计算属性
    const totalScore = computed(() => {
      if (!exercises.value.length) return 0;
      return exercises.value.reduce((sum, ex) => sum + (ex.score || 0), 0);
    });

    const maxScore = computed(() => {
      if (!exercises.value.length) return 0;
      return exercises.value.reduce((sum, ex) => sum + getMaxScore(ex), 0);
    });

    const completionRate = computed(() => {
      if (!exercises.value.length) return 0;
      const answered = exercises.value.filter(ex =>
        ex.student_answer && ex.student_answer !== '未作答'
      ).length;
      return Math.round((answered / exercises.value.length) * 100);
    });

    const correctRate = computed(() => {
      if (!exercises.value.length) return 0;
      const correctCount = exercises.value.filter(ex => ex.is_correct === 1).length;
      return Math.round((correctCount / exercises.value.length) * 100);
    });

    const formattedCompletionTime = computed(() => {
      if (!submissionInfo.value || !submissionInfo.value.end_time) return '未完成';
      return formatDate(submissionInfo.value.end_time);
    });
    
    // 处理图片URL
    const getImageUrl = (path) => {
      if (!path) return '';
      
      // 确保路径以/开头
      const normalizedPath = path.startsWith('/') ? path : `/${path}`;
      
      // 基于当前域名构建完整URL
      const baseUrl = window.location.origin;
      return `${baseUrl}${normalizedPath}`;
    };

    // 处理图片预览
    const handleImagePreview = (imagePath) => {
      previewImage.value = getImageUrl(imagePath);
      previewVisible.value = true;
    };
    
    // 判断是否是图片路径
    const isImagePath = (path) => {
      if (!path) return false;
      // 假设所有上传图片的路径都以/uploads/开头
      return String(path).startsWith('/uploads/') || String(path).startsWith('uploads/');
    };

    // 获取习题答案
    const fetchStudentAnswers = async () => {
      try {
        loading.value = true;

        // 先获取章节名称
        const chapterResponse = await axios.get(`/api/analysis/class/${classId}/chapters`);
        if (chapterResponse.data.success) {
          const chapter = chapterResponse.data.data.find(c => c.id === chapterId);
          if (chapter) {
            chapterTitle.value = chapter.title;
          }
        }

        // 获取学生答案详情
        const response = await axios.get(`/api/student-answers/${classId}/${chapterId}/${batchId}/${studentId}`);

        if (response.data.success) {
          exercises.value = response.data.data.exercises;
          submissionInfo.value = response.data.data.submission;
        } else {
          error.value = response.data.message || '获取答案详情失败';
        }
      } catch (err) {
        console.error('获取学生答案详情失败:', err);
        error.value = err.response?.data?.message || '获取答案详情失败';
      } finally {
        loading.value = false;
      }
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    };

    // 获取题目类型名称
    const getQuestionTypeName = (type) => {
      const types = {
        1: '选择题',
        2: '填空题',
        3: '判断题',
        4: '简答题',
        5: '计算题'
      };
      return types[type] || '未知类型';
    };

    // 获取题目标题
    const getExerciseHeader = (exercise, index) => {
      return `第 ${index + 1} 题 - ${getQuestionTypeName(exercise.question_type)} ${exercise.score ? `(${exercise.score}分)` : ''}`;
    };

    // 获取答案状态颜色
    const getAnswerStatusColor = (exercise) => {
      if (!exercise.student_answer || exercise.student_answer === '未作答') {
        return 'default';
      }

      if (exercise.is_correct === 1) {
        return 'success';
      } else {
        return 'error';
      }
    };

    // 获取答案状态文本
    const getAnswerStatusText = (exercise) => {
      if (!exercise.student_answer || exercise.student_answer === '未作答') {
        return '未作答';
      }

      if (exercise.is_correct === 1) {
        return '正确';
      } else {
        return '错误';
      }
    };

    // 获取最大分数
    const getMaxScore = (exercise) => {
      return exercise.question_type <= 3 ? 5 : 10;
    };

    // 获取分数样式类
    const getScoreClass = (exercise) => {
      if (!exercise.score) return '';
      const maxScore = getMaxScore(exercise);
      const ratio = exercise.score / maxScore;

      if (ratio >= 0.8) return 'score-high';
      if (ratio >= 0.6) return 'score-medium';
      return 'score-low';
    };

    // 根据分数比例获取颜色
    const getScoreColor = (score, maxScore) => {
      if (!score || !maxScore) return '#999';

      const percent = score / maxScore;

      if (percent >= 0.8) return '#52c41a';
      if (percent >= 0.6) return '#faad14';
      return '#f5222d';
    };

    // 获取完成率颜色
    const getCompletionColor = (rate) => {
      if (rate >= 80) return '#52c41a';
      if (rate >= 60) return '#faad14';
      return '#f5222d';
    };

    // 滚动到指定题目
    const scrollToQuestion = (index) => {
      // 确保该题目在activeKeys中
      if (!activeKeys.value.includes(index)) {
        activeKeys.value = [...activeKeys.value, index];
      }

      // 滚动到题目位置
      setTimeout(() => {
        const element = document.getElementById(`question-${index}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 300);
    };

    // 获取题目类型颜色
    const getQuestionTypeColor = (type) => {
      const colors = {
        1: 'purple',  // 选择题
        2: 'blue',    // 填空题
        3: 'green',   // 判断题
        4: 'orange',  // 简答题
        5: 'geekblue' // 计算题
      };
      return colors[type] || 'default';
    };

    // 返回上一页
    const goBack = () => {
      router.back();
    };

    onMounted(() => {
      if (!classId || !chapterId || !batchId || !studentId) {
        error.value = '参数不完整，无法获取学生答案';
        loading.value = false;
        return;
      }

      fetchStudentAnswers();
    });

    return {
      loading,
      error,
      exercises,
      activeKeys,
      studentName,
      chapterTitle,
      batchId,
      totalScore,
      maxScore,
      completionRate,
      correctRate,
      formattedCompletionTime,
      formatDate,
      getQuestionTypeName,
      getExerciseHeader,
      getAnswerStatusColor,
      getAnswerStatusText,
      getMaxScore,
      getScoreClass,
      getScoreColor,
      getCompletionColor,
      scrollToQuestion,
      getQuestionTypeColor,
      goBack,
      // 图片相关
      previewVisible,
      previewImage,
      getImageUrl,
      handleImagePreview,
      isImagePath
    };
  }
});
</script>

<style scoped>
/* 使用统一的样式类名，样式已在assessment.css中定义 */

/* 添加新的样式 */
.header-actions {
  margin-bottom: 16px;
}

/* 题目导航 */
.question-navigator {
  position: fixed;
  right: 32px;
  top: 250px;
  width: 70px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 12px 8px;
  z-index: 100;
}

.nav-title {
  text-align: center;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  font-size: 14px;
}

.nav-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
  padding: 0 5px;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 28px;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  border: 1px solid #d9d9d9;
}

.nav-button:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
  transform: scale(1.05);
}

.nav-button.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.nav-button.correct-btn {
  background-color: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
}

.nav-button.wrong-btn {
  background-color: #fff2f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.nav-button.unanswered-btn {
  background-color: #f5f5f5;
  color: #999;
}

/* 调整内容区域宽度，为右侧导航栏留出空间 */
.question-details {
  margin-right: 80px;
}

/* 美化题目展示 */
.custom-collapse :deep(.ant-collapse-header) {
  background-color: #f6f8fa;
  border-radius: 4px;
  padding: 12px 16px;
  font-weight: 500;
}

.custom-collapse :deep(.ant-collapse-content-box) {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 0 0 4px 4px;
}

.question {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  align-items: center;
}

.question-content {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 4px;
  background-color: white;
  border: 1px solid #eee;
  position: relative;
  overflow: hidden;
}

.option-marker {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: transparent;
}

.marker-correct {
  background-color: #52c41a;
}

.marker-wrong {
  background-color: #ff4d4f;
}

.marker-selected {
  background-color: #1890ff;
}

.option-key {
  font-weight: 500;
  margin-right: 12px;
  color: #666;
  min-width: 20px;
}

.option-value {
  flex: 1;
}

.correct-icon {
  color: #52c41a;
  margin-left: 8px;
}

.answer-section {
  margin-bottom: 16px;
  background-color: white;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.answer-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.answer-content {
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.answer-content.correct {
  border-left: 4px solid #52c41a;
}

.student-correct {
  border-left: 4px solid #52c41a;
}

.student-wrong {
  border-left: 4px solid #ff4d4f;
}

.analysis-section {
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.analysis-content {
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 4px;
  margin-bottom: 16px;
  line-height: 1.6;
}

.score-section {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.score-label {
  font-weight: 500;
  margin-right: 8px;
  color: #666;
}

.score-value {
  font-weight: 600;
  font-size: 16px;
}

.evaluation-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #eee;
}

.evaluation-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.evaluation-content {
  padding: 12px;
  background-color: #fffbe6;
  border-radius: 4px;
  border-left: 4px solid #faad14;
}

.score-high {
  color: #52c41a;
}

.score-medium {
  color: #faad14;
}

.score-low {
  color: #ff4d4f;
}

/* 图片答案样式 */
.image-answer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
}

.answer-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #eee;
  transition: transform 0.3s;
}

.answer-image:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
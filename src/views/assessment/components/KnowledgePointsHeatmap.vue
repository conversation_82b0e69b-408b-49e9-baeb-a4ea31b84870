<template>
  <div class="knowledge-points-card">
    <div class="card-header">
      <h3>知识点错误热图</h3>
      <div class="header-actions">
        <a-switch
          v-model:checked="enableAiAnalysis"
          checked-children="AI分析开启"
          un-checked-children="AI分析关闭"
          @change="handleAiSwitchChange"
        />
        <a-button
          type="primary"
          :loading="loading"
          @click="refreshData"
          :disabled="!currentClass || !currentChapter"
        >
          <template #icon><reload-outlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <a-spin :spinning="loading">
      <div v-if="!hasData" class="empty-state">
        <a-empty description="暂无知识点错误数据" />
      </div>

      <template v-else>
        <!-- 热力图展示 -->
        <div class="heatmap-container">
          <div class="heatmap-header">
            <h4>错误率分布热图</h4>
            <div class="legend">
              <div class="legend-item">
                <span class="legend-color" style="background-color: #f5222d;"></span>
                <span>严重 (≥80%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #fa8c16;"></span>
                <span>高 (≥60%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #faad14;"></span>
                <span>中 (≥40%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #52c41a;"></span>
                <span>低 (≥20%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #1890ff;"></span>
                <span>很低 (<20%)</span>
              </div>
            </div>
          </div>
          <div id="knowledgeHeatmapRef" ref="knowledgeHeatmapRef" class="heatmap"></div>
        </div>

        <!-- 知识点列表 -->
        <div class="knowledge-list">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(point, index) in knowledgePoints" :key="index">
              <a-card hoverable class="knowledge-card" :class="getErrorLevelClass(point.errorRate)">
                <template #title>
                  <div class="knowledge-title">
                    <span>{{ point.name }}</span>
                    <a-tag :color="getErrorLevelColor(point.errorRate)">
                      错误率: {{ point.errorRate }}%
                    </a-tag>
                  </div>
                </template>
                <div class="knowledge-exercises">
                  <p class="exercise-count">相关错题: {{ point.exercises.length }}道</p>
                  <div class="exercise-list">
                    <a-tooltip v-for="(exercise, i) in point.exercises" :key="i" :title="exercise.title">
                      <a-tag class="exercise-tag">
                        题目{{ i+1 }}: {{ exercise.errorCount }}人错
                      </a-tag>
                    </a-tooltip>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- AI分析结果 -->
        <a-collapse v-if="aiSuggestions" class="ai-suggestions">
          <a-collapse-panel key="1" header="AI教学建议">
            <div class="ai-content">
              <a-alert type="info" show-icon>
                <template #message>AI分析仅供参考，请结合实际情况使用</template>
              </a-alert>
              <div class="markdown-content" v-html="formattedAiSuggestions"></div>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </template>
    </a-spin>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import axios from '@/axios';
import * as echarts from 'echarts';
import { marked } from 'marked';

export default defineComponent({
  components: {
    ReloadOutlined
  },
  props: {
    currentClass: {
      type: [String, Number],
      default: null
    },
    currentChapter: {
      type: [String, Number],
      default: null
    }
  },
  setup(props) {
    const loading = ref(false);
    const knowledgePoints = ref([]);
    const studentCount = ref(0);
    const aiSuggestions = ref(null);
    const enableAiAnalysis = ref(false);

    const knowledgeHeatmapRef = ref(null);
    let knowledgeHeatmap = null;

    // 计算属性
    const hasData = computed(() => knowledgePoints.value && knowledgePoints.value.length > 0);

    const formattedAiSuggestions = computed(() => {
      if (!aiSuggestions.value) return '';

      // 使用marked将markdown格式转换为HTML
      return marked(aiSuggestions.value);
    });

    // 获取知识点错误热图数据
    const fetchKnowledgePointsData = async () => {
      if (!props.currentClass || !props.currentChapter) {
        return;
      }

      try {
        loading.value = true;

        const response = await axios.get(
          `/api/preview-evaluation/class/${props.currentClass}/chapter/${props.currentChapter}/knowledge-points`,
          { params: { ai: enableAiAnalysis.value } }
        );

        if (response.data.success) {
          knowledgePoints.value = response.data.data.knowledgePoints;
          studentCount.value = response.data.data.studentCount;
          aiSuggestions.value = response.data.data.aiSuggestions;

          nextTick(() => {
            initHeatmap();
          });
        } else {
          message.error('获取知识点数据失败');
        }
      } catch (error) {
        console.error('获取知识点热图数据失败:', error);
        message.error('获取知识点热图数据失败');
      } finally {
        loading.value = false;
      }
    };

    // 初始化热力图
    const initHeatmap = () => {
      if (!knowledgeHeatmapRef.value || !hasData.value) return;

      if (knowledgeHeatmap) {
        knowledgeHeatmap.dispose();
      }

      knowledgeHeatmap = echarts.init(knowledgeHeatmapRef.value);

      const data = knowledgePoints.value.map((point, index) => ({
        name: point.name,
        value: point.errorRate,
        itemStyle: {
          color: getHeatmapColor(point.errorRate)
        }
      }));

      const option = {
        tooltip: {
          formatter: function(params) {
            return `${params.data.name}<br/>错误率: ${params.data.value}%`;
          }
        },
        // 添加缩放控制组件
        dataZoom: [
          {
            type: 'inside',
            filterMode: 'none',
            disabled: false
          }
        ],
        toolbox: {
          show: true,
          feature: {
            restore: {
              show: true,
              title: '重置'
            }
          },
          right: 20,
          top: 0
        },
        series: [{
          type: 'treemap',
          roam: true, // 允许缩放和平移
          data: data,
          label: {
            show: true,
            formatter: '{b}\n{c}%',
            fontSize: 14
          },
          breadcrumb: {
            show: false
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
            gapWidth: 4
          },
          levels: [
            {
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2,
                gapWidth: 4
              },
              upperLabel: {
                show: false
              }
            }
          ]
        }]
      };

      knowledgeHeatmap.setOption(option);

      // 添加缩放事件监听
      knowledgeHeatmap.on('mousewheel', function(params) {
        // 阻止默认滚动行为
        if (params.event && params.event.event) {
          params.event.event.preventDefault();
        }
      });
    };

    // 获取热图渐变色
    const getGradientColorStops = (errorRate) => {
      if (errorRate >= 80) {
        return [
          { offset: 0, color: '#f5222d' },   // 深红
          { offset: 1, color: '#ff7875' }    // 浅红
        ];
      }
      if (errorRate >= 60) {
        return [
          { offset: 0, color: '#fa8c16' },   // 深橙
          { offset: 1, color: '#ffc069' }    // 浅橙
        ];
      }
      if (errorRate >= 40) {
        return [
          { offset: 0, color: '#faad14' },   // 深黄
          { offset: 1, color: '#ffd666' }    // 浅黄
        ];
      }
      if (errorRate >= 20) {
        return [
          { offset: 0, color: '#52c41a' },   // 深绿
          { offset: 1, color: '#95de64' }    // 浅绿
        ];
      }
      return [
        { offset: 0, color: '#4c4de6' },   // 深蓝
        { offset: 1, color: '#6364e9' }    // 浅蓝
      ];
    };

    // 获取错误等级颜色
    const getErrorLevelColor = (errorRate) => {
      if (errorRate >= 80) return '#f5222d';  // 严重
      if (errorRate >= 60) return '#fa8c16';  // 高
      if (errorRate >= 40) return '#faad14';  // 中
      if (errorRate >= 20) return '#52c41a';  // 低
      return '#1890ff';                       // 很低
    };

    // 获取热图颜色
    const getHeatmapColor = (errorRate) => {
      if (errorRate >= 80) return '#f5222d';  // 严重
      if (errorRate >= 60) return '#fa8c16';  // 高
      if (errorRate >= 40) return '#faad14';  // 中
      if (errorRate >= 20) return '#2fc25b';  // 低
      return '#1890ff';                       // 很低
    };

    // 获取错误等级样式类
    const getErrorLevelClass = (errorRate) => {
      if (errorRate >= 80) return 'error-severe';
      if (errorRate >= 60) return 'error-high';
      if (errorRate >= 40) return 'error-medium';
      if (errorRate >= 20) return 'error-low';
      return 'error-very-low';
    };

    // 刷新数据
    const refreshData = () => {
      fetchKnowledgePointsData();
    };

    // 处理AI开关改变
    const handleAiSwitchChange = (checked) => {
      enableAiAnalysis.value = checked;
      if (checked && props.currentClass && props.currentChapter) {
        refreshData();
      }
    };

    // 监听属性变化
    watch(
      [() => props.currentClass, () => props.currentChapter],
      ([newClass, newChapter], [oldClass, oldChapter]) => {
        if (newClass && newChapter && (newClass !== oldClass || newChapter !== oldChapter)) {
          fetchKnowledgePointsData();
        }
      }
    );

    // 组件挂载
    onMounted(() => {
      if (props.currentClass && props.currentChapter) {
        fetchKnowledgePointsData();
      }

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (knowledgeHeatmap) {
          knowledgeHeatmap.resize();
        }
      });
    });

    return {
      loading,
      knowledgePoints,
      studentCount,
      aiSuggestions,
      enableAiAnalysis,
      hasData,
      formattedAiSuggestions,
      knowledgeHeatmapRef,

      refreshData,
      getErrorLevelColor,
      getErrorLevelClass,
      handleAiSwitchChange,
      getGradientColorStops
    };
  }
});
</script>

<style scoped>
.knowledge-points-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.heatmap-container {
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.heatmap-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
  flex-wrap: wrap;
  gap: 12px;
}

.heatmap-header h4 {
  margin: 0;
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

.legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.heatmap {
  height: 350px;
  width: 100%;
}

.knowledge-list {
  padding: 0 24px 24px;
}

.knowledge-card {
  height: auto;
  min-height: 140px;
  transition: all 0.3s ease;
  border-left: 4px solid #1890ff;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
}

.knowledge-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.knowledge-card.error-severe {
  background: linear-gradient(135deg, rgba(245, 34, 45, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  border-left-color: #f5222d;
}

.knowledge-card.error-high {
  background: linear-gradient(135deg, rgba(250, 140, 22, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  border-left-color: #fa8c16;
}

.knowledge-card.error-medium {
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  border-left-color: #faad14;
}

.knowledge-card.error-low {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  border-left-color: #52c41a;
}

.knowledge-card.error-very-low {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  border-left-color: #1890ff;
}

.knowledge-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  font-weight: 500;
  flex-wrap: wrap;
  gap: 8px;
}

.knowledge-title span {
  word-break: break-word;
  flex: 1;
  min-width: 0;
}

.knowledge-title a-tag {
  flex-shrink: 0;
}

.knowledge-exercises {
  margin-top: 8px;
}

.exercise-count {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.exercise-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  min-height: 30px;
}

.exercise-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.exercise-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ai-suggestions {
  margin: 0 24px 24px;
}

.ai-content {
  margin-top: 16px;
}

.markdown-content {
  margin-top: 16px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  line-height: 1.6;
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3 {
  margin-top: 16px;
  margin-bottom: 8px;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 24px;
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }

  .heatmap-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .legend {
    margin-top: 8px;
  }

  .knowledge-title {
    flex-direction: column;
    align-items: stretch;
  }

  .knowledge-title span {
    margin-bottom: 5px;
  }
}
</style>
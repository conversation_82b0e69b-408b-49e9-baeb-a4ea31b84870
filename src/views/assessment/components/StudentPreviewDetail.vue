<template>
  <div class="student-detail">
    <div class="student-header">
      <div class="student-info">
        <div class="avatar">
          <a-avatar :size="64" :src="studentData.avatar || null">
            {{ studentData.name ? studentData.name.substring(0, 1) : 'S' }}
          </a-avatar>
        </div>
        <div class="info-text">
          <h2>{{ studentData.name || '未知学生' }}</h2>
          <p>学号: {{ studentData.student_id || '未知' }}</p>
        </div>
      </div>
      <div class="status-badge" :class="{ 'completed': studentData.preview_status === 1 }">
        {{ studentData.preview_status === 1 ? '已完成预习' : '未完成预习' }}
      </div>
    </div>

    <!-- 预习指标卡片 -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-title">预习时长</div>
        <div class="stat-value">{{ studentData.view_duration || 0 }}分钟</div>
        <div class="stat-desc">共{{ studentData.view_times || 0 }}次查看</div>
      </div>
      <div class="stat-card">
        <div class="stat-title">预习材料完成度</div>
        <div class="stat-value">{{ studentData.material_completion || 0 }}%</div>
        <div class="stat-desc">
          {{ studentData.completed_materials || 0 }}/{{ studentData.total_materials || 0 }}个材料
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-title">习题正确率</div>
        <div class="stat-value">{{ studentData.exercise_correct_rate || 0 }}%</div>
        <div class="stat-desc">
          {{ studentData.correct_questions || 0 }}/{{ studentData.total_questions || 0 }}题正确
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-title">最近预习时间</div>
        <div class="stat-value">{{ formatDate(studentData.last_view_time) }}</div>
      </div>
    </div>

    <!-- 预习材料详情 -->
    <div class="section-card">
      <div class="section-header">
        <h3>预习材料学习情况</h3>
      </div>
      <a-table
        :columns="materialColumns"
        :data-source="studentData.materials || []"
        :pagination="{ pageSize: 5 }"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.viewed ? 'success' : 'error'">
              {{ record.viewed ? '已学习' : '未学习' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'progress'">
            <a-progress 
              :percent="record.view_percentage || 0" 
              size="small"
              :status="record.view_percentage >= 100 ? 'success' : 'active'"
            />
          </template>
          <template v-if="column.key === 'duration'">
            {{ record.view_duration || 0 }}分钟
          </template>
        </template>
      </a-table>
    </div>

    <!-- 预习习题详情 -->
    <div class="section-card">
      <div class="section-header">
        <h3>预习习题完成情况</h3>
      </div>
      <a-table
        :columns="questionColumns"
        :data-source="studentData.questions || []"
        :pagination="{ pageSize: 5 }"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getQuestionStatusColor(record.status)">
              {{ getQuestionStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'result'">
            <a-tag :color="record.is_correct ? 'success' : 'error'">
              {{ record.is_correct ? '正确' : '错误' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'answer'">
            <a-tooltip>
              <template #title>{{ record.student_answer || '未作答' }}</template>
              <div class="answer-cell">{{ record.student_answer || '未作答' }}</div>
            </a-tooltip>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 预习时间轴记录 -->
    <div class="section-card">
      <div class="section-header">
        <h3>预习学习记录</h3>
      </div>
      <a-timeline>
        <a-timeline-item v-for="(record, index) in studentData.view_records || []" :key="index">
          <template #dot>
            <template v-if="record.type === 'material'">
              <read-outlined style="font-size: 16px;" />
            </template>
            <template v-else-if="record.type === 'exercise'">
              <form-outlined style="font-size: 16px;" />
            </template>
            <template v-else>
              <clock-circle-outlined style="font-size: 16px;" />
            </template>
          </template>
          <div class="timeline-content">
            <div class="timeline-title">
              {{ record.title || '学习活动' }}
              <a-tag color="blue" v-if="record.duration">
                {{ record.duration }}分钟
              </a-tag>
            </div>
            <div class="timeline-time">{{ formatDate(record.time) }}</div>
            <div class="timeline-desc" v-if="record.description">{{ record.description }}</div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue';
import { 
  ReadOutlined, 
  FormOutlined, 
  ClockCircleOutlined 
} from '@ant-design/icons-vue';

export default defineComponent({
  components: {
    ReadOutlined,
    FormOutlined,
    ClockCircleOutlined
  },
  props: {
    studentData: {
      type: Object,
      required: true
    }
  },
  setup() {
    // 预习材料表格列
    const materialColumns = [
      {
        title: '材料名称',
        dataIndex: 'title',
        key: 'title',
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        customRender: ({ text }) => {
          const typeMap = {
            'video': '视频',
            'doc': '文档',
            'pdf': 'PDF',
            'image': '图片',
            'other': '其他'
          };
          return typeMap[text] || text;
        }
      },
      {
        title: '学习状态',
        dataIndex: 'status',
        key: 'status',
      },
      {
        title: '学习进度',
        dataIndex: 'progress',
        key: 'progress',
      },
      {
        title: '学习时长',
        dataIndex: 'duration',
        key: 'duration',
      }
    ];

    // 预习习题表格列
    const questionColumns = [
      {
        title: '题目',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true,
      },
      {
        title: '题型',
        dataIndex: 'type',
        key: 'type',
        customRender: ({ text }) => {
          const typeMap = {
            'single': '单选题',
            'multiple': '多选题',
            'fill': '填空题',
            'judge': '判断题',
            'short': '简答题'
          };
          return typeMap[text] || text;
        }
      },
      {
        title: '完成状态',
        dataIndex: 'status',
        key: 'status',
      },
      {
        title: '学生答案',
        dataIndex: 'answer',
        key: 'answer',
        ellipsis: true,
      },
      {
        title: '结果',
        dataIndex: 'result',
        key: 'result',
      }
    ];

    // 获取习题状态颜色
    const getQuestionStatusColor = (status) => {
      const statusColorMap = {
        0: 'default',  // 未作答
        1: 'success',  // 已完成
        2: 'warning',  // 部分完成
        3: 'error'     // 超时未完成
      };
      return statusColorMap[status] || 'default';
    };

    // 获取习题状态文本
    const getQuestionStatusText = (status) => {
      const statusTextMap = {
        0: '未作答',
        1: '已完成',
        2: '部分完成',
        3: '超时未完成'
      };
      return statusTextMap[status] || '未知状态';
    };

    // 日期格式化
    const formatDate = (dateStr) => {
      if (!dateStr) return '-';
      
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    };

    return {
      materialColumns,
      questionColumns,
      getQuestionStatusColor,
      getQuestionStatusText,
      formatDate
    };
  }
});
</script>

<style scoped>
.student-detail {
  margin-bottom: 24px;
}

.student-header {
  background: #fff;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.student-info {
  display: flex;
  align-items: center;
}

.avatar {
  margin-right: 20px;
}

.info-text h2 {
  margin: 0;
  font-size: 24px;
  color: #1e293b;
}

.info-text p {
  margin: 4px 0 0;
  color: #64748b;
}

.status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 500;
  background-color: #fecaca;
  color: #ef4444;
}

.status-badge.completed {
  background-color: #dcfce7;
  color: #16a34a;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.stat-title {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.stat-desc {
  font-size: 14px;
  color: #6b7280;
  margin-top: 8px;
}

.section-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.answer-cell {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.timeline-content {
  margin-bottom: 8px;
}

.timeline-title {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #4b5563;
}

@media (max-width: 768px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .student-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .status-badge {
    margin-top: 16px;
  }
}
</style> 
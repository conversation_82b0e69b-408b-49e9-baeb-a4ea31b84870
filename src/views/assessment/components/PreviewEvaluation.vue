<template>
  <div class="page-container">
    <div class="page-header">
      <h1>预习评估</h1>
      <p class="subtitle">评估学生预习资料和习题的完成情况</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 筛选条件区域 -->
      <div class="filter-card">
        <div class="filter-header">
          <h3>数据筛选</h3>
          <a-button 
            type="link" 
            :loading="loading"
            @click="refreshData"
          >
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </div>
        <div class="filter-content">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="班级">
                  <a-select
                    v-model:value="currentClass.id"
                    style="width: 100%"
                    placeholder="选择班级"
                    @change="handleClassChange"
                  >
                    <a-select-option v-for="item in classList" :key="item.id" :value="item.id">
                      {{ item.class_name }}
                      <a-tag color="blue" size="small">{{ item.semester }}</a-tag>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="章节">
                  <a-select
                    v-model:value="selectedChapter"
                    style="width: 100%"
                    placeholder="选择章节"
                    @change="handleChapterChange"
                    :disabled="!currentClass.id || chapters.length === 0"
                  >
                    <a-select-option v-for="chapter in chapters" :key="chapter.id" :value="chapter.id">
                      {{ chapter.title }}
                    </a-select-option>
                  </a-select>
                  <div class="select-tip" v-if="!loading && currentClass.id && chapters.length === 0">
                    <a-alert
                      message="提示"
                      description="当前班级没有同时发布预习资料和预习题的章节"
                      type="info"
                      show-icon
                    />
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>

      <!-- 评估结果展示 -->
      <template v-if="evaluationData">
        <!-- 班级平均分卡片 -->
        <div class="stats-overview">
          <div class="stat-card">
            <div class="stat-title">预习资料平均分</div>
            <div class="stat-value">{{ evaluationData.averages.material_score }}分</div>
            <div class="stat-desc">推荐阅读时间: {{ formatDuration(evaluationData.expected_duration) }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">预习题平均分</div>
            <div class="stat-value">{{ evaluationData.averages.exercise_score }}分</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">综合平均分</div>
            <div class="stat-value">{{ evaluationData.averages.total_score }}分</div>
            <div class="stat-desc">资料权重: 40%, 习题权重: 60%</div>
          </div>
        </div>

        <!-- 学生得分柱状图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>学生预习评估得分</h3>
          </div>
          <div id="scoreChartRef" ref="scoreChartRef" class="chart-container"></div>
        </div>

        <!-- 学生得分详情表格 -->
        <div class="data-card">
          <div class="card-header">
            <h3>学生预习评估详情</h3>
            <div class="header-actions">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索学生姓名"
                style="width: 250px"
                @search="onSearch"
              />
            </div>
          </div>
          <a-table
            :columns="columns"
            :data-source="filteredStudentsData"
            :pagination="{ 
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: total => `共 ${total} 条记录`
            }"
            :loading="loading"
            size="middle"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'material_score'">
                <a-tag :color="getScoreColor(record.material_score)">
                  {{ record.material_score }}分
                </a-tag>
              </template>
              <template v-if="column.key === 'exercise_score'">
                <a-tag :color="getScoreColor(record.exercise_score)">
                  {{ record.exercise_score }}分
                </a-tag>
              </template>
              <template v-if="column.key === 'total_score'">
                <a-tag :color="getScoreColor(record.total_score)">
                  {{ record.total_score }}分
                </a-tag>
              </template>
              <template v-if="column.key === 'view_duration'">
                {{ formatDuration(record.view_duration) }}
              </template>
            </template>
          </a-table>
        </div>
      </template>

      <!-- 数据为空时的占位符 -->
      <template v-if="!loading && !evaluationData">
        <div class="empty-state">
          <a-empty description="请选择班级和章节查看预习评估数据" />
        </div>
      </template>
    </a-spin>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import axios from '@/axios';
import * as echarts from 'echarts';

export default defineComponent({
  components: {
    ReloadOutlined
  },
  setup() {
    const loading = ref(false);
    const classList = ref([]);
    const chapters = ref([]);
    const searchText = ref('');
    
    const evaluationData = ref(null);
    
    const scoreChartRef = ref(null);
    let scoreChart = null;
    
    const currentClass = reactive({
      id: null,
      name: '',
      semester: ''
    });
    
    const selectedChapter = ref(null);
    
    // 表格列定义
    const columns = [
      {
        title: '学生姓名',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '学号',
        dataIndex: 'student_id',
        key: 'student_id',
      },
      {
        title: '预习资料得分',
        dataIndex: 'material_score',
        key: 'material_score',
        sorter: (a, b) => a.material_score - b.material_score,
      },
      {
        title: '预习题得分',
        dataIndex: 'exercise_score',
        key: 'exercise_score',
        sorter: (a, b) => a.exercise_score - b.exercise_score,
      },
      {
        title: '综合得分',
        dataIndex: 'total_score',
        key: 'total_score',
        sorter: (a, b) => a.total_score - b.total_score,
      },
      {
        title: '观看时长',
        dataIndex: 'view_duration',
        key: 'view_duration',
        sorter: (a, b) => a.view_duration - b.view_duration,
      },
      {
        title: '习题完成情况',
        key: 'exercise_completion',
        customRender: ({ record }) => 
          `${record.correct_exercises}/${record.total_exercises}题正确`
      }
    ];
    
    // 计算属性
    const filteredStudentsData = computed(() => {
      if (!evaluationData.value || !evaluationData.value.students) return [];
      
      if (!searchText.value) {
        return evaluationData.value.students;
      }
      
      return evaluationData.value.students.filter(item => 
        item.name && item.name.toLowerCase().includes(searchText.value.toLowerCase())
      );
    });
    
    // 获取教师的班级列表
    const fetchTeacherClasses = async () => {
      try {
        loading.value = true;
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        if (!userInfo.system_teacher_id) {
          message.error('未找到教师信息');
          return;
        }

        const response = await axios.get(`/api/classes/teacher/${userInfo.system_teacher_id}`);
        if (response.data.success) {
          classList.value = response.data.data;
          
          // 获取上次选中的班级ID
          const lastSelectedClassId = localStorage.getItem('previewEvaluation_lastSelectedClassId');
          if (lastSelectedClassId && response.data.data.find(c => c.id === parseInt(lastSelectedClassId))) {
            currentClass.id = parseInt(lastSelectedClassId);
            await handleClassChange(currentClass.id);
          }
        }
      } catch (error) {
        console.error('获取班级列表失败:', error);
        message.error('获取班级列表失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 获取班级的章节列表
    const fetchClassChapters = async (classId) => {
      try {
        loading.value = true;
        
        const response = await axios.get(`/api/preview-analysis/class/${classId}/chapters`);
        if (response.data.success) {
          chapters.value = response.data.data;
          
          // 获取上次选中的章节ID
          const lastSelectedChapterId = localStorage.getItem('previewEvaluation_lastSelectedChapterId');
          if (lastSelectedChapterId && response.data.data.find(c => c.id === lastSelectedChapterId)) {
            selectedChapter.value = lastSelectedChapterId;
            await fetchClassPreviewEvaluation(classId, selectedChapter.value);
          } else if (chapters.value.length > 0) {
            // 默认选择第一个章节
            selectedChapter.value = chapters.value[0].id;
            await fetchClassPreviewEvaluation(classId, selectedChapter.value);
          }
        }
      } catch (error) {
        console.error('获取班级章节失败:', error);
        message.error('获取班级章节失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 获取班级预习评估数据
    const fetchClassPreviewEvaluation = async (classId, chapterId) => {
      try {
        loading.value = true;
        
        const response = await axios.get(`/api/preview-evaluation/class/${classId}/chapter/${chapterId}`);
        if (response.data.success) {
          evaluationData.value = response.data.data;
          
          // 初始化图表
          nextTick(() => {
            setTimeout(() => {
              initChart();
            }, 300);
          });
        }
      } catch (error) {
        console.error('获取班级预习评估数据失败:', error);
        message.error('获取班级预习评估数据失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 切换班级
    const handleClassChange = async (classId) => {
      if (!classId) return;
      
      currentClass.id = classId;
      selectedChapter.value = null;
      chapters.value = [];
      evaluationData.value = null;
      
      // 保存当前选中的班级ID
      localStorage.setItem('previewEvaluation_lastSelectedClassId', classId);
      
      // 获取班级的章节列表
      await fetchClassChapters(classId);
    };
    
    // 切换章节
    const handleChapterChange = async (chapterId) => {
      if (!chapterId || !currentClass.id) return;
      
      selectedChapter.value = chapterId;
      
      // 保存当前选中的章节ID
      localStorage.setItem('previewEvaluation_lastSelectedChapterId', chapterId);
      
      // 获取班级预习评估数据
      await fetchClassPreviewEvaluation(currentClass.id, chapterId);
    };
    
    // 搜索功能
    const onSearch = () => {
      // 搜索逻辑已通过计算属性实现
    };
    
    // 刷新数据
    const refreshData = async () => {
      if (currentClass.id && selectedChapter.value) {
        await fetchClassPreviewEvaluation(currentClass.id, selectedChapter.value);
      }
    };
    
    // 格式化时长
    const formatDuration = (seconds) => {
      if (!seconds) return '0分钟';
      
      const minutes = Math.floor(seconds / 60);
      return `${minutes}分钟`;
    };
    
    // 获取得分颜色
    const getScoreColor = (score) => {
      if (score >= 90) return 'success';
      if (score >= 80) return 'processing';
      if (score >= 60) return 'warning';
      return 'error';
    };
    
    // 初始化图表
    const initChart = () => {
      if (!evaluationData.value || !scoreChartRef.value) return;
      
      if (scoreChart) {
        scoreChart.dispose();
      }
      
      scoreChart = echarts.init(scoreChartRef.value);
      
      const students = evaluationData.value.students;
      const studentNames = students.map(s => s.name);
      const materialScores = students.map(s => s.material_score);
      const exerciseScores = students.map(s => s.exercise_score);
      const totalScores = students.map(s => s.total_score);
      
      scoreChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['预习资料得分', '预习题得分', '综合得分']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: studentNames,
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value',
          name: '得分',
          max: 100
        },
        series: [
          {
            name: '预习资料得分',
            type: 'bar',
            data: materialScores,
            itemStyle: {
              color: '#52c41a'
            }
          },
          {
            name: '预习题得分',
            type: 'bar',
            data: exerciseScores,
            itemStyle: {
              color: '#1890ff'
            }
          },
          {
            name: '综合得分',
            type: 'bar',
            data: totalScores,
            itemStyle: {
              color: '#722ed1'
            }
          }
        ]
      });
    };
    
    // 组件挂载时初始化
    onMounted(() => {
      fetchTeacherClasses();
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (scoreChart) scoreChart.resize();
      });
    });
    
    return {
      loading,
      classList,
      chapters,
      currentClass,
      selectedChapter,
      evaluationData,
      columns,
      searchText,
      filteredStudentsData,
      scoreChartRef,
      
      handleClassChange,
      handleChapterChange,
      onSearch,
      refreshData,
      formatDuration,
      getScoreColor
    };
  }
});
</script>

<style scoped>
.page-container {
  padding: 24px;
  background: #f8fafc;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page-header h1 {
  font-size: 32px;
  color: #1e293b;
  margin-bottom: 12px;
  font-weight: 600;
}

.subtitle {
  color: #64748b;
  font-size: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.filter-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  overflow: hidden;
}

.filter-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.filter-content {
  padding: 24px;
}

.select-tip {
  margin-top: 8px;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;

  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.stat-title {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.stat-desc {
  font-size: 14px;
  color: #6b7280;
  margin-top: 8px;
}

.chart-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 24px;
}

.chart-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.chart-container {
  height: 400px;
  padding: 16px;
}

.data-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-top: 24px;
}

@media (max-width: 1200px) {
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
}
</style> 
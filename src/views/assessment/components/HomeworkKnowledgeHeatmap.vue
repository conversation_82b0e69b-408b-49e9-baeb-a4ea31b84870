<template>
  <div class="knowledge-points-card">
    <div class="card-header">
      <h3>作业错误知识点分析</h3>
      <div class="header-actions">
        <a-switch
          v-model:checked="enableAiAnalysis"
          checked-children="AI分析开启"
          un-checked-children="AI分析关闭"
          @change="handleAiSwitchChange"
        />
        <a-button
          type="primary"
          :loading="loading"
          @click="refreshData"
          :disabled="!currentClass || !currentChapter || !currentBatch"
        >
          <template #icon><reload-outlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <a-spin :spinning="loading">
      <div v-if="!hasData" class="empty-state">
        <a-empty description="暂无知识点错误数据" />
      </div>

      <template v-else>
        <!-- 热力图展示 -->
        <div class="heatmap-container">
          <div class="heatmap-header">
            <h4>错误率分布热图</h4>
            <div class="legend">
              <div class="legend-item">
                <span class="legend-color" style="background-color: #f5222d;"></span>
                <span>严重 (≥80%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #fa8c16;"></span>
                <span>高 (≥60%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #faad14;"></span>
                <span>中 (≥40%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #52c41a;"></span>
                <span>低 (≥20%)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background-color: #1890ff;"></span>
                <span>很低 (<20%)</span>
              </div>
            </div>
          </div>
          <div id="knowledgeHeatmapRef" ref="knowledgeHeatmapRef" class="heatmap"></div>
        </div>

        <!-- 知识点列表 -->
        <div class="knowledge-list">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(point, index) in knowledgePoints" :key="index">
              <a-card hoverable class="knowledge-card" :class="getErrorLevelClass(point.errorRate)">
                <template #title>
                  <div class="knowledge-title">
                    <span>{{ point.name }}</span>
                    <a-tag :color="getErrorLevelColor(point.errorRate)">
                      错误率: {{ point.errorRate }}%
                    </a-tag>
                  </div>
                </template>
                <div class="knowledge-exercises">
                  <p class="exercise-count">相关错题: {{ point.exercises.length }}道</p>
                  <div class="exercise-list">
                    <a-tooltip v-for="(exercise, i) in point.exercises" :key="i" :title="exercise.title">
                      <a-tag class="exercise-tag">
                        题目{{ i+1 }}: {{ exercise.errorCount }}人错
                      </a-tag>
                    </a-tooltip>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- AI分析结果 -->
        <a-collapse v-if="aiSuggestions" class="ai-suggestions">
          <a-collapse-panel key="1" header="AI教学建议">
            <div class="ai-content">
              <a-alert type="info" show-icon>
                <template #message>AI分析仅供参考，请结合实际情况使用</template>
              </a-alert>
              <div class="markdown-content" v-html="formattedAiSuggestions"></div>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </template>
    </a-spin>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import axios from '@/axios';
import * as echarts from 'echarts';
import { marked } from 'marked';

export default defineComponent({
  components: {
    ReloadOutlined
  },
  props: {
    currentClass: {
      type: [String, Number],
      default: null
    },
    currentChapter: {
      type: [String, Number],
      default: null
    },
    currentBatch: {
      type: [String, Number],
      default: null
    }
  },
  setup(props) {
    const loading = ref(false);
    const knowledgePoints = ref([]);
    const studentCount = ref(0);
    const aiSuggestions = ref(null);
    const enableAiAnalysis = ref(false);

    const knowledgeHeatmapRef = ref(null);
    let knowledgeHeatmap = null;

    // 计算属性
    const hasData = computed(() => knowledgePoints.value && knowledgePoints.value.length > 0);

    const formattedAiSuggestions = computed(() => {
      if (!aiSuggestions.value) return '';
      return marked(aiSuggestions.value);
    });

    // 获取知识点错误热图数据
    const fetchKnowledgePointsData = async () => {
      if (!props.currentClass || !props.currentChapter || !props.currentBatch) {
        return;
      }

      try {
        loading.value = true;

        const response = await axios.get(
          `/api/analysis/homework/knowledge-points/${props.currentClass}/${props.currentChapter}/${props.currentBatch}`,
          { params: { ai: enableAiAnalysis.value } }
        );

        if (response.data.success) {
          knowledgePoints.value = response.data.data.knowledgePoints;
          studentCount.value = response.data.data.studentCount;
          aiSuggestions.value = response.data.data.aiSuggestions;

          nextTick(() => {
            initHeatmap();
          });
        } else {
          message.error('获取知识点数据失败');
        }
      } catch (error) {
        console.error('获取知识点热图数据失败:', error);
        message.error('获取知识点热图数据失败');
      } finally {
        loading.value = false;
      }
    };

    // 初始化热力图
    const initHeatmap = () => {
      if (!knowledgeHeatmapRef.value || !hasData.value) return;

      if (knowledgeHeatmap) {
        knowledgeHeatmap.dispose();
      }

      knowledgeHeatmap = echarts.init(knowledgeHeatmapRef.value);

      const data = knowledgePoints.value.map((point, index) => ({
        name: point.name,
        value: point.errorRate,
        itemStyle: {
          color: getHeatmapColor(point.errorRate)
        }
      }));

      const option = {
        tooltip: {
          formatter: function(params) {
            return `${params.data.name}<br/>错误率: ${params.data.value}%`;
          }
        },
        dataZoom: [
          {
            type: 'inside',
            filterMode: 'none',
            disabled: false
          }
        ],
        toolbox: {
          show: true,
          feature: {
            restore: {
              show: true,
              title: '重置'
            }
          },
          right: 20,
          top: 0
        },
        series: [{
          type: 'treemap',
          roam: true,
          data: data,
          label: {
            show: true,
            formatter: '{b}\n{c}%',
            fontSize: 14
          },
          breadcrumb: {
            show: false
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
            gapWidth: 4
          },
          levels: [
            {
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2,
                gapWidth: 4
              },
              upperLabel: {
                show: false
              }
            }
          ]
        }]
      };

      knowledgeHeatmap.setOption(option);
    };

    // 获取热图颜色
    const getHeatmapColor = (errorRate) => {
      if (errorRate >= 80) return '#f5222d';
      if (errorRate >= 60) return '#fa8c16';
      if (errorRate >= 40) return '#faad14';
      if (errorRate >= 20) return '#52c41a';
      return '#1890ff';
    };

    // 获取错误等级类名
    const getErrorLevelClass = (errorRate) => {
      if (errorRate >= 80) return 'error-level-critical';
      if (errorRate >= 60) return 'error-level-high';
      if (errorRate >= 40) return 'error-level-medium';
      if (errorRate >= 20) return 'error-level-low';
      return 'error-level-minimal';
    };

    // 获取错误等级颜色
    const getErrorLevelColor = (errorRate) => {
      if (errorRate >= 80) return '#f5222d';
      if (errorRate >= 60) return '#fa8c16';
      if (errorRate >= 40) return '#faad14';
      if (errorRate >= 20) return '#52c41a';
      return '#1890ff';
    };

    // 刷新数据
    const refreshData = () => {
      fetchKnowledgePointsData();
    };

    // 处理AI开关变化
    const handleAiSwitchChange = (checked) => {
      enableAiAnalysis.value = checked;
      fetchKnowledgePointsData();
    };

    // 监听属性变化
    watch(
      [() => props.currentClass, () => props.currentChapter, () => props.currentBatch],
      () => {
        if (props.currentClass && props.currentChapter && props.currentBatch) {
          fetchKnowledgePointsData();
        } else {
          knowledgePoints.value = [];
        }
      }
    );

    // 组件挂载时初始化
    onMounted(() => {
      if (props.currentClass && props.currentChapter && props.currentBatch) {
        fetchKnowledgePointsData();
      }

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (knowledgeHeatmap) knowledgeHeatmap.resize();
      });
    });

    return {
      loading,
      knowledgePoints,
      studentCount,
      aiSuggestions,
      enableAiAnalysis,
      hasData,
      formattedAiSuggestions,
      knowledgeHeatmapRef,
      getErrorLevelClass,
      getErrorLevelColor,
      refreshData,
      handleAiSwitchChange
    };
  }
});
</script>

<style scoped>
.knowledge-points-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  overflow: hidden;
}

.card-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.empty-state {
  padding: 48px 0;
  text-align: center;
}

.heatmap-container {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.heatmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.heatmap-header h4 {
  margin: 0;
  font-size: 16px;
  color: #374151;
}

.legend {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.heatmap {
  height: 400px;
  width: 100%;
}

.knowledge-list {
  padding: 24px;
}

.knowledge-card {
  transition: all 0.3s ease;
  height: 100%;
}

.knowledge-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.knowledge-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.knowledge-exercises {
  margin-top: 8px;
}

.exercise-count {
  margin-bottom: 8px;
  color: #6b7280;
  font-size: 14px;
}

.exercise-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.exercise-tag {
  cursor: pointer;
}

.ai-suggestions {
  margin: 0 24px 24px;
}

.ai-content {
  margin-top: 12px;
}

.markdown-content {
  margin-top: 16px;
  line-height: 1.6;
}

/* 错误等级样式 */
.error-level-critical {
  border-top: 3px solid #f5222d;
}

.error-level-high {
  border-top: 3px solid #fa8c16;
}

.error-level-medium {
  border-top: 3px solid #faad14;
}

.error-level-low {
  border-top: 3px solid #52c41a;
}

.error-level-minimal {
  border-top: 3px solid #1890ff;
}

@media (max-width: 768px) {
  .heatmap-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .legend {
    width: 100%;
  }
}
</style> 
<template>
  <div class="grade-analysis">
    <a-spin :spinning="loading">
    <div class="page-header">
        <h1>测验成绩分析</h1>
        <p class="subtitle">分析学生测验习题完成情况和得分统计</p>
      </div>
      
      <!-- 错误提示 -->
      <a-alert
        v-if="error"
        type="error"
        :message="error"
        class="mb-4"
        closable
        @close="error = ''"
      />
      
      <!-- 班级与章节批次选择 -->
      <div class="content-section">
        <div class="filter-card">
          <div class="filter-header">
            <h3>数据筛选</h3>
            <a-button 
              type="link" 
              :loading="loading"
              @click="refreshCurrentClass"
            >
              <template #icon><ReloadOutlined /></template>
              刷新
      </a-button>
    </div>
          <div class="filter-content">
            <a-form layout="vertical">
              <a-form-item label="班级">
                <a-select
                  v-model:value="currentClass.id"
                  style="width: 100%"
                  placeholder="选择班级"
                  @change="switchClass"
                >
                  <a-select-option v-for="item in classList" :key="item.id" :value="item.id">
                    {{ item.class_name }}
                    <a-tag color="blue" size="small">{{ item.semester }}</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="章节">
                <a-select
                  v-model:value="selectedChapter"
                  style="width: 100%"
                  placeholder="选择章节"
                  @change="switchChapter"
                  :disabled="!currentClass.id || chapters.length === 0"
                >
                  <a-select-option v-for="chapter in chapters" :key="chapter.id" :value="chapter.id">
                    {{ chapter.title }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="批次">
                <a-select
                  v-model:value="currentBatchId"
                  style="width: 100%"
                  placeholder="选择批次"
                  @change="switchBatch"
                  :disabled="!currentClass.id || !selectedChapter || homeworkBatches.length === 0"
                >
                  <a-select-option v-for="batch in homeworkBatches" :key="batch.id" :value="batch.id">
                    {{ batch.batch_name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </div>
        </div>

        <!-- 根据是否有数据显示不同内容 -->
        <div v-if="homeworkData.length > 0">
          <!-- 统计概览卡片 -->
          <div class="stats-overview">
            <div class="stat-card">
              <div class="stat-title">当前测验数</div>
              <div class="stat-value">{{ statsData.exerciseCount || 0 }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">平均总分</div>
              <div class="stat-value">{{ getAverageScore() }}分</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">平均用时</div>
              <div class="stat-value">{{ getAverageTime() }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">平均正确率</div>
              <div class="stat-value">{{ getCorrectRate() }}%</div>
            </div>
          </div>
          
          <!-- 分析图表区域 -->
          <div class="chart-section">
            <div class="chart-card">
              <div class="chart-header">
                <h3>学生得分情况</h3>
              </div>
              <div id="scoreChartRef" ref="scoreChartRef" class="chart-container"></div>
            </div>
            
            <div class="chart-card">
              <div class="chart-header">
                <h3>学生用时分布</h3>
              </div>
              <div id="timeChartRef" ref="timeChartRef" class="chart-container"></div>
            </div>
            
            <div class="chart-card">
              <div class="chart-header">
                <h3>题目完成率</h3>
              </div>
              <div id="completionChartRef" ref="completionChartRef" class="chart-container"></div>
            </div>
          </div>
          
          <!-- 表格数据展示 -->
          <div class="data-card">
            <div class="card-header">
              <h3>测验成绩详情</h3>
              <div class="header-actions">
                <a-input-search
                  v-model:value="searchText"
                  placeholder="搜索学生姓名"
                  style="width: 250px"
                  @search="onSearch"
                />
              </div>
            </div>
            <HomeworkKnowledgeHeatmap 
              :current-class="currentClass.id" 
              :current-chapter="selectedChapter" 
              :current-batch="currentBatchId" 
            />
            <a-table
              :columns="columns"
              :data-source="filteredHomeworkData"
              :pagination="{ 
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'],
                showTotal: total => `共 ${total} 条记录`
              }"
              :loading="loading"
              size="middle"
              :scroll="{ x: 'max-content' }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <a-button
                    type="link"
                    @click="viewStudentAnswers(record)"
                    :disabled="!record.is_completed"
                  >
                    查看详情
                  </a-button>
                </template>
              </template>
            </a-table>
          </div>
      </div>
      
        <!-- 数据为空展示 -->
        <div v-else class="empty-state">
          <a-empty
            description="请选择班级、章节和批次查看测验成绩分析"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { Empty } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { h } from 'vue'
import axios from '@/axios'
import * as echarts from 'echarts'
import HomeworkKnowledgeHeatmap from './components/HomeworkKnowledgeHeatmap.vue'

export default {
  components: {
    ReloadOutlined,
    HomeworkKnowledgeHeatmap
  },
  data() {
    return {
      Empty,
      loading: false,
      error: '',
      classList: [],
      chapters: [],
      selectedChapter: null,
      homeworkBatches: [],
      homeworkData: [], // 重命名studentsData为homeworkData
      searchText: '', // 搜索文本
      scoreChartRef: null,
      timeChartRef: null,
      completionChartRef: null,
      scoreChart: null,
      timeChart: null,
      completionChart: null,
      // 当前选中的班级和批次
      currentClass: {
        id: null,
        name: '',
        semester: '',
      },
      currentBatchId: null,
      // 统计数据
      statsData: {
        studentCount: 0,
        completedCount: 0,
        avgScore: 0,
        completionRate: 0
      },
      // 表格列定义
      columns: [
        {
          title: '学生姓名',
          dataIndex: 'student_name',
          key: 'student_name',
        },
        {
          title: '学号',
          dataIndex: 'student_number',
          key: 'student_number',
        },
        {
          title: '完成状态',
          dataIndex: 'completion_status',
          key: 'completion_status',
          customRender: ({ text }) => {
            return {
              props: {
                style: {
                  color: text === '已完成' ? '#52c41a' : '#ff4d4f',
                },
              },
              children: text,
            }
          }
        },
        {
          title: '得分',
          dataIndex: 'score',
          key: 'score',
          sorter: (a, b) => (a.score || 0) - (b.score || 0),
          customRender: ({ text }) => {
            if (text === null || text === undefined) return '-';
            // 保留一位小数
            return parseFloat(text).toFixed(1);
          }
        },
        {
          title: '完成时间',
          dataIndex: 'completion_time',
          key: 'completion_time',
          sorter: (a, b) => {
            if (!a.completion_time) return 1;
            if (!b.completion_time) return -1;
            return new Date(a.completion_time) - new Date(b.completion_time);
          },
          customRender: ({ text }) => {
            if (!text) return '-';
            // 格式化日期为 YYYY-MM-DD HH:mm:ss
            const date = new Date(text);
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
          }
        },
        {
          title: '用时',
          dataIndex: 'time_spent',
          key: 'time_spent',
          sorter: (a, b) => (a.time_spent || 0) - (b.time_spent || 0),
          customRender: ({ text }) => {
            if (!text) return '-';
            // 将毫秒转换为分钟和秒
            const totalSeconds = Math.floor(text / 1000);
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;
            return `${minutes}分${seconds}秒`;
          }
        },
        {
          title: '操作',
          key: 'action',
          width: 120,
          fixed: 'right'
        },
      ]
    };
  },
  computed: {
    // 筛选后的数据
    filteredHomeworkData() {
      if (!this.searchText) {
        return this.homeworkData;
      }
      return this.homeworkData.filter(item => 
        item.student_name && item.student_name.toLowerCase().includes(this.searchText.toLowerCase())
      );
    }
  },
  mounted() {
    this.fetchTeacherClasses();
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  updated() {
    // 组件更新后尝试初始化图表
    if (this.homeworkData.length > 0 && this.$refs.scoreChartRef && this.$refs.timeChartRef && this.$refs.completionChartRef) {
      this.initCharts();
    }
  },
  beforeUnmount() {
    // 组件卸载时移除事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 获取平均分
    getAverageScore() {
      if (!this.homeworkData.length) return '0.0';
      
      // 优先使用后端计算好的平均分
      if (this.statsData.averageScore !== undefined) {
        // 保留一位小数
        return parseFloat(this.statsData.averageScore).toFixed(1);
      }
      
      // 否则前端计算
      let totalScore = 0;
      let count = 0;
      
      this.homeworkData.forEach(item => {
        if (item.score !== null && item.score !== undefined) {
          totalScore += parseFloat(item.score);
          count++;
        }
      });
      
      return count > 0 ? (totalScore / count).toFixed(1) : '0.0';
    },
    
    // 获取平均时间（分钟:秒）
    getAverageTime() {
      if (!this.homeworkData.length) return '0分0秒';
      
      let totalTime = 0;
      let count = 0;
      
      this.homeworkData.forEach(item => {
        if (item.time_spent) {
          totalTime += parseInt(item.time_spent);
          count++;
        }
      });
      
      if (count === 0) return '0分0秒';
      
      // 转换为分钟和秒
      const avgTimeMs = totalTime / count;
      const totalSeconds = Math.floor(avgTimeMs / 1000);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      
      return `${minutes}分${seconds}秒`;
    },
    
    // 获取正确率
    getCorrectRate() {
      if (!this.homeworkData.length) return '0.0';
      
      // 优先使用后端计算好的正确率
      if (this.statsData.averageCorrectRate !== undefined) {
        return this.statsData.averageCorrectRate.toFixed(1);
      }
      
      // 否则前端计算
      let totalCorrect = 0;
      let totalQuestions = 0;
      
      this.homeworkData.forEach(item => {
        if (item.correct_count !== undefined) {
          totalCorrect += item.correct_count;
          totalQuestions += item.total_exercises;
        } else if (item.is_correct !== undefined) {
          totalCorrect += item.is_correct === 1 ? 1 : 0;
          totalQuestions += 1;
        }
      });
      
      return totalQuestions > 0 ? ((totalCorrect / totalQuestions) * 100).toFixed(1) : '0.0';
    },
    
    // 搜索功能
    onSearch() {
      // 搜索逻辑已通过计算属性实现
    },
    
    // 初始化图表
    initCharts() {
      console.log('初始化图表, refs:', this.$refs.scoreChartRef, this.$refs.timeChartRef, this.$refs.completionChartRef);
      
      this.$nextTick(() => {
        // 检查DOM元素是否存在
        if (!this.$refs.scoreChartRef || !this.$refs.timeChartRef || !this.$refs.completionChartRef) {
          console.error('图表DOM元素不存在');
          return;
        }
        
        // 销毁旧图表
        if (this.scoreChart) {
          this.scoreChart.dispose();
          this.scoreChart = null;
        }
        if (this.timeChart) {
          this.timeChart.dispose();
          this.timeChart = null;
        }
        if (this.completionChart) {
          this.completionChart.dispose();
          this.completionChart = null;
        }
        
        // 创建新图表
        this.scoreChart = echarts.init(this.$refs.scoreChartRef);
        this.timeChart = echarts.init(this.$refs.timeChartRef);
        this.completionChart = echarts.init(this.$refs.completionChartRef);
        
        // 更新图表数据
        this.updateCharts();
      });
    },
    
    // 更新图表
    updateCharts() {
      if (!this.homeworkData.length) {
        console.warn('没有数据用于更新图表');
        return;
      }
      
      console.log('更新图表数据:', this.homeworkData.length, '条记录');
      
      // 准备得分图表数据
      const scoreData = this.homeworkData
        .filter(student => student.student_name) // 确保有学生姓名
        .map(student => ({
          name: student.student_name,
          value: student.score || 0
        }));
      
      console.log('得分图表数据:', scoreData);
      
      // 准备时间图表数据
      const timeData = this.homeworkData
        .filter(student => student.student_name && student.time_spent)
        .map(student => {
          const seconds = Math.floor(student.time_spent / 1000);
          const minutes = Math.floor(seconds / 60);
          return {
            name: student.student_name,
            value: parseFloat((seconds / 60).toFixed(1)) // 转换为分钟(小数)
          };
        });
      
      console.log('时间图表数据:', timeData);
      
      // 准备完成率饼图数据
      const completedCount = this.homeworkData.filter(student => student.is_completed).length;
      const notCompletedCount = this.homeworkData.length - completedCount;
      const completionRate = this.homeworkData.length > 0 
        ? Math.round((completedCount / this.homeworkData.length) * 100) 
        : 0;
      
      // 设置得分图表
      if (this.scoreChart) {
        this.scoreChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: scoreData.map(item => item.name),
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value',
            name: '分数'
          },
          series: [
            {
              name: '得分',
              type: 'bar',
              data: scoreData.map(item => item.value),
              itemStyle: {
                color: function(params) {
                  // 根据分数设置不同颜色
                  const value = params.value;
                  if (value >= 90) return '#52c41a';
                  if (value >= 80) return '#1890ff';
                  if (value >= 60) return '#faad14';
                  return '#ff4d4f';
                }
              }
            }
          ]
        });
      }
      
      // 设置时间图表
      if (this.timeChart) {
        this.timeChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: timeData.map(item => item.name),
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value',
            name: '时间(分钟)'
          },
          series: [
            {
              name: '完成时间',
              type: 'bar',
              data: timeData.map(item => item.value),
              itemStyle: {
                color: '#1890ff'
              }
            }
          ]
        });
      }
      
      // 设置完成率饼图
      if (this.completionChart) {
        this.completionChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 10,
            data: ['已完成', '未完成']
          },
          series: [
            {
              name: '完成情况',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                position: 'center',
                fontSize: 20,
                fontWeight: 'bold',
                formatter: `完成率\n${completionRate}%`
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 22,
                  fontWeight: 'bold',
                  formatter: `完成率\n${completionRate}%`
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: completedCount, name: '已完成', itemStyle: { color: '#52c41a' } },
                { value: notCompletedCount, name: '未完成', itemStyle: { color: '#ff4d4f' } }
              ]
            }
          ]
        });
      }
    },
    
    // 窗口大小变化时重绘图表
    handleResize() {
      if (this.scoreChart) {
        this.scoreChart.resize();
      }
      if (this.timeChart) {
        this.timeChart.resize();
      }
      if (this.completionChart) {
        this.completionChart.resize();
      }
    },
    
    // 获取教师的班级列表
    async fetchTeacherClasses() {
      try {
        this.loading = true;
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        if (!userInfo.system_teacher_id) {
          throw new Error('未找到教师信息');
        }

        const response = await axios.get(`/api/classes/teacher/${userInfo.system_teacher_id}`);
        if (response.data.success) {
          this.classList = response.data.data;
          
          // 获取上次选中的班级ID
          const lastSelectedClassId = localStorage.getItem('gradeAnalysis_lastSelectedClassId');
          if (lastSelectedClassId && response.data.data.find(c => c.id === parseInt(lastSelectedClassId))) {
            this.currentClass.id = parseInt(lastSelectedClassId);
            await this.switchClass(this.currentClass.id);
          }
        }
      } catch (error) {
        console.error('获取班级列表失败:', error);
        this.error = error.response?.data?.message || '获取班级列表失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 获取班级章节列表
    async fetchClassChapters(classId) {
      try {
        this.loading = true;
        
        const response = await axios.get(`/api/analysis/class/${classId}/chapters`);
        if (response.data.success) {
          this.chapters = response.data.data;
          
          // 获取上次选中的章节ID
          const lastSelectedChapterId = localStorage.getItem('gradeAnalysis_lastSelectedChapterId');
          if (lastSelectedChapterId && response.data.data.find(c => c.id === lastSelectedChapterId)) {
            this.selectedChapter = lastSelectedChapterId;
            await this.fetchChapterBatches(classId, this.selectedChapter);
          } else if (this.chapters.length > 0) {
            // 默认选择第一个章节
            this.selectedChapter = this.chapters[0].id;
            await this.fetchChapterBatches(classId, this.selectedChapter);
          }
        }
      } catch (error) {
        console.error('获取班级章节失败:', error);
        this.error = error.response?.data?.message || '获取班级章节失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 获取章节批次
    async fetchChapterBatches(classId, chapterId) {
      try {
        this.loading = true;
        this.homeworkBatches = [];
        this.currentBatchId = null;
        
        if (!classId || !chapterId) return;
        
        const response = await axios.get(`/api/analysis/class/${classId}/chapter/${chapterId}/batches`);
        if (response.data.success) {
          this.homeworkBatches = response.data.data;
          console.log('获取到批次数据:', this.homeworkBatches);
          
          // 如果有批次，默认选择第一个
          if (this.homeworkBatches.length > 0) {
            this.currentBatchId = this.homeworkBatches[0].id;
            await this.fetchHomeworkAnalysis();
          }
        }
      } catch (error) {
        console.error('获取章节批次失败:', error);
        this.error = error.response?.data?.message || '获取章节批次失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 切换班级
    async switchClass(classId) {
      try {
        this.loading = true;
        this.selectedChapter = null;
        this.currentBatchId = null;
        this.chapters = [];
        this.homeworkBatches = [];
        this.homeworkData = [];
        
        if (!classId) return;
        
        // 获取班级信息
        const classInfo = this.classList.find(item => item.id === classId);
        if (classInfo) {
          this.currentClass = {
            id: classInfo.id,
            name: classInfo.class_name,
            semester: classInfo.semester
          };
          
          // 保存当前选中的班级ID
          localStorage.setItem('gradeAnalysis_lastSelectedClassId', classId);
          
          // 获取该班级的章节列表
          await this.fetchClassChapters(classId);
        }
      } catch (error) {
        console.error('切换班级失败:', error);
        this.error = error.response?.data?.message || '切换班级失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 切换章节
    async switchChapter(chapterId) {
      if (!chapterId || !this.currentClass.id) return;
      
      this.selectedChapter = chapterId;
      localStorage.setItem('gradeAnalysis_lastSelectedChapterId', chapterId);
      
      // 获取该章节的批次列表
      await this.fetchChapterBatches(this.currentClass.id, chapterId);
    },
    
    // 切换批次
    async switchBatch(batchId) {
      if (!batchId) return;
      this.currentBatchId = batchId;
      await this.fetchHomeworkAnalysis();
    },
    
    // 刷新班级数据
    async refreshCurrentClass() {
      if (this.currentClass?.id) {
        await this.switchClass(this.currentClass.id);
      }
    },
    
    // 获取作业分析数据
    async fetchHomeworkAnalysis() {
      if (!this.currentClass.id || !this.selectedChapter || !this.currentBatchId) return;
      
      try {
        this.loading = true;
        this.error = '';
        
        // 获取学生完成情况数据
        const response = await axios.get(`/api/analysis/homework/${this.currentClass.id}/${this.selectedChapter}/${this.currentBatchId}`);
        
        if (response.data.success) {
          console.log('获取到分析数据:', response.data.data);
          
          this.homeworkData = response.data.data.students.map(student => ({
            ...student,
            completion_status: student.is_completed ? '已完成' : '未完成',
          }));
          
          this.statsData = {
            studentCount: response.data.data.statsData.totalStudents || 0,
            completedCount: response.data.data.statsData.completedStudents || 0,
            avgScore: response.data.data.statsData.averageScore || 0,
            completionRate: response.data.data.statsData.completionRate || 0,
            averageScore: response.data.data.statsData.averageScore || 0,
            averageCorrectRate: response.data.data.statsData.averageCorrectRate || 0,
            exerciseCount: response.data.data.statsData.exerciseCount || 0
          };
          
          // 初始化/更新图表
          this.$nextTick(() => {
            setTimeout(() => {
              this.initCharts();
            }, 500); // 延迟确保DOM已经渲染
          });
        }
      } catch (error) {
        console.error('获取作业分析数据失败:', error);
        this.error = error.response?.data?.message || '获取作业分析数据失败';
      } finally {
        this.loading = false;
      }
    },
    viewStudentAnswers(record) {
      // 实现查看答案详情的逻辑
      console.log('查看答案详情:', record);
      this.$router.push({
        path: `/assessment/student-answers/${this.currentClass.id}/${this.selectedChapter}/${this.currentBatchId}/${record.student_number}`,
        query: {
          studentName: record.student_name
        }
      });
    }
  }
}
</script>

<style scoped>
.grade-analysis {
  padding: 32px;
  background: #f8fafc;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 40px;
  text-align: center;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page-header h1 {
  font-size: 32px;
  color: #1e293b;
  margin-bottom: 12px;
  font-weight: 600;
}

.subtitle {
  color: #64748b;
  font-size: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.content-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  background: transparent;
}

.filter-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  overflow: hidden;
}

.filter-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.filter-content {
  padding: 24px;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.stat-title {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.data-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.card-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.analysis-container {
  margin-top: 32px;
}

.chart-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.chart-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.chart-container {
  height: 400px;
  padding: 16px;
  width: 100%;
}

.student-list-section {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  padding: 24px;
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h3 {
  font-size: 18px;
  color: #1f2937;
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-section {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .chart-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .grade-analysis {
    padding: 16px;
  }
  
  .class-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .class-header .class-actions {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .class-actions .ant-select {
    width: 100% !important;
    margin-right: 0 !important;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
}
</style> 
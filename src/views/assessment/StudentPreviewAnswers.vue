<template>
  <div class="assessment-page">
    <a-spin :spinning="loading">
      <div class="assessment-header">
        <h1>学生预习详情</h1>
        <div class="header-actions">
          <a-button type="primary" @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回预习分析
          </a-button>
        </div>
        <p class="subtitle">
          学生: <strong>{{ studentName }}</strong> |
          章节: <strong>{{ chapterTitle }}</strong>
        </p>
      </div>

      <div class="assessment-content">
        <!-- 错误提示 -->
        <a-alert
          v-if="error"
          type="error"
          :message="error"
          class="mb-4"
          closable
          @close="error = ''"
        />

        <!-- 学生预习概览 -->
        <div class="content-section" v-if="previewData">
          <div class="assessment-card">
            <div class="assessment-card-header">
              <h3>预习概览</h3>
            </div>
            <div class="assessment-card-content">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic
                    title="预习状态"
                    :value="previewData.stats.status === 1 ? '已完成' : '未完成'"
                    :value-style="{ color: previewData.stats.status === 1 ? '#52c41a' : '#ff4d4f' }"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="阅读时长"
                    :value="previewData.stats.view_minutes"
                    suffix="分钟"
                    :value-style="{ color: '#1890ff' }"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="习题正确率"
                    :value="previewData.stats.correct_rate"
                    suffix="%"
                    :value-style="{ color: getCompletionColor(previewData.stats.correct_rate) }"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="查看次数"
                    :value="previewData.stats.view_count || 0"
                    :value-style="{ color: '#722ed1' }"
                  />
                </a-col>
              </a-row>
            </div>
          </div>

          <!-- 预习材料查看记录 -->
          <div class="assessment-card">
            <div class="assessment-card-header">
              <h3>预习材料查看记录</h3>
            </div>
            <div class="assessment-card-content">
              <a-table
                :columns="viewLogsColumns"
                :data-source="previewData.view_logs"
                :pagination="{ pageSize: 5 }"
                size="middle"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'view_time'">
                    {{ formatDate(record.view_time) }}
                  </template>
                  <template v-if="column.key === 'duration_minutes'">
                    {{ record.duration_minutes }}分钟
                  </template>
                </template>
              </a-table>
            </div>
          </div>

          <!-- 题目导航 - 固定在右侧 -->
          <div class="question-navigator" v-if="previewData.exercises.length > 0">
            <div class="nav-title">题目导航</div>
            <div class="nav-buttons">
              <div
                v-for="(exercise, index) in previewData.exercises"
                :key="index"
                class="nav-button"
                :class="{
                  'correct-btn': exercise.is_correct,
                  'wrong-btn': exercise.is_answered && !exercise.is_correct,
                  'unanswered-btn': !exercise.is_answered,
                  'active': activeKeys.includes(index)
                }"
                @click="scrollToQuestion(index)"
              >
                {{ index + 1 }}
              </div>
            </div>
          </div>

          <!-- 预习习题详情 -->
          <div class="assessment-card question-details" v-if="previewData.exercises.length > 0">
            <div class="assessment-card-header">
              <h3>预习习题详情</h3>
            </div>
            <div class="assessment-card-content">
              <a-collapse v-model:activeKey="activeKeys" class="custom-collapse">
                <a-collapse-panel
                  v-for="(exercise, index) in previewData.exercises"
                  :key="index"
                  :header="getExerciseHeader(exercise, index)"
                  :id="`question-${index}`"
                >
                  <div class="exercise-content">
                    <!-- 题目内容 -->
                    <div class="question">
                      <div class="question-header">
                        <div class="question-status">
                          <a-tag :color="getAnswerStatusColor(exercise)">
                            {{ getAnswerStatusText(exercise) }}
                          </a-tag>
                        </div>
                      </div>

                      <div class="question-body">
                        <div class="question-content">{{ exercise.content }}</div>

                        <!-- 答题情况 -->
                        <div class="question-answer">
                          <div class="answer-section">
                            <div class="answer-label">正确答案:</div>
                            <div class="answer-content correct">{{ exercise.answer || '未设置答案' }}</div>
                          </div>
                          <div class="answer-section">
                            <div class="answer-label">学生答案:</div>
                            <div class="answer-content" :class="{ 'student-correct': exercise.is_correct, 'student-wrong': exercise.is_answered && !exercise.is_correct }">
                              {{ exercise.student_answer || '未作答' }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 答题时间 -->
                    <div class="time-section" v-if="exercise.submit_time">
                      <div class="time-label">提交时间:</div>
                      <div class="time-value">{{ formatDate(exercise.submit_time) }}</div>
                    </div>

                    <!-- 用时 -->
                    <div class="time-section" v-if="exercise.time_spent">
                      <div class="time-label">用时:</div>
                      <div class="time-value">{{ formatTimeSpent(exercise.time_spent) }}</div>
                    </div>
                  </div>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </div>
          
          <!-- 无习题提示 -->
          <div class="assessment-card" v-else>
            <div class="assessment-card-content">
              <a-empty description="该章节暂无预习习题" />
            </div>
          </div>
        </div>

        <!-- 数据为空展示 -->
        <div v-else-if="!loading" class="empty-state assessment-card">
          <a-empty
            description="未找到该学生的预习记录"
          />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { CheckOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
import axios from '@/axios';

export default defineComponent({
  components: {
    CheckOutlined,
    ArrowLeftOutlined
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const { classId, chapterId, studentId } = route.params;
    const studentName = route.query.studentName || '未知学生';

    // 状态定义
    const loading = ref(true);
    const error = ref('');
    const previewData = ref(null);
    const activeKeys = ref([0]); // 默认展开第一题
    const chapterTitle = ref('');

    // 表格列定义
    const viewLogsColumns = [
      {
        title: '查看时间',
        dataIndex: 'view_time',
        key: 'view_time',
      },
      {
        title: '查看时长',
        dataIndex: 'duration_minutes',
        key: 'duration_minutes',
      }
    ];

    // 获取预习详情
    const fetchStudentPreviewData = async () => {
      try {
        loading.value = true;

        // 先获取章节名称
        const chapterResponse = await axios.get(`/api/preview-analysis/class/${classId}/chapters`);
        if (chapterResponse.data.success) {
          const chapter = chapterResponse.data.data.find(c => c.id === chapterId);
          if (chapter) {
            chapterTitle.value = chapter.title;
          }
        }

        // 获取学生预习详情
        const response = await axios.get(`/api/preview-analysis/student-preview/${classId}/${chapterId}/${studentId}`);

        if (response.data.success) {
          previewData.value = response.data.data;
        } else {
          error.value = response.data.message || '获取预习详情失败';
        }
      } catch (err) {
        console.error('获取学生预习详情失败:', err);
        error.value = err.response?.data?.message || '获取预习详情失败';
      } finally {
        loading.value = false;
      }
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    };

    // 格式化用时
    const formatTimeSpent = (milliseconds) => {
      if (!milliseconds) return '0秒';
      const totalSeconds = Math.floor(milliseconds / 1000);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      return minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`;
    };

    // 获取题目标题
    const getExerciseHeader = (exercise, index) => {
      return `第 ${index + 1} 题 - ${exercise.title || '预习题'}`;
    };

    // 获取答案状态颜色
    const getAnswerStatusColor = (exercise) => {
      if (!exercise.is_answered) {
        return 'default';
      }

      if (exercise.is_correct) {
        return 'success';
      } else {
        return 'error';
      }
    };

    // 获取答案状态文本
    const getAnswerStatusText = (exercise) => {
      if (!exercise.is_answered) {
        return '未作答';
      }

      if (exercise.is_correct) {
        return '正确';
      } else {
        return '错误';
      }
    };

    // 获取完成率颜色
    const getCompletionColor = (rate) => {
      if (rate >= 80) return '#52c41a';
      if (rate >= 60) return '#faad14';
      return '#ff4d4f';
    };

    // 滚动到指定题目
    const scrollToQuestion = (index) => {
      // 确保该题目在activeKeys中
      if (!activeKeys.value.includes(index)) {
        activeKeys.value = [...activeKeys.value, index];
      }

      // 滚动到题目位置
      setTimeout(() => {
        const element = document.getElementById(`question-${index}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 300);
    };

    // 返回上一页
    const goBack = () => {
      router.back();
    };

    onMounted(() => {
      if (!classId || !chapterId || !studentId) {
        error.value = '参数不完整，无法获取学生预习详情';
        loading.value = false;
        return;
      }

      fetchStudentPreviewData();
    });

    return {
      loading,
      error,
      previewData,
      activeKeys,
      studentName,
      chapterTitle,
      viewLogsColumns,
      formatDate,
      formatTimeSpent,
      getExerciseHeader,
      getAnswerStatusColor,
      getAnswerStatusText,
      getCompletionColor,
      scrollToQuestion,
      goBack
    };
  }
});
</script>

<style scoped>
.assessment-page {
  padding: 24px;
  background: #f8fafc;
  min-height: calc(100vh - 64px);
}

.assessment-header {
  margin-bottom: 24px;
  text-align: center;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.assessment-header h1 {
  font-size: 28px;
  color: #1e293b;
  margin-bottom: 16px;
}

.subtitle {
  color: #64748b;
  margin-top: 12px;
}

.header-actions {
  margin-bottom: 16px;
}

.assessment-content {
  max-width: 1200px;
  margin: 0 auto;
}

.assessment-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.assessment-card-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.assessment-card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.assessment-card-content {
  padding: 24px;
}

/* 题目导航 */
.question-navigator {
  position: fixed;
  right: 32px;
  top: 250px;
  width: 70px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 12px 8px;
  z-index: 100;
}

.nav-title {
  text-align: center;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  font-size: 14px;
}

.nav-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
  padding: 0 5px;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 28px;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  border: 1px solid #d9d9d9;
}

.nav-button:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
  transform: scale(1.05);
}

.nav-button.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.nav-button.correct-btn {
  background-color: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
}

.nav-button.wrong-btn {
  background-color: #fff2f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.nav-button.unanswered-btn {
  background-color: #f5f5f5;
  color: #999;
}

/* 调整内容区域宽度，为右侧导航栏留出空间 */
.question-details {
  margin-right: 80px;
}

/* 美化题目展示 */
.custom-collapse :deep(.ant-collapse-header) {
  background-color: #f6f8fa;
  border-radius: 4px;
  padding: 12px 16px;
  font-weight: 500;
}

.custom-collapse :deep(.ant-collapse-content-box) {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 0 0 4px 4px;
}

.question {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.question-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
  align-items: center;
}

.question-content {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.answer-section {
  margin-bottom: 16px;
  background-color: white;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.answer-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.answer-content {
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.answer-content.correct {
  border-left: 4px solid #52c41a;
}

.student-correct {
  border-left: 4px solid #52c41a;
}

.student-wrong {
  border-left: 4px solid #ff4d4f;
}

.time-section {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background-color: white;
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.time-label {
  font-weight: 500;
  color: #666;
  width: 80px;
}

.time-value {
  color: #1890ff;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

@media (max-width: 768px) {
  .assessment-page {
    padding: 16px;
  }
  
  .question-navigator {
    display: none;
  }
  
  .question-details {
    margin-right: 0;
  }
}
</style> 
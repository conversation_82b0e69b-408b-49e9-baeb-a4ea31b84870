<template>
  <div class="plagiarism-analysis">
    <a-spin :spinning="loading">
      <div class="page-header">
        <h1>查重分析</h1>
        <p class="subtitle">分析学生简答题的相似度，检测潜在的抄袭行为</p>
        <p class="info-text">选择班级、章节和批次后将自动显示查重结果，点击"重新进行查重分析"按钮可以重新检测并更新结果。</p>
      </div>
      
      <!-- 错误提示 -->
      <a-alert
        v-if="error"
        type="error"
        :message="error"
        class="mb-4"
        closable
        @close="error = ''"
      />
      
      <!-- 班级与章节批次选择 -->
      <div class="content-section">
        <div class="filter-card">
          <div class="filter-header">
            <h3>数据筛选</h3>
            <a-button 
              type="link" 
              :loading="loading"
              @click="refreshCurrentClass"
            >
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </div>
          <div class="filter-content">
            <a-form layout="vertical">
              <a-form-item label="班级">
                <a-select
                  v-model:value="currentClass.id"
                  style="width: 100%"
                  placeholder="选择班级"
                  @change="switchClass"
                >
                  <a-select-option v-for="item in classList" :key="item.id" :value="item.id">
                    {{ item.class_name }}
                    <a-tag color="blue" size="small">{{ item.semester }}</a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="章节">
                <a-select
                  v-model:value="selectedChapter"
                  style="width: 100%"
                  placeholder="选择章节"
                  @change="switchChapter"
                  :disabled="!currentClass.id || chapters.length === 0"
                >
                  <a-select-option v-for="chapter in chapters" :key="chapter.id" :value="chapter.id">
                    {{ chapter.title }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="批次">
                <a-select
                  v-model:value="currentBatchId"
                  style="width: 100%"
                  placeholder="选择批次"
                  @change="switchBatch"
                  :disabled="!currentClass.id || !selectedChapter || homeworkBatches.length === 0"
                >
                  <a-select-option v-for="batch in homeworkBatches" :key="batch.id" :value="batch.id">
                    {{ batch.batch_name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </div>
          <div class="filter-action">
            <a-button 
              type="primary" 
              size="large"
              block
              :loading="runningCheck"
              @click="runPlagiarismCheck"
              title="重新检测并覆盖已有查重记录"
            >
              <template #icon><ScanOutlined /></template>
              重新进行查重分析
            </a-button>
          </div>
        </div>

        <!-- 根据是否有数据显示不同内容 -->
        <div v-if="plagiarismData.length > 0">
          <!-- 统计概览卡片 -->
          <div class="stats-overview">
            <div class="stat-card">
              <div class="stat-title">简答题数量</div>
              <div class="stat-value">{{ statsData.totalQuestions || 0 }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">检测学生数</div>
              <div class="stat-value">{{ statsData.totalStudents || 0 }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">相似度警告</div>
              <div class="stat-value danger">{{ statsData.highSimilarityCount || 0 }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">警告率</div>
              <div class="stat-value danger">{{ getWarningRate() }}%</div>
            </div>
          </div>
    
          <!-- 表格数据展示 -->
          <div class="data-card">
            <div class="card-header">
              <h3>查重结果详情</h3>
              <div class="header-actions">
                <a-input-search
                  v-model:value="searchText"
                  placeholder="搜索学生姓名"
                  style="width: 250px"
                  @search="onSearch"
                />
              </div>
            </div>
            <a-table
              :columns="columns"
              :data-source="filteredPlagiarismData"
              :pagination="{ 
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'],
                showTotal: total => `共 ${total} 条记录`
              }"
              :loading="loading"
              size="middle"
              :scroll="{ x: 'max-content' }"
              :expandable="expandableConfig"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'similarity_score'">
                  <div class="similarity-bar">
                    <a-progress 
                      :percent="record.similarity_score * 100" 
                      :strokeColor="getSimilarityColor(record.similarity_score)"
                      :format="percent => `${(percent).toFixed(1)}%`"
                    />
                  </div>
                </template>
                <template v-if="column.dataIndex === 'status'">
                  <a-tag :color="record.similarity_score >= 0.85 ? 'red' : record.similarity_score >= 0.65 ? 'orange' : 'green'">
                    {{ getSimilarityLevel(record.similarity_score) }}
                  </a-tag>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <a-button type="link" @click="showAnswerDetail(record)">查看详情</a-button>
                </template>
              </template>
            </a-table>
          </div>
        </div>
        
        <!-- 数据为空展示 -->
        <div v-else class="empty-state">
          <a-empty
            description="请选择班级、章节和批次查看查重分析结果"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </div>
      </div>
    </a-spin>

    <!-- 答案详情弹窗 -->
    <a-modal
      v-model:visible="answerModalVisible"
      title="答案详情对比"
      width="80%"
      :footer="null"
      @cancel="answerModalVisible = false"
    >
      <div class="answer-detail-modal">
        <div class="answer-comparison">
          <div class="answer-container">
            <h4>{{ currentAnswerDetail.student1_name }} ({{ currentAnswerDetail.student1_id }}) 的答案</h4>
            <pre class="answer-text">{{ currentAnswerDetail.student1_answer }}</pre>
          </div>
          <div class="answer-container">
            <h4>{{ currentAnswerDetail.student2_name }} ({{ currentAnswerDetail.student2_id }}) 的答案</h4>
            <pre class="answer-text">{{ currentAnswerDetail.student2_answer }}</pre>
          </div>
          <div class="question-container">
            <h4>题目内容</h4>
            <div class="question-text" v-html="currentAnswerDetail.exercise_content"></div>
          </div>
          <div class="similarity-info">
            <h4>相似度信息</h4>
            <div class="similarity-score">
              <span>相似度：</span>
              <a-progress 
                :percent="currentAnswerDetail.similarity_score * 100" 
                :strokeColor="getSimilarityColor(currentAnswerDetail.similarity_score)"
                :format="percent => `${(percent).toFixed(1)}%`"
              />
            </div>
            <div class="similarity-level">
              <span>相似度等级：</span>
              <a-tag :color="currentAnswerDetail.similarity_score >= 0.85 ? 'red' : currentAnswerDetail.similarity_score >= 0.65 ? 'orange' : 'green'">
                {{ getSimilarityLevel(currentAnswerDetail.similarity_score) }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { Empty } from 'ant-design-vue'
import { ReloadOutlined, ScanOutlined } from '@ant-design/icons-vue'
import { h } from 'vue'
import axios from '@/axios'
import { Modal } from 'ant-design-vue';

export default {
  name: 'PlagiarismAnalysis',
  components: {
    ReloadOutlined,
    ScanOutlined,
  },
  data() {
    return {
      Empty,
      loading: false,
      runningCheck: false, // 运行查重状态
      error: '',
      classList: [],
      chapters: [],
      selectedChapter: null,
      homeworkBatches: [],
      plagiarismData: [], // 查重数据
      searchText: '', // 搜索文本
      
      // 当前选中的班级和批次
      currentClass: {
        id: null,
        name: '',
        semester: '',
      },
      currentBatchId: null,
      
      // 统计数据
      statsData: {
        totalQuestions: 0,
        totalStudents: 0,
        highSimilarityCount: 0,
      },
      
      // 查重阈值
      thresholds: {
        HIGH: 0.85,
        MEDIUM: 0.65,
        LOW: 0.45
      },
      
      // 表格列定义
      columns: [
        {
          title: '题目ID',
          dataIndex: 'exercise_id',
          key: 'exercise_id',
          width: 100,
        },
        {
          title: '题目类型',
          dataIndex: 'question_type_name',
          key: 'question_type_name',
          width: 120,
        },
        {
          title: '学生1',
          dataIndex: 'student1_name',
          key: 'student1_name',
          width: 120,
        },
        {
          title: '学号1',
          dataIndex: 'student1_id',
          key: 'student1_id',
          width: 120,
        },
        {
          title: '学生2',
          dataIndex: 'student2_name',
          key: 'student2_name',
          width: 120,
        },
        {
          title: '学号2',
          dataIndex: 'student2_id',
          key: 'student2_id',
          width: 120,
        },
        {
          title: '相似度',
          dataIndex: 'similarity_score',
          key: 'similarity_score',
          sorter: (a, b) => a.similarity_score - b.similarity_score,
          defaultSortOrder: 'descend',
          width: 200,
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 120,
          filters: [
            { text: '高度相似', value: 'HIGH' },
            { text: '中度相似', value: 'MEDIUM' },
            { text: '低度相似', value: 'LOW' },
          ],
          onFilter: (value, record) => this.getSimilarityLevel(record.similarity_score) === value,
        },
        {
          title: '创建时间',
          dataIndex: 'created_at',
          key: 'created_at',
          width: 180,
          sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at),
          customRender: ({ text }) => {
            if (!text) return '-';
            // 格式化日期
            const date = new Date(text);
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
          width: 100,
          fixed: 'right',
        },
      ],
      
      // 可展开配置
      expandableConfig: {
        expandedRowRender: record => {
          return h('div', { class: 'answer-comparison' }, [
            h('div', { class: 'answer-container' }, [
              h('h4', '学生1答案'),
              h('pre', { class: 'answer-text' }, record.student1_answer)
            ]),
            h('div', { class: 'answer-container' }, [
              h('h4', '学生2答案'),
              h('pre', { class: 'answer-text' }, record.student2_answer)
            ]),
            h('div', { class: 'question-container' }, [
              h('h4', '题目内容'),
              h('div', { 
                class: 'question-text', 
                innerHTML: record.exercise_content 
              })
            ])
          ]);
        }
      },
      answerModalVisible: false,
      currentAnswerDetail: {},
    };
  },
  computed: {
    // 筛选后的数据
    filteredPlagiarismData() {
      if (!this.searchText) {
        return this.plagiarismData;
      }
      return this.plagiarismData.filter(item => 
        (item.student1_name && item.student1_name.toLowerCase().includes(this.searchText.toLowerCase())) ||
        (item.student2_name && item.student2_name.toLowerCase().includes(this.searchText.toLowerCase()))
      );
    }
  },
  mounted() {
    this.fetchTeacherClasses();
  },
  methods: {
    // 获取警告率
    getWarningRate() {
      if (!this.statsData.totalStudents || this.statsData.totalStudents === 0) return '0.0';
      
      // 使用相似度警告数除以学生总数计算警告率
      const rate = (this.statsData.highSimilarityCount / this.statsData.totalStudents) * 100;
      return rate.toFixed(1);
    },
    
    // 获取相似度颜色
    getSimilarityColor(score) {
      if (score >= this.thresholds.HIGH) return '#ff4d4f';
      if (score >= this.thresholds.MEDIUM) return '#faad14';
      if (score >= this.thresholds.LOW) return '#52c41a';
      return '#52c41a';
    },
    
    // 获取相似度级别
    getSimilarityLevel(score) {
      if (score >= this.thresholds.HIGH) return 'HIGH';
      if (score >= this.thresholds.MEDIUM) return 'MEDIUM';
      if (score >= this.thresholds.LOW) return 'LOW';
      return 'NONE';
    },
    
    // 搜索功能
    onSearch() {
      // 搜索逻辑已通过计算属性实现
    },
    
    // 获取教师的班级列表
    async fetchTeacherClasses() {
      try {
        this.loading = true;
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        if (!userInfo.system_teacher_id) {
          throw new Error('未找到教师信息');
        }

        const response = await axios.get(`/api/classes/teacher/${userInfo.system_teacher_id}`);
        if (response.data.success) {
          this.classList = response.data.data;
          
          // 获取上次选中的班级ID
          const lastSelectedClassId = localStorage.getItem('plagiarismAnalysis_lastSelectedClassId');
          if (lastSelectedClassId && response.data.data.find(c => c.id === parseInt(lastSelectedClassId))) {
            this.currentClass.id = parseInt(lastSelectedClassId);
            await this.switchClass(this.currentClass.id);
          }
        }
      } catch (error) {
        console.error('获取班级列表失败:', error);
        this.error = error.response?.data?.message || '获取班级列表失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 获取班级章节列表
    async fetchClassChapters(classId) {
      try {
        this.loading = true;
        
        const response = await axios.get(`/api/analysis/class/${classId}/chapters`);
        if (response.data.success) {
          this.chapters = response.data.data;
          
          // 获取上次选中的章节ID
          const lastSelectedChapterId = localStorage.getItem('plagiarismAnalysis_lastSelectedChapterId');
          if (lastSelectedChapterId && response.data.data.find(c => c.id === lastSelectedChapterId)) {
            this.selectedChapter = lastSelectedChapterId;
            await this.fetchChapterBatches(classId, this.selectedChapter);
          } else if (this.chapters.length > 0) {
            // 默认选择第一个章节
            this.selectedChapter = this.chapters[0].id;
            await this.fetchChapterBatches(classId, this.selectedChapter);
          }
        }
      } catch (error) {
        console.error('获取班级章节失败:', error);
        this.error = error.response?.data?.message || '获取班级章节失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 获取章节批次
    async fetchChapterBatches(classId, chapterId) {
      try {
        this.loading = true;
        this.homeworkBatches = [];
        this.currentBatchId = null;
        
        if (!classId || !chapterId) return;
        
        const response = await axios.get(`/api/analysis/class/${classId}/chapter/${chapterId}/batches`);
        if (response.data.success) {
          this.homeworkBatches = response.data.data;
          console.log('获取到批次数据:', this.homeworkBatches);
          
          // 如果有批次，默认选择第一个
          if (this.homeworkBatches.length > 0) {
            this.currentBatchId = this.homeworkBatches[0].id;
            await this.fetchPlagiarismAnalysis();
          }
        }
      } catch (error) {
        console.error('获取章节批次失败:', error);
        this.error = error.response?.data?.message || '获取章节批次失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 切换班级
    async switchClass(classId) {
      try {
        this.loading = true;
        this.selectedChapter = null;
        this.currentBatchId = null;
        this.chapters = [];
        this.homeworkBatches = [];
        this.plagiarismData = [];
        
        if (!classId) return;
        
        // 获取班级信息
        const classInfo = this.classList.find(item => item.id === classId);
        if (classInfo) {
          this.currentClass = {
            id: classInfo.id,
            name: classInfo.class_name,
            semester: classInfo.semester
          };
          
          // 保存当前选中的班级ID
          localStorage.setItem('plagiarismAnalysis_lastSelectedClassId', classId);
          
          // 获取该班级的章节列表
          await this.fetchClassChapters(classId);
        }
      } catch (error) {
        console.error('切换班级失败:', error);
        this.error = error.response?.data?.message || '切换班级失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 切换章节
    async switchChapter(chapterId) {
      if (!chapterId || !this.currentClass.id) return;
      
      this.selectedChapter = chapterId;
      localStorage.setItem('plagiarismAnalysis_lastSelectedChapterId', chapterId);
      
      // 获取该章节的批次列表
      await this.fetchChapterBatches(this.currentClass.id, chapterId);
    },
    
    // 切换批次
    async switchBatch(batchId) {
      if (!batchId) return;
      this.currentBatchId = batchId;
      // 自动获取查重分析数据
      await this.fetchPlagiarismAnalysis();
    },
    
    // 刷新班级数据
    async refreshCurrentClass() {
      if (this.currentClass?.id) {
        await this.switchClass(this.currentClass.id);
      }
    },
    
    // 获取查重分析数据
    async fetchPlagiarismAnalysis() {
      if (!this.currentClass.id || !this.selectedChapter || !this.currentBatchId) return;
      
      try {
        this.loading = true;
        this.error = '';
        
        // 获取查重分析数据，固定study_phase为2
        const response = await axios.get(`/api/plagiarism-analysis/${this.currentClass.id}/${this.selectedChapter}/${this.currentBatchId}?study_phase=2`);
        
        if (response.data.success) {
          console.log('获取到查重分析数据:', response.data.data);
          
          this.plagiarismData = response.data.data.similarities.map((item, index) => ({
            ...item,
            key: index
          }));
          
          this.statsData = {
            totalQuestions: response.data.data.statsData.totalQuestions || 0,
            totalStudents: response.data.data.statsData.totalStudents || 0,
            highSimilarityCount: response.data.data.statsData.highSimilarityCount || 0,
          };
        }
      } catch (error) {
        console.error('获取查重分析数据失败:', error);
        this.error = error.response?.data?.message || '获取查重分析数据失败';
      } finally {
        this.loading = false;
      }
    },
    
    // 手动运行查重分析
    async runPlagiarismCheck() {
      if (!this.currentClass.id || !this.selectedChapter || !this.currentBatchId) {
        this.error = '请先选择班级、章节和批次';
        return;
      }
      
      try {
        this.runningCheck = true;
        
        // 确认是否要重新分析
        return new Promise((resolve) => {
          Modal.confirm({
            title: '确认重新分析',
            content: '重新分析将覆盖当前的查重记录，确定要继续吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
              try {
                // 调用查重API，固定study_phase为2
                const response = await axios.post(`/api/plagiarism-analysis/run-check/${this.currentClass.id}/${this.selectedChapter}/${this.currentBatchId}?study_phase=2`);
                
                if (response.data.success) {
                  this.$message.success(response.data.message);
                  
                  // 刷新查重结果
                  await this.fetchPlagiarismAnalysis();
                }
                resolve(true);
              } catch (error) {
                console.error('运行查重分析失败:', error);
                this.error = error.response?.data?.message || '运行查重分析失败';
                resolve(false);
              } finally {
                this.runningCheck = false;
              }
            },
            onCancel: () => {
              this.runningCheck = false;
              resolve(false);
            },
          });
        });
      } catch (error) {
        console.error('运行查重分析失败:', error);
        this.error = error.response?.data?.message || '运行查重分析失败';
        this.runningCheck = false;
        return false;
      }
    },
    showAnswerDetail(record) {
      this.currentAnswerDetail = record;
      this.answerModalVisible = true;
    },
  }
}
</script>

<style scoped>
.plagiarism-analysis {
  padding: 32px;
  background: #f8fafc;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 40px;
  text-align: center;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page-header h1 {
  font-size: 32px;
  color: #1e293b;
  margin-bottom: 12px;
  font-weight: 600;
}

.subtitle {
  color: #64748b;
  font-size: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.info-text {
  color: #1677ff;
  font-size: 14px;
  max-width: 600px;
  margin: 12px auto 0;
}

.content-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  background: transparent;
}

.filter-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  overflow: hidden;
}

.filter-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.filter-content {
  padding: 24px;
}

.filter-action {
  padding: 24px;
  border-top: 1px solid #f1f5f9;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.stat-title {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.stat-value.danger {
  color: #ff4d4f;
}

.data-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.similarity-bar {
  width: 100%;
}

.answer-comparison {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.question-container {
  grid-column: span 2;
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.answer-container {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.answer-container h4,
.question-container h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #374151;
  font-size: 16px;
}

.answer-text,
.question-text {
  margin: 0;
  padding: 12px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.5;
}

.answer-text {
  white-space: pre-wrap;
  word-break: break-word;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .answer-comparison {
    grid-template-columns: 1fr;
  }
  
  .question-container {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .plagiarism-analysis {
    padding: 16px;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
}

.answer-detail-modal {
  padding: 20px;
}

.answer-detail-modal .answer-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.answer-detail-modal .question-container,
.answer-detail-modal .similarity-info {
  grid-column: span 2;
  margin-top: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
}

.answer-detail-modal .similarity-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.answer-detail-modal .similarity-score,
.answer-detail-modal .similarity-level {
  display: flex;
  align-items: center;
  gap: 16px;
}

.answer-detail-modal .similarity-score {
  flex: 1;
}

.answer-detail-modal .answer-container {
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
}

.answer-detail-modal .answer-text {
  margin: 0;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
}

.answer-detail-modal h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 16px;
  font-weight: 500;
}
</style> 
<template>
  <div class="page-container">
    <div class="page-header">
      <h1>预习分析</h1>
      <p class="subtitle">分析学生预习资料和习题的完成情况</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 筛选条件区域 -->
      <div class="filter-card">
        <div class="filter-header">
          <h3>数据筛选</h3>
          <a-button 
            type="link" 
            :loading="loading"
            @click="refreshData"
          >
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </div>
        <div class="filter-content">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="班级">
                  <a-select
                    v-model:value="currentClass.id"
                    style="width: 100%"
                    placeholder="选择班级"
                    @change="handleClassChange"
                  >
                    <a-select-option v-for="item in classList" :key="item.id" :value="item.id">
                      {{ item.class_name }}
                      <a-tag color="blue" size="small">{{ item.semester }}</a-tag>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="章节">
                  <a-select
                    v-model:value="selectedChapter"
                    style="width: 100%"
                    placeholder="选择章节"
                    @change="handleChapterChange"
                    :disabled="!currentClass.id || chapters.length === 0"
                  >
                    <a-select-option v-for="chapter in chapters" :key="chapter.id" :value="chapter.id">
                      {{ chapter.title }}
                    </a-select-option>
                  </a-select>
                  <div class="select-tip" v-if="!loading && currentClass.id && chapters.length === 0">
                    <a-alert
                      message="提示"
                      description="当前班级没有同时发布预习资料和预习题的章节"
                      type="info"
                      show-icon
                    />
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>

      <!-- 显示单个学生详情或者班级概览 -->
      <template v-if="selectedStudent && studentData">
        <StudentPreviewDetail :student-data="studentData" />
      </template>
      
      <template v-else-if="classData">
        <!-- 班级预习概览 -->
        <div class="stats-overview">
          <div class="stat-card">
            <div class="stat-title">预习完成率</div>
            <div class="stat-value">{{ classData.completionRate || 0 }}%</div>
            <div class="stat-desc">{{ classData.completedStudents || 0 }}/{{ classData.totalStudents || 0 }}名学生完成</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">平均阅读时间</div>
            <div class="stat-value">{{ classData.avgViewTime || 0 }}分钟</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">平均习题正确率</div>
            <div class="stat-value">{{ classData.avgCorrectRate || 0 }}%</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">截止时间</div>
            <div class="stat-value" :class="{'text-danger': isDeadlinePassed}">
              {{ classData.previewInfo?.deadline ? formatDate(classData.previewInfo.deadline) : '无' }}
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-section">
          <!-- 学生预习时长分布图 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>预习时长分布</h3>
            </div>
            <div id="timeChartRef" ref="timeChartRef" class="chart-container"></div>
          </div>
          
          <!-- 学生题目完成率图 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>预习习题正确率</h3>
            </div>
            <div id="correctRateChartRef" ref="correctRateChartRef" class="chart-container"></div>
          </div>
          
          <!-- 学生预习评估得分图 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>学生预习评估得分</h3>
            </div>
            <div id="scoreChartRef" ref="scoreChartRef" class="chart-container"></div>
          </div>
        </div>

        <!-- 知识点错误热图 -->
        <KnowledgePointsHeatmap 
          v-if="currentClass.id && selectedChapter"
          :currentClass="currentClass.id"
          :currentChapter="selectedChapter"
        />

        <!-- 学生预习详情表格 -->
        <div class="data-card">
          <div class="card-header">
            <h3>学生预习详情</h3>
            <div class="header-actions">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索学生姓名"
                style="width: 250px"
                @search="onSearch"
              />
            </div>
          </div>
          <a-table
            :columns="columns"
            :data-source="filteredStudentsData"
            :pagination="{ 
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: total => `共 ${total} 条记录`
            }"
            :loading="loading"
            size="middle"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="record.status === 1 ? 'success' : 'error'">
                  {{ record.status === 1 ? '已完成' : '未完成' }}
                </a-tag>
              </template>
              <template v-if="column.key === 'actions'">
                <a-button type="link" @click="viewStudentDetail(record)">查看详情</a-button>
              </template>
            </template>
          </a-table>
        </div>
      </template>

      <!-- 数据为空时的占位符 -->
      <template v-if="!loading && !classData">
        <div class="empty-state">
          <a-empty description="请选择班级和章节查看预习分析数据" />
        </div>
      </template>
    </a-spin>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import axios from '@/axios';
import * as echarts from 'echarts';
import StudentPreviewDetail from './components/StudentPreviewDetail.vue';
import KnowledgePointsHeatmap from './components/KnowledgePointsHeatmap.vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  components: {
    ReloadOutlined,
    StudentPreviewDetail,
    KnowledgePointsHeatmap
  },
  setup() {
    const loading = ref(false);
    const classList = ref([]);
    const chapters = ref([]);
    const studentsList = ref([]);
    const searchText = ref('');
    
    const classData = ref(null);
    
    const timeChartRef = ref(null);
    const correctRateChartRef = ref(null);
    const scoreChartRef = ref(null);
    let timeChart = null;
    let correctRateChart = null;
    let scoreChart = null;
    
    const currentClass = reactive({
      id: null,
      name: '',
      semester: ''
    });
    
    const selectedChapter = ref(null);
    
    const router = useRouter();
    
    // 表格列定义
    const columns = [
      {
        title: '学生姓名',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '学号',
        dataIndex: 'student_id',
        key: 'student_id',
      },
      {
        title: '预习状态',
        dataIndex: 'status',
        key: 'status',
        sorter: (a, b) => a.status - b.status,
      },
      {
        title: '查看次数',
        dataIndex: 'view_times',
        key: 'view_times',
        sorter: (a, b) => a.view_times - b.view_times,
      },
      {
        title: '阅读时长(分钟)',
        dataIndex: 'view_minutes',
        key: 'view_minutes',
        sorter: (a, b) => a.view_minutes - b.view_minutes,
      },
      {
        title: '习题正确率',
        dataIndex: 'correct_rate',
        key: 'correct_rate',
        sorter: (a, b) => a.correct_rate - b.correct_rate,
        customRender: ({ text }) => `${text || 0}%`
      },
      {
        title: '操作',
        key: 'actions',
      },
    ];
    
    // 计算属性
    const filteredStudentsData = computed(() => {
      if (!classData.value || !classData.value.students) return [];
      
      if (!searchText.value) {
        return classData.value.students;
      }
      
      return classData.value.students.filter(item => 
        item.name && item.name.toLowerCase().includes(searchText.value.toLowerCase())
      );
    });
    
    const isDeadlinePassed = computed(() => {
      if (!classData.value || !classData.value.previewInfo?.deadline) return false;
      
      const deadline = new Date(classData.value.previewInfo.deadline);
      return deadline < new Date();
    });
    
    // 获取教师的班级列表
    const fetchTeacherClasses = async () => {
      try {
        loading.value = true;
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        if (!userInfo.system_teacher_id) {
          message.error('未找到教师信息');
          return;
        }

        const response = await axios.get(`/api/classes/teacher/${userInfo.system_teacher_id}`);
        if (response.data.success) {
          classList.value = response.data.data;
          
          // 获取上次选中的班级ID
          const lastSelectedClassId = localStorage.getItem('previewAnalysis_lastSelectedClassId');
          if (lastSelectedClassId && response.data.data.find(c => c.id === parseInt(lastSelectedClassId))) {
            currentClass.id = parseInt(lastSelectedClassId);
            await handleClassChange(currentClass.id);
          }
        }
      } catch (error) {
        console.error('获取班级列表失败:', error);
        message.error('获取班级列表失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 获取班级的章节列表
    const fetchClassChapters = async (classId) => {
      try {
        loading.value = true;
        
        const response = await axios.get(`/api/preview-analysis/class/${classId}/chapters`);
        if (response.data.success) {
          chapters.value = response.data.data;
          
          // 获取上次选中的章节ID
          const lastSelectedChapterId = localStorage.getItem('previewAnalysis_lastSelectedChapterId');
          if (lastSelectedChapterId && response.data.data.find(c => c.id === lastSelectedChapterId)) {
            selectedChapter.value = lastSelectedChapterId;
            await fetchClassPreviewStats(classId, selectedChapter.value);
          } else if (chapters.value.length > 0) {
            // 默认选择第一个章节
            selectedChapter.value = chapters.value[0].id;
            await fetchClassPreviewStats(classId, selectedChapter.value);
          }
        }
      } catch (error) {
        console.error('获取班级章节失败:', error);
        message.error('获取班级章节失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 获取班级学生列表
    const fetchClassStudents = async (classId) => {
      try {
        const response = await axios.get(`/api/preview-analysis/class/${classId}/students`);
        if (response.data.success) {
          studentsList.value = response.data.data;
        }
      } catch (error) {
        console.error('获取班级学生列表失败:', error);
        message.error('获取班级学生列表失败');
      }
    };
    
    // 获取班级预习统计数据
    const fetchClassPreviewStats = async (classId, chapterId) => {
      try {
        loading.value = true;
        
        const response = await axios.get(`/api/preview-analysis/class/${classId}/chapter/${chapterId}/stats`);
        if (response.data.success) {
          classData.value = response.data.data;
          
          // 初始化图表
          nextTick(() => {
            setTimeout(() => {
              initCharts();
            }, 300);
          });
        }
      } catch (error) {
        console.error('获取班级预习统计数据失败:', error);
        message.error('获取班级预习统计数据失败');
      } finally {
        loading.value = false;
      }
    };
    
    // 切换班级
    const handleClassChange = async (classId) => {
      if (!classId) return;
      
      currentClass.id = classId;
      selectedChapter.value = null;
      chapters.value = [];
      studentsList.value = [];
      classData.value = null;
      
      // 保存当前选中的班级ID
      localStorage.setItem('previewAnalysis_lastSelectedClassId', classId);
      
      // 获取班级的章节和学生列表
      await Promise.all([
        fetchClassChapters(classId),
        fetchClassStudents(classId)
      ]);
    };
    
    // 切换章节
    const handleChapterChange = async (chapterId) => {
      if (!chapterId || !currentClass.id) return;
      
      selectedChapter.value = chapterId;
      
      // 保存当前选中的章节ID
      localStorage.setItem('previewAnalysis_lastSelectedChapterId', chapterId);
      
      // 获取班级预习统计数据
      await fetchClassPreviewStats(currentClass.id, chapterId);
    };
    
    // 查看学生详情
    const viewStudentDetail = (record) => {
      // 导航到学生预习详情页面
      router.push({
        path: `/assessment/student-preview/${currentClass.id}/${selectedChapter.value}/${record.student_id}`,
        query: {
          studentName: record.name
        }
      });
    };
    
    // 搜索功能
    const onSearch = () => {
      // 搜索逻辑已通过计算属性实现
    };
    
    // 刷新数据
    const refreshData = async () => {
      if (currentClass.id && selectedChapter.value) {
        await fetchClassPreviewStats(currentClass.id, selectedChapter.value);
      }
    };
    
    // 日期格式化
    const formatDate = (dateStr) => {
      if (!dateStr) return '-';
      
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    };
    
    // 初始化图表
    const initCharts = () => {
      if (!classData.value) return;
      
      // 预习时长图表
      if (timeChartRef.value) {
        if (timeChart) {
          timeChart.dispose();
        }
        timeChart = echarts.init(timeChartRef.value);
        
        const timeData = classData.value.students.map(student => ({
          name: student.name,
          value: student.view_minutes || 0
        }));
        
        timeChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: timeData.map(item => item.name),
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value',
            name: '时长(分钟)'
          },
          series: [
            {
              name: '预习时长',
              type: 'bar',
              data: timeData.map(item => item.value),
              itemStyle: {
                color: function(params) {
                  // 根据时长设置不同颜色
                  const value = params.value;
                  if (value >= 30) return '#52c41a';
                  if (value >= 15) return '#1890ff';
                  if (value >= 5) return '#faad14';
                  return '#ff4d4f';
                }
              }
            }
          ]
        });
      }
      
      // 正确率图表
      if (correctRateChartRef.value) {
        if (correctRateChart) {
          correctRateChart.dispose();
        }
        correctRateChart = echarts.init(correctRateChartRef.value);
        
        const rateData = classData.value.students.map(student => ({
          name: student.name,
          value: student.correct_rate || 0
        }));
        
        correctRateChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: rateData.map(item => item.name),
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value',
            name: '正确率(%)',
            max: 100
          },
          series: [
            {
              name: '习题正确率',
              type: 'bar',
              data: rateData.map(item => item.value),
              itemStyle: {
                color: function(params) {
                  // 根据正确率设置不同颜色
                  const value = params.value;
                  if (value >= 80) return '#52c41a';
                  if (value >= 60) return '#1890ff';
                  if (value >= 40) return '#faad14';
                  return '#ff4d4f';
                }
              }
            }
          ]
        });
      }
      
      // 预习评估得分图表
      if (scoreChartRef.value) {
        if (scoreChart) {
          scoreChart.dispose();
        }
        scoreChart = echarts.init(scoreChartRef.value);
        
        // 计算每个学生的预习评估得分
        const scoreData = classData.value.students.map(student => {
          // 预习资料得分 (基于观看时长与推荐时长的比较)
          const expectedDuration = classData.value.previewInfo?.expected_duration || 1800; // 默认30分钟
          const viewDuration = student.view_minutes * 60 || 0; // 转换为秒
          const durationDiff = Math.abs(viewDuration - expectedDuration);
          const materialScore = Math.max(0, 100 - (durationDiff / expectedDuration) * 100);
          
          // 预习题得分 (基于正确率)
          const exerciseScore = student.correct_rate || 0;
          
          // 综合得分 (资料40%, 习题60%)
          const totalScore = materialScore * 0.4 + exerciseScore * 0.6;
          
          return {
            name: student.name,
            materialScore: Math.round(materialScore),
            exerciseScore: Math.round(exerciseScore),
            totalScore: Math.round(totalScore)
          };
        });
        
        scoreChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['预习资料得分', '预习题得分', '综合得分']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: scoreData.map(item => item.name),
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value',
            name: '得分',
            max: 100
          },
          series: [
            {
              name: '预习资料得分',
              type: 'bar',
              data: scoreData.map(item => item.materialScore),
              itemStyle: {
                color: '#52c41a'
              }
            },
            {
              name: '预习题得分',
              type: 'bar',
              data: scoreData.map(item => item.exerciseScore),
              itemStyle: {
                color: '#1890ff'
              }
            },
            {
              name: '综合得分',
              type: 'bar',
              data: scoreData.map(item => item.totalScore),
              itemStyle: {
                color: '#722ed1'
              }
            }
          ]
        });
      }
    };
    
    // 组件挂载时初始化
    onMounted(() => {
      fetchTeacherClasses();
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (timeChart) timeChart.resize();
        if (correctRateChart) correctRateChart.resize();
        if (scoreChart) scoreChart.resize();
      });
    });
    
    return {
      loading,
      classList,
      chapters,
      studentsList,
      currentClass,
      selectedChapter,
      classData,
      columns,
      searchText,
      filteredStudentsData,
      isDeadlinePassed,
      timeChartRef,
      correctRateChartRef,
      scoreChartRef,
      
      handleClassChange,
      handleChapterChange,
      viewStudentDetail,
      onSearch,
      refreshData,
      formatDate
    };
  }
});
</script>

<style scoped>
.page-container {
  padding: 24px;
  background: #f8fafc;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page-header h1 {
  font-size: 32px;
  color: #1e293b;
  margin-bottom: 12px;
  font-weight: 600;
}

.subtitle {
  color: #64748b;
  font-size: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.filter-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  overflow: hidden;
}

.filter-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.filter-content {
  padding: 24px;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  margin-left: 50px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.stat-title {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.stat-desc {
  font-size: 14px;
  color: #6b7280;
  margin-top: 8px;
}

.text-danger {
  color: #f56565;
}

.chart-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.chart-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.chart-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.chart-container {
  height: 400px;
  padding: 16px;
}

.data-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-top: 24px;
}

.select-tip {
  margin-top: 8px;
}

@media (max-width: 1200px) {
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
}
</style> 
<template>
  <div class="core-functions-page">
    <div class="return-home">
      <router-link to="/" class="return-btn">
        <i class="fas fa-arrow-left"></i> 返回首页
      </router-link>
    </div>
    <div class="page-header">
      <div class="container">
        <h1>核心功能</h1>
        <p class="subtitle">启智星为教师和学生提供全方位的智能教学解决方案</p>
      </div>
    </div>

    <div class="container">
      <div class="content-section">
        <div class="functions-grid">
          <div class="function-card">
            <div class="function-icon">
              <i class="fas fa-book-open"></i>
            </div>
            <h3>智能备课</h3>
            <p>AI辅助教案编写，自动生成教学资源，提供个性化教学建议</p>
            <div class="function-details">
              <h4>功能亮点</h4>
              <ul>
                <li>教材目录智能解析，自动识别章节结构与知识点</li>
                <li>基于知识点复杂度的动态课时分配</li>
                <li>一键生成包含教学目标、互动环节的教案模板</li>
                <li>教师可通过对话窗口与AI实时调整教案内容</li>
              </ul>
              <h4>应用场景</h4>
              <p>教师可以在短时间内完成高质量的备课工作，将更多精力投入到教学设计和课堂互动中，提升教学效果。</p>
            </div>
          </div>

          <div class="function-card">
            <div class="function-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>班级管理</h3>
            <p>便捷的学生信息管理，班级数据分析，提升教学效率</p>
            <div class="function-details">
              <h4>功能亮点</h4>
              <ul>
                <li>学生信息一键导入与智能管理</li>
                <li>班级整体学习情况可视化分析</li>
                <li>学生分组与个性化教学管理</li>
                <li>班级动态监测与预警机制</li>
              </ul>
              <h4>应用场景</h4>
              <p>教师可以全面了解班级学习状况，及时发现学习问题，有针对性地调整教学策略，提高班级整体学习效果。</p>
            </div>
          </div>

          <div class="function-card">
            <div class="function-icon">
              <i class="fas fa-tasks"></i>
            </div>
            <h3>作业与考试</h3>
            <p>智能生成习题，自动批改作业，快速生成分析报告</p>
            <div class="function-details">
              <h4>功能亮点</h4>
              <ul>
                <li>按"预习→基础→拓展→挑战"分层生成习题</li>
                <li>支持在线测试与限时练习</li>
                <li>全题型自动批改（客观题、简答题、计算题）</li>
                <li>作业查重检测，生成可视化查重报告</li>
              </ul>
              <h4>应用场景</h4>
              <p>教师可以快速生成高质量的习题和测试，大幅减少批改时间，通过数据分析精准把握学生掌握情况。</p>
            </div>
          </div>

          <div class="function-card">
            <div class="function-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>学习分析</h3>
            <p>数据可视化分析，个性化学习路径推荐，精准掌握学情动态</p>
            <div class="function-details">
              <h4>功能亮点</h4>
              <ul>
                <li>班级/个体学情报告自动生成</li>
                <li>知识点掌握程度可视化展示</li>
                <li>个性化学习路径推荐</li>
                <li>学习薄弱点精准识别与资源推送</li>
              </ul>
              <h4>应用场景</h4>
              <p>教师可以基于数据分析结果，为学生提供个性化的学习指导，学生也可以了解自己的学习状况，有针对性地进行学习。</p>
            </div>
          </div>
        </div>
      </div>

      <div class="content-section">
        <h2>功能优势</h2>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-icon">
              <i class="fas fa-bolt"></i>
            </div>
            <div class="advantage-content">
              <h3>高效率</h3>
              <p>AI辅助教学全流程，5分钟解析教材结构，10分钟生成教案+PPT+习题，效率提升95%</p>
            </div>
          </div>

          <div class="advantage-item">
            <div class="advantage-icon">
              <i class="fas fa-bullseye"></i>
            </div>
            <div class="advantage-content">
              <h3>精准性</h3>
              <p>基于动态知识图谱与深度学习模型，实现"教—学—练"闭环优化，资源匹配度90%+</p>
            </div>
          </div>

          <div class="advantage-item">
            <div class="advantage-icon">
              <i class="fas fa-user-cog"></i>
            </div>
            <div class="advantage-content">
              <h3>个性化</h3>
              <p>针对不同学生学习特点，提供个性化学习路径和资源推荐，满足差异化教学需求</p>
            </div>
          </div>

          <div class="advantage-item">
            <div class="advantage-icon">
              <i class="fas fa-sync-alt"></i>
            </div>
            <div class="advantage-content">
              <h3>适应性</h3>
              <p>支持多学科、多年级、多场景应用，适应不同教育环境和教学需求</p>
            </div>
          </div>
        </div>
      </div>

      <div class="content-section">
        <div class="cta-section">
          <h3>了解更多关于启智星的信息</h3>
          <div class="cta-buttons">
            <router-link to="/overview" class="cta-button">平台概述</router-link>
            <router-link to="/features" class="cta-button">功能特点</router-link>
            <router-link to="/technology" class="cta-button">技术架构</router-link>
            <router-link to="/application" class="cta-button">应用场景</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面组件逻辑
</script>

<style scoped>
.core-functions-page {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
  padding-bottom: 4rem;
}

.page-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 8rem 0 6rem;
  text-align: center;
  margin-top: 64px;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/stars-pattern.png');
  background-size: cover;
  opacity: 0.1;
  z-index: 0;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

h1 {
  font-size: 3.5rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.subtitle {
  font-size: 1.8rem;
  color: #666;
  margin-bottom: 2.5rem;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.content-section {
  margin: 4rem 0;
  background: white;
  padding: 3rem;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.05);
}

h2 {
  font-size: 2.2rem;
  color: #4c4de6;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
}

h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: #4c4de6;
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  margin: 0 auto;
}

.function-card {
  padding: 3rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s;
  height: 100%;
  border: 1px solid #eee;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  border-color: rgba(76, 77, 230, 0.2);
}

.function-icon {
  width: 90px;
  height: 90px;
  background: rgba(76, 77, 230, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

.function-icon i {
  font-size: 2.5rem;
  color: #4c4de6;
}

.function-card h3 {
  font-size: 1.8rem;
  margin-bottom: 1.2rem;
  color: #2c3e50;
}

.function-card > p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.function-details {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.function-details h4 {
  font-size: 1.3rem;
  color: #4c4de6;
  margin-bottom: 1rem;
}

.function-details ul {
  padding-left: 1.2rem;
  margin-bottom: 1.2rem;
}

.function-details li {
  margin-bottom: 0.8rem;
  color: #555;
  font-size: 1.05rem;
}

.function-details p {
  color: #666;
  line-height: 1.6;
  font-size: 1.05rem;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  margin: 0 auto;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  padding: 2.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s;
  border: 1px solid #eee;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.advantage-icon {
  width: 70px;
  height: 70px;
  background: rgba(76, 77, 230, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.8rem;
  flex-shrink: 0;
}

.advantage-icon i {
  font-size: 2rem;
  color: #4c4de6;
}

.advantage-content h3 {
  font-size: 1.6rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.advantage-content p {
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-size: 1.1rem;
}

.cta-section {
  text-align: center;
  padding: 3rem 2rem;
}

.cta-section h3 {
  font-size: 1.8rem;
  margin-bottom: 2.5rem;
  color: #2c3e50;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-block;
  padding: 1rem 2.5rem;
  background: #4c4de6;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s;
  font-size: 1.1rem;
}

.cta-button:hover {
  background: #3a3bb0;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(76, 77, 230, 0.3);
}

@media (max-width: 1200px) {
  .container {
    max-width: 1000px;
  }

  .functions-grid,
  .advantages-grid {
    gap: 2rem;
  }

  .function-card {
    padding: 2.5rem;
  }

  .advantage-item {
    padding: 2rem;
  }
}

@media (max-width: 992px) {
  .container {
    max-width: 90%;
  }

  h1 {
    font-size: 3rem;
  }

  .subtitle {
    font-size: 1.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .functions-grid,
  .advantages-grid {
    grid-template-columns: 1fr;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
}

.return-home {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 100;
}

.return-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: rgba(76, 77, 230, 0.9);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.return-btn i {
  margin-right: 0.5rem;
}

.return-btn:hover {
  background: #3a3bb0;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .return-home {
    top: 70px;
    right: 10px;
  }

  .return-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .page-header {
    padding: 6rem 0 4rem;
  }

  h1 {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.3rem;
  }

  .content-section {
    padding: 2rem;
    margin: 2.5rem 0;
  }

  h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .function-card {
    padding: 2rem;
  }

  .function-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 1.5rem;
  }

  .function-card h3 {
    font-size: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-icon {
    width: 50px;
    height: 50px;
    margin-right: 1.2rem;
  }

  .advantage-content h3 {
    font-size: 1.4rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 250px;
    margin-bottom: 1rem;
    text-align: center;
    padding: 0.8rem 2rem;
  }
}
</style>

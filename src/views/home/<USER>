<template>
  <div class="technology-page">
    <div class="return-home">
      <router-link to="/" class="return-btn">
        <i class="fas fa-arrow-left"></i> 返回首页
      </router-link>
    </div>
    <div class="page-header">
      <div class="container">
        <h1>技术架构</h1>
        <p class="subtitle">启智星EduSpark平台的技术实现与部署方案</p>
      </div>
    </div>

    <div class="container">
      <div class="content-section">
        <h2>开发工具与技术</h2>

        <div class="tech-grid">
          <div class="tech-card">
            <div class="tech-icon">
              <i class="fab fa-vuejs"></i>
            </div>
            <h3>前端技术</h3>
            <ul>
              <li>Vue 3 框架</li>
              <li>Ant Design Vue 组件库</li>
              <li>双端交互设计（教师Web/学生Web）</li>
              <li>响应式布局，支持多设备访问</li>
            </ul>
          </div>

          <div class="tech-card">
            <div class="tech-icon">
              <i class="fab fa-node-js"></i>
            </div>
            <h3>后端技术</h3>
            <ul>
              <li>Node.js微服务架构</li>
              <li>Python（Flask）服务</li>
              <li>集成JWT认证机制</li>
              <li>异步任务队列处理</li>
            </ul>
          </div>

          <div class="tech-card">
            <div class="tech-icon">
              <i class="fas fa-database"></i>
            </div>
            <h3>数据库</h3>
            <ul>
              <li>MySQL 关系型数据库</li>
              <li>Elasticsearch 搜索引擎</li>
              <li>动态知识图谱构建</li>
              <li>高效数据索引与检索</li>
            </ul>
          </div>

          <div class="tech-card">
            <div class="tech-icon">
              <i class="fas fa-brain"></i>
            </div>
            <h3>AI工具链</h3>
            <ul>
              <li>OpenAI API 集成</li>
              <li>火山引擎OCR技术</li>
              <li>BERT模型语义分析</li>
              <li>Manim动画引擎</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="content-section">
        <h2>部署方案</h2>
        <div class="deployment-info">
          <div class="deployment-item">
            <h3><i class="fab fa-docker"></i> 容器化部署</h3>
            <p>采用Docker容器化技术进行部署，结合Nginx服务器，支持云端部署和本地私有化部署两种方式，满足不同机构的需求。</p>
          </div>

          <div class="deployment-item">
            <h3><i class="fas fa-server"></i> 服务器环境</h3>
            <p>支持Linux/Windows系统环境，需部署Docker环境。基础版配置建议：4核CPU/8GB内存/50GB存储，支持根据用户规模进行横向扩展。</p>
          </div>

          <div class="deployment-item">
            <h3><i class="fas fa-network-wired"></i> 网络要求</h3>
            <p>支持离线部署模式，部分AI功能（如大模型调用）需要API联网调用。可根据实际需求配置网络环境。</p>
          </div>

          <div class="deployment-item">
            <h3><i class="fas fa-shield-alt"></i> 安全保障</h3>
            <p>采用多层次安全架构，包括数据加密、权限控制、安全审计等机制，保障用户数据安全与隐私保护。</p>
          </div>
        </div>

        <div class="architecture-diagram">
          <h3>系统架构图</h3>
          <div class="diagram-placeholder">
            <i class="fas fa-project-diagram"></i>
            <p>启智星EduSpark平台采用前后端分离的微服务架构，实现高可用、可扩展的教育智能服务</p>
          </div>
        </div>
      </div>

      <div class="content-section">
        <div class="cta-section">
          <h3>了解更多关于启智星的信息</h3>
          <div class="cta-buttons">
            <router-link to="/overview" class="cta-button">平台概述</router-link>
            <router-link to="/features" class="cta-button">功能特点</router-link>
            <router-link to="/application" class="cta-button">应用场景</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面组件逻辑
</script>

<style scoped>
.technology-page {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
  padding-bottom: 4rem;
}

.page-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 6rem 0 4rem;
  text-align: center;
  margin-top: 64px;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/stars-pattern.png');
  background-size: cover;
  opacity: 0.1;
  z-index: 0;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

h1 {
  font-size: 2.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 700;
}

.subtitle {
  font-size: 1.4rem;
  color: #666;
  margin-bottom: 2rem;
}

.content-section {
  margin: 3rem 0;
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

h2 {
  font-size: 1.8rem;
  color: #4c4de6;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.8rem;
}

h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: #4c4de6;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.tech-card {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s;
  height: 100%;
}

.tech-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.tech-icon {
  width: 60px;
  height: 60px;
  background: rgba(76, 77, 230, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.tech-icon i {
  font-size: 1.8rem;
  color: #4c4de6;
}

.tech-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.tech-card ul {
  padding-left: 1.2rem;
  margin: 0;
}

.tech-card li {
  margin-bottom: 0.5rem;
  color: #666;
}

.deployment-info {
  margin-bottom: 2rem;
}

.deployment-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.deployment-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.deployment-item h3 {
  font-size: 1.3rem;
  color: #2c3e50;
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
}

.deployment-item h3 i {
  margin-right: 0.8rem;
  color: #4c4de6;
}

.deployment-item p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.architecture-diagram {
  margin-top: 3rem;
  text-align: center;
}

.architecture-diagram h3 {
  font-size: 1.4rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.diagram-placeholder {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #ccc;
}

.diagram-placeholder i {
  font-size: 4rem;
  color: #4c4de6;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.diagram-placeholder p {
  max-width: 500px;
  text-align: center;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.cta-section {
  text-align: center;
  padding: 2rem;
}

.cta-section h3 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: #2c3e50;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-block;
  padding: 0.8rem 2rem;
  background: #4c4de6;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
}

.cta-button:hover {
  background: #3a3bb0;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(76, 77, 230, 0.3);
}

.return-home {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 100;
}

.return-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: rgba(76, 77, 230, 0.9);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.return-btn i {
  margin-right: 0.5rem;
}

.return-btn:hover {
  background: #3a3bb0;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .return-home {
    top: 70px;
    right: 10px;
  }

  .return-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  h1 {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .content-section {
    padding: 1.5rem;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 250px;
    margin-bottom: 1rem;
    text-align: center;
  }
}
</style>

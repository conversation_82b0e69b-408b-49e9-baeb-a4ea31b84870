<template>
  <div class="home">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-content">
        <div class="logo">
          <span class="logo-text">启智星</span>
          <span class="logo-text-en">EduSpark</span>
        </div>
        <div class="nav-links" :class="{ 'nav-active': menuActive }">
          <router-link to="/" class="active">首页</router-link>
          <router-link to="/overview">平台概述</router-link>
          <router-link to="/core-functions">核心功能</router-link>
          <router-link to="/features">功能特点</router-link>
          <router-link to="/technology">技术架构</router-link>
          <router-link to="/application">应用场景</router-link>
          <router-link to="/contact">联系我们</router-link>
        </div>
        <div class="nav-auth">
          <router-link to="/login" class="login-btn" @click="handleLoginClick">登录</router-link>
          <router-link to="/register" class="register-btn">注册</router-link>
        </div>
        <div class="mobile-menu-icon" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- 主标题区域 -->
    <div class="hero">
      <div class="hero-content">
        <h1>启迪智慧，点亮教学之星</h1>
        <p>人工智能赋能教育，为教学提供智慧导航</p>
        <div class="hero-buttons">
          <router-link to="/login" class="start-btn">教师登录</router-link>
          <router-link to="/student-login" class="start-btn student-btn">学生登录</router-link>
        </div>
      </div>
    </div>

    <!-- 核心功能区域 -->
    <div class="core-features">
      <div class="section-content">
        <h2 class="section-title">核心功能</h2>
        <div class="section-description">启智星为教师和学生提供全方位的智能教学解决方案</div>
        <div class="features-grid">
          <router-link to="/core-functions" class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-book-open"></i>
            </div>
            <h3>智能备课</h3>
            <p>AI辅助教案编写，自动生成教学资源，提供个性化教学建议</p>
          </router-link>

          <router-link to="/core-functions" class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>班级管理</h3>
            <p>便捷的学生信息管理，班级数据分析，提升教学效率</p>
          </router-link>

          <router-link to="/core-functions" class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-tasks"></i>
            </div>
            <h3>作业与考试</h3>
            <p>智能生成习题，自动批改作业，快速生成分析报告</p>
          </router-link>

          <router-link to="/core-functions" class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>学习分析</h3>
            <p>数据可视化分析，个性化学习路径推荐，精准掌握学情动态</p>
          </router-link>
        </div>
        <div class="view-more-container">
          <router-link to="/core-functions" class="view-more-btn">查看更多功能详情</router-link>
        </div>
      </div>
    </div>



    <!-- 数据统计 -->
    <div class="statistics">
      <div class="stat-container">
        <div class="stat-item">
          <h3>10,000+</h3>
          <p>注册教师</p>
        </div>
        <div class="stat-item">
          <h3>100,000+</h3>
          <p>生成教案</p>
        </div>
        <div class="stat-item">
          <h3>500,000+</h3>
          <p>批改作业</p>
        </div>
        <div class="stat-item">
          <h3>98%</h3>
          <p>教师好评</p>
        </div>
      </div>
    </div>

    <!-- 用户反馈部分 -->
    <div class="testimonials">
      <div class="section-content">
        <h2 class="section-title">用户反馈</h2>
        <div class="testimonial-grid">
          <div class="testimonial-card">
            <div class="quote">"启智星平台的AI备课助手帮我节省了至少50%的备课时间，生成的教案质量高，内容丰富。"</div>
            <div class="author">— 王老师，高中物理教师</div>
          </div>
          <div class="testimonial-card">
            <div class="quote">"习题生成和自动批改功能大大减轻了我的工作量，数据分析报告帮助我更好地了解学生的学习情况。"</div>
            <div class="author">— 李老师，初中数学教师</div>
          </div>
          <div class="testimonial-card">
            <div class="quote">"启智星的个性化学习路径推荐功能帮助我的学生更有针对性地学习，学习效果显著提高，家长反馈非常正面。"</div>
            <div class="author">— 张老师，小学英语教师</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-section">
          <h4>关于我们</h4>
          <p>启智星致力于为教育工作者提供智能化教学解决方案，点亮教育之星</p>
        </div>
        <div class="footer-section">
          <h4>联系方式</h4>
          <p>邮箱：<EMAIL></p>
          <p>电话：400-123-4567</p>
          <p>地址：北京市海淀区中关村</p>
        </div>
        <div class="footer-section">
          <h4>快速链接</h4>
          <router-link to="/overview">平台概述</router-link>
          <router-link to="/core-functions">核心功能</router-link>
          <router-link to="/features">功能特点</router-link>
          <router-link to="/technology">技术架构</router-link>
          <router-link to="/application">应用场景</router-link>
          <router-link to="/privacy">隐私政策</router-link>
          <router-link to="/terms">使用条款</router-link>
        </div>
        <div class="footer-section">
          <h4>订阅我们</h4>
          <p>获取最新的教育科技资讯和更新</p>
          <div class="subscribe-form">
            <input type="email" placeholder="输入您的邮箱" />
            <button>订阅</button>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 启智星 EduSpark. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const menuActive = ref(false);

function toggleMenu() {
  menuActive.value = !menuActive.value;
}

function handleLoginClick() {
  // 清除本地存储的登录信息
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: #4c4de6;
  letter-spacing: -0.5px;
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
}

.logo-text-en {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.8;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s;
  font-weight: 500;
}

.nav-links a:hover,
.nav-links a.active {
  color: #4c4de6;
}

.nav-auth {
  display: flex;
  gap: 1rem;
}

.login-btn,
.register-btn {
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  transition: all 0.3s;
  font-weight: 500;
}

.login-btn {
  color: #4c4de6;
  border: 1px solid #4c4de6;
}

.login-btn:hover {
  background: rgba(76, 77, 230, 0.1);
}

.register-btn {
  background: #4c4de6;
  color: white;
}

.register-btn:hover {
  background: #3a3bb0;
  transform: translateY(-2px);
}

.mobile-menu-icon {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 24px;
  height: 18px;
  cursor: pointer;
}

.mobile-menu-icon span {
  width: 100%;
  height: 2px;
  background-color: #4c4de6;
  transition: all 0.3s;
}

.hero {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  text-align: center;
  margin-top: 64px;
  overflow: hidden;
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/stars-pattern.png');
  background-size: cover;
  opacity: 0.1;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.hero h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  color: #2c3e50;
  font-weight: 700;
}

.hero p {
  font-size: 1.4rem;
  color: #666;
  margin-bottom: 2rem;
}

.hero-buttons {
  margin-bottom: 3rem;
}

.start-btn {
  display: inline-block;
  padding: 1rem 3rem;
  margin-right: 15px;
  background: #4c4de6;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.3s;
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(76, 77, 230, 0.3);
}

.start-btn:hover {
  background: #3a3bb0;
  transform: translateY(-3px);
  box-shadow: 0 7px 15px rgba(76, 77, 230, 0.4);
}

.student-btn {
  background: transparent;
  color: #4c4de6;
  border: 2px solid #4c4de6;
  box-shadow: none;
}

.student-btn:hover {
  background: rgba(76, 77, 230, 0.1);
  color: #4c4de6;
  box-shadow: none;
}

/* 调整核心功能区域 */
.core-features {
  padding: 6rem 0;
  background: white;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 1rem;
  color: #2c3e50;
  font-weight: 700;
}

.section-description {
  text-align: center;
  max-width: 700px;
  margin: 0 auto 3rem;
  color: #666;
  font-size: 1.1rem;
}

.section-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-top: 3rem;
}

.feature-card {
  padding: 1.8rem 1.2rem;
  text-align: center;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.4s ease;
  text-decoration: none;
  color: inherit;
  border: 1px solid #f0f0f0;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.feature-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 40px rgba(76, 77, 230, 0.12);
  border-color: rgba(76, 77, 230, 0.2);
}

.feature-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f4ff;
  border-radius: 50%;
  color: #4c4de6;
  transition: all 0.3s ease;
  font-size: 2rem;
}

.feature-card:hover .feature-icon {
  background: rgba(76, 77, 230, 0.1);
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: #2c3e50;
  font-weight: 600;
  transition: color 0.3s;
}

.feature-card:hover h3 {
  color: #4c4de6;
}

.view-more-container {
  text-align: center;
  margin-top: 2rem;
}

.view-more-btn {
  display: inline-block;
  padding: 0.8rem 2rem;
  background: #4c4de6;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
}

.view-more-btn:hover {
  background: #3a3bb0;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(76, 77, 230, 0.3);
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
  margin: 0 auto;
}

/* 平台特色部分 */
.platform-features {
  padding: 6rem 0;
  background: #f8f9fa;
  position: relative;
}

.platform-features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.platform-feature-item {
  padding: 2.5rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.4s ease;
  text-align: center;
}

.platform-feature-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(76, 77, 230, 0.12);
}

.feature-icon-large {
  width: 90px;
  height: 90px;
  margin: 0 auto 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f4ff;
  border-radius: 50%;
  color: #4c4de6;
  font-size: 2.5rem;
  transition: all 0.3s ease;
}

.platform-feature-item:hover .feature-icon-large {
  background: rgba(76, 77, 230, 0.15);
  transform: scale(1.1);
}

.platform-feature-item h3 {
  font-size: 1.6rem;
  margin-bottom: 1.2rem;
  color: #2c3e50;
  font-weight: 600;
}

.platform-feature-item p {
  color: #666;
  line-height: 1.7;
  font-size: 1rem;
}

/* 媒体查询调整 */
@media (max-width: 1200px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 1100px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    gap: 2rem;
  }

  .feature-card {
    padding: 2rem 1.5rem;
  }

  .feature-icon {
    width: 80px;
    height: 80px;
  }

  .feature-card h3 {
    font-size: 1.5rem;
  }

  .feature-card p {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .features-grid,
  .platform-features-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

.statistics {
  padding: 5rem 0;
  background: #f8f9fa;
  position: relative;
}

.statistics::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #69c0ff);
}

.stat-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.stat-item {
  padding: 2rem;
  text-align: center;
  min-width: 200px;
  transition: transform 0.3s;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-item h3 {
  font-size: 3rem;
  color: #1890ff;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.stat-item p {
  color: #666;
  font-size: 1.1rem;
  font-weight: 500;
}

.testimonials {
  padding: 6rem 0;
  background: white;
}

.testimonial-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.testimonial-card {
  padding: 2rem;
  background: #f9f9f9;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
}

.testimonial-card::before {
  content: "\201C";
  position: absolute;
  top: 0;
  left: 20px;
  font-size: 5rem;
  color: #e6e6fa;
  line-height: 1;
  font-family: serif;
  opacity: 0.5;
}

.quote {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
  color: #444;
}

.author {
  font-style: italic;
  color: #777;
}

.footer {
  background: #2c3e50;
  color: white;
  padding: 4rem 0 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 2rem;
}

.footer-section h4 {
  margin-bottom: 1.2rem;
  font-size: 1.2rem;
  position: relative;
  padding-bottom: 0.8rem;
}

.footer-section h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: #1890ff;
}

.footer-section a {
  display: block;
  color: #ccc;
  text-decoration: none;
  margin-bottom: 0.8rem;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: white;
}

.subscribe-form {
  margin-top: 1rem;
  display: flex;
}

.subscribe-form input {
  flex: 1;
  padding: 0.8rem;
  border: none;
  border-radius: 4px 0 0 4px;
  outline: none;
}

.subscribe-form button {
  padding: 0.8rem 1.2rem;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  transition: background 0.3s;
}

.subscribe-form button:hover {
  background: #40a9ff;
}

.footer-bottom {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 992px) {
  .hero h1 {
    font-size: 2.8rem;
  }

  .section-title {
    font-size: 2.2rem;
  }
}

@media (max-width: 768px) {
  .mobile-menu-icon {
    display: flex;
  }

  .nav-links {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    flex-direction: column;
    background: white;
    padding: 1rem;
    gap: 1rem;
    box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    display: none;
    text-align: center;
  }

  .nav-active {
    display: flex;
  }

  .hero h1 {
    font-size: 2.2rem;
  }

  .hero p {
    font-size: 1.1rem;
  }

  .start-btn {
    padding: 0.8rem 2rem;
    display: block;
    margin: 0 auto 1rem;
    max-width: 200px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .stat-container {
    flex-direction: column;
    align-items: center;
  }

  .stat-item {
    width: 100%;
    max-width: 300px;
    padding: 1.5rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }

  .subscribe-form {
    flex-direction: column;
  }

  .subscribe-form input {
    border-radius: 4px;
    margin-bottom: 0.5rem;
  }

  .subscribe-form button {
    border-radius: 4px;
  }
}

@media (max-width: 480px) {
  .nav-content {
    padding: 0.8rem;
  }

  .logo {
    font-size: 1.5rem;
  }

  .login-btn, .register-btn {
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
  }

  .hero {
    padding: 7rem 0 3rem;
  }

  .hero h1 {
    font-size: 1.8rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .stat-item h3 {
    font-size: 2.2rem;
  }

  .testimonial-grid {
    grid-template-columns: 1fr;
  }
}
</style>
<template>
  <div class="about-page">
    <div class="return-home">
      <router-link to="/" class="return-btn">
        <i class="fas fa-arrow-left"></i> 返回首页
      </router-link>
    </div>
    <div class="page-header">
      <div class="container">
        <h1>启智星EduSpark智能备课平台</h1>
        <p class="subtitle">技术为教育服务，创新为未来奠基</p>
      </div>
    </div>

    <div class="container">
      <div class="content-section">
        <h2>前言</h2>
        <p>在人工智能与教育深度融合的背景下，传统教学模式面临备课效率低、资源整合耗时长、学情分析滞后等挑战。启智星EduSpark——首个覆盖"课表—教案—资源—批改—学情"全链路的AI备课平台，旨在通过技术创新赋能教师，将繁琐的备课流程自动化、智能化，助力教师从"机械劳动"转向"教学设计"，推动教育迈向"精准化、个性化、公平化"新时代。</p>
      </div>

      <div class="content-section">
        <h2>创意描述</h2>
        <p>项目以"AI+教育"为核心，创新性融合多模态大模型（如DeepSeek、ChatGPT、火山引擎）、开源项目与智能分析算法，构建以教材、课表为起点，通过"一键课时分配→多模态资源生成→实时学情反馈→动态教案调整"全链路闭环。实现教学流程自动化与数据化，助力教师从"单向输出"转向"双向优化"，真正实现"以学定教"，解决教师备课效率低、资源匹配不精准、教学反馈滞后等核心痛点，为教育数字化转型提供高效工具。</p>
      </div>

      <div class="content-section">
        <div class="cta-section">
          <h3>了解更多关于启智星的信息</h3>
          <div class="cta-buttons">
            <router-link to="/features" class="cta-button">功能特点</router-link>
            <router-link to="/technology" class="cta-button">技术架构</router-link>
            <router-link to="/application" class="cta-button">应用场景</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面组件逻辑
</script>

<style scoped>
.about-page {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
  padding-bottom: 4rem;
}

.page-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 6rem 0 4rem;
  text-align: center;
  margin-top: 64px;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/stars-pattern.png');
  background-size: cover;
  opacity: 0.1;
  z-index: 0;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

h1 {
  font-size: 2.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 700;
}

.subtitle {
  font-size: 1.4rem;
  color: #666;
  margin-bottom: 2rem;
}

.content-section {
  margin: 3rem 0;
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

h2 {
  font-size: 1.8rem;
  color: #4c4de6;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.8rem;
}

h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: #4c4de6;
}

p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 1.5rem;
}

.cta-section {
  text-align: center;
  padding: 2rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: #2c3e50;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-block;
  padding: 0.8rem 2rem;
  background: #4c4de6;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
}

.cta-button:hover {
  background: #3a3bb0;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(76, 77, 230, 0.3);
}

.return-home {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 100;
}

.return-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: rgba(76, 77, 230, 0.9);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.return-btn i {
  margin-right: 0.5rem;
}

.return-btn:hover {
  background: #3a3bb0;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .return-home {
    top: 70px;
    right: 10px;
  }

  .return-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  h1 {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .content-section {
    padding: 1.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 250px;
    margin-bottom: 1rem;
    text-align: center;
  }
}
</style>

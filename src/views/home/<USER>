<template>
  <div class="application-page">
    <div class="return-home">
      <router-link to="/" class="return-btn">
        <i class="fas fa-arrow-left"></i> 返回首页
      </router-link>
    </div>
    <div class="page-header">
      <div class="container">
        <h1>应用场景</h1>
        <p class="subtitle">启智星EduSpark平台的应用对象与使用环境</p>
      </div>
    </div>

    <div class="container">
      <div class="content-section">
        <h2>应用对象</h2>

        <div class="user-groups">
          <div class="user-group-card primary">
            <div class="user-icon">
              <i class="fas fa-user-tie"></i>
            </div>
            <h3>核心用户</h3>
            <p>大学教师为主，初高中教师也可贴合使用。平台提供的智能备课、资源生成和学情分析功能，能够显著提升教师的教学效率和教学质量。</p>
          </div>

          <div class="user-group-card secondary">
            <div class="user-icon">
              <i class="fas fa-user-graduate"></i>
            </div>
            <h3>辅助用户</h3>
            <p>学生（在线练习、学情反馈）、教育管理者（教学数据监控）。学生可以通过平台完成在线练习，获取个性化学习反馈；教育管理者可以通过平台监控教学数据，优化教学管理。</p>
          </div>
        </div>

        <div class="application-scenarios">
          <h3>适用场景</h3>
          <div class="scenario-grid">
            <div class="scenario-card">
              <div class="scenario-icon">
                <i class="fas fa-book"></i>
              </div>
              <h4>日常备课</h4>
              <p>教师可以利用平台快速生成教案、PPT和习题，大幅提高备课效率。</p>
            </div>

            <div class="scenario-card">
              <div class="scenario-icon">
                <i class="fas fa-archive"></i>
              </div>
              <h4>课程资源库建设</h4>
              <p>学校可以利用平台构建丰富的课程资源库，实现资源共享和标准化。</p>
            </div>

            <div class="scenario-card">
              <div class="scenario-icon">
                <i class="fas fa-user-cog"></i>
              </div>
              <h4>个性化教学</h4>
              <p>基于学情分析，教师可以为学生提供个性化的学习路径和资源推荐。</p>
            </div>

            <div class="scenario-card">
              <div class="scenario-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <h4>学术诚信管理</h4>
              <p>通过作业查重功能，有效防止抄袭行为，维护学术诚信。</p>
            </div>
          </div>
        </div>
      </div>

      <div class="content-section">
        <h2>应用环境</h2>

        <div class="environment-requirements">
          <div class="requirement-item">
            <div class="req-icon">
              <i class="fas fa-server"></i>
            </div>
            <div class="req-content">
              <h3>服务器</h3>
              <p>Linux/Windows系统，需部署Docker环境。</p>
            </div>
          </div>

          <div class="requirement-item">
            <div class="req-icon">
              <i class="fas fa-microchip"></i>
            </div>
            <div class="req-content">
              <h3>硬件配置</h3>
              <p>4核CPU/8GB内存/50GB存储（基础版），支持横向扩展。</p>
            </div>
          </div>

          <div class="requirement-item">
            <div class="req-icon">
              <i class="fas fa-network-wired"></i>
            </div>
            <div class="req-content">
              <h3>网络要求</h3>
              <p>支持离线部署（部分AI功能需API调用）。</p>
            </div>
          </div>

          <div class="requirement-item">
            <div class="req-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="req-content">
              <h3>用户规模</h3>
              <p>基础配置支持100名教师同时在线使用，可根据需求扩展。</p>
            </div>
          </div>
        </div>
      </div>

      <div class="content-section">
        <h2>结语</h2>
        <div class="conclusion">
          <p>启智星EduSpark不仅是工具，更是教育公平与高质量发展的"新基建"。通过"AI+教育"的深度融合，重构备课流程，显著提升教学效率与质量。项目以轻量化架构、智能化设计、可扩展能力为核心，赋能教师回归育人本质，助力学生实现个性化成长，为教育公平与高质量发展提供创新范式。</p>

          <div class="vision-statement">
            <h3>我们的愿景</h3>
            <p>让每一位教师都能享受AI带来的教学便利，让每一位学生都能获得个性化的学习体验，共同推动教育向更加公平、高效、个性化的方向发展。</p>
          </div>
        </div>
      </div>

      <div class="content-section">
        <div class="cta-section">
          <h3>了解更多关于启智星的信息</h3>
          <div class="cta-buttons">
            <router-link to="/overview" class="cta-button">平台概述</router-link>
            <router-link to="/features" class="cta-button">功能特点</router-link>
            <router-link to="/technology" class="cta-button">技术架构</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面组件逻辑
</script>

<style scoped>
.application-page {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
  padding-bottom: 4rem;
}

.page-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 6rem 0 4rem;
  text-align: center;
  margin-top: 64px;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/stars-pattern.png');
  background-size: cover;
  opacity: 0.1;
  z-index: 0;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

h1 {
  font-size: 2.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 700;
}

.subtitle {
  font-size: 1.4rem;
  color: #666;
  margin-bottom: 2rem;
}

.content-section {
  margin: 3rem 0;
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

h2 {
  font-size: 1.8rem;
  color: #4c4de6;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.8rem;
}

h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: #4c4de6;
}

.user-groups {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

.user-group-card {
  padding: 2rem;
  border-radius: 8px;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.user-group-card.primary {
  background: rgba(76, 77, 230, 0.05);
  border: 1px solid rgba(76, 77, 230, 0.2);
}

.user-group-card.secondary {
  background: rgba(24, 144, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.user-group-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.user-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.user-group-card.primary .user-icon {
  background: rgba(76, 77, 230, 0.1);
}

.user-group-card.secondary .user-icon {
  background: rgba(24, 144, 255, 0.1);
}

.user-icon i {
  font-size: 2.5rem;
}

.user-group-card.primary .user-icon i {
  color: #4c4de6;
}

.user-group-card.secondary .user-icon i {
  color: #1890ff;
}

.user-group-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.user-group-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.application-scenarios h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  text-align: center;
}

.scenario-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.scenario-card {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.scenario-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.scenario-icon {
  width: 60px;
  height: 60px;
  background: rgba(76, 77, 230, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.scenario-icon i {
  font-size: 1.8rem;
  color: #4c4de6;
}

.scenario-card h4 {
  font-size: 1.2rem;
  margin-bottom: 0.8rem;
  color: #2c3e50;
}

.scenario-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

.environment-requirements {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.requirement-item {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.requirement-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.req-icon {
  width: 50px;
  height: 50px;
  background: rgba(76, 77, 230, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.req-icon i {
  font-size: 1.5rem;
  color: #4c4de6;
}

.req-content h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.req-content p {
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

.conclusion {
  padding: 1rem 0;
}

.conclusion p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 2rem;
}

.vision-statement {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  border-left: 4px solid #4c4de6;
}

.vision-statement h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.vision-statement p {
  color: #555;
  line-height: 1.7;
  margin: 0;
  font-size: 1.1rem;
}

.cta-section {
  text-align: center;
  padding: 2rem;
}

.cta-section h3 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: #2c3e50;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-block;
  padding: 0.8rem 2rem;
  background: #4c4de6;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
}

.cta-button:hover {
  background: #3a3bb0;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(76, 77, 230, 0.3);
}

.return-home {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 100;
}

.return-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: rgba(76, 77, 230, 0.9);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.return-btn i {
  margin-right: 0.5rem;
}

.return-btn:hover {
  background: #3a3bb0;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .return-home {
    top: 70px;
    right: 10px;
  }

  .return-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  h1 {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .content-section {
    padding: 1.5rem;
  }

  .user-groups,
  .scenario-grid,
  .environment-requirements {
    grid-template-columns: 1fr;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 250px;
    margin-bottom: 1rem;
    text-align: center;
  }
}
</style>

<template>
  <div class="features-page">
    <div class="return-home">
      <router-link to="/" class="return-btn">
        <i class="fas fa-arrow-left"></i> 返回首页
      </router-link>
    </div>
    <div class="page-header">
      <div class="container">
        <h1>功能特点</h1>
        <p class="subtitle">启智星EduSpark平台的核心功能与特色</p>
      </div>
    </div>

    <div class="container">
      <div class="content-section">
        <h2>功能简介</h2>

        <div class="feature-group">
          <h3><i class="fas fa-chalkboard-teacher"></i> 教学设计自动化</h3>
          <ul class="feature-list">
            <li>
              <strong>教材目录智能解析：</strong>
              <p>上传教材目录图片或PDF，AI自动识别章节结构与知识点层级。</p>
            </li>
            <li>
              <strong>动态课时分配：</strong>
              <p>基于知识点复杂度与总课时数，AI加权计算课时分配方案。</p>
            </li>
            <li>
              <strong>教案框架生成：</strong>
              <p>一键生成包含教学目标、互动环节、重难点标注的教案模板。</p>
            </li>
            <li>
              <strong>动态交互优化：</strong>
              <p>教师通过对话窗口与AI实时调整教案内容。</p>
            </li>
          </ul>
        </div>

        <div class="feature-group">
          <h3><i class="fas fa-photo-video"></i> 多模态资源生成</h3>
          <ul class="feature-list">
            <li>
              <strong>PPT智能生成：</strong>
              <p>根据教案框架，自动生成图文并茂的PPT，支持演讲备注等。</p>
            </li>
            <li>
              <strong>数理动画制作：</strong>
              <p>将抽象公式、几何图形转化为动态演示视频。</p>
            </li>
            <li>
              <strong>配套图表库：</strong>
              <p>提供网络搜索或关键词生成PPT插图。</p>
            </li>
            <li>
              <strong>课堂记录：</strong>
              <p>实时转写课堂音频，标记重点知识点与学生互动热点，生成教学笔记。</p>
            </li>
          </ul>
        </div>

        <div class="feature-group">
          <h3><i class="fas fa-tasks"></i> 智能习题系统</h3>
          <ul class="feature-list">
            <li>
              <strong>梯度习题生成：</strong>
              <p>按"预习→基础→拓展→挑战"分层生成习题，关联知识点标签。</p>
            </li>
            <li>
              <strong>在线测试与限时练习：</strong>
              <p>教师发布限时测试，学生端支持倒计时、答案提交。</p>
            </li>
            <li>
              <strong>全题型自动批改：</strong>
              <p>客观题规则引擎、简答题BERT语义评分、计算题OCR逻辑校验。</p>
            </li>
            <li>
              <strong>作业查重检测：</strong>
              <p>基于文本相似度算法，检测抄袭与拼接内容，生成可视化查重报告。</p>
            </li>
          </ul>
        </div>

        <div class="feature-group">
          <h3><i class="fas fa-chart-line"></i> 学情分析与推荐</h3>
          <ul class="feature-list">
            <li>
              <strong>学情报告生成：</strong>
              <p>输出班级/个体学情报告，包含错题归因与资源推荐。</p>
            </li>
            <li>
              <strong>端到端资源推荐：</strong>
              <p>教师端推送薄弱点教案，学生端强化错题练习。</p>
            </li>
          </ul>
        </div>
      </div>

      <div class="content-section">
        <h2>特色综述</h2>
        <div class="special-features">
          <div class="special-feature-item">
            <div class="icon-circle">
              <i class="fas fa-robot"></i>
            </div>
            <h3>全链路AI驱动</h3>
            <p>首创"教材目录解析→课时分配→教案生成→资源制作→学情反馈"全流程AI驱动，5分钟解析教材结构，10分钟生成教案+PPT+习题，效率提升95%，教师可动态调整内容，兼顾效率与灵活性。</p>
          </div>

          <div class="special-feature-item">
            <div class="icon-circle">
              <i class="fas fa-brain"></i>
            </div>
            <h3>多模态智能解析引擎</h3>
            <p>融合OCR识别、NLP生成、Manim动画、BERT批改技术，覆盖文本、图像、语音多模态处理，攻克手写计算题步骤校验、简答题语义评分等教育场景难题。</p>
          </div>

          <div class="special-feature-item">
            <div class="icon-circle">
              <i class="fas fa-users-cog"></i>
            </div>
            <h3>动态人机协同</h3>
            <p>教师通过多轮对话实时优化教案，保留教学主动权，动态Prompt生成技术响应速度＜3秒，实现"AI辅助不替代"的智能协作。</p>
          </div>

          <div class="special-feature-item">
            <div class="icon-circle">
              <i class="fas fa-project-diagram"></i>
            </div>
            <h3>精准推荐引擎</h3>
            <p>基于动态知识图谱与深度学习模型，实现"教—学—练"闭环优化，资源匹配度90%+。</p>
          </div>
        </div>
      </div>

      <div class="content-section">
        <div class="cta-section">
          <h3>了解更多关于启智星的信息</h3>
          <div class="cta-buttons">
            <router-link to="/overview" class="cta-button">平台概述</router-link>
            <router-link to="/technology" class="cta-button">技术架构</router-link>
            <router-link to="/application" class="cta-button">应用场景</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面组件逻辑
</script>

<style scoped>
.features-page {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
  padding-bottom: 4rem;
}

.page-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 6rem 0 4rem;
  text-align: center;
  margin-top: 64px;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/stars-pattern.png');
  background-size: cover;
  opacity: 0.1;
  z-index: 0;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

h1 {
  font-size: 2.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 700;
}

.subtitle {
  font-size: 1.4rem;
  color: #666;
  margin-bottom: 2rem;
}

.content-section {
  margin: 3rem 0;
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

h2 {
  font-size: 1.8rem;
  color: #4c4de6;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.8rem;
}

h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: #4c4de6;
}

.feature-group {
  margin-bottom: 2.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.feature-group:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.feature-group h3 {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
}

.feature-group h3 i {
  margin-right: 0.8rem;
  color: #4c4de6;
}

.feature-list {
  list-style: none;
  padding: 0;
}

.feature-list li {
  margin-bottom: 1.5rem;
}

.feature-list li strong {
  display: block;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.feature-list li p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.special-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.special-feature-item {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s;
}

.special-feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.icon-circle {
  width: 60px;
  height: 60px;
  background: rgba(76, 77, 230, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon-circle i {
  font-size: 1.8rem;
  color: #4c4de6;
}

.special-feature-item h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.special-feature-item p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.cta-section {
  text-align: center;
  padding: 2rem;
}

.cta-section h3 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: #2c3e50;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-block;
  padding: 0.8rem 2rem;
  background: #4c4de6;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
}

.cta-button:hover {
  background: #3a3bb0;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(76, 77, 230, 0.3);
}

.return-home {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 100;
}

.return-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.6rem 1.2rem;
  background: rgba(76, 77, 230, 0.9);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.return-btn i {
  margin-right: 0.5rem;
}

.return-btn:hover {
  background: #3a3bb0;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .return-home {
    top: 70px;
    right: 10px;
  }

  .return-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  h1 {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .content-section {
    padding: 1.5rem;
  }

  .special-features {
    grid-template-columns: 1fr;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 250px;
    margin-bottom: 1rem;
    text-align: center;
  }
}
</style>

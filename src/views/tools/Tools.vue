<template>
  <div class="tools-page">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-content">
        <div class="logo">
          <span class="logo-text">启智星</span>
          <span class="logo-text-en">EduSpark</span>
        </div>
        <div class="nav-links" :class="{ 'nav-active': menuActive }">
          <router-link to="/">首页</router-link>
          <router-link to="/features">功能特点</router-link>
          <router-link to="/tools" class="active">教学工具</router-link>
          <router-link to="/resources">教学资源</router-link>
          <router-link to="/contact">联系我们</router-link>
        </div>
        <div class="nav-auth">
          <router-link to="/login" class="login-btn" @click="handleLoginClick">登录</router-link>
          <router-link to="/register" class="register-btn">注册</router-link>
        </div>
        <div class="mobile-menu-icon" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1>教学工具</h1>
        <p>启智星EduSpark提供的智能教学工具集</p>
      </div>
    </div>

    <!-- 工具列表 -->
    <section class="tools-section">
      <div class="section-content">
        <div class="tools-grid">
          <!-- 教学设计自动化 -->
          <div class="tool-card">
            <div class="tool-icon">
              <i class="fas fa-book-open"></i>
            </div>
            <h3>教学设计自动化</h3>
            <div class="tool-features">
              <div class="feature-item">
                <h4>教材目录智能解析</h4>
                <p>上传教材目录图片或PDF，AI自动识别章节结构与知识点层级</p>
              </div>
              <div class="feature-item">
                <h4>动态课时分配</h4>
                <p>基于知识点复杂度与总课时数，AI加权计算课时分配方案</p>
              </div>
              <div class="feature-item">
                <h4>教案框架生成</h4>
                <p>一键生成包含教学目标、互动环节、重难点标注的教案模板</p>
              </div>
              <div class="feature-item">
                <h4>动态交互优化</h4>
                <p>教师通过对话窗口与AI实时调整教案内容</p>
              </div>
            </div>
            <div class="tool-action">
              <router-link to="/login" class="try-btn">立即体验</router-link>
            </div>
          </div>

          <!-- 多模态资源生成 -->
          <div class="tool-card">
            <div class="tool-icon">
              <i class="fas fa-photo-video"></i>
            </div>
            <h3>多模态资源生成</h3>
            <div class="tool-features">
              <div class="feature-item">
                <h4>PPT智能生成</h4>
                <p>根据教案框架，自动生成图文并茂的PPT，支持演讲备注等</p>
              </div>
              <div class="feature-item">
                <h4>数理动画制作</h4>
                <p>将抽象公式、几何图形转化为动态演示视频</p>
              </div>
              <div class="feature-item">
                <h4>配套图表库</h4>
                <p>提供网络搜索或关键词生成PPT插图</p>
              </div>
              <div class="feature-item">
                <h4>课堂记录</h4>
                <p>实时转写课堂音频，标记重点知识点与学生互动热点，生成教学笔记</p>
              </div>
            </div>
            <div class="tool-action">
              <router-link to="/login" class="try-btn">立即体验</router-link>
            </div>
          </div>

          <!-- 智能习题系统 -->
          <div class="tool-card">
            <div class="tool-icon">
              <i class="fas fa-tasks"></i>
            </div>
            <h3>智能习题系统</h3>
            <div class="tool-features">
              <div class="feature-item">
                <h4>梯度习题生成</h4>
                <p>按"预习→基础→拓展→挑战"分层生成习题，关联知识点标签</p>
              </div>
              <div class="feature-item">
                <h4>在线测试与限时练习</h4>
                <p>教师发布限时测试，学生端支持倒计时、答案提交</p>
              </div>
              <div class="feature-item">
                <h4>全题型自动批改</h4>
                <p>客观题规则引擎、简答题BERT语义评分、计算题OCR逻辑校验</p>
              </div>
              <div class="feature-item">
                <h4>作业查重检测</h4>
                <p>基于文本相似度算法，检测抄袭与拼接内容，生成可视化查重报告</p>
              </div>
            </div>
            <div class="tool-action">
              <router-link to="/login" class="try-btn">立即体验</router-link>
            </div>
          </div>

          <!-- 学情分析与推荐 -->
          <div class="tool-card">
            <div class="tool-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>学情分析与推荐</h3>
            <div class="tool-features">
              <div class="feature-item">
                <h4>学情报告生成</h4>
                <p>输出班级/个体学情报告，包含错题归因与资源推荐</p>
              </div>
              <div class="feature-item">
                <h4>端到端资源推荐</h4>
                <p>教师端推送薄弱点教案，学生端强化错题练习</p>
              </div>
              <div class="feature-item">
                <h4>知识图谱可视化</h4>
                <p>直观展示学生知识掌握情况，帮助教师精准把握教学重点</p>
              </div>
              <div class="feature-item">
                <h4>学习路径规划</h4>
                <p>基于学生学习数据，推荐个性化学习路径，提高学习效率</p>
              </div>
            </div>
            <div class="tool-action">
              <router-link to="/login" class="try-btn">立即体验</router-link>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 工具使用流程 -->
    <section class="workflow-section">
      <div class="section-content">
        <div class="section-header">
          <h2>使用流程</h2>
          <p>简单四步，轻松开启智能教学之旅</p>
        </div>
        
        <div class="workflow-steps">
          <div class="workflow-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>上传教材</h3>
              <p>上传教材目录或内容，AI自动解析知识结构</p>
            </div>
          </div>
          
          <div class="workflow-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>生成教案</h3>
              <p>一键生成教案框架，包含教学目标、重难点和教学流程</p>
            </div>
          </div>
          
          <div class="workflow-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>制作资源</h3>
              <p>自动生成PPT、习题和教学辅助资料</p>
            </div>
          </div>
          
          <div class="workflow-step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h3>教学反馈</h3>
              <p>收集学生反馈，分析学情，优化教学</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 用户案例 -->
    <section class="case-section">
      <div class="section-content">
        <div class="section-header">
          <h2>用户案例</h2>
          <p>看看其他教师如何使用启智星提升教学效果</p>
        </div>
        
        <div class="case-grid">
          <div class="case-card">
            <div class="case-image">
              <div class="image-placeholder">
                <span>高中物理</span>
              </div>
            </div>
            <div class="case-content">
              <h3>王老师 - 高中物理</h3>
              <p>"使用启智星的教案生成和PPT制作功能，我的备课时间减少了60%，学生对动态演示的物理概念理解更加深刻。"</p>
              <div class="case-results">
                <div class="result-item">
                  <span class="result-number">60%</span>
                  <span class="result-label">备课时间减少</span>
                </div>
                <div class="result-item">
                  <span class="result-number">25%</span>
                  <span class="result-label">学生成绩提升</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="case-card">
            <div class="case-image">
              <div class="image-placeholder">
                <span>初中数学</span>
              </div>
            </div>
            <div class="case-content">
              <h3>李老师 - 初中数学</h3>
              <p>"智能习题系统帮助我为不同水平的学生提供个性化练习，自动批改功能让我有更多时间关注学生的学习过程。"</p>
              <div class="case-results">
                <div class="result-item">
                  <span class="result-number">80%</span>
                  <span class="result-label">批改时间减少</span>
                </div>
                <div class="result-item">
                  <span class="result-number">30%</span>
                  <span class="result-label">学生参与度提升</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-section">
          <h4>关于我们</h4>
          <p>启智星致力于为教育工作者提供智能化教学解决方案，点亮教育之星</p>
        </div>
        <div class="footer-section">
          <h4>联系方式</h4>
          <p>邮箱：<EMAIL></p>
          <p>电话：400-123-4567</p>
          <p>地址：北京市海淀区中关村</p>
        </div>
        <div class="footer-section">
          <h4>快速链接</h4>
          <router-link to="/about">关于我们</router-link>
          <router-link to="/privacy">隐私政策</router-link>
          <router-link to="/terms">使用条款</router-link>
          <router-link to="/help">帮助中心</router-link>
        </div>
        <div class="footer-section">
          <h4>订阅我们</h4>
          <p>获取最新的教育科技资讯和更新</p>
          <div class="subscribe-form">
            <input type="email" placeholder="输入您的邮箱" />
            <button>订阅</button>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 启智星 EduSpark. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const menuActive = ref(false);

function toggleMenu() {
  menuActive.value = !menuActive.value;
}

function handleLoginClick() {
  // 清除本地存储的登录信息
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
}
</script>

<style scoped>
.tools-page {
  min-height: 100vh;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 导航栏样式 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: #1890ff;
  letter-spacing: -0.5px;
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
}

.logo-text-en {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.8;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s;
  font-weight: 500;
}

.nav-links a:hover,
.nav-links a.active {
  color: #1890ff;
}

.nav-auth {
  display: flex;
  gap: 1rem;
}

.login-btn,
.register-btn {
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  transition: all 0.3s;
  font-weight: 500;
}

.login-btn {
  color: #1890ff;
  border: 1px solid #1890ff;
}

.login-btn:hover {
  background: rgba(24, 144, 255, 0.1);
}

.register-btn {
  background: #1890ff;
  color: white;
}

.register-btn:hover {
  background: #40a9ff;
  transform: translateY(-2px);
}

.mobile-menu-icon {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 24px;
  height: 18px;
  cursor: pointer;
}

.mobile-menu-icon span {
  width: 100%;
  height: 2px;
  background-color: #1890ff;
  transition: all 0.3s;
}

/* 页面标题样式 */
.page-header {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  text-align: center;
  margin-top: 64px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #2c3e50;
  font-weight: 700;
}

.page-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

/* 工具列表样式 */
.tools-section {
  padding: 5rem 0;
}

.section-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.tool-card {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
}

.tool-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(24, 144, 255, 0.12);
}

.tool-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
  background: #e6f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: #1890ff;
}

.tool-card h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.tool-features {
  margin-bottom: 2rem;
}

.feature-item {
  margin-bottom: 1.2rem;
}

.feature-item h4 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.feature-item p {
  color: #666;
  line-height: 1.6;
}

.tool-action {
  margin-top: auto;
  text-align: center;
}

.try-btn {
  display: inline-block;
  padding: 0.8rem 2.5rem;
  background: #1890ff;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
}

.try-btn:hover {
  background: #40a9ff;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);
}

/* 工作流程样式 */
.workflow-section {
  padding: 5rem 0;
  background: #f8f9fa;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 700;
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

.workflow-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  max-width: 900px;
  margin: 0 auto;
}

.workflow-steps::before {
  content: "";
  position: absolute;
  top: 40px;
  left: 60px;
  right: 60px;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.workflow-step {
  position: relative;
  z-index: 2;
  text-align: center;
  flex: 1;
}

.step-number {
  width: 80px;
  height: 80px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 auto 1.5rem;
}

.step-content h3 {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 0.8rem;
  font-weight: 600;
}

.step-content p {
  color: #666;
  line-height: 1.6;
  max-width: 200px;
  margin: 0 auto;
}

/* 用户案例样式 */
.case-section {
  padding: 5rem 0;
}

.case-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.case-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
}

.case-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(24, 144, 255, 0.12);
}

.case-image {
  height: 200px;
  overflow: hidden;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1890ff, #69c0ff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
}

.case-content {
  padding: 2rem;
}

.case-content h3 {
  font-size: 1.6rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.case-content p {
  color: #666;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.case-results {
  display: flex;
  justify-content: space-around;
  margin-top: 1.5rem;
}

.result-item {
  text-align: center;
}

.result-number {
  display: block;
  font-size: 2rem;
  color: #1890ff;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.result-label {
  color: #666;
  font-size: 0.9rem;
}

/* 页脚样式 */
.footer {
  background: #2c3e50;
  color: white;
  padding: 4rem 0 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 2rem;
}

.footer-section h4 {
  margin-bottom: 1.2rem;
  font-size: 1.2rem;
  position: relative;
  padding-bottom: 0.8rem;
}

.footer-section h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: #1890ff;
}

.footer-section a {
  display: block;
  color: #ccc;
  text-decoration: none;
  margin-bottom: 0.8rem;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: white;
}

.subscribe-form {
  margin-top: 1rem;
  display: flex;
}

.subscribe-form input {
  flex: 1;
  padding: 0.8rem;
  border: none;
  border-radius: 4px 0 0 4px;
  outline: none;
}

.subscribe-form button {
  padding: 0.8rem 1.2rem;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  transition: background 0.3s;
}

.subscribe-form button:hover {
  background: #40a9ff;
}

.footer-bottom {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .tools-grid {
    grid-template-columns: 1fr;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .workflow-steps {
    flex-direction: column;
    gap: 2rem;
  }
  
  .workflow-steps::before {
    display: none;
  }
  
  .case-grid {
    grid-template-columns: 1fr;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 768px) {
  .mobile-menu-icon {
    display: flex;
  }
  
  .nav-links {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    flex-direction: column;
    background: white;
    padding: 1rem;
    gap: 1rem;
    box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    display: none;
    text-align: center;
  }
  
  .nav-active {
    display: flex;
  }
  
  .page-header h1 {
    font-size: 2.2rem;
  }
  
  .section-header h2 {
    font-size: 2rem;
  }
  
  .step-content p {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .nav-content {
    padding: 0.8rem;
  }
  
  .logo {
    font-size: 1.5rem;
  }
  
  .login-btn, .register-btn {
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
  }
  
  .page-header {
    padding: 7rem 0 3rem;
  }
  
  .page-header h1 {
    font-size: 1.8rem;
  }
  
  .tool-card {
    padding: 1.5rem;
  }
  
  .tool-card h3 {
    font-size: 1.5rem;
  }
  
  .case-results {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>

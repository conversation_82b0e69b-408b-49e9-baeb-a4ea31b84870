import axios from 'axios'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'

const instance = axios.create({
  baseURL: 'http://localhost:3000',  // 确保指向后端服务器
  timeout: 60000, // 增加超时时间到 60 秒
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 添加请求拦截器
instance.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    // 移除可能导致问题的头部
    delete config.headers['Access-Control-Allow-Origin']
    
    return config
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 添加响应拦截器
instance.interceptors.response.use(
  response => {
    return response
  },
  error => {
    console.error('Response Error:', error)
    
    const userStore = useUserStore()
    
    if (error.response) {
      // 只处理token相关的错误
      if (error.response.status === 401 && error.response.data?.message?.includes('token')) {
        message.error('登录已过期，请重新登录')
        const isStudent = userStore.userInfo?.role === 'student'
        userStore.clearUser()
        window.location.href = isStudent ? '/student-login' : '/login'
        return Promise.reject(error)
      }

      // 其他错误只显示消息，不跳转
      message.error(error.response.data?.message || '请求失败')
    } else if (error.request) {
      message.error('网络错误，请检查网络连接')
    } else {
      message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default instance 
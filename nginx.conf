# Nginx配置文件示例
server {
    listen 80;
    server_name your_domain.com;  # 替换成你的域名

    # 前端静态文件
    location / {
        root /path/to/your/dist;  # 替换成你的dist目录路径
        index index.html;
        try_files $uri $uri/ /index.html;  # 支持vue-router的history模式
    }

    # 后端API代理
    location /api {
        proxy_pass http://localhost:3000;  # 后端服务地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
} 
import json
import os
from openai import OpenAI
from icrawler.builtin import BaiduImageCrawler
from icrawler.downloader import Downloader
import sys
import io
import traceback
import time
import pymysql
import pymysql.cursors
import base64
from urllib.parse import urlparse, unquote

# 设置编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stdin = io.TextIOWrapper(sys.stdin.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 数据库配置
db_config = {
    'host': '**************',
    'user': 'root',
    'password': 'mydb123',
    'database': 'teaching_assistant',
    'port': 3306,
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 尝试从数据库读取API配置
def get_api_key():
    try:
        # 连接到数据库
        connection = pymysql.connect(**db_config)
        with connection.cursor() as cursor:
            # 查询火山引擎配置
            cursor.execute("SELECT api_key FROM model_configs WHERE provider = 'volcano' AND status = 1 LIMIT 1")
            result = cursor.fetchone()
            
            if result and 'api_key' in result:
                return result['api_key']
            
            # 如果数据库中没有找到，从环境变量获取
            return os.environ.get('ARK_API_KEY', '')
    except Exception as e:
        print(f"从数据库获取API密钥失败: {e}", file=sys.stderr)
        # 失败时从环境变量获取
        return os.environ.get('ARK_API_KEY', '')
    finally:
        if 'connection' in locals() and connection:
            connection.close()

# 初始化 OpenAI 客户端
ark_api_key = get_api_key()
client = OpenAI(
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key=ark_api_key
)

# 自定义下载器，只收集链接不下载图片
class LinkCollectorDownloader(Downloader):
    def __init__(self, thread_pool, signal, session, storage):
        super(LinkCollectorDownloader, self).__init__(thread_pool, signal, session, storage)
        self.collected_links = []

    def download(self, task, default_ext, timeout=5, max_retry=3, **kwargs):
        image_url = task['file_url']
        if image_url not in self.collected_links:
            self.collected_links.append(image_url)
            self.logger.info('收集链接 #{} {}'.format(len(self.collected_links), image_url))
        return 1

    def get_links(self):
        return self.collected_links

# 改进清理和验证图片URL的函数
def clean_image_url(url):
    """清理图片URL，移除特殊修饰符和不必要的参数"""
    if not url or not isinstance(url, str):
        return url
    
    # 原始URL
    original_url = url
    
    try:
        # 1. 处理B站等平台的特殊格式：xxx.jpg@.webp
        if '@' in url and '.jpg@' in url.lower():
            # 找到 .jpg@ 的位置，只保留前面部分
            jpg_pos = url.lower().find('.jpg@')
            if jpg_pos > 0:
                url = url[:jpg_pos+4]  # 保留.jpg
                
        # 2. 处理百度等平台的图片：移除复杂的处理参数
        elif '?' in url and ('.jpg?' in url.lower() or '.jpeg?' in url.lower() or 
                        '.png?' in url.lower() or '.gif?' in url.lower() or '.webp?' in url.lower()):
            # 找到第一个问号位置，只保留问号前面的部分
            q_pos = url.find('?')
            if q_pos > 0:
                url = url[:q_pos]
                
        # 3. 处理其他可能的格式问题
        # 有些URL可能包含!开头的修饰符
        if '!' in url and any(ext in url.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
            for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                if ext in url.lower():
                    ext_pos = url.lower().find(ext)
                    url = url[:ext_pos+len(ext)]
                    break
                    
        # 记录URL是否被清理
        if url != original_url:
            print(f"已清理URL: {original_url} -> {url}", file=sys.stderr)
            
        return url
    except Exception as e:
        print(f"清理URL时出错: {e}", file=sys.stderr)
        return original_url

# 重写图片链接验证函数，支持特殊URL格式
def is_valid_image_url(url, clean_if_invalid=True):
    """严格检查URL是否是有效的图片链接，支持特殊格式处理"""
    if not url or not isinstance(url, str):
        return False, url
        
    # 首先尝试直接验证
    is_valid = _check_image_url(url)
    
    # 如果无效且启用了清理选项，尝试清理后再验证
    if not is_valid and clean_if_invalid:
        cleaned_url = clean_image_url(url)
        if cleaned_url != url:
            is_valid = _check_image_url(cleaned_url)
            if is_valid:
                return True, cleaned_url
    
    return is_valid, url

# 将原有的验证逻辑拆分为独立函数
def _check_image_url(url):
    """执行实际的URL验证检查"""
    try:
        # 移除URL参数部分
        url_without_params = url.split('?')[0].lower()
        
        # 提取URL路径的最后部分来检查文件名
        path_parts = url_without_params.split('/')
        if not path_parts:
            return False
            
        filename = path_parts[-1]
        
        # 检查文件名是否包含其他分隔符，并且不按实际文件名处理
        if ':' in filename:
            return False
            
        # 如果文件名中有@符号，可能是特殊格式的图片，处理它
        if '@' in filename:
            parts = filename.split('@')
            filename = parts[0]  # 取@前面的部分
            
        # 严格验证文件扩展名
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        
        # 文件名必须以有效扩展名结尾
        is_valid = any(filename.endswith(ext) for ext in valid_extensions)
        
        # 排除常见的非图片URL模式
        invalid_patterns = [
            'tplv-dy-aweme-images',  # 抖音特殊格式
            'tos-cn-i-',             # 抖音标识
            '/view/',                # 通常是预览页面而不是直接图片
            '.html', '.php', '.asp', '.jsp'  # 网页格式
        ]
        
        if any(pattern in url_without_params for pattern in invalid_patterns):
            return False
            
        return is_valid
        
    except Exception as e:
        print(f"URL验证出错: {e}", file=sys.stderr)
        return False

# 修改获取图片链接函数，使用支持特殊格式的验证
def get_image_links(keyword, limit=5):
    """获取指定关键词的图片链接，支持特殊格式URL处理"""
    try:
        crawler = BaiduImageCrawler(
            downloader_cls=LinkCollectorDownloader,
            storage={'root_dir': 'temp_dir'}
        )
        
        # 尝试获取更多链接以确保足够的有效图片
        max_crawl = min(limit * 10, 60)  # 增加抓取数量以确保有足够的有效图片
        crawler.crawl(keyword=keyword, max_num=max_crawl)
        
        if not hasattr(crawler.downloader, 'collected_links'):
            print(f"关键词 '{keyword}' 没有找到任何链接", file=sys.stderr)
            return []
            
        all_links = crawler.downloader.collected_links
        
        # 严格过滤，只保留真正的图片链接
        valid_links = []
        for link in all_links:
            is_valid, clean_link = is_valid_image_url(link)
            if is_valid:
                valid_links.append(clean_link)  # 使用清理后的链接
                if len(valid_links) >= limit:
                    break
        
        print(f"关键词 '{keyword}' 的图片链接: 总计{len(all_links)}个, 有效{len(valid_links)}个", file=sys.stderr)
        
        return valid_links
        
    except Exception as e:
        print(f"获取图片链接时出错 ({keyword}): {e}", file=sys.stderr)
        return []

# 更新辅助函数，使用支持特殊格式的验证
def validate_existing_links(links):
    """验证现有链接并返回有效的图片链接"""
    valid_links = []
    for link in links:
        is_valid, clean_link = is_valid_image_url(link)
        if is_valid:
            valid_links.append(clean_link)  # 使用清理后的链接
    
    print(f"链接验证: 总计{len(links)}个, 有效{len(valid_links)}个", file=sys.stderr)
    return valid_links

def analyze_ppt_content(content):
    """使用AI分析PPT内容，找出需要配图的页面和关键词"""
    prompt = f"""分析下面的PPT大纲，返回需要配图的页面信息。直接返回JSON格式：

PPT内容：
{content}

只返回如下格式的JSON（不要包含任何其他内容）：
{{
    "image_requirements": [
        {{
            "page_number": "第4页",
            "content": "实验仪器图片",
            "keywords": ["气垫导轨", "光电门", "数字毫秒计"]
        }},
        {{
            "page_number": "第11页",
            "content": "安全事项",
            "keywords": ["实验室安全警示", "用电安全标识", "实验室应急处理"]
        }}
    ]
}}

注意：
1. 只分析明确需要图片的页面，且最终的图应该是解释性的，而不是有例题之类的
2. 关键词要具体且准确
3. 每页最多3个关键词
4. 必须是标准JSON格式"""

    try:
        response = client.chat.completions.create(
            model="ep-20250310135629-zgwb7",
            messages=[
                {"role": "system", "content": "你是一个PPT设计专家。请只返回JSON格式数据，不要有任何其他文字。"},
                {"role": "user", "content": prompt}
            ],
            stream=False
        )
        
        result = response.choices[0].message.content.strip()
        
        try:
            if not result.startswith('{'):
                import re
                json_match = re.search(r'\{.*\}', result, re.DOTALL)
                if json_match:
                    result = json_match.group()
            
            result = result.strip()
            result = result.replace('\n', '')
            result = result.replace('    ', '')
            
            return json.loads(result)
            
        except json.JSONDecodeError as e:
            result = result.replace("'", '"')
            if not result.startswith('{'):
                result = '{' + result
            if not result.endswith('}'):
                result = result + '}'
            
            try:
                return json.loads(result)
            except json.JSONDecodeError:
                return None
        
    except Exception as e:
        return None

# 添加数据库连接和查询功能
def connect_to_database():
    """连接到MySQL数据库"""
    try:
        connection = pymysql.connect(**db_config)
        print("数据库连接成功", file=sys.stderr)
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}", file=sys.stderr)
        return None

def find_matching_template(connection, ppt_content):
    """根据PPT内容查找匹配的模板记录"""
    try:
        # 首先检查环境变量中是否有指定的模板ID
        template_id = os.environ.get('TEMPLATE_ID')
        if template_id:
            try:
                template_id = int(template_id)
                print(f"从环境变量中获取模板ID: {template_id}", file=sys.stderr)
                
                with connection.cursor() as cursor:
                    cursor.execute(
                        "SELECT id, teacher_id, course_code, title_id FROM ppt_templates WHERE id = %s",
                        (template_id,)
                    )
                    result = cursor.fetchone()
                    if result:
                        print(f"使用指定的模板ID记录: ID={result['id']}", file=sys.stderr)
                        return result
            except ValueError:
                print(f"环境变量中的模板ID无效: {template_id}", file=sys.stderr)
        
        # 如果没有传递ID或无效，继续使用原来的查询逻辑
        with connection.cursor() as cursor:
            # 尝试使用标题匹配
            title = ppt_content.get('title', '')
            if title:
                cursor.execute(
                    "SELECT id, teacher_id, course_code, title_id FROM ppt_templates WHERE title = %s ORDER BY create_time DESC LIMIT 1",
                    (title,)
                )
                result = cursor.fetchone()
                if result:
                    print(f"通过标题找到匹配记录: ID={result['id']}", file=sys.stderr)
                    return result
            
            # 如果没有通过标题找到，尝试通过内容的部分比较
            if ppt_content.get('chapters'):
                # 获取第一个章节标题作为关键词
                first_chapter_title = ppt_content['chapters'][0].get('chapterTitle', '')
                if first_chapter_title:
                    # 使用LIKE查询查找内容中包含此章节标题的记录
                    cursor.execute(
                        "SELECT id, teacher_id, course_code, title_id FROM ppt_templates WHERE content LIKE %s ORDER BY create_time DESC LIMIT 1",
                        (f'%{first_chapter_title}%',)
                    )
                    result = cursor.fetchone()
                    if result:
                        print(f"通过章节内容找到匹配记录: ID={result['id']}", file=sys.stderr)
                        return result
            
            # 如果还是没找到，获取最近创建的记录
            cursor.execute(
                "SELECT id, teacher_id, course_code, title_id FROM ppt_templates ORDER BY create_time DESC LIMIT 1"
            )
            result = cursor.fetchone()
            if result:
                print(f"使用最新记录: ID={result['id']}", file=sys.stderr)
                return result
                
            return None
            
    except Exception as e:
        print(f"查找匹配模板时出错: {e}", file=sys.stderr)
        return None

def update_template_pictures(connection, template_id, pictures_json):
    """更新模板的PPT图片字段"""
    try:
        with connection.cursor() as cursor:
            cursor.execute(
                "UPDATE ppt_templates SET ppt_picture = %s, update_time = NOW() WHERE id = %s",
                (pictures_json, template_id)
            )
            connection.commit()
            print(f"成功更新模板ID={template_id}的图片数据", file=sys.stderr)
        return True
    except Exception as e:
        print(f"更新模板图片数据失败: {e}", file=sys.stderr)
        connection.rollback()
        return False

def main():
    try:
        # 从标准输入读取 PPT 内容
        input_data = sys.stdin.read()
        
        if not input_data:
            return
            
        try:
            ppt_content = json.loads(input_data)
        except json.JSONDecodeError:
            return
            
        # 分析PPT内容
        analysis_result = analyze_ppt_content(ppt_content)
        if not analysis_result:
            analysis_result = {
                "image_requirements": []
            }
            
        # 收集每个页面的图片链接
        all_results = {}
        
        for page in analysis_result.get('image_requirements', []):
            page_num = page['page_number']
            page_content = page['content']
            
            # 这里是关键修改：确保每页只有一个与内容同名的关键词
            # 关键词列表只取第一个，如果没有就用页面内容作为关键词
            keyword = page_content
            
            page_results = {
                "content": page_content,
                "keywords": {}
            }
            
            # 为关键词获取图片链接
            links = get_image_links(keyword)
            # 只保存有效链接
            page_results["keywords"][keyword] = links
                    
            all_results[page_num] = page_results
        
        # 如果没有分析出需要图片的页面，自动为每个章节生成图片需求
        if not all_results:
            page_num = 2
            if ppt_content.get("chapters"):
                for chapter in ppt_content["chapters"]:
                    chapter_title = chapter.get("chapterTitle", "")
                    
                    # 为每个章节添加图片需求，使用章节标题作为内容和唯一关键词
                    all_results[f"第{page_num}页"] = {
                        "content": chapter_title,
                        "keywords": {}
                    }
                    
                    # 为章节标题获取图片
                    links = get_image_links(chapter_title)
                    all_results[f"第{page_num}页"]["keywords"][chapter_title] = links
                    
                    page_num += 1
                    
                    # 为子章节添加图片需求
                    if chapter.get("chapterContents"):
                        for content in chapter["chapterContents"]:
                            if isinstance(content, dict) and content.get("chapterTitle"):
                                content_title = content.get("chapterTitle", "")
                                
                                # 同样使用内容标题作为唯一关键词
                                all_results[f"第{page_num}页"] = {
                                    "content": content_title,
                                    "keywords": {}
                                }
                                
                                # 为子章节标题获取图片
                                links = get_image_links(content_title)
                                all_results[f"第{page_num}页"]["keywords"][content_title] = links
                                
                                page_num += 1
        
        # 最终验证所有链接
        for page_key in all_results:
            page = all_results[page_key]
            # 确保每页只有一个关键词，且关键词名称与页面内容相同
            if not page["keywords"] or page["content"] not in page["keywords"]:
                content = page["content"]
                # 如果没有关键词或内容不在关键词中，使用内容作为唯一关键词
                old_keywords = page["keywords"]
                links = []
                
                # 如果有其他关键词，使用第一个关键词的链接
                if old_keywords and len(old_keywords) > 0:
                    first_key = list(old_keywords.keys())[0]
                    links = old_keywords[first_key]
                
                page["keywords"] = {content: links}
            
            # 如果有多个关键词，只保留与内容同名的一个
            elif len(page["keywords"]) > 1:
                content = page["content"]
                if content in page["keywords"]:
                    links = page["keywords"][content]
                    page["keywords"] = {content: links}
                else:
                    # 如果没有与内容同名的关键词，使用第一个关键词
                    first_key = list(page["keywords"].keys())[0]
                    links = page["keywords"][first_key]
                    page["keywords"] = {content: links}
            
            # 验证链接
            for keyword in page["keywords"]:
                links = page["keywords"][keyword]
                # 进行最终验证，确保只有真正的图片链接
                validated_links = validate_existing_links(links)
                # 更新为验证后的链接
                page["keywords"][keyword] = validated_links
        
        # 添加数据库存储功能 - 存储原始JSON结构
        connection = connect_to_database()
        if connection:
            try:
                # 寻找匹配的模板记录
                template = find_matching_template(connection, ppt_content)
                if template:
                    # 更新模板的ppt_picture字段
                    json_data = json.dumps(all_results, ensure_ascii=False)
                    success = update_template_pictures(connection, template['id'], json_data)
                    if success:
                        print(f"已成功将图片数据保存到数据库，模板ID: {template['id']}", file=sys.stderr)
                        # 关键修改：输出成功状态到标准输出（非标准错误）
                        print(f"DB_SAVED:{template['id']}")
                    else:
                        print("保存到数据库失败", file=sys.stderr)
                else:
                    print("未找到匹配的PPT模板记录", file=sys.stderr)
            finally:
                connection.close()
        
    except Exception as e:
        traceback.print_exc(file=sys.stderr)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        traceback.print_exc(file=sys.stderr) 

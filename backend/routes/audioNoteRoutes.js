import express from 'express';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { transcribeAudioFile, generateNote, saveNote, upload } from '../controllers/audioNoteController.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 音频笔记相关接口
router.post('/audio-summary/transcribe', upload.single('audioFile'), transcribeAudioFile);
router.post('/audio-summary/generate', generateNote);
router.post('/audio-summary/save', saveNote);

export default router;

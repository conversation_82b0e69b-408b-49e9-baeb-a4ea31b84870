import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import {
  getAvailableCourses,
  getStudentCourses,
  registerCourse,
  dropCourse
} from '../controllers/courseRegistrationController.js';

const router = express.Router();

// 获取可选课程列表
router.get('/available', authenticateToken, getAvailableCourses);

// 获取学生已选课程
router.get('/my-courses', authenticateToken, getStudentCourses);

// 选课
router.post('/register', authenticateToken, registerCourse);

// 退课
router.delete('/drop/:course_code', authenticateToken, dropCourse);

export default router; 
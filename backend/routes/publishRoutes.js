import express from 'express';
import {
  getChapters,
  getExercises,
  createExercise,
  getExerciseDetail,
  getExerciseStats,
  getChapterExercises,
  batchCreateExercises,
  getAllExercises,
  getExercisePublishCount,
  getAllExercisePublishCounts,
  getNextBatch,
  getChapterBatches,
  getBatchExercises
} from '../controllers/publishController.js';
import { authenticateToken, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// 设置可选认证
router.use(optionalAuth);

// 获取章节列表（按课程和班级）
router.get('/classes/:classId/chapters', getChapters);

// 获取发布的习题列表
router.get('/classes/:classId/exercises', getExercises);

// 获取所有习题（包括未发布的）
router.get('/classes/:classId/exercises/all', getAllExercises);

// 获取习题详情
router.get('/exercises/:exerciseId', getExerciseDetail);

// 获取习题统计
router.get('/exercises/:exerciseId/stats', getExerciseStats);

// 发布单个习题
router.post('/classes/:classId/exercises', authenticateToken, createExercise);

// 批量发布习题
router.post('/classes/:classId/exercises/batch', authenticateToken, batchCreateExercises);

// 获取章节下的习题（用于发布前预览）
router.get('/classes/exercises/chapter/:titleId', getChapterExercises);

// 获取习题发布次数
router.get('/classes/:classId/exercises/:exerciseId/count', getExercisePublishCount);

// 批量获取习题发布次数
router.get('/classes/:classId/exercises/counts', getAllExercisePublishCounts);

// 获取下一个批次号
router.get('/classes/:classId/exercises/nextbatch', getNextBatch);

// 获取章节批次列表
router.get('/classes/:classId/exercises/batches', getChapterBatches);

// 获取指定批次的习题列表
router.get('/classes/:classId/exercises/batch/:batchNumber', getBatchExercises);

export default router;

import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import {
    processSyllabus,
    generateLessonPlan,
    getTeacherAndCourses
} from '../controllers/lessonPlanController.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../public/uploads/syllabus');
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ storage: storage });

// 路由配置 - 注意中间件的顺序
router.get('/teacher-info', authenticateToken, getTeacherAndCourses);
router.post('/process-syllabus', [authenticateToken, upload.array('images', 10)], processSyllabus);
router.post('/generate-lesson-plan', authenticateToken, generateLessonPlan);

export default router; 
import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { 
  getStudentClasses,
  getStudentPreviewMaterials,
  getPreviewDetail,
  submitPreview,
  recordPreviewView,
  updateStudyDuration,
  getStudyDuration
} from '../controllers/student_previewController.js';

const router = express.Router();

// 获取学生所在的班级列表
router.get('/classes', authenticateToken, getStudentClasses);

// 获取学生的预习资料列表
router.get('/previews', authenticateToken, getStudentPreviewMaterials);

// 获取预习资料详情
router.get('/previews/:previewId', authenticateToken, getPreviewDetail);

// 提交预习作业
router.post('/previews/:previewId/submit', authenticateToken, submitPreview);

// 记录预习查看记录
router.post('/previews/:previewId/view', authenticateToken, recordPreviewView);

// 更新学习时长（已废弃，保留以兼容旧版前端）
router.post('/previews/:previewId/duration', authenticateToken, updateStudyDuration);

// 获取学习时长
router.get('/previews/:previewId/duration', authenticateToken, getStudyDuration);

export default router; 
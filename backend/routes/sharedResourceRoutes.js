import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { 
  getTeacherClasses, 
  getClassStudents, 
  shareVideoToStudents 
} from '../controllers/sharedResourceController.js';

const router = express.Router();

// 应用认证中间件
router.use(authenticateToken);

// 获取教师班级列表
router.get('/classes', getTeacherClasses);

// 获取班级学生列表
router.get('/classes/:classId/students', getClassStudents);

// 分享视频给学生
router.post('/recommend', shareVideoToStudents);

export default router; 
import express from 'express';
import { authenticateToken, optionalAuth } from '../middleware/auth.js';
import {
  createAnimation,
  getAnimationStatus,
  getAnimationRecords,
  getAnimationRecord
} from '../controllers/anime_make.js';

const router = express.Router();

// 生成动画 - 使用authenticateToken中间件要求登录
router.post('/generate', authenticateToken, createAnimation);

// 获取动画生成状态
router.get('/status/:taskId', authenticateToken, getAnimationStatus);

// 获取动画记录列表
router.get('/records', authenticateToken, getAnimationRecords);

// 获取动画记录详情
router.get('/records/:id', authenticateToken, getAnimationRecord);

export default router;

import express from 'express';
import { getStudentClasses, getClassChapters, getChapterPreview } from '../controllers/exercise_previewController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取学生所在的班级列表
router.get('/student/:studentId/classes', getStudentClasses);

// 获取班级的章节列表（有预习题的章节）
router.get('/class/:classId/chapters', getClassChapters);

// 获取章节预习题
router.get('/class/:classId/chapter/:titleId/preview', getChapterPreview);

export default router;

import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import pool from '../config/db.js';

const router = express.Router();

// 这里暂时只是创建一个空路由文件，以解决导入错误
// 后续可以添加更多课程时间相关的路由

// 简单的测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '课程时间路由测试成功'
  });
});

// 获取教师所有课程的时间冲突
router.get('/conflicts', authenticateToken, async (req, res) => {
  try {
    const user_id = req.user?.system_teacher_id;
    
    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: '未找到教师ID'
      });
    }

    // 获取当前教师的所有课程时间
    const [schedules] = await pool.execute(
      `SELECT 
        cs.id, 
        cs.course_id, 
        c.course_name,
        cs.class_time
      FROM class_schedules cs
      JOIN courses c ON cs.course_id = c.course_code
      WHERE cs.user_id = ? AND cs.is_current = 1`,
      [user_id]
    );

    // 解析所有课程的上课时间
    const allClassTimes = [];
    
    for (const schedule of schedules) {
      const classTimesArray = JSON.parse(schedule.class_time || '[]');
      
      if (Array.isArray(classTimesArray)) {
        classTimesArray.forEach(timeSlot => {
          allClassTimes.push({
            schedule_id: schedule.id,
            course_id: schedule.course_id,
            course_name: schedule.course_name,
            day: timeSlot.day,
            periods: timeSlot.periods,
            location: timeSlot.location
          });
        });
      }
    }

    // 检测时间冲突
    const conflicts = [];
    
    for (let i = 0; i < allClassTimes.length; i++) {
      for (let j = i + 1; j < allClassTimes.length; j++) {
        const timeA = allClassTimes[i];
        const timeB = allClassTimes[j];
        
        // 如果两个时间段在同一天
        if (timeA.day === timeB.day) {
          // 检查是否有重叠的节次
          const overlappingPeriods = timeA.periods.filter(period => 
            timeB.periods.includes(period)
          );
          
          if (overlappingPeriods.length > 0) {
            conflicts.push({
              courseA: {
                id: timeA.schedule_id,
                course_id: timeA.course_id,
                course_name: timeA.course_name,
                location: timeA.location
              },
              courseB: {
                id: timeB.schedule_id,
                course_id: timeB.course_id,
                course_name: timeB.course_name,
                location: timeB.location
              },
              day: timeA.day,
              periods: overlappingPeriods
            });
          }
        }
      }
    }

    res.json({
      success: true,
      data: conflicts
    });
  } catch (error) {
    console.error('检测时间冲突失败:', error);
    res.status(500).json({
      success: false,
      message: '检测时间冲突失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 获取教师指定周的课程时间分布
router.get('/week-distribution', authenticateToken, async (req, res) => {
  try {
    const user_id = req.user?.system_teacher_id;
    const week = parseInt(req.query.week) || 1;
    
    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: '未找到教师ID'
      });
    }

    // 获取当前教师的所有课程
    const [schedules] = await pool.execute(
      `SELECT 
        cs.id, 
        cs.course_id, 
        c.course_name,
        cs.class_time,
        cs.teaching_weeks
      FROM class_schedules cs
      JOIN courses c ON cs.course_id = c.course_code
      WHERE cs.user_id = ? AND cs.is_current = 1`,
      [user_id]
    );

    // 初始化每天的课程分布
    const weekDistribution = {
      1: [], // 周一
      2: [], // 周二
      3: [], // 周三
      4: [], // 周四
      5: []  // 周五
    };

    // 处理每个课程
    for (const schedule of schedules) {
      // 解析教学周
      const teachingWeeks = JSON.parse(schedule.teaching_weeks || '{"start":1,"end":20,"exclude":[]}');
      
      // 检查当前周是否在教学周范围内
      const isInRange = week >= teachingWeeks.start && 
                        week <= teachingWeeks.end && 
                        !teachingWeeks.exclude.includes(week);
      
      if (!isInRange) {
        continue;
      }
      
      // 解析上课时间
      const classTimesArray = JSON.parse(schedule.class_time || '[]');
      
      if (Array.isArray(classTimesArray)) {
        classTimesArray.forEach(timeSlot => {
          const day = timeSlot.day;
          
          if (day >= 1 && day <= 5) {
            timeSlot.periods.forEach(period => {
              weekDistribution[day].push({
                period,
                course_id: schedule.course_id,
                course_name: schedule.course_name,
                location: timeSlot.location
              });
            });
          }
        });
      }
    }

    // 对每天的课程按照节次排序
    for (const day in weekDistribution) {
      weekDistribution[day].sort((a, b) => a.period - b.period);
    }

    res.json({
      success: true,
      data: weekDistribution,
      week
    });
  } catch (error) {
    console.error('获取周课程分布失败:', error);
    res.status(500).json({
      success: false,
      message: '获取周课程分布失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router; 
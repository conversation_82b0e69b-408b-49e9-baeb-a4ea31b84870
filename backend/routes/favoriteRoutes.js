import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { addFavorite, getFavorites, removeFavorite, checkFavoriteStatus } from '../controllers/favoriteController.js';

const router = express.Router();

// 所有收藏相关的路由都需要认证
router.use(authenticateToken);

// 添加收藏
router.post('/', addFavorite);

// 获取收藏列表
router.get('/', getFavorites);

// 检查收藏状态
router.get('/check', checkFavoriteStatus);

// 取消收藏
router.delete('/:id', removeFavorite);

export default router; 
import express from 'express';
import plagiarismAnalysisController from '../controllers/plagiarismAnalysisController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 查重分析路由
// 根据班级、章节、批次获取查重分析数据
router.get('/:classId/:chapterId/:batchId', authenticateToken, plagiarismAnalysisController.getPlagiarismAnalysis);

// 手动触发查重分析
router.post('/run-check/:classId/:chapterId/:batchId', authenticateToken, plagiarismAnalysisController.runPlagiarismCheck);

export default router; 
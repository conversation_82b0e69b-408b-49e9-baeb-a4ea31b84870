import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import {
  generatePreviewMaterial,
  getPreviewMaterial,
  getClassPreviews,
  getPreviewSubmissions
} from '../controllers/previewController.js';
import { getPreviewDetail } from '../controllers/preview_publishController.js';

const router = express.Router();

// 生成预习资料
router.post('/generate', authenticateToken, generatePreviewMaterial);

// 获取预习资料
router.get('/section/:courseCode/:titleId', authenticateToken, getPreviewMaterial);

// 获取班级预习任务列表（包含完成率）
router.get('/class/:classId', authenticateToken, getClassPreviews);

// 获取预习任务的学生提交情况
router.get('/:previewId/submissions', authenticateToken, getPreviewSubmissions);

// 获取预习详情（包括学生完成情况）
router.get('/:previewId/detail', authenticateToken, getPreviewDetail);

export default router;

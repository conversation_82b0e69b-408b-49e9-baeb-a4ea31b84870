import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { 
  getTeacherCourses, 
  getCurrentSchedule,
  updateSchedule,
  getScheduleHistory,
  testDatabaseQuery,
  createSchedule,
  getScheduleBySemester,
  deleteClassTime
} from '../controllers/courseController.js';
import db from '../config/db.js';

const router = express.Router();

// 获取所有课程
router.get('/', async (req, res) => {
    try {
        console.log('获取所有课程...');
        const [courses] = await db.query('SELECT * FROM courses');
        console.log('查询结果:', courses);
        res.json({ success: true, data: courses });
    } catch (error) {
        console.error('获取课程列表失败:', error);
        res.status(500).json({ success: false, message: '获取课程列表失败' });
    }
});

// 获取教师的课程列表
router.get('/teacher/:teacherId', getTeacherCourses);

// 获取当前课表
router.get('/schedule/current/:teacherId', authenticateToken, getCurrentSchedule);

// 获取指定学期的课表
router.get('/schedule/:teacherId', authenticateToken, getScheduleBySemester);

// 获取历史课表
router.get('/schedule/history/:teacherId', authenticateToken, getScheduleHistory);

// 更新课表
router.put('/schedule/:id', authenticateToken, updateSchedule);

// 创建课程表
router.post('/schedule', authenticateToken, createSchedule);

// 删除上课时间
router.delete('/schedule/:scheduleId/time', authenticateToken, deleteClassTime);

// 测试查询接口
router.get('/test/database', testDatabaseQuery);

export default router; 
import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { 
  getClassChaptersWithPreview,
  publishPreview,
  getClassPreviews,
  getPreviewDetail,
  cancelPreview,
  getPreviewMaterialContent
} from '../controllers/preview_publishController.js';

const router = express.Router();

// 获取班级的章节列表（包含预习资料信息）
router.get('/:classId/chapters-with-preview', authenticateToken, getClassChaptersWithPreview);

// 获取班级的预习列表
router.get('/:classId/previews', authenticateToken, getClassPreviews);

// 获取预习详情
router.get('/previews/:previewId', authenticateToken, getPreviewDetail);

// 获取预习资料内容
router.get('/previews/material/:titleId', authenticateToken, getPreviewMaterialContent);

// 发布预习资料
router.post('/:classId/publish-preview', authenticateToken, publishPreview);

// 取消发布预习资料
router.delete('/previews/:previewId', authenticateToken, cancelPreview);

export default router;

import express from 'express';
import { authenticateToken } from '../middleware/authMiddleware.js';

const router = express.Router();

// 导入控制器
import videoNoteController from '../controllers/videoNoteController.js';

// 所有路由都需要认证
router.use(authenticateToken);

// 视频笔记相关接口
router.post('/video-summary/transcribe', videoNoteController.transcribeVideo);
router.post('/video-summary/generate', videoNoteController.generateNote);
router.post('/video-summary/save', videoNoteController.saveNote);

// 添加检查视频是否已有转录和笔记数据的路由
router.get('/video-summary/check/:videoId', videoNoteController.checkVideoData);

// 笔记管理接口
router.get('/video-notes', videoNoteController.getUserNotes);
router.get('/video-notes/:id', videoNoteController.getNoteDetail);
router.delete('/video-notes/:id', videoNoteController.deleteNote);

export default router; 
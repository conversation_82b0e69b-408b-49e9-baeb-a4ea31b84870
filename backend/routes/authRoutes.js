import express from 'express'
import cors from 'cors'
import multer from 'multer'
import path from 'path'
import fs from 'fs'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import mysql from 'mysql2/promise'
import { dbConfig } from '../config/db.js'

import { register, login, updateProfile, uploadAvatar } from '../controllers/userController.js'
import { authenticateToken } from '../middleware/auth.js'
import { loginLimiter, registerLimiter } from '../middleware/rateLimiter.js'

const router = express.Router()

// 获取当前目录
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 确保头像目录存在
const avatarDir = path.join(__dirname, '../..', 'public/avatar')
if (!fs.existsSync(avatarDir)) {
  fs.mkdirSync(avatarDir, { recursive: true })
}

// 获取下一个可用的头像ID
async function getNextAvatarId() {
  try {
    // 读取avatar目录下的所有文件
    const files = fs.readdirSync(avatarDir)
    
    // 筛选出avatar-开头的文件，并提取数字部分
    const avatarFiles = files.filter(file => /^avatar-\d+\.(jpg|jpeg|png|gif)$/i.test(file))
    const avatarNumbers = avatarFiles.map(file => {
      const match = file.match(/^avatar-(\d+)\./i)
      return match ? parseInt(match[1], 10) : 0
    })
    
    // 找出最大的数字
    const maxNumber = avatarNumbers.length > 0 ? Math.max(...avatarNumbers) : 0
    // 返回下一个数字，并格式化为四位数
    return (maxNumber + 1).toString().padStart(4, '0')
  } catch (error) {
    console.error('获取下一个头像ID失败:', error)
    // 默认从1开始
    return '0001'
  }
}

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, avatarDir)
  },
  filename: async function (req, file, cb) {
    try {
      // 获取下一个头像ID
      const nextId = await getNextAvatarId()
      const extension = path.extname(file.originalname).toLowerCase()
      const filename = `avatar-${nextId}${extension}`
      cb(null, filename)
    } catch (error) {
      console.error('生成头像文件名失败:', error)
      // 发生错误时使用时间戳
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
      const extension = path.extname(file.originalname).toLowerCase()
      cb(null, `avatar-${uniqueSuffix}${extension}`)
    }
  }
})

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 仅接受图片文件
  if (file.mimetype.startsWith('image/')) {
    // 检查扩展名
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif']
    const extension = path.extname(file.originalname).toLowerCase()
    
    if (allowedExtensions.includes(extension)) {
      cb(null, true)
    } else {
      cb(new Error('只允许上传JPG, JPEG, PNG或GIF格式的图片'), false)
    }
  } else {
    cb(new Error('仅允许上传图片文件'), false)
  }
}

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 限制为2MB
  },
  fileFilter: fileFilter
})

// 允许CORS
router.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true
}))

// 教师登录和注册路由
router.post('/auth/login', loginLimiter, login)
router.post('/auth/register', registerLimiter, register)

// 个人信息管理
router.put('/auth/profile', authenticateToken, updateProfile)

// 头像上传
router.post('/auth/avatar', authenticateToken, upload.single('avatar'), uploadAvatar);

// 处理预检请求
router.options('/auth/login', cors())
router.options('/auth/register', cors())
router.options('/auth/profile', cors())
router.options('/auth/avatar', cors())

// 处理Multer错误
router.use((err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    // Multer错误处理
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大2MB）'
      });
    }
    return res.status(400).json({
      success: false,
      message: '文件上传错误: ' + err.message
    });
  }
  next(err);
});

export default router 
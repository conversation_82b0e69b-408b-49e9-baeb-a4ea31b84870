import express from 'express';
import { pool } from '../config/db.js';

const router = express.Router();

// 获取测试数据
router.get('/data/:teacherId', async (req, res) => {
  const { teacherId } = req.params;
  
  try {
    // 获取课程数据
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE teacher_id = ? AND status = 1',
      [teacherId]
    );
    
    // 获取课表数据
    const [schedules] = await pool.query(
      `SELECT cs.*, c.course_name, c.course_code
       FROM class_schedules cs
       LEFT JOIN courses c ON cs.course_id = c.id
       WHERE cs.user_id = ?`,
      [teacherId]
    );
    
    // 获取教案数据
    const [lessonPlans] = await pool.query(
      `SELECT lp.*, s.subject
       FROM lesson_plans lp
       LEFT JOIN syllabus s ON lp.syllabus_id = s.id
       WHERE lp.teacher_id = ?`,
      [teacherId]
    );

    // 获取表结构
    const [structure] = await pool.query(
      `SELECT 
        TABLE_NAME as table_name,
        COLUMN_NAME as column_name,
        COLUMN_TYPE as data_type,
        COLUMN_KEY as key_type,
        IS_NULLABLE as is_nullable,
        COLUMN_COMMENT as comment
       FROM information_schema.COLUMNS
       WHERE TABLE_SCHEMA = 'teaching_assistant'
       AND TABLE_NAME IN ('courses', 'class_schedules', 'lesson_plans')
       ORDER BY TABLE_NAME, ORDINAL_POSITION`
    );

    // 格式化表结构数据
    const formattedStructure = structure.reduce((acc, col) => {
      if (!acc[col.table_name]) {
        acc[col.table_name] = {
          name: col.table_name,
          columns: []
        };
      }
      
      acc[col.table_name].columns.push({
        field: col.column_name,
        type: col.data_type,
        key: col.key_type,
        nullable: col.is_nullable,
        comment: col.comment
      });
      
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        courses,
        schedules,
        details: lessonPlans,
        structure: Object.values(formattedStructure)
      }
    });
  } catch (error) {
    console.error('获取测试数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取测试数据失败',
      error: error.message
    });
  }
});

// 获取教学大纲测试数据
router.get('/syllabus/:courseId', async (req, res) => {
  const { courseId } = req.params;
  
  try {
    const [rows] = await pool.query(
      'SELECT * FROM syllabus WHERE course_id = ?',
      [courseId]
    );
    
    if (rows.length === 0) {
      return res.json({
        success: true,
        data: {
          id: null,
          course_id: courseId,
          content: JSON.stringify({
            "第一章 绪论": "",
            "第二章 基础知识": {
              "2.1 基本概念": "",
              "2.2 基本原理": ""
            }
          })
        }
      });
    }

    res.json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    console.error('获取教学大纲失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教学大纲失败',
      error: error.message
    });
  }
});

export default router; 
import express from 'express';
import cors from 'cors';
import { authenticateToken } from '../middleware/auth.js';
import {
  getTeacherCourses,
  updateCourseStatus,
  getCourseDetails,
  getNextCourseCode,
  createCourse,
  updateCourse,
  deleteCourse
} from '../controllers/teacherCourseController.js';

const router = express.Router();

// 允许CORS
router.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true
}));

// 获取教师开设的课程列表
router.get('/teacher-courses/teacher/:teacherId', authenticateToken, getTeacherCourses);

// 更新课程状态
router.put('/teacher-courses/status/:courseId', authenticateToken, updateCourseStatus);

// 获取课程详情
router.get('/teacher-courses/details/:courseId', authenticateToken, getCourseDetails);

// 获取下一个可用的课程代码
router.get('/teacher-courses/next-code', authenticateToken, getNextCourseCode);

// 创建新课程
router.post('/teacher-courses', authenticateToken, createCourse);

// 更新课程 - 使用更明确的路由参数名称
router.put('/teacher-courses/:courseId', authenticateToken, (req, res) => {
  console.log('收到更新课程请求，参数:', req.params);
  updateCourse(req, res);
});

// 删除课程 - 使用更明确的路由参数名称
router.delete('/teacher-courses/:courseId', authenticateToken, (req, res) => {
  console.log('收到删除课程请求，参数:', req.params);
  deleteCourse(req, res);
});

// 处理预检请求
router.options('*', cors());

export default router;
import express from 'express';
import modelController from '../controllers/modelController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 所有请求需要认证
router.use(authenticateToken);

// 获取所有模型配置
router.get('/', modelController.getAllModels);

// 获取单个模型配置
router.get('/:id', modelController.getModelById);

// 创建新的模型配置
router.post('/', modelController.createModel);

// 更新模型配置
router.put('/:id', modelController.updateModel);

// 删除模型配置
router.delete('/:id', modelController.deleteModel);

// 测试模型连接
router.post('/test', modelController.testModelConnection);

export default router; 
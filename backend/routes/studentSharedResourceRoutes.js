import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { 
  getStudentSharedVideos,
  getSharedVideoDetail,
  markVideoAsViewed
} from '../controllers/studentSharedResourceController.js';

const router = express.Router();

// 应用认证中间件
router.use(authenticateToken);

// 获取分享给学生的视频列表
router.get('/shared-videos', getStudentSharedVideos);

// 获取分享视频详情
router.get('/shared-videos/:videoId', getSharedVideoDetail);

// 标记视频为已观看
router.post('/shared-videos/:videoId/view', markVideoAsViewed);

export default router;

import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  getStudentById
} from '../controllers/studentController.js';

const router = express.Router();

// 公开路由
router.post('/register', register);
router.post('/login', login);

// 需要认证的路由
router.get('/profile', authenticateToken, getProfile);
router.get('/:studentId', authenticateToken, getStudentById);
router.put('/profile', authenticateToken, updateProfile);
router.put('/change-password', authenticateToken, changePassword);

export default router;
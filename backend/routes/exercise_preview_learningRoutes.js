import express from 'express';
import { getExercises, submitPreview, uploadImage, submitOne } from '../controllers/exercise_preview_learningController.js';
import { authenticateToken } from '../middleware/auth.js';
import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 确保上传目录存在
const uploadDir = path.join(__dirname, '..', 'uploads', 'student_exercise');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置上传存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
  fileFilter: function (req, file, cb) {
    // 只接受图片文件
    const filetypes = /jpeg|jpg|png|gif/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('只允许上传图片文件'));
  }
});

const router = express.Router();

// 获取预习题列表
router.get('/exercises/:classId/:chapterId', authenticateToken, getExercises);

// 提交预习题答案
router.post('/submit/:classId/:chapterId', authenticateToken, submitPreview);

// 提交单个题目答案
router.post('/submit-one/:classId/:chapterId', authenticateToken, submitOne);

// 上传图片
router.post('/upload-image', authenticateToken, upload.single('image'), uploadImage);

export default router;

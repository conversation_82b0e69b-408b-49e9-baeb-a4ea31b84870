import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import {
  generateExercises,
  getExercisesBySection,
  saveExercise,
  getExerciseStats,
  getTopErrorKnowledgePoints
} from '../controllers/exerciseController.js';

const router = express.Router();

// 所有路由都需要身份验证
router.use(authenticateToken);

// 生成习题
router.post('/generate', generateExercises);

// 获取指定章节的习题列表
router.get('/section/:courseCode/:titleId', getExercisesBySection);

// 获取习题统计信息
router.get('/stats/:courseCode/:titleId', getExerciseStats);

// 保存习题
router.post('/save', saveExercise);

// 获取错误知识点排行
router.get('/error-knowledge-points/:classId/:titleId/:courseCode', getTopErrorKnowledgePoints);

export default router;

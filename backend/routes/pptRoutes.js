import express from 'express';
import { generatePPT, getPPTProgress, getPPTTemplate, savePPTTemplate, generatePPTAuto, getAllPPTTemplates } from '../controllers/pptController.js';
import { generatePPTPictures, loadPPTPictures } from '../controllers/ppt_picture_generate.js';
import authMiddleware from '../middleware/authMiddleware.js';
import pool from '../config/db.js';
import multer from 'multer';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import axios from 'axios';
import mime from 'mime-types';

// 获取当前文件的路径和目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

// 获取所有PPT模板列表
router.get('/templates', authMiddleware, getAllPPTTemplates);

// 获取PPT模板 - 使用课时名称
router.get('/template/:courseId/:hour', authMiddleware, getPPTTemplate);

// 这里添加两个新的路由，使用课程ID和titleId
router.get('/template/:courseId/byTitleId/:titleId', authMiddleware, getPPTTemplate);
router.post('/template/:courseId/byTitleId/:titleId', authMiddleware, savePPTTemplate);

// 生成PPT
router.post('/generate/:courseId/:hour', authMiddleware, generatePPT);

// 查询PPT生成进度
router.get('/progress', authMiddleware, getPPTProgress);

// 保存PPT模板
router.post('/template/:courseId/:hour', authMiddleware, savePPTTemplate);

// 智能模式生成路由（完整路径）
router.post(
  '/generateAuto',
  authMiddleware,
  upload.single('file'),
  generatePPTAuto
);

// 获取PPT记录列表
router.get('/records', authMiddleware, async (req, res) => {
  try {
    const teacherId = req.user.system_teacher_id;

    const [records] = await pool.execute(
      `SELECT * FROM ppt_records
       WHERE teacher_id = ?
       ORDER BY create_time DESC`,
      [teacherId]
    );

    res.json({
      flag: true,
      data: records
    });
  } catch (error) {
    console.error('获取PPT记录失败:', error);
    res.json({
      flag: false,
      desc: '获取PPT记录失败'
    });
  }
});

// AI生成PPT插图路由
router.post('/generate-ai-pictures', authMiddleware, generatePPTPictures);

// 从数据库加载AI生成的PPT插图路由
router.post('/load-ai-pictures', authMiddleware, loadPPTPictures);

// 获取PPT插图列表
router.get('/pictures', authMiddleware, async (req, res) => {
  try {
    const { page = 1, pageSize = 12, query = '', category = '' } = req.query;
    const teacherId = req.user?.system_teacher_id;

    if (!teacherId) {
      return res.json({
        flag: false,
        desc: '未登录或无法获取教师ID'
      });
    }



    // 返回数据
    return res.json({
      flag: true,
      data: {
        items: mockPictures,
        total: mockPictures.length,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    return res.json({
      flag: false
    });
  }
});

// 修改图片分析路由
router.post('/analyze-images', authMiddleware, async (req, res) => {
  try {
    const { outline } = req.body;
    const teacherId = req.user.system_teacher_id;

    if (!outline) {
      return res.json({
        flag: false,
        desc: '缺少PPT大纲内容'
      });
    }

    console.log('收到分析图片请求，输入大纲:', JSON.stringify(req.body.outline, null, 2));

    // 首先尝试从数据库中查找匹配的记录
    try {
      // 尝试通过PPT标题或内容查找匹配的记录
      const pptTitle = outline.title || '';
      let matchedTemplate = null;

      // 1. 首先尝试通过标题精确匹配
      if (pptTitle) {
        const [titleMatches] = await pool.execute(
          `SELECT id, title, ppt_picture, content FROM ppt_templates
           WHERE teacher_id = ? AND title = ?
           ORDER BY update_time DESC LIMIT 1`,
          [teacherId, pptTitle]
        );

        if (titleMatches && titleMatches.length > 0) {
          // 增加额外的验证 - 确认大纲内容也相似
          if (outline.chapters && outline.chapters.length > 0 && titleMatches[0].content) {
            try {
              const templateContent = JSON.parse(titleMatches[0].content);
              // 检查章节标题是否匹配
              if (templateContent.chapters && templateContent.chapters.length > 0 &&
                  templateContent.chapters[0].chapterTitle === outline.chapters[0].chapterTitle) {
                matchedTemplate = titleMatches[0];
                console.log(`通过标题和章节内容找到精确匹配记录: ID=${matchedTemplate.id}`);
              } else {
                console.log('标题匹配但章节内容不匹配，继续寻找更精确的匹配');
              }
            } catch (e) {
              console.error('解析模板内容失败:', e);
              // 如果内容解析失败，仍使用标题匹配的记录
              matchedTemplate = titleMatches[0];
              console.log(`通过标题找到匹配记录: ID=${matchedTemplate.id}`);
            }
          } else {
            matchedTemplate = titleMatches[0];
            console.log(`通过标题找到匹配记录: ID=${matchedTemplate.id}`);
          }
        }
      }

      // 2. 如果没找到，尝试更精确的章节匹配
      if (!matchedTemplate && outline.chapters && outline.chapters.length > 0) {
        const firstChapterTitle = outline.chapters[0].chapterTitle || '';

        if (firstChapterTitle) {
          // 更精确的匹配 - 使用更多章节信息
          let matchQuery = `%${firstChapterTitle}%`;
          let additionalCondition = '';

          // 如果有子章节，也加入匹配条件
          if (outline.chapters[0].chapterContents && outline.chapters[0].chapterContents.length > 0) {
            const subChapterTitle = outline.chapters[0].chapterContents[0].chapterTitle || '';
            if (subChapterTitle) {
              additionalCondition = ` AND content LIKE ?`;
              matchQuery = [`%${firstChapterTitle}%`, `%${subChapterTitle}%`];
            }
          }

          const [contentMatches] = await pool.execute(
            `SELECT id, title, ppt_picture FROM ppt_templates
             WHERE teacher_id = ? AND content LIKE ?${additionalCondition}
             ORDER BY update_time DESC LIMIT 1`,
            [teacherId, ...matchQuery].filter(Boolean) // 过滤掉可能的undefined值
          );

          if (contentMatches && contentMatches.length > 0) {
            matchedTemplate = contentMatches[0];
            console.log(`通过多层章节内容找到更精确匹配记录: ID=${matchedTemplate.id}`);
          }
        }
      }

      // 3. 如果找到匹配记录并且有图片数据，直接返回图片数据
      if (matchedTemplate && matchedTemplate.ppt_picture) {
        try {
          const pictureData = JSON.parse(matchedTemplate.ppt_picture);

          // 检查图片数据是否有效
          if (pictureData && Object.keys(pictureData).length > 0) {
            console.log(`从数据库中找到图片数据，ID=${matchedTemplate.id}`);

            // 统计图片数据
            let totalValidImages = 0;
            let totalPages = Object.keys(pictureData).length;
            let totalKeywords = 0;

            // 验证图片链接并统计
            for (const pageKey in pictureData) {
              const page = pictureData[pageKey];
              for (const keyword in page.keywords) {
                totalKeywords++;
                const images = page.keywords[keyword];
                if (Array.isArray(images)) {
                  totalValidImages += images.length;
                }
              }
            }

            console.log(`从数据库获取图片数据: ${totalPages}页, ${totalKeywords}个关键词, ${totalValidImages}个图片链接`);

            return res.json({
              flag: true,
              desc: '从数据库获取图片数据成功',
              data: pictureData,
              stats: {
                pages: totalPages,
                keywords: totalKeywords,
                images: totalValidImages
              },
              source: 'database'
            });
          }
        } catch (parseError) {
          console.error(`解析数据库中的图片数据失败: ${parseError.message}`);
          // 解析失败，继续执行Python脚本
        }
      }

      // 如果找到了匹配记录但没有图片数据，使用Python脚本生成
      if (matchedTemplate && !matchedTemplate.ppt_picture) {
        console.log(`模板ID=${matchedTemplate.id}没有图片数据，将使用Python脚本生成`);
      } else if (!matchedTemplate) {
        console.log(`数据库中没有找到匹配的图片数据，将使用Python脚本生成`);
      }

      // 以下是执行Python脚本的代码...
      const pythonScriptPath = path.resolve(__dirname, 'picture_seek.py');

      if (!fs.existsSync(pythonScriptPath)) {
        console.error(`Python脚本不存在: ${pythonScriptPath}`);
        return res.json({
          flag: false,
          desc: 'Python脚本文件不存在'
        });
      }

      console.log(`准备执行Python脚本: ${pythonScriptPath}`);

      const pythonProcess = spawn('python', [pythonScriptPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PYTHONIOENCODING: 'utf-8',
          TEMPLATE_ID: matchedTemplate ? matchedTemplate.id.toString() : ''
        }
      });

      // 将大纲内容发送给Python脚本
      pythonProcess.stdin.write(JSON.stringify(outline), 'utf-8');
      pythonProcess.stdin.end();

      let resultData = '';
      pythonProcess.stdout.on('data', (data) => {
        resultData += data.toString();
      });

      let errorData = '';
      pythonProcess.stderr.on('data', (data) => {
        errorData += data.toString();
        console.error(`Python日志: ${data}`);
      });

      // 封装成Promise以等待Python脚本完成
      const pythonResult = await new Promise((resolve, reject) => {
        pythonProcess.on('close', (code) => {
          console.log(`Python脚本执行完成，返回代码: ${code}`);
          console.log(`Python脚本标准输出: ${resultData}`);

          if (code !== 0) {
            console.error(`Python脚本执行失败，代码: ${code}, 错误: ${errorData}`);
            reject(new Error(`Python脚本执行失败: ${errorData}`));
            return;
          }

          // 修改这部分逻辑：不再期望Python返回文件路径
          // 从标准输出获取JSON文件路径（确保只获取最后一行）
          const outputLines = resultData.trim().split('\n');
          const filePath = outputLines[outputLines.length - 1].trim();
          console.log(`解析到的文件路径: ${filePath}`);

          // 这里是关键修改：即使没有文件路径也不报错，因为数据可能已经保存到数据库
          resolve({ success: true, filePath: filePath || '' });
        });

        pythonProcess.on('error', (error) => {
          console.error('启动Python进程失败:', error);
          reject(error);
        });
      });

      // Python脚本执行完毕后，从数据库中获取最新的图片数据
      try {
        // 再次查询数据库，获取Python脚本可能已经插入的记录
        const [updatedRecords] = await pool.execute(
          `SELECT id, title, ppt_picture FROM ppt_templates
           WHERE teacher_id = ?
           ORDER BY update_time DESC
           LIMIT 1`,
          [teacherId]
        );

        if (updatedRecords && updatedRecords.length > 0 && updatedRecords[0].ppt_picture) {
          try {
            // 尝试解析最新的图片数据
            const latestPictureData = JSON.parse(updatedRecords[0].ppt_picture);

            if (latestPictureData && Object.keys(latestPictureData).length > 0) {
              console.log(`Python脚本执行后，从数据库获取图片数据: ID=${updatedRecords[0].id}`);

              // 统计图片数据
              let totalValidImages = 0;
              let totalPages = Object.keys(latestPictureData).length;
              let totalKeywords = 0;

              // 验证图片链接并统计
              for (const pageKey in latestPictureData) {
                const page = latestPictureData[pageKey];
                for (const keyword in page.keywords) {
                  totalKeywords++;
                  const images = page.keywords[keyword];
                  if (Array.isArray(images)) {
                    totalValidImages += images.length;
                  }
                }
              }

              console.log(`从数据库获取最新图片数据: ${totalPages}页, ${totalKeywords}个关键词, ${totalValidImages}个图片链接`);

              return res.json({
                flag: true,
                desc: 'Python脚本执行成功并从数据库获取图片数据',
                data: latestPictureData,
                stats: {
                  pages: totalPages,
                  keywords: totalKeywords,
                  images: totalValidImages
                },
                source: 'python_and_database'
              });
            }
          } catch (parseError) {
            console.error(`解析数据库中的最新图片数据失败: ${parseError.message}`);
            // 继续使用文件中的数据
          }
        }
      } catch (dbError) {
        console.error(`Python脚本执行后查询数据库失败: ${dbError.message}`);
        // 继续使用文件中的数据
      }

      // 修改这里：只有当pythonResult中有文件路径时才尝试读取文件
      if (pythonResult.filePath && fs.existsSync(pythonResult.filePath)) {
        // 原有的文件读取逻辑...
        const filePath = pythonResult.filePath;
        console.log(`从文件中读取图片数据: ${filePath}`);

        if (!fs.existsSync(filePath)) {
          console.error(`文件不存在: ${filePath}`);
          return res.json({
            flag: false,
            desc: `找不到生成的图片数据文件: ${filePath}`
          });
        }

        const fileContent = fs.readFileSync(filePath, 'utf-8');
        const analysisResult = JSON.parse(fileContent);

        console.log(`成功解析JSON文件，包含${Object.keys(analysisResult).length}个页面数据`);

        // 确认每个页面的图片链接有效性
        let totalValidImages = 0;
        let totalPages = 0;
        let totalKeywords = 0;

        // 检查图片链接是否有效的函数
        const isValidImageUrl = (url) => {
          if (!url || typeof url !== 'string') return false;

          // 移除URL参数部分进行检查
          const urlWithoutParams = url.split('?')[0].toLowerCase();

          // 检查是否以图片扩展名结尾
          const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
          return validExtensions.some(ext => urlWithoutParams.endsWith(ext));
        };

        // 过滤每个页面的每个关键词的图片链接
        for (const pageKey in analysisResult) {
          totalPages++;
          const page = analysisResult[pageKey];

          for (const keyword in page.keywords) {
            totalKeywords++;
            const images = page.keywords[keyword];

            if (Array.isArray(images)) {
              // 只保留有效的图片链接
              const validImages = images.filter(url => isValidImageUrl(url));
              page.keywords[keyword] = validImages;
              totalValidImages += validImages.length;
            }
          }
        }

        console.log(`统计信息: ${totalPages}页, ${totalKeywords}个关键词, ${totalValidImages}个有效图片链接`);

        // 返回结果
        return res.json({
          flag: true,
          desc: '分析成功',
          data: analysisResult,
          stats: {
            pages: totalPages,
            keywords: totalKeywords,
            images: totalValidImages
          },
          source: 'file'
        });
      } else {
        // 如果没有文件路径但Python执行成功，可能已经保存到数据库但我们无法获取
        return res.json({
          flag: false,
          desc: '无法获取图片数据：Python脚本没有生成文件，且数据库中未找到有效数据'
        });
      }

    } catch (error) {
      console.error('处理失败:', error);
      return res.json({
        flag: false,
        desc: `获取图片推荐失败: ${error.message}`
      });
    }
  } catch (error) {
    console.error('分析PPT图片失败:', error);
    return res.json({
      flag: false,
      desc: error.message || '分析失败'
    });
  }
});

// 更精确的 URL 清理函数 - 确保处理 @ 符号
const cleanImageUrl = (url) => {
  if (!url || typeof url !== 'string') return url;

  try {
    // 对于AI生成的图片URL，不进行处理
    if (url.includes('oaiusercontent.com') || url.includes('filesystem.site')) {
      console.log('检测到AI生成的图片URL，不进行处理');
      return url; // 直接返回原始URL
    }

    let cleanedUrl = url;
    // 1. 移除 @ 符号及其之后的所有内容
    cleanedUrl = cleanedUrl.split('@')[0];
    // 2. 移除查询参数
    cleanedUrl = cleanedUrl.split('?')[0];
    // 3. (可选) 移除可能的多余斜杠或特殊字符，根据需要添加
    // cleanedUrl = cleanedUrl.replace(/\/$/, ''); // 移除末尾斜杠

    return cleanedUrl.trim();
  } catch (e) {
    console.error(`Error cleaning URL ${url}:`, e);
    return url;
  }
};

// 更新获取请求头函数 - 调整 xuehai.net 和 docsou.com 的 Referer
const getAxiosHeadersForUrl = (targetUrl) => {
  let referer = ''; // 默认不发送 Referer
  const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  let domain = '';
  try {
      domain = new URL(targetUrl).hostname;
  } catch {}

  // 根据域名设置特定的 Referer
  if (domain.includes('bdstatic.com') || domain.includes('baidu.com')) {
    referer = 'https://image.baidu.com/';
  } else if (domain.includes('sinaimg.cn')) {
    referer = 'https://weibo.com/';
  } else if (domain.includes('hdslb.com')) {
    referer = 'https://www.bilibili.com/';
  }
  // 对于 xuehai.net 和 docsou.com，保持 referer 为空字符串 ''

  const headers = {
    'User-Agent': userAgent,
    'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    // 'sec-fetch-*' 头部有时会引起问题，先移除试试
    // 'sec-fetch-site': 'none',
    // 'sec-fetch-mode': 'no-cors',
    // 'sec-fetch-dest': 'image',
    'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"'
  };

  if (referer) { // 仅当 referer 不为空时才添加
      headers['Referer'] = referer;
  }

  return headers;
};

// 优化流式图片API - 使用更新后的清理和请求头函数
router.get('/stream-image', async (req, res) => {
  const originalUrl = req.query.url;
  try {
    const { url } = req.query;
    if (!url) return res.status(400).send('缺少图片URL参数');

    const decodedUrl = decodeURIComponent(url);
    const processedUrl = cleanImageUrl(decodedUrl); // 使用更新的清理函数
    console.log('请求流式处理图片:', { 原始URL: decodedUrl, 处理后URL: processedUrl });

    const headers = getAxiosHeadersForUrl(processedUrl); // 使用更新的请求头函数
    console.log('Axios请求头:', headers);

    try {
      const imageResponse = await axios({
        method: 'GET',
        url: processedUrl,
        responseType: 'arraybuffer',
        headers: headers,
        timeout: 20000,
        maxRedirects: 5,
        validateStatus: (status) => status >= 200 && status < 400,
      });

      const contentType = imageResponse.headers['content-type'] || 'image/jpeg';
      if (!contentType.startsWith('image/')) {
           console.warn(`非预期的Content-Type: ${contentType} for URL: ${processedUrl}`);
           return res.status(400).send('获取到的不是图片资源');
       }

      res.set('Content-Type', contentType);
      res.set('Cache-Control', 'public, max-age=86400');
      res.send(imageResponse.data);

    } catch (axiosError) {
      console.error(`Axios请求失败: ${axiosError.message}`, { url: processedUrl, status: axiosError.response?.status });
      const status = axiosError.response?.status || 502;
      res.status(status).send(`无法获取上游图片 (${status})`);
    }

  } catch (error) {
    console.error(`流式处理图片路由内部错误: ${error.message}`, { url: originalUrl });
    res.status(500).send('图片处理失败 (服务器内部错误)');
  }
});

// 优化客户端下载路由 - 使用更新后的清理和请求头函数
router.get('/download-for-client', async (req, res) => {
  const originalUrl = req.query.url;
  // 直接使用前端传递的、已经解码和清理过的文件名基础
  const fileNameBaseFromQuery = decodeURIComponent(req.query.name || 'image');
  try {
    const { url } = req.query;
    if (!url) return res.status(400).send('缺少图片URL参数');

    const decodedUrl = decodeURIComponent(url);
    const processedUrl = cleanImageUrl(decodedUrl);
    console.log('客户端下载请求:', { 原始URL: decodedUrl, 处理后URL: processedUrl, 文件名基础: fileNameBaseFromQuery });

    const headers = getAxiosHeadersForUrl(processedUrl);
    console.log('Axios请求头:', headers);

    try {
        const imageResponse = await axios({
          method: 'GET',
          url: processedUrl,
          responseType: 'arraybuffer',
          headers: headers,
          timeout: 25000,
          maxRedirects: 5,
          validateStatus: (status) => status >= 200 && status < 400,
        });

        // --- 后端响应头修改 ---
        // 1. 获取实际的 Content-Type
        const contentType = imageResponse.headers['content-type'] || 'application/octet-stream';
        console.log('从源获取的 Content-Type:', contentType);

        // 2. 根据 Content-Type 推断文件扩展名
        let extension = mime.extension(contentType);
        // 如果推断失败或不是已知图片类型，回退到 .jpg 或其他默认值
        if (!extension || !['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
            console.warn(`无法从 Content-Type (${contentType}) 推断有效图片扩展名，将使用 .jpg`);
            extension = 'jpg';
        }

        // 3. 组合最终文件名 (使用前端提供的基础名和后端推断的扩展名)
        // 不再需要后端的 safeFileName，因为前端已经清理过了
        const finalFileName = `${fileNameBaseFromQuery}.${extension}`;
        console.log('后端生成的最终文件名:', finalFileName);

        // 4. 设置正确的 Content-Type 和 Content-Disposition
        // 使用 RFC 5987 兼容的 filename* 来更好地处理非 ASCII 字符
        res.setHeader('Content-Type', contentType);
        res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(finalFileName)}`);
        res.setHeader('Content-Length', imageResponse.data.length);
        res.setHeader('Cache-Control', 'no-cache');

        res.send(imageResponse.data);
        console.log(`图片数据已发送给客户端下载，文件名为: ${finalFileName}`);

    } catch (axiosError) {
        console.error(`Axios下载失败: ${axiosError.message}`, { url: processedUrl, status: axiosError.response?.status });
        const status = axiosError.response?.status || 502;
        res.status(status).send(`无法获取上游图片用于下载 (${status})`);
    }

  } catch (error) {
    console.error(`客户端下载路由内部错误: ${error.message}`, { url: originalUrl, name: fileNameBaseFromQuery });
    res.status(500).send('下载图片失败 (服务器内部错误)');
  }
});

export default router;
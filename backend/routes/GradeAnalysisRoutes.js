import express from 'express';
import { 
  getHomeworkAnalysis, 
  getClassHomeworkBatches,
  getClassChapters,
  getChapterBatches,
  getHomeworkKnowledgePoints,
  testDatabaseConnection
} from '../controllers/GradeAnalysisController.js';

const router = express.Router();

// 测试数据库连接和表结构
router.get('/db-test', testDatabaseConnection);

// 获取班级的章节列表
router.get('/class/:classId/chapters', getClassChapters);

// 获取班级章节的批次列表
router.get('/class/:classId/chapter/:chapterId/batches', getChapterBatches);

// 获取班级的作业批次（旧API，保留向后兼容性）
router.get('/homework/class/:classId/batches', getClassHomeworkBatches);

// 获取班级指定章节和批次的作业分析数据
router.get('/homework/:classId/:chapterId/:batchId', getHomeworkAnalysis);

// 获取班级指定章节和批次的知识点错误率
router.get('/homework/knowledge-points/:classId/:chapterId/:batchId', getHomeworkKnowledgePoints);

export default router;

import express from 'express';
import { 
  getExercises, 
  startHomework, 
  submitHomework, 
  uploadImage,
  getCompletion,
  saveAnswer
} from '../controllers/exercise_homework_learningController.js';
import { authenticateToken } from '../middleware/auth.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = express.Router();

// 唯一正确的上传目录 - 使用绝对路径
const uploadDir = path.join(process.cwd(), 'uploads', 'student_exercise');
console.log('设置上传目录:', uploadDir);

// 确保目录存在
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
  console.log(`已创建上传目录: ${uploadDir}`);
}

// 仅用于计算题图片的存储配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 使用唯一的上传目录
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const originalName = file.originalname || '';
    const ext = originalName.includes('.') ? originalName.substring(originalName.lastIndexOf('.')) : '.jpg';
    const filename = `calc-${uniqueSuffix}${ext}`;
    console.log(`生成文件名: ${filename}`);
    cb(null, filename);
  }
});

// 文件过滤器 - 只接受图片文件
const fileFilter = function (req, file, cb) {
  // 检查MIME类型
  if (!file.mimetype.startsWith('image/')) {
    return cb(new Error('只允许上传图片文件!'), false);
  }
  
  // 检查文件扩展名
  const ext = file.originalname.split('.').pop().toLowerCase();
  if (!['jpg', 'jpeg', 'png'].includes(ext)) {
    return cb(new Error('只允许上传JPG/PNG格式的图片!'), false);
  }
  
  // 确保扩展名和MIME类型匹配
  if ((ext === 'jpg' || ext === 'jpeg') && file.mimetype !== 'image/jpeg') {
    return cb(new Error('JPG文件MIME类型不匹配!'), false);
  }
  
  if (ext === 'png' && file.mimetype !== 'image/png') {
    return cb(new Error('PNG文件MIME类型不匹配!'), false);
  }
  
  cb(null, true);
};

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB限制
  },
  fileFilter: fileFilter
});

// 处理文件上传错误
const handleUploadErrors = (req, res, next) => {
  return function(err) {
    if (err instanceof multer.MulterError) {
      // Multer错误处理
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          success: false,
          message: '文件大小超过限制，最大2MB'
        });
      }
      return res.status(400).json({
        success: false,
        message: `文件上传错误: ${err.message}`
      });
    } else if (err) {
      // 其他错误
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }
    // 如果没有错误，继续处理请求
    next();
  };
};

// 获取作业题目
router.get('/exercises/:classId/:batchId', authenticateToken, getExercises);

// 记录作业开始时间
router.post('/start/:classId/:batchId', authenticateToken, startHomework);

// 提交作业答案
router.post('/submit/:classId/:batchId', authenticateToken, submitHomework);

// 保存单个答案 - 只保存在前端，不写入数据库
router.post('/save-answer', authenticateToken, saveAnswer);

// 上传计算题图片 - 添加错误处理
router.post('/upload-image', authenticateToken, function(req, res, next) {
  console.log('接收到上传请求');
  upload.single('image')(req, res, handleUploadErrors(req, res, next));
}, uploadImage);

// 获取作业完成情况（得分等）
router.get('/completion/:classId/:batchId', authenticateToken, getCompletion);

export default router; 
import express from 'express';
import { getStudentClasses, getClassHomework, getClassChapters, getChapterExercises, getChapterHomework } from '../controllers/exercise_homeworkController.js';

const router = express.Router();

// 获取学生所在的班级列表
router.get('/student/:studentId/classes', getStudentClasses);

// 获取班级的作业列表
router.get('/class/:classId/homework', getClassHomework);

// 获取班级的章节列表
router.get('/class/:classId/chapters', getClassChapters);

// 获取章节的习题列表
router.get('/class/:classId/chapter/:titleId/exercises', getChapterExercises);

// 获取章节的作业批次列表
router.get('/class/:classId/chapter/:titleId/homework', getChapterHomework);

export default router;

import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { 
  getClasses, 
  getTeacherClasses, 
  createClass, 
  updateClass,
  deleteClass,
  getClassDetails,
  generateClassName,
  getClassStudents,
  removeStudentFromClass
} from '../controllers/classController.js';

const router = express.Router();

// 应用认证中间件
router.use(authenticateToken);

// 获取所有班级
router.get('/', getClasses);

// 生成推荐的班级名称 - 移到具体ID路由之前
router.get('/generate-name', generateClassName);

// 获取当前登录教师的班级列表
router.get('/teacher', (req, res) => {
  // 从认证中间件获取教师ID
  const teacherId = req.user.system_teacher_id;
  req.params.teacherId = teacherId;
  getTeacherClasses(req, res);
});

// 获取特定教师的班级列表
router.get('/teacher/:teacherId', getTeacherClasses);

// 获取班级学生列表
router.get('/:classId/students', getClassStudents);

// 从班级移除学生
router.delete('/:classId/students/:studentId', removeStudentFromClass);

// 创建班级
router.post('/', createClass);

// 更新班级
router.put('/:id', updateClass);

// 删除班级
router.delete('/:id', deleteClass);

// 获取班级详情 - 移到最后，避免捕获其他路由
router.get('/:id', getClassDetails);

export default router;
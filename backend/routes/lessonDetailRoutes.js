import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import {
  getLessonDetail,
  saveLessonDetail,
  generateLessonDetail,
  exportLessonDetail,
  getHoursStatus
} from '../controllers/lessonDetailController.js';

const router = express.Router();

// 获取课时教案详情
router.get('/detail/:courseId/:titleId', authenticateToken, getLessonDetail);

// 保存课时教案
router.post('/save', authenticateToken, saveLessonDetail);

// 生成课时教案
router.post('/generate', authenticateToken, generateLessonDetail);

// 导出课时教案
router.get('/export/:courseId/:titleId', authenticateToken, exportLessonDetail);

// 获取课程所有课时的教案状态
router.get('/hours-status/:courseId', authenticateToken, getHoursStatus);

export default router;


import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import {
  getRecentPreparations,
  startPreparation,
  getTeachingAdvice
} from '../controllers/smartPreparationController.js';

const router = express.Router();

// 获取最近备课记录
router.get('/recent', authenticateToken, getRecentPreparations);



// 开始智能备课
router.post('/start', authenticateToken, startPreparation);

// 获取教学建议
router.get('/advice/:lessonPlanId', authenticateToken, getTeachingAdvice);

export default router;
import express from 'express';
import { 
  getClassPreviewStats,
  getClassChapters,
  getStudentsList,
  getPreviewViewTimeDistribution,
  getChapterQuestionStats,
  getKnowledgePointCoverage,
  getStudentPreviewDetails
} from '../controllers/previewAnalysisController.js';

const router = express.Router();

// 获取班级预习总体统计数据
router.get('/class/:classId/chapter/:chapterId/stats', getClassPreviewStats);

// 获取班级的章节列表（有预习资料的章节）
router.get('/class/:classId/chapters', getClassChapters);

// 获取班级学生列表
router.get('/class/:classId/students', getStudentsList);

// 获取预习查看时间分布
router.get('/class/:classId/chapter/:chapterId/view-time-distribution', getPreviewViewTimeDistribution);

// 获取预习习题统计
router.get('/class/:classId/chapter/:chapterId/questions', getChapterQuestionStats);

// 获取知识点覆盖率
router.get('/class/:classId/chapter/:chapterId/knowledge-coverage', getKnowledgePointCoverage);

// 获取学生预习详情
router.get('/student-preview/:classId/:chapterId/:studentId', getStudentPreviewDetails);

export default router; 
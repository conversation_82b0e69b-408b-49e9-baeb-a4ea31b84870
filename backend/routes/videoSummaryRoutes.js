/**
 * 视频摘要和笔记相关路由
 */
import express from 'express';
import videoNoteController from '../controllers/videoNoteController.js';
import { verifyToken } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要验证用户令牌
router.use(verifyToken);

// 转录视频
router.post('/transcribe', videoNoteController.transcribeVideo);

// 生成摘要
router.post('/generate', videoNoteController.generateNote);

// 保存笔记
router.post('/save', videoNoteController.saveNote);

// 获取用户所有笔记列表
router.get('/notes', videoNoteController.getUserNotes);

// 获取笔记详情
router.get('/notes/:noteId', videoNoteController.getNoteDetail);

// 删除笔记
router.delete('/notes/:noteId', videoNoteController.deleteNote);

// 检查视频是否已有转录和笔记数据
router.get('/check/:videoId', videoNoteController.checkVideoData);

export default router; 
import express from 'express';
import pool from '../config/db.js';
import { getSyllabus, clearAllCache, clearCourseCache } from '../controllers/syllabusController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取教学大纲列表
router.get('/', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT * FROM syllabus ORDER BY created_at DESC'
    );
    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取教学大纲列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教学大纲列表失败'
    });
  }
});

// 获取指定课程的教学大纲
router.get('/course/:courseId', async (req, res) => {
  try {
    const { courseId } = req.params;
    const [rows] = await pool.query(
      'SELECT * FROM syllabus WHERE course_id = ?',
      [courseId]
    );
    
    if (rows.length === 0) {
      return res.json({
        success: true,
        data: {
          id: null,
          course_id: courseId,
          content: JSON.stringify({
            "第一章 绪论": "",
            "第二章 基础知识": {
              "2.1 基本概念": "",
              "2.2 基本原理": ""
            }
          })
        }
      });
    }

    res.json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    console.error('获取课程教学大纲失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程教学大纲失败'
    });
  }
});

// 创建教学大纲
router.post('/', async (req, res) => {
  try {
    const { course_id, content } = req.body;
    const [result] = await pool.query(
      'INSERT INTO syllabus (course_id, content) VALUES (?, ?)',
      [course_id, content]
    );
    
    res.json({
      success: true,
      data: {
        id: result.insertId,
        course_id,
        content
      }
    });
  } catch (error) {
    console.error('创建教学大纲失败:', error);
    res.status(500).json({
      success: false,
      message: '创建教学大纲失败'
    });
  }
});

// 更新教学大纲
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { content } = req.body;
    
    await pool.query(
      'UPDATE syllabus SET content = ? WHERE id = ?',
      [content, id]
    );
    
    res.json({
      success: true,
      message: '教学大纲更新成功'
    });
  } catch (error) {
    console.error('更新教学大纲失败:', error);
    res.status(500).json({
      success: false,
      message: '更新教学大纲失败'
    });
  }
});

// 删除教学大纲
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    await pool.query(
      'DELETE FROM syllabus WHERE id = ?',
      [id]
    );
    
    res.json({
      success: true,
      message: '教学大纲删除成功'
    });
  } catch (error) {
    console.error('删除教学大纲失败:', error);
    res.status(500).json({
      success: false,
      message: '删除教学大纲失败'
    });
  }
});

// 获取课程大纲
router.get('/:courseCode', authenticateToken, getSyllabus);

// 清除特定课程的缓存
router.post('/cache/clear/:courseCode', authenticateToken, clearCourseCache);

// 清除所有缓存的路由（可选，用于管理员手动清除缓存）
router.post('/cache/clear', authenticateToken, clearAllCache);

export default router; 
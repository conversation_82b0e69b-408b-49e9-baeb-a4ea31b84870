import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import * as audioSummaryController from '../controllers/audio_summary.js';

const router = express.Router();

// 所有路由都需要身份验证
router.use(authenticateToken);

// 音频转录
router.post('/transcribe', audioSummaryController.transcribeAudio);

// 生成笔记
router.post('/generate', audioSummaryController.generateSummary);

// 保存笔记
router.post('/save', audioSummaryController.saveNote);

export default router;

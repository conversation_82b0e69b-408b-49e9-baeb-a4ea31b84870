import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import {
  getPreviewStats,
  getPreviewDetail,
  getPreviewTrend
} from '../controllers/DataAnalysisController.js';
import pool from '../config/db.js';

const router = express.Router();

// 路由中间件 - 记录所有请求
router.use((req, res, next) => {
  console.log(`\n[${new Date().toISOString()}] 数据分析路由收到请求: ${req.method} ${req.url}`);
  console.log('请求参数:', req.params);
  console.log('查询参数:', req.query);
  console.log('PATH:', req.path);
  console.log('ORIGINAL URL:', req.originalUrl);
  next();
});

// 测试路由 - 返回固定数据
router.get('/test-data', (req, res) => {
  res.json({
    success: true,
    message: '测试数据路由工作正常',
    timestamp: new Date().toISOString()
  });
});

// 获取班级预习情况统计
// 恢复身份验证中间件
router.get('/class-preview/:classId', authenticateToken, (req, res, next) => {
  getPreviewStats(req, res, next);
});

// 获取预习任务详情（包括学生完成情况）
router.get('/preview/detail/:previewId', authenticateToken, (req, res, next) => {
  getPreviewDetail(req, res, next);
});

// 临时路由 - 直接查询学生数量
router.get('/check-students/:classId', async (req, res) => {
  try {
    const { classId } = req.params;
    
    // 查询学生数量
    const [studentCount] = await pool.query(
      `SELECT COUNT(DISTINCT scr.student_id) as total_students 
       FROM student_course_registration scr 
       WHERE scr.class_id = ? AND scr.status = 1`,
      [classId]
    );
    
    // 查询班级信息
    const [classInfo] = await pool.query(
      `SELECT cc.id, cc.class_name, cc.course_code 
       FROM course_classes cc 
       WHERE cc.id = ?`,
      [classId]
    );
    
    // 查询预习任务列表
    const [previewList] = await pool.query(
      `SELECT id, title, deadline, create_time
       FROM preview_publish
       WHERE class_id = ?
       ORDER BY create_time DESC`,
      [classId]
    );
    
    res.json({
      success: true,
      data: {
        classInfo: classInfo[0] || {},
        studentCount: studentCount[0] || { total_students: 0 },
        previewTaskCount: previewList.length,
        rawQueries: {
          studentCountQuery: `SELECT COUNT(DISTINCT scr.student_id) as total_students 
                              FROM student_course_registration scr 
                              WHERE scr.class_id = ${classId} AND scr.status = 1`,
          previewListQuery: `SELECT id, title, deadline, create_time
                            FROM preview_publish
                            WHERE class_id = ${classId}
                            ORDER BY create_time DESC`
        }
      }
    });
  } catch (error) {
    console.error('直接查询学生数量失败:', error);
    res.status(500).json({
      success: false,
      message: '查询失败: ' + error.message
    });
  }
});

// 注册根路由处理 - 确保默认响应
router.get('/', (req, res) => {
  console.log('访问数据分析根路由');
  res.json({
    success: true,
    message: '数据分析API可用',
    routes: [
      '/api/analysis/class-preview/:classId',
      '/api/analysis/test-data',
      '/api/analysis/check-students/:classId'
    ]
  });
});


export default router;

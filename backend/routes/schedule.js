import express from 'express';
import db from '../config/db.js';

const router = express.Router();

// 获取当前课表基本信息
router.get('/current-info', async (req, res) => {
  try {
    const userId = req.query.userId;

    // 获取当前课表信息
    const [schedules] = await db.query(
      `SELECT * FROM class_schedules 
       WHERE user_id = ? AND is_current = 1`,
      [userId]
    );

    if (schedules.length > 0) {
      res.json({
        success: true,
        data: schedules[0]
      });
    } else {
      res.json({
        success: false,
        message: '未找到课表信息'
      });
    }
  } catch (error) {
    console.error('Database error:', error);
    res.status(500).json({
      success: false,
      message: '获取课表信息失败'
    });
  }
});

// 获取课程详细信息
router.get('/course-detail', async (req, res) => {
  try {
    const { userId, scheduleId } = req.query;

    // 获取课程详细信息
    const [details] = await db.query(
      `SELECT * FROM schedule_details 
       WHERE user_id = ? AND schedule_id = ?
       ORDER BY create_time DESC 
       LIMIT 1`,
      [userId, scheduleId]
    );

    if (details.length > 0) {
      res.json({
        success: true,
        data: details[0]
      });
    } else {
      res.json({
        success: false,
        message: '未找到课程信息'
      });
    }
  } catch (error) {
    console.error('Database error:', error);
    res.status(500).json({
      success: false,
      message: '获取课程信息失败'
    });
  }
});

// 获取当前课表（管理员视图）
router.get('/current/admin', async (req, res) => {
    try {
        console.log('获取当前课表...');
        const [schedules] = await db.query(`
            SELECT 
                id,
                user_id,
                semester,
                current_week,
                total_weeks,
                is_current,
                create_time,
                update_time
            FROM schedules 
            WHERE user_id = ? AND is_current = 1
        `, [req.user?.id || 1]);

        const currentSchedule = schedules[0] || {
            id: null,
            user_id: req.user?.id || 1,
            semester: '2024-1',
            current_week: 1,
            total_weeks: 20,
            is_current: 1
        };

        console.log('当前课表:', currentSchedule);
        res.json({ success: true, data: currentSchedule });
    } catch (error) {
        console.error('获取当前课表失败:', error);
        res.status(500).json({ 
            success: false, 
            message: '获取当前课表失败',
            error: error.message 
        });
    }
});

// 获取课表详情（管理员视图）
router.get('/details/admin', async (req, res) => {
    try {
        console.log('获取课表详情...');
        const [details] = await db.query(`
            SELECT 
                sd.id,
                sd.schedule_id,
                sd.course_id,
                sd.classroom,
                sd.class_group,
                sd.start_week,
                sd.end_week,
                sd.weekday,
                sd.period,
                c.course_name,
                c.course_code,
                c.credits,
                c.total_hours
            FROM schedule_details sd
            LEFT JOIN courses c ON sd.course_id = c.id
            WHERE sd.user_id = ?
            ORDER BY sd.weekday, sd.period
        `, [req.user?.id || 1]);

        console.log('课表详情:', details);
        res.json({ success: true, data: details });
    } catch (error) {
        console.error('获取课表详情失败:', error);
        res.status(500).json({ 
            success: false, 
            message: '获取课表详情失败',
            error: error.message 
        });
    }
});

// 更新课表
router.put('/update', async (req, res) => {
    try {
        const { id, current_week, total_weeks } = req.body;
        await db.query(`
            UPDATE schedules 
            SET current_week = ?, total_weeks = ?
            WHERE id = ?
        `, [current_week, total_weeks, id]);
        
        res.json({ success: true, message: '课表更新成功' });
    } catch (error) {
        console.error('更新课表失败:', error);
        res.status(500).json({ 
            success: false, 
            message: '更新课表失败',
            error: error.message 
        });
    }
});

export default router; 
import express from 'express';
import db from '../config/db.js';

const router = express.Router();

// 测试路由
router.get('/test', (req, res) => {
  try {
    console.log('访问测试路由');
    res.json({ message: 'Syllabus router is working' });
  } catch (error) {
    console.error('测试路由错误:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

// 获取指定课程的教学大纲
router.get('/course/:courseId', async (req, res) => {
  try {
    console.log('路由参数:', req.params);
    const courseId = req.params.courseId;
    
    if (!courseId) {
      return res.status(400).json({ error: '缺少课程ID参数' });
    }

    // 测试数据库连接
    await db.query('SELECT 1');
    console.log('数据库连接正常');

    const [rows] = await db.query(
      'SELECT * FROM syllabus WHERE course_id = ?',
      [courseId]
    );
    
    console.log('查询结果:', rows);
    
    if (!rows || rows.length === 0) {
      return res.status(404).json({ 
        error: '未找到教学大纲数据',
        courseId 
      });
    }
    
    res.json(rows[0]);
  } catch (error) {
    console.error('获取教学大纲失败:', error);
    res.status(500).json({ 
      error: '获取教学大纲失败',
      details: error.message 
    });
  }
});

// 创建新的教学大纲
router.post('/', async (req, res) => {
  console.log('创建教学大纲，请求数据:', req.body);
  
  try {
    const { course_id, teacher_id, subject, content } = req.body;
    
    if (!course_id || !teacher_id || !subject) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const [result] = await db.query(
      'INSERT INTO syllabus (course_id, teacher_id, subject, content, allocation) VALUES (?, ?, ?, ?, ?)',
      [course_id, teacher_id, subject, content, JSON.stringify({})]
    );
    
    console.log('插入结果:', result);

    if (result.affectedRows === 1) {
      res.json({ 
        success: true, 
        id: result.insertId,
        message: '创建教学大纲成功'
      });
    } else {
      throw new Error('创建教学大纲失败');
    }
  } catch (error) {
    console.error('创建教学大纲失败:', error);
    res.status(500).json({ 
      error: '创建教学大纲失败',
      details: error.message 
    });
  }
});

// 更新教学大纲
router.put('/:id', async (req, res) => {
  console.log('更新教学大纲，ID:', req.params.id, '数据:', req.body);
  
  try {
    const { id } = req.params;
    const { content, allocation } = req.body;
    
    const [result] = await db.query(
      'UPDATE syllabus SET content = ?, allocation = ?, update_time = CURRENT_TIMESTAMP WHERE id = ?',
      [content, allocation, id]
    );
    
    console.log('更新结果:', result);

    if (result.affectedRows === 1) {
      res.json({ 
        success: true,
        message: '更新教学大纲成功'
      });
    } else {
      throw new Error('更新教学大纲失败');
    }
  } catch (error) {
    console.error('更新教学大纲失败:', error);
    res.status(500).json({ 
      error: '更新教学大纲失败',
      details: error.message 
    });
  }
});

export default router; 
import express from 'express';
import {
  uploadSchedule,
  getCurrentSchedule,
  getScheduleHistory,
  downloadSchedule,
  deleteClass,
  downloadTemplate,
  getTeacherSchedules,
  getScheduleDetails,
  updateSchedule,
  getTeacherCourses
} from '../controllers/scheduleController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 课表相关路由
router.post('/upload', authenticateToken, uploadSchedule);
router.get('/current/:userId', getCurrentSchedule);
router.get('/history/:userId', getScheduleHistory);
router.get('/download/:userId/:scheduleId', downloadSchedule);
router.delete('/delete-class', deleteClass);
router.get('/template', downloadTemplate);
router.get('/teacher/:teacherId/courses', getTeacherCourses);
router.get('/details/:teacherId', getScheduleDetails);
router.put('/:scheduleId', authenticateToken, updateSchedule);

export default router; 
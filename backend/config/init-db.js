import mysql from 'mysql2/promise';

const initDatabase = async () => {
  // 创建连接
  const connection = await mysql.createConnection({
    host: '**************',
    user: 'root',
    password: 'mydb123'
  });

  try {
    // 创建数据库
    await connection.query(`CREATE DATABASE IF NOT EXISTS teaching_assistant`);
    
    // 使用数据库
    await connection.query(`USE teaching_assistant`);
    
    // 创建教师表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS teaching_assistant (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_id VARCHAR(20) UNIQUE NOT NULL,
        name VARCHAR(50) NOT NULL,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建教案表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS lesson_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_id VARCHAR(20),
        title VARCHAR(100) NOT NULL,
        content TEXT,
        syllabus_content TEXT,
        course_content TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_id) REFERENCES teaching_assistant(teacher_id)
      )
    `);

    // 创建课时分配表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS lesson_hours (
        id INT AUTO_INCREMENT PRIMARY KEY,
        lesson_plan_id INT,
        hour_number INT,
        content TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (lesson_plan_id) REFERENCES lesson_plans(id)
      )
    `);

    // 创建教师收藏表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS teacher_favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        system_teacher_id VARCHAR(20) NOT NULL,
        resource_type ENUM('video', 'article') NOT NULL,
        resource_id VARCHAR(255) NOT NULL,
        title VARCHAR(255) NOT NULL,
        url TEXT NOT NULL,
        description TEXT,
        cover_url TEXT,
        source VARCHAR(50) NOT NULL,
        source_type VARCHAR(50) NOT NULL,
        author VARCHAR(100),
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        status TINYINT DEFAULT 1,
        INDEX idx_teacher_resource (system_teacher_id, resource_type, resource_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log('Database and tables created successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
  } finally {
    await connection.end();
  }
};

initDatabase();

export default initDatabase; 
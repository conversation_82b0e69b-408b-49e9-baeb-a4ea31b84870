import pool from './db.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量（仅用于后备配置）
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// 内存中的模型配置缓存
let modelConfigCache = null;
let lastCacheTime = 0;
const CACHE_TTL = 60 * 60 * 1000; // 缓存有效期为1小时

// API使用场景
export const API_USAGES = {
  CHAT: "chat",
  EXERCISE: "exercise",
  PREVIEW: "preview",
  SEARCH: "search",
  LESSON_PLAN: "lessonPlan",
  LESSON_DETAIL: "lessonDetail",
  TRANSCRIPTION: "transcription", // 添加转录场景
  PPT_GENERATION: "ppt_generation" // 添加PPT生成场景
};

// 使用场景到提供商的映射
const usageToProvider = {
  [API_USAGES.CHAT]: "volcano",
  [API_USAGES.EXERCISE]: "deepseek",
  [API_USAGES.PREVIEW]: "deepseek",
  [API_USAGES.SEARCH]: "moonshot",
  [API_USAGES.LESSON_PLAN]: "volcano",
  [API_USAGES.LESSON_DETAIL]: "volcano",
  [API_USAGES.TRANSCRIPTION]: "xfyun",
  [API_USAGES.PPT_GENERATION]: "xfyun_ppt" // 新增PPT生成场景的提供商映射
};

// 获取环境变量中的后备API密钥
const backupConfig = {
  volcano: {
    apiKey: process.env.ARK_API_KEY,
    baseURL: "https://ark.cn-beijing.volces.com/api/v3"
  },
  deepseek: {
    apiKey: process.env.DEEPSEEK_API_KEY,
    baseURL: "https://api.deepseek.com"
  },
  moonshot: {
    apiKey: process.env.MOONSHOT_API_KEY,
    baseURL: "https://api.moonshot.cn/v1"
  },
  xfyun: {
    apiKey: process.env.XFYUN_SECRET_KEY,
    appId: process.env.XFYUN_APPID,
    baseURL: "https://raasr.xfyun.cn/v2/api"
  },
  xfyun_ppt: { // 新增讯飞PPT生成的后备配置
    apiKey: process.env.XFYUN_PPT_SECRET || 'MWMyNzZmYTU5NDMyYmRhN2RlNzQzNzE4',
    appId: process.env.XFYUN_PPT_APPID || '183534df',
    baseURL: "https://zwapi.xfyun.cn"
  }
};

/**
 * 从数据库加载所有激活的模型配置
 * @returns {Promise<Object>} 模型配置对象
 */
export async function loadModelConfigs() {
  try {
    // 检查缓存是否有效
    const now = Date.now();
    if (modelConfigCache && (now - lastCacheTime < CACHE_TTL)) {
      return modelConfigCache;
    }

    // 从数据库中获取配置
    const [rows] = await pool.execute(
      'SELECT * FROM model_configs WHERE status = 1'
    );

    // 重建配置对象
    const configs = {};
    rows.forEach(row => {
      configs[row.provider] = {
        id: row.id,
        name: row.name,
        apiKey: row.api_key,
        appId: row.app_id,
        baseURL: row.base_url,
        usageScenario: row.usage_scenario
      };
    });

    // 更新缓存
    modelConfigCache = configs;
    lastCacheTime = now;
    
    console.log('模型配置已从数据库加载');
    return configs;
  } catch (error) {
    console.error('从数据库加载模型配置失败:', error);
    // 加载失败时使用备用配置
    console.warn('使用备用环境变量配置');
    return getBackupConfigs();
  }
}

/**
 * 强制刷新模型配置缓存
 */
export async function refreshModelConfigs() {
  modelConfigCache = null;
  return await loadModelConfigs();
}

/**
 * 获取备用配置
 * @returns {Object} 配置对象
 */
function getBackupConfigs() {
  return backupConfig;
}

/**
 * 根据使用场景获取默认提供商
 * @param {string} usage 使用场景
 * @returns {string} 提供商标识
 */
export function getProviderForUsage(usage) {
  return usageToProvider[usage] || "volcano";
}

/**
 * 根据使用场景获取模型配置
 * @param {string} usage 使用场景
 * @param {string|null} provider 可选的提供商覆盖
 * @returns {Promise<Object>} 模型配置
 */
export async function getModelConfig(usage, provider = null) {
  // 如果没有指定provider，使用默认provider
  const selectedProvider = provider || getProviderForUsage(usage);
  
  // 加载配置
  const configs = await loadModelConfigs();
  const config = configs[selectedProvider] || backupConfig[selectedProvider];
  
  if (!config) {
    console.error(`未找到提供商 ${selectedProvider} 的配置`);
    throw new Error(`未找到提供商 ${selectedProvider} 的配置`);
  }
  
  return config;
}

/**
 * 创建OpenAI客户端配置
 * @param {string} usage 使用场景
 * @param {string|null} provider 可选的提供商覆盖
 * @returns {Promise<Object>} OpenAI客户端配置
 */
export async function createOpenAIConfig(usage, provider = null) {
  const config = await getModelConfig(usage, provider);
  return {
    apiKey: config.apiKey,
    baseURL: config.baseURL
  };
}

/**
 * 获取讯飞语音转写API配置
 * @returns {Promise<Object>} 讯飞API配置
 */
export async function getXfyunConfig() {
  try {
    const configs = await loadModelConfigs();
    const config = configs['xfyun'];
    
    if (!config) {
      console.warn('未找到讯飞API配置，使用备用配置');
      return {
        appId: process.env.XFYUN_APPID,
        secretKey: process.env.XFYUN_SECRET_KEY,
        secret: process.env.XFYUN_SECRET
      };
    }
    
    return {
      appId: config.appId,
      secretKey: config.apiKey,
      secret: config.secret || process.env.XFYUN_SECRET
    };
  } catch (error) {
    console.error('获取讯飞配置失败:', error);
    return {
      appId: process.env.XFYUN_APPID,
      secretKey: process.env.XFYUN_SECRET_KEY,
      secret: process.env.XFYUN_SECRET
    };
  }
}

/**
 * 获取讯飞PPT生成API配置
 * @returns {Promise<Object>} 讯飞PPT API配置
 */
export async function getXfyunPptConfig() {
  try {
    const configs = await loadModelConfigs();
    const config = configs['xfyun_ppt'];
    
    if (!config) {
      console.warn('未找到讯飞PPT API配置，使用备用配置');
      return {
        appId: process.env.XFYUN_PPT_APPID || '183534df',
        secret: process.env.XFYUN_PPT_SECRET || 'MWMyNzZmYTU5NDMyYmRhN2RlNzQzNzE4'
      };
    }
    
    return {
      appId: config.appId,
      secret: config.apiKey
    };
  } catch (error) {
    console.error('获取讯飞PPT配置失败:', error);
    return {
      appId: process.env.XFYUN_PPT_APPID || '183534df',
      secret: process.env.XFYUN_PPT_SECRET || 'MWMyNzZmYTU5NDMyYmRhN2RlNzQzNzE4'
    };
  }
}

export default {
  API_USAGES,
  loadModelConfigs,
  refreshModelConfigs,
  getProviderForUsage,
  getModelConfig,
  createOpenAIConfig,
  getXfyunConfig,
  getXfyunPptConfig // 添加新的方法到导出对象
}; 
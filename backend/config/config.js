import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// 转录服务配置
const config = {
  // 使用哪种转录服务：'openai', 'xunfei', 'azure'
  TRANSCRIPTION_SERVICE: process.env.TRANSCRIPTION_SERVICE || 'openai',
  
  // OpenAI API 配置
  OPENAI_API_KEY: process.env.OPENAI_API_KEY,
  OPENAI_API_BASE: process.env.OPENAI_API_BASE,
  
  // 讯飞 API 配置
  XFYUN_APPID: process.env.XFYUN_APPID,
  XFYUN_SECRET_KEY: process.env.XFYUN_SECRET_KEY,
  
  // Azure API 配置
  AZURE_SPEECH_KEY: process.env.AZURE_SPEECH_KEY,
  AZURE_SPEECH_REGION: process.env.AZURE_SPEECH_REGION,
  
  // 临时文件存储路径
  TEMP_DIR: path.join(__dirname, '..', 'temp'),
  
  // 数据库表名
  DB_TABLES: {
    AUDIO_TRANSCRIPTIONS: 'audio_transcriptions',
    AUDIO_SUMMARIES: 'audio_summaries',
    NOTES: 'notes'
  }
};

export default config;

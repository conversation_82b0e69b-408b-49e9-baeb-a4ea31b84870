import mysql from 'mysql2/promise';

export const dbConfig = {
  host: '**************',
  user: 'root',
  password: 'mydb123',
  database: 'teaching_assistant',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// 创建连接池
export const pool = mysql.createPool(dbConfig);

// 测试数据库连接
pool.getConnection()
  .then(connection => {
    console.log('数据库连接成功');
    connection.release();
  })
  .catch(err => {
    console.error('数据库连接失败:', err);
  });

export default pool;


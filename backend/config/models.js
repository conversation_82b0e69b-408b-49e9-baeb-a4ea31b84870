import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// 获取环境变量中的API密钥
const ARK_API_KEY = process.env.ARK_API_KEY;
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
const MOONSHOT_API_KEY = process.env.MOONSHOT_API_KEY;

// 大模型API配置
export const modelProviders = {
  // 火山引擎配置
  volcano: {
    baseURL: "https://ark.cn-beijing.volces.com/api/v3",
    apiKey: ARK_API_KEY,
    models: {
      chat: "ep-20250310135947-rlxqx",
      lessonPlan: "ep-20250310135947-rlxqx",
      lessonDetail: "ep-20250310135947-rlxqx"
    }
  },
  
  // DeepSeek配置
  deepseek: {
    baseURL: "https://api.deepseek.com",
    apiKey: DEEPSEEK_API_KEY,
    models: {
      chat: "deepseek-chat",
      exercise: "deepseek-chat",
      preview: "deepseek-chat"
    }
  },
  
  // Moonshot配置
  moonshot: {
    baseURL: "https://api.moonshot.cn/v1",
    apiKey: MOONSHOT_API_KEY,
    models: {
      search: "moonshot-v1-32k"
    }
  }
};

// API使用场景
export const API_USAGES = {
  CHAT: "chat",
  EXERCISE: "exercise",
  PREVIEW: "preview",
  SEARCH: "search",
  LESSON_PLAN: "lessonPlan",
  LESSON_DETAIL: "lessonDetail"
};

// 根据使用场景获取默认提供商
export function getProviderForUsage(usage) {
  const usageToProvider = {
    [API_USAGES.CHAT]: "volcano",
    [API_USAGES.EXERCISE]: "deepseek",
    [API_USAGES.PREVIEW]: "deepseek",
    [API_USAGES.SEARCH]: "moonshot",
    [API_USAGES.LESSON_PLAN]: "volcano",
    [API_USAGES.LESSON_DETAIL]: "volcano"
  };
  
  return usageToProvider[usage] || "volcano";
}

// 根据使用场景获取模型配置
export function getModelConfig(usage, provider = null) {
  // 如果没有指定provider，使用默认provider
  const selectedProvider = provider || getProviderForUsage(usage);
  return modelProviders[selectedProvider];
}

// 创建OpenAI客户端配置
export function createOpenAIConfig(usage, provider = null) {
  const config = getModelConfig(usage, provider);
  return {
    apiKey: config.apiKey,
    baseURL: config.baseURL
  };
}

export default {
  modelProviders,
  API_USAGES,
  getProviderForUsage,
  getModelConfig,
  createOpenAIConfig
}; 
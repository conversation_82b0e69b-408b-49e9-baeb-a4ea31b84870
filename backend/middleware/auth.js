import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// 从环境变量获取JWT密钥
const JWT_SECRET = process.env.JWT_SECRET;

export const authenticateToken = (req, res, next) => {
  try {
    console.log('认证中间件开始处理请求');
    console.log('请求完整路径:', req.originalUrl);
    // 使用完整路径检查登录、注册路径豁免
    if (
      req.originalUrl === '/login' || 
      req.originalUrl === '/register' ||
      req.originalUrl === '/api/students/login' || 
      req.originalUrl === '/api/students/register' ||
      req.originalUrl === '/api/teacher/login' ||
      req.originalUrl === '/api/student/login' ||
      req.originalUrl === '/api/student/register'
    ) {
      console.log('登录/注册路径，豁免验证');
      return next();
    }
    
    // 从请求头获取 token
    const authHeader = req.headers['authorization'];
    console.log('Authorization 头:', authHeader);
    
    const token = authHeader && authHeader.split(' ')[1];
    console.log('提取的 token:', token ? '存在' : '不存在');

    if (!token) {
      console.log('未提供 token');
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    // 验证 token
    jwt.verify(token, JWT_SECRET, (err, user) => {
      if (err) {
        console.error('Token 验证失败:', err);
        return res.status(403).json({
          success: false,
          message: '无效的认证令牌'
        });
      }

      console.log('Token 验证成功，用户信息:', user);
      // 将用户信息添加到请求对象
      req.user = user;
      next();
    });
  } catch (error) {
    console.error('认证中间件错误:', error);
    res.status(500).json({
      success: false,
      message: '认证过程发生错误'
    });
  }
};

// 生成 token
export const generateToken = (user) => {
  const token = jwt.sign(
    {
      id: user.id,
      system_teacher_id: user.system_teacher_id,
      school_teacher_id: user.school_teacher_id,
      name: user.name,
      role: user.role
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
  console.log('生成新 token:', {
    userId: user.id,
    systemTeacherId: user.system_teacher_id,
    schoolTeacherId: user.school_teacher_id,
    tokenLength: token.length
  });
  return token;
};

// 验证 token 但不要求必须存在
export const optionalAuth = (req, res, next) => {
  try {
    console.log('可选认证中间件开始处理请求');
    console.log('请求路径:', req.path);
    
    const authHeader = req.headers['authorization'];
    console.log('Authorization 头:', authHeader);
    
    const token = authHeader && authHeader.split(' ')[1];
    console.log('提取的 token:', token ? '存在' : '不存在');

    if (token) {
      jwt.verify(token, JWT_SECRET, (err, user) => {
        if (!err) {
          req.user = user;
          console.log('可选认证成功，用户信息:', user);
        } else {
          console.log('无效token，但不阻止访问');
          req.user = null;
        }
        next();
      });
    } else {
      console.log('未提供token，允许匿名访问');
      req.user = null;
      next();
    }
  } catch (error) {
    console.error('可选认证中间件错误:', error);
    req.user = null;
    next();
  }
}; 
import jwt from 'jsonwebtoken';
import pool from '../config/db.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

const authMiddleware = async (req, res, next) => {
  try {
    // 从请求头获取 token
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    // 验证 token
    const decoded = jwt.verify(token, process.env.JWT_SECRET); // 使用环境变量获取JWT密钥

    // 根据用户角色从不同表验证用户
    let query;
    let params;
    
    if (decoded.role === 'student') {
      query = 'SELECT student_id FROM students WHERE student_id = ?';
      params = [decoded.student_id];
    } else if (decoded.role === 'teacher') {
      query = 'SELECT system_teacher_id FROM teaching_assistant WHERE system_teacher_id = ?';
      params = [decoded.system_teacher_id];
    } else {
      return res.status(401).json({
        success: false,
        message: '无效的用户角色'
      });
    }

    // 从数据库验证用户
    const [rows] = await pool.execute(query, params);

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 将用户信息添加到请求对象
    req.user = decoded;

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '认证令牌已过期'
      });
    }

    console.error('认证中间件错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

export const authenticateToken = authMiddleware;

export default authMiddleware; 
import rateLimit from 'express-rate-limit';

// 登录请求限制
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 限制每个IP在windowMs时间内最多5次尝试
  message: {
    success: false,
    message: '登录尝试次数过多，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 注册请求限制
export const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 限制每个IP在windowMs时间内最多3次尝试
  message: {
    success: false,
    message: '注册尝试次数过多，请1小时后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 通用API请求限制
export const apiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 100, // 限制每个IP在windowMs时间内最多100次请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
}); 
export const requestLogger = (req, res, next) => {
  const start = Date.now();

  // 请求结束时记录日志
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log('\n--- 请求日志 ---');
    console.log(`时间: ${new Date().toISOString()}`);
    console.log(`方法: ${req.method}`);
    console.log(`路径: ${req.path}`);
    console.log(`状态码: ${res.statusCode}`);
    console.log(`耗时: ${duration}ms`);
    console.log(`IP: ${req.ip}`);
    console.log(`用户代理: ${req.get('user-agent')}`);
    console.log('----------------\n');
  });

  next();
}; 
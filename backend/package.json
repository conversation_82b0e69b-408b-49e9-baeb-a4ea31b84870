{"name": "teacher-assistant-backend", "version": "1.0.0", "type": "module", "description": "教师助手后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "child_process": "^1.0.2", "cors": "^2.8.5", "docx": "^9.2.0", "dotenv": "^16.4.7", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "fs": "^0.0.1-security", "fs-extra": "^11.3.0", "jsonwebtoken": "^9.0.2", "libreoffice-convert": "^1.6.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.0", "node-xlsx": "^0.23.0", "openai": "^4.92.0", "path": "^0.12.7", "url": "^0.11.4", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2"}}
import { OpenAI } from 'openai';
import pool from '../config/db.js';
import { createOpenAIConfig, API_USAGES } from '../config/modelService.js';

// 初始化 DeepSeek AI 客户端（异步）
let ai_client;
(async () => {
  try {
    const config = await createOpenAIConfig(API_USAGES.EXERCISE);
    ai_client = new OpenAI(config);
    console.log('DeepSeek AI 客户端初始化成功');
  } catch (error) {
    console.error('DeepSeek AI 客户端初始化失败:', error);
  }
})();

// 生成习题
export const generateExercises = async (req, res) => {
  try {
    console.log('\n=== 开始生成习题 ===');
    
    // 确保AI客户端已初始化
    if (!ai_client) {
      console.log('AI客户端尚未初始化，正在尝试初始化...');
      const config = await createOpenAIConfig(API_USAGES.EXERCISE);
      ai_client = new OpenAI(config);
      console.log('成功初始化AI客户端');
    }
    
    const { courseCode, titleId, exerciseType, difficulty, count, questionType, knowledgePoint } = req.body;
    const teacherId = req.user?.system_teacher_id;

    console.log('参数信息:', {
      courseCode,
      titleId,
      exerciseType,
      difficulty,
      count,
      questionType,
      knowledgePoint,
      teacherId
    });

    if (!courseCode || !titleId || !exerciseType || !difficulty || !count || !questionType) {
      console.log('❌ 参数验证失败: 缺少必要参数');
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 获取课程大纲内容
    console.log('\n1. 获取课程大纲...');
    const [syllabusRows] = await pool.execute(
      `SELECT content
       FROM syllabus 
       WHERE course_code = ? AND teacher_id = ?
       ORDER BY create_time DESC LIMIT 1`,
      [courseCode, teacherId]
    );

    if (syllabusRows.length === 0) {
      console.log('❌ 未找到课程大纲');
      return res.status(404).json({
        success: false,
        message: '未找到课程大纲'
      });
    }

    console.log('✓ 成功获取大纲');

    // 解析大纲内容
    const syllabusContent = JSON.parse(syllabusRows[0].content);
    
    // 查找对应的章节标题
    let sectionTitle = '';
    
    // 递归查找函数
    function findSection(obj) {
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'object' && value !== null) {
          findSection(value);
        } else if (value === titleId.toString()) {
          sectionTitle = key;
          return;
        }
      }
    }
    
    findSection(syllabusContent);
    
    if (!sectionTitle) {
      console.log('❌ 未找到对应章节');
      return res.status(404).json({
        success: false,
        message: '未找到对应的章节'
      });
    }

    console.log('✓ 找到章节:', sectionTitle);

    // 构建提示词
    console.log('\n2. 构建AI提示词...');
    const questionTypeText = {
      'choice': '选择题',
      'completion': '填空题',
      'judgment': '判断题',
      'short-answer': '简答题',
      'calculation': '计算题'
    }[questionType] || '选择题';
    
    // 将前端的questionType转换为数据库中的数字格式
    const questionTypeToDb = {
      'choice': 1,
      'completion': 2,
      'judgment': 3,
      'short-answer': 4,
      'calculation': 5
    };
    
    // 构建基础提示词
    let promptBase = `作为一名专业的教育工作者，请根据以下内容生成${count}道${
      difficulty === 1 ? '简单' : difficulty === 2 ? '中等' : '困难'
    }难度的${
      exerciseType === '1' ? '预习' : '课后'
    }${questionTypeText}：

章节：${sectionTitle}`;

    // 如果提供了知识点，添加知识点要求
    if (knowledgePoint && exerciseType === '2') {
      console.log('✓ 添加知识点要求:', knowledgePoint);
      promptBase += `\n\n重点知识点：${knowledgePoint}\n\n请确保题目主要围绕上述知识点设计，帮助学生掌握和巩固这个重要概念。生成的题目必须将知识点字段设置为"${knowledgePoint}"，一字不差。`;
    }

    // 完成提示词
    const prompt = `${promptBase}

要求：
1. ${questionType === 'choice' ? '选择题要有4个选项(A-D)' : 
     questionType === 'completion' ? '填空题要有明确的填空位置，用下划线"_"表示填空处，且只能有一个空。' : 
     questionType === 'judgment' ? '判断题答案必须为"正确"或"错误"' : 
     questionType === 'short-answer' ? '简答题一定答案要详细，解析要说明答题要点。' : 
     '计算题必须包含以下内容：\n   - 题目要清晰表述计算要求\n   - 答案要给出完整的计算步骤\n   - 解析要说明每一步的原理和注意事项'}
2. 难度要符合${difficulty === 1 ? '简单' : difficulty === 2 ? '中等' : '困难'}等级
3. 必须以JSON格式返回结果，每道题包含title(题目)、answer(答案)、analysis(解析)、point(知识点)四个字段，point字段应当返回与题目相关的简短知识点或核心概念，避免冗长的解释和细节描述。
4. 知识点必须简洁明了，例如"数列收敛的判别条件"，而不是"递推数列极限求解：假设极限存在并解方程"这样冗长的描述。
5. 注意：
   - ${questionType === 'choice' ? '选择题的选项以options对象返回，格式为{"A":"选项A内容","B":"选项B内容","C":"选项C内容","D":"选项D内容"}' : 
     questionType === 'judgment' ? '判断题的选项以options对象返回，格式为{"T":"正确","F":"错误"}' : 
     '此类题型无需options字段，可设为空对象{}'}
   - 返回的JSON数组应为如下格式：
[
  {
    "title": "题目内容",${questionType === 'choice' || questionType === 'judgment' ? '\n    "options": {"A":"选项A", "B":"选项B", "C":"选项C", "D":"选项D"},' : '\n    "options": {},'}
    "answer": "答案内容",
    "analysis": "解析内容",
    "point": "知识点内容 (必须简洁)"
  },
  // 更多题目
]`;

    console.log('✓ 提示词构建完成');
    console.log('\n生成的提示词：', prompt);

    // 调用AI生成习题
    console.log('\n3. 调用AI生成习题...');
    const completion = await ai_client.chat.completions.create({
      model: "deepseek-chat",
      messages: [
        {
          role: "system",
          content: "你是一个专业的教育工作者，擅长出题和解析。请以JSON格式返回习题数据，确保每道题都包含完整的题目、答案、解析和知识点。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });

    // 获取AI返回的内容
    const exerciseContent = completion.choices[0].message.content.trim();
    
    // 解析JSON数据
    console.log('\n4. 处理AI生成的习题...');
    let processedExercises = [];
    try {
      // 提取JSON部分，排除可能的非JSON前缀
      const jsonMatch = exerciseContent.match(/(\[[\s\S]*\])/);
      const jsonContent = jsonMatch ? jsonMatch[0] : exerciseContent;
      
      // 解析JSON数据
      const exercisesJson = JSON.parse(jsonContent);
      
      if (!Array.isArray(exercisesJson)) {
        throw new Error('返回的数据不是数组格式');
      }
      
      // 处理每道习题的数据
      processedExercises = exercisesJson.map((exercise, index) => {
        console.log(`\n处理第 ${index + 1} 道习题...`);
        
        // 如果有指定的知识点，则使用指定的知识点
        let knowledgePoint = exercise.point || '';
        if (knowledgePoint && typeof knowledgePoint === 'string') {
          console.log(`AI生成的知识点: ${knowledgePoint}`);
        }
        
        // 如果请求中指定了知识点，直接使用请求中的知识点
        if (knowledgePoint && exerciseType === '2') {
          knowledgePoint = knowledgePoint;
          console.log(`保留AI生成的知识点: ${knowledgePoint}`);
        }
        
        // 确保所有必要字段存在
        const result = {
          title: exercise.title || '',
          content: exercise.title || '',
          options: exercise.options || {},
          answer: exercise.answer || '',
          analysis: exercise.analysis || '',
          point: knowledgePoint, // 保存正确的知识点
          studyPhase: exerciseType === '1' ? 1 : 2, // 1: 预习, 2: 课后
          type: exerciseType, // 确保type字段是字符串格式的"1"或"2"
          questionType: questionTypeToDb[questionType] || 1,
          difficulty: difficulty
        };
        
        return result;
      });
      
      console.log(`✓ 解析出 ${processedExercises.length} 道习题`);
    } catch (error) {
      console.error('解析JSON数据失败:', error);
      console.log('原始数据:', exerciseContent);
      
      // 尝试使用备用方式解析
      try {
        // 如果JSON解析失败，尝试从文本中提取JSON部分
        const jsonStart = exerciseContent.indexOf('[');
        const jsonEnd = exerciseContent.lastIndexOf(']') + 1;
        
        if (jsonStart >= 0 && jsonEnd > jsonStart) {
          const jsonContent = exerciseContent.substring(jsonStart, jsonEnd);
          const exercisesJson = JSON.parse(jsonContent);
          
          processedExercises = exercisesJson.map((exercise, index) => {
            // 如果有指定的知识点，则使用指定的知识点
            let knowledgePoint = exercise.point || '';
            if (knowledgePoint && typeof knowledgePoint === 'string') {
              console.log(`备用方式处理的AI生成知识点: ${knowledgePoint}`);
            }
            
            // 如果请求中指定了知识点，直接使用请求中的知识点
            if (knowledgePoint && exerciseType === '2') {
              knowledgePoint = knowledgePoint;
              console.log(`备用方式保留的AI生成知识点: ${knowledgePoint}`);
            }
            
            return {
              title: exercise.title || '',
              content: exercise.title || '',
              options: exercise.options || {},
              answer: exercise.answer || '',
              analysis: exercise.analysis || '',
              point: knowledgePoint, // 保存正确的知识点
              studyPhase: exerciseType === '1' ? 1 : 2,
              type: exerciseType,
              questionType: questionTypeToDb[questionType] || 1,
              difficulty: difficulty
            };
          });
          
          console.log(`✓ 备用方式解析出 ${processedExercises.length} 道习题`);
        } else {
          throw new Error('无法从文本中提取JSON数据');
        }
      } catch (backupError) {
        console.error('备用解析方式也失败:', backupError);
        throw new Error('无法解析AI返回的数据，请重试');
      }
    }

    if (processedExercises.length > 0) {
      console.log('\n5. 保存习题到数据库...');
      
      // 获取大纲ID
      const [syllabusResult] = await pool.execute(
        'SELECT id FROM syllabus WHERE course_code = ? AND teacher_id = ? ORDER BY create_time DESC LIMIT 1',
        [courseCode, teacherId]
      );

      if (syllabusResult.length === 0) {
        throw new Error('未找到相关大纲信息');
      }

      const syllabusId = syllabusResult[0].id;
      console.log('✓ 获取大纲ID:', syllabusId);

      // 批量保存习题
      console.log('开始保存习题...');
      for (const [index, exercise] of processedExercises.entries()) {
        await pool.execute(
          `INSERT INTO exercise_bank (
            syllabus_id, teacher_id, question_type, difficulty,
            content, answer, analysis, point, title, title_id, course_code,
            study_phase, \`option\`
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            syllabusId,
            teacherId,
            exercise.questionType, // 现在存储题目类型(1-5)
            exercise.difficulty,
            exercise.content, // 只存题干
            exercise.answer,
            exercise.analysis,
            exercise.point,  // 存储知识点
            exercise.title,
            titleId,
            courseCode,
            exercise.studyPhase, // 存储是预习题(1)还是课后题(2)
            JSON.stringify(exercise.options) // 选项存储在option字段
          ]
        );
        console.log(`✓ 已保存第 ${index + 1}/${processedExercises.length} 道习题`);
      }

      console.log('\n✓ 所有习题保存完成!');
      
      return res.json({
        success: true,
        data: processedExercises
      });
    }

    // 如果所有方法都失败了，返回错误
    console.log('\n❌ 无法解析任何有效习题');
    return res.status(500).json({
      success: false,
      message: '生成的习题格式不正确，请重试'
    });

  } catch (error) {
    console.log('\n❌ 生成习题失败:', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({
      success: false,
      message: error.message || '生成习题失败'
    });
  } finally {
    console.log('\n=== 习题生成结束 ===\n');
  }
};

// 获取指定章节的习题列表
export const getExercisesBySection = async (req, res) => {
  try {
    console.log('\n=== 获取章节习题列表 ===');
    const { courseCode, titleId } = req.params;
    const teacherId = req.user?.system_teacher_id;

    console.log('查询参数:', {
      courseCode,
      titleId,
      teacherId
    });

    // 获取课程大纲内容以验证 titleId
    console.log('\n1. 验证章节...');
    const [syllabusRows] = await pool.execute(
      `SELECT content
       FROM syllabus 
       WHERE course_code = ? AND teacher_id = ?
       ORDER BY create_time DESC LIMIT 1`,
      [courseCode, teacherId]
    );

    if (syllabusRows.length === 0) {
      console.log('❌ 未找到课程大纲');
      return res.status(404).json({
        success: false,
        message: '未找到课程大纲'
      });
    }

    // 解析大纲内容并验证 titleId
    const syllabusContent = JSON.parse(syllabusRows[0].content);
    let isValidTitleId = false;
    
    function validateTitleId(obj) {
      for (const [, value] of Object.entries(obj)) {
        if (typeof value === 'object') {
          validateTitleId(value);
        } else if (value === titleId.toString()) {
          isValidTitleId = true;
          return;
        }
      }
    }
    
    validateTitleId(syllabusContent);
    
    if (!isValidTitleId) {
      console.log('❌ 未找到对应章节');
      return res.status(404).json({
        success: false,
        message: '未找到对应的章节'
      });
    }

    console.log('✓ 章节验证通过');

    // 获取习题列表
    console.log('\n2. 查询习题列表...');
    const [exercises] = await pool.execute(
      `SELECT 
        exercise_id,
        question_type,
        difficulty,
        content,
        \`option\`,
        answer,
        analysis,
        point,
        create_time,
        study_phase,
        title
       FROM exercise_bank 
       WHERE course_code = ? 
         AND title_id = ? 
         AND teacher_id = ?
       ORDER BY create_time DESC`,
      [courseCode, titleId, teacherId]
    );

    console.log(`✓ 找到 ${exercises.length} 道习题`);

    // 处理习题内容
    console.log('\n3. 处理习题数据...');
    const formattedExercises = exercises.map((exercise, index) => {
      let options = {};
      try {
        options = exercise.option ? JSON.parse(exercise.option) : {};
      } catch (e) {
        console.warn(`⚠️ 解析第 ${index + 1} 道习题选项失败:`, e.message);
      }
      
      // 将数据库中的题目类型转换为前端显示用的字符串
      const questionTypeMap = {
        1: 'choice',
        2: 'completion',
        3: 'judgment',
        4: 'short-answer',
        5: 'calculation'
      };
      
      return {
        id: exercise.exercise_id,
        type: exercise.study_phase.toString(), // 直接转换为字符串，1->预习题，2->课后题
        difficulty: exercise.difficulty,
        title: exercise.title || exercise.content,
        content: exercise.content,
        options: options,
        answer: exercise.answer,
        analysis: exercise.analysis,
        point: exercise.point || '', // 添加知识点字段
        createTime: exercise.create_time,
        studyPhase: exercise.study_phase,
        questionType: questionTypeMap[exercise.question_type] || 'choice'
      };
    });

    console.log('✓ 数据处理完成');

    res.json({
      success: true,
      data: formattedExercises
    });

  } catch (error) {
    console.log('\n❌ 获取习题列表失败:', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({
      success: false,
      message: '获取习题列表失败'
    });
  } finally {
    console.log('\n=== 获取习题列表结束 ===\n');
  }
};

// 保存习题
export const saveExercise = async (req, res) => {
  try {
    const { courseCode, titleId, exercise } = req.body;
    const teacherId = req.user?.system_teacher_id;

    // 将前端的questionType转换为数据库中的数字格式
    const questionTypeToDb = {
      'choice': 1,
      'completion': 2,
      'judgment': 3,
      'short-answer': 4,
      'calculation': 5
    };

    // 获取大纲ID
    const [syllabus] = await pool.execute(
      'SELECT id FROM syllabus WHERE course_code = ? AND teacher_id = ? ORDER BY create_time DESC LIMIT 1',
      [courseCode, teacherId]
    );

    if (syllabus.length === 0) {
      throw new Error('未找到相关大纲信息');
    }

    // 保存习题
    const [result] = await pool.execute(
      `INSERT INTO exercise_bank (
        syllabus_id, teacher_id, question_type, difficulty,
        content, answer, analysis, point, title, title_id, course_code,
        study_phase, \`option\`
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        syllabus[0].id,
        teacherId,
        questionTypeToDb[exercise.questionType] || 1, // 存储题目类型(1-5)
        exercise.difficulty,
        exercise.content || exercise.title, // 只存题干
        exercise.answer,
        exercise.analysis,
        exercise.point || '', // 添加知识点字段
        exercise.title,
        titleId,
        courseCode,
        exercise.studyPhase || (exercise.type === '1' ? 1 : 2), // 存储是预习题(1)还是课后题(2)
        JSON.stringify(exercise.options || {}) // 选项存储在option字段
      ]
    );

    res.json({
      success: true,
      data: {
        id: result.insertId
      }
    });

  } catch (error) {
    console.error('保存习题失败:', error);
    res.status(500).json({
      success: false,
      message: '保存习题失败'
    });
  }
};

// 获取习题统计信息
export const getExerciseStats = async (req, res) => {
  try {
    const { courseCode, titleId } = req.params;
    const teacherId = req.user?.system_teacher_id;

    // 获取课程大纲内容以验证 titleId
    const [syllabusRows] = await pool.execute(
      `SELECT content
       FROM syllabus 
       WHERE course_code = ? AND teacher_id = ?
       ORDER BY create_time DESC LIMIT 1`,
      [courseCode, teacherId]
    );

    if (syllabusRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到课程大纲'
      });
    }

    // 解析大纲内容并验证 titleId
    const syllabusContent = JSON.parse(syllabusRows[0].content);
    let isValidTitleId = false;
    
    function validateTitleId(obj) {
      for (const [, value] of Object.entries(obj)) {
        if (typeof value === 'object') {
          validateTitleId(value);
        } else if (value === titleId.toString()) {
          isValidTitleId = true;
          return;
        }
      }
    }
    
    validateTitleId(syllabusContent);
    
    if (!isValidTitleId) {
      return res.status(404).json({
        success: false,
        message: '未找到对应的章节'
      });
    }

    // 获取习题统计信息
    const [stats] = await pool.execute(
      `SELECT 
        question_type,
        study_phase,
        difficulty,
        COUNT(*) as count
       FROM exercise_bank 
       WHERE course_code = ? 
         AND title_id = ? 
         AND teacher_id = ?
       GROUP BY question_type, study_phase, difficulty`,
      [courseCode, titleId, teacherId]
    );

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取习题统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取习题统计失败'
    });
  }
};

/**
 * 获取班级章节最近一批次的错误知识点排行
 * @param {object} req - 请求对象
 * @param {object} res - 响应对象
 */
export const getTopErrorKnowledgePoints = async (req, res) => {
  try {
    console.log('\n=== 获取错误知识点排行 ===');
    const { classId, titleId, courseCode } = req.params;
    
    console.log('查询参数:', { classId, titleId, courseCode });
    
    if (!classId || !titleId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    // 1. 先获取该班级章节的最新一个批次
    console.log('1. 获取最新批次...');
    const [batchRows] = await pool.execute(
      `SELECT release_batch 
       FROM publish_exercise 
       WHERE class_id = ? 
         AND title_id = ? 
         AND study_phase = 2
       ORDER BY release_batch DESC 
       LIMIT 1`,
      [classId, titleId]
    );
    
    if (batchRows.length === 0) {
      console.log('❌ 未找到批次数据');
      return res.json({
        success: true,
        message: '该班级章节尚未发布课后题',
        data: {
          latestBatch: null,
          knowledgePoints: []
        }
      });
    }
    
    const latestBatch = batchRows[0].release_batch;
    console.log(`✓ 找到最新批次: ${latestBatch}`);
    
    // 2. 找出该批次所有习题中包含知识点的题目
    console.log('2. 获取习题知识点数据...');
    
    // 先判断表中有哪个知识点字段(point或knowledge_points)
    let pointFieldName = 'point';
    try {
      // 尝试查询point字段是否存在
      await pool.execute(
        `SELECT point FROM exercise_bank LIMIT 1`
      );
    } catch (error) {
      // 如果point字段不存在，尝试knowledge_points字段
      console.log('point字段不存在，尝试使用knowledge_points字段');
      try {
        await pool.execute(
          `SELECT knowledge_points FROM exercise_bank LIMIT 1`
        );
        pointFieldName = 'knowledge_points';
      } catch (innerError) {
        console.error('两个知识点字段都不存在');
        return res.status(500).json({
          success: false,
          message: '数据库缺少知识点字段'
        });
      }
    }
    
    console.log(`✓ 使用 ${pointFieldName} 字段查询知识点数据`);
    
    const [exercisesQuery] = await pool.execute(
      `SELECT 
         pe.id AS publish_id, 
         eb.exercise_id,
         eb.${pointFieldName} AS knowledge_point,
         eb.title
       FROM publish_exercise pe
       JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
       WHERE pe.class_id = ? 
         AND pe.title_id = ? 
         AND pe.study_phase = 2
         AND pe.release_batch = ?
         AND eb.${pointFieldName} IS NOT NULL 
         AND eb.${pointFieldName} != ''`,
      [classId, titleId, latestBatch]
    );
    
    if (exercisesQuery.length === 0) {
      console.log('❌ 未找到包含知识点的习题');
      return res.json({
        success: true,
        message: '该批次没有包含知识点的习题',
        data: {
          latestBatch,
          knowledgePoints: []
        }
      });
    }
    
    console.log(`✓ 找到 ${exercisesQuery.length} 道包含知识点的习题`);
    
    // 3. 解析知识点并统计错误率
    console.log('3. 计算知识点错误率...');
    const knowledgePointMap = new Map();
    
    // 处理每个习题的知识点和错误率
    for (const exercise of exercisesQuery) {
      // 解析知识点
      let pointsList = [];
      
      try {
        if (typeof exercise.knowledge_point === 'string') {
          try {
            // 尝试解析JSON
            const pointsJson = JSON.parse(exercise.knowledge_point);
            if (Array.isArray(pointsJson)) {
              pointsList = pointsJson;
            } else if (typeof pointsJson === 'object') {
              pointsList = Object.values(pointsJson);
            } else {
              pointsList = [String(pointsJson)];
            }
          } catch (e) {
            // 如果不是JSON，尝试以逗号分隔解析
            pointsList = exercise.knowledge_point.split(',').map(p => p.trim());
          }
        }
      } catch (e) {
        console.error(`解析知识点失败: ${e.message}`);
        continue;
      }
      
      // 如果没有解析出知识点，跳过
      if (pointsList.length === 0) {
        continue;
      }
      
      // 获取该习题的错误提交数据
      const [errorRecords] = await pool.execute(
        `SELECT 
           COUNT(*) as error_count
         FROM student_exercise_records
         WHERE publish_exercise_id = ? AND is_correct = 0`,
        [exercise.publish_id]
      );
      
      // 获取该习题的总提交数
      const [totalRecords] = await pool.execute(
        `SELECT 
           COUNT(*) as total_count
         FROM student_exercise_records
         WHERE publish_exercise_id = ?`,
        [exercise.publish_id]
      );
      
      const errorCount = errorRecords[0].error_count;
      const totalCount = totalRecords[0].total_count;
      
      // 只在有人提交的情况下计算错误率
      if (totalCount > 0) {
        const errorRate = Math.round((errorCount / totalCount) * 100);
        
        // 更新每个知识点的统计
        for (const point of pointsList) {
          if (!point || point.trim() === '') continue;
          
          const pointName = point.trim();
          if (!knowledgePointMap.has(pointName)) {
            knowledgePointMap.set(pointName, {
              name: pointName,
              totalErrors: 0,
              totalSubmissions: 0,
              exercises: []
            });
          }
          
          const pointData = knowledgePointMap.get(pointName);
          pointData.totalErrors += errorCount;
          pointData.totalSubmissions += totalCount;
          pointData.exercises.push({
            id: exercise.publish_id,
            title: exercise.title,
            errorCount,
            totalCount,
            errorRate
          });
        }
      }
    }
    
    // 4. 计算每个知识点的总错误率并排序
    console.log('4. 排序知识点错误率...');
    const knowledgePoints = Array.from(knowledgePointMap.values())
      .map(point => {
        const errorRate = point.totalSubmissions > 0 
          ? Math.round((point.totalErrors / point.totalSubmissions) * 100)
          : 0;
          
        return {
          name: point.name,
          errorRate,
          errorCount: point.totalErrors,
          totalCount: point.totalSubmissions,
          exerciseCount: point.exercises.length
        };
      })
      .sort((a, b) => b.errorRate - a.errorRate)
      .slice(0, 5); // 只取前5个
    
    console.log(`✓ 找到 ${knowledgePoints.length} 个错误知识点`);
    
    // 5. 返回结果
    res.json({
      success: true,
      data: {
        latestBatch,
        knowledgePoints
      }
    });
    
  } catch (error) {
    console.error('获取错误知识点排行失败:', error);
    res.status(500).json({
      success: false,
      message: '获取错误知识点排行失败'
    });
  } finally {
    console.log('\n=== 错误知识点排行查询结束 ===\n');
  }
};

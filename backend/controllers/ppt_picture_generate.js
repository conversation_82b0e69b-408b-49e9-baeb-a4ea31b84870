import OpenAI from "openai";
import fetch from 'node-fetch';
import { loadModelConfigs } from '../config/modelService.js';

// 创建存储图片链接的对象
const imageLinks = {};

// 从数据库获取API配置
async function getOpenAIClient() {
  try {
    // 加载模型配置
    const configs = await loadModelConfigs();

    // 获取deepseek配置
    const deepseekConfig = configs['deepseek'];

    if (!deepseekConfig) {
      console.error('未找到deepseek配置');
      // 使用备用配置
      return new OpenAI({
        baseURL: 'https://api.deepseek.com/v1/',
        apiKey: '***********************************'
      });
    }

    return new OpenAI({
      baseURL: `${deepseekConfig.baseURL}/v1/`,
      apiKey: deepseekConfig.apiKey
    });
  } catch (error) {
    console.error('获取OpenAI客户端失败:', error);
    // 使用备用配置
    return new OpenAI({
      baseURL: 'https://api.deepseek.com/v1/',
      apiKey: '***********************************'
    });
  }
}

async function generateImageList(pptOutline) {
  console.log("开始调用 deepseek API 获取插图列表...");

  // 获取OpenAI客户端
  const openai = await getOpenAIClient();

  const completion = await openai.chat.completions.create({
      messages: [{ role: "system", content: 'You are a helpful assistant.'},
          { role: "user", content: `${JSON.stringify(pptOutline)}
  仔细观察PPT大纲的内容，思考哪些地方可以生成PPT插图，返回一个json数据，包含插图名称和如何实现该插图的提示词，一个示例如下
  {"插图列表": [
      {
        "插图名称": "函数曲线示例图",
        "提示词": "二维坐标系中绘制常见函数曲线（如线性函数、二次函数、三角函数），用不同颜色区分，包含坐标轴标签和图例，白底矢量图"
      },
      {
        "插图名称": "极限过程动态图",
        "提示词": "动画分帧图展示x→a时f(x)→L的过程，包含函数曲线、趋近点标记和极限值虚线，用箭头标注趋势，白底矢量图"
      }]},
  请严格遵循下面的约定，不要违背：
  1、严格按照上面的json示例格式返回数据
  2、请你仔细甄别插图的数量，你返回的json数据最多包含1张插图
  3、请注意返回最重要和最有意义的插图，因为只有1张插图
  4、只返回json数据
  5、插图列表的每一个元素里面只能包含一个插图名称和一个提示词，不允许出现下面情况{
      "插图名称": "数学学习流程图",
      "提示词": "流程图展示学习高等数学的步骤：理解概念、掌握方法、练习题目、应用实践，用箭头连接各步骤，白底矢量图"
      "提示词": "展示旋转体体积或曲线长度的积分计算示意图，标注关键参数和积分符号，白底矢量图"
    }{
      "插图名称": "数学符号集合图",
      "插图名称": "数学学习流程图",
      "提示词": "流程图展示学习高等数学的步骤：理解概念、掌握方法、练习题目、应用实践，用箭头连接各步骤，白底矢量图"
    }和{}，一旦出现上面的情况就舍弃该答案
` }],
      model: "deepseek-chat",
    });

  return completion.choices[0].message.content;
}

// 清理 JSON 响应，移除 Markdown 代码块标记和其他非 JSON 内容
function cleanJsonResponse(response) {
  // 移除 Markdown 代码块标记
  let cleaned = response.replace(/```json\s*\n/g, '').replace(/```\s*$/g, '');

  // 如果有多余的文本，尝试提取 JSON 部分
  const jsonStart = cleaned.indexOf('{');
  const jsonEnd = cleaned.lastIndexOf('}');

  if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
    cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
  }

  return cleaned;
}

// 从文本中提取图片链接
function extractImageLinks(text) {
  const result = {
    imageUrl: null,
    downloadUrl: null
  };

  // 如果文本为空，直接返回
  if (!text || text.length === 0) {
    return result;
  }

  // 检查文本中是否包含图片或下载链接的关键字
  const hasImageKeyword = text.includes('![');
  const hasDownloadKeyword = text.includes('下载⏬');

  // 如果没有关键字，直接返回
  if (!hasImageKeyword && !hasDownloadKeyword) {
    return result;
  }

  // 尝试不同的正则表达式来提取图片URL
  const imgPatterns = [
    /!\[.*?\]\((https:\/\/.*?)\)/,  // 标准 Markdown 图片语法
    /!\[.*?\]\((https:\/\/.*?\.png)\)/,  // 指定 .png 扩展名
    /!\[.*?\]\((https:\/\/filesystem\.site\/cdn\/.*?)\)/,  // 特定域名
    /!\[file_.*?\]\((https:\/\/.*?)\)/  // 带有 file_ 前缀的图片
  ];

  // 尝试不同的正则表达式来提取下载链接
  const downloadPatterns = [
    /\[下载⏬\]\((https:\/\/.*?)\)/,  // 标准下载链接语法
    /\[下载⏬\]\((https:\/\/.*?\.png)\)/,  // 指定 .png 扩展名
    /\[下载⏬\]\((https:\/\/filesystem\.site\/cdn\/download\/.*?)\)/,  // 特定域名
    /\[下载.*?\]\((https:\/\/.*?)\)/  // 带有下载字样的链接
  ];

  // 尝试每个图片URL模式
  if (hasImageKeyword) {
    for (const pattern of imgPatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        result.imageUrl = match[1];
        console.log(`使用模式 ${pattern} 找到图片URL: ${match[1]}`);
        break;
      }
    }
  }

  // 尝试每个下载链接模式
  if (hasDownloadKeyword) {
    for (const pattern of downloadPatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        result.downloadUrl = match[1];
        console.log(`使用模式 ${pattern} 找到下载链接: ${match[1]}`);
        break;
      }
    }
  }

  return result;
}

// 设置 GPT-4o API 请求头
async function getHeaders() {
  const myHeaders = new Headers();
  myHeaders.append("Accept", "application/json");

  try {
    // 使用loadModelConfigs函数加载所有模型配置
    const configs = await loadModelConfigs();

    // 先尝试获取openai配置
    let config = configs['openai'];

    // 如果没有openai配置，尝试获取任何可用的配置
    if (!config) {
      console.log('未找到openai配置，尝试使用其他配置');
      // 获取第一个可用的配置
      const providers = Object.keys(configs);
      if (providers.length > 0) {
        config = configs[providers[0]];
        console.log(`使用${providers[0]}的配置作为备用`);
      }
    }

    if (config) {
      console.log(`使用数据库中的API密钥 (provider: ${config.name})`);
      // 确保API密钥格式正确
      const apiKey = config.apiKey;
      myHeaders.append("Authorization", apiKey.startsWith('Bearer ') ? apiKey : `Bearer ${apiKey}`);

      // 如果有baseURL，保存下来供后续使用
      if (config.baseURL) {
        console.log(`使用数据库中的API端点: ${config.baseURL}`);
        // 将baseURL保存到全局变量供后续使用
        global.gptApiBaseUrl = config.baseURL;
      }
    } else {
      // 使用备用密钥
      console.log('未找到任何可用配置，使用硬编码的备用API密钥');
      myHeaders.append("Authorization", "Bearer sk-uJ2XAQGbIrz1P06y903fCinZfTTHwJorwL5nc5Cy0LBV5Igh");
      global.gptApiBaseUrl = "https://api2.qyfxw.cn/v1";
    }
  } catch (error) {
    console.error('获取API密钥失败:', error);
    // 使用备用密钥
    console.log('由于错误使用硬编码的备用API密钥');
    myHeaders.append("Authorization", "Bearer sk-uJ2XAQGbIrz1P06y903fCinZfTTHwJorwL5nc5Cy0LBV5Igh");
    global.gptApiBaseUrl = "https://api2.qyfxw.cn/v1";
  }

  myHeaders.append("Content-Type", "application/json");
  return myHeaders;
}

// 处理单个图片生成请求
async function generateImage(imageName, prompt) {
  console.log(`开始生成图片: ${imageName}`);

  const raw = JSON.stringify({
    "model": "gpt-4o-all",
    "max_tokens": 1000,
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": `生成一张:${imageName} 提示词: ${prompt}`
      }
    ],
    "temperature": 1,
    "stream": true // 使用流式响应
  });

  const headers = await getHeaders();
  const requestOptions = {
    method: 'POST',
    headers: headers,
    body: raw,
    redirect: 'follow'
  };

  try {
    console.log(`发送请求到 GPT-4o API...`);
    console.log(`请求头部信息: Authorization=${headers.get('Authorization').substring(0, 15)}...`);

    // 使用全局变量中保存的API端点
    let apiEndpoint = "https://api2.qyfxw.cn/v1/chat/completions";
    if (global.gptApiBaseUrl) {
      apiEndpoint = `${global.gptApiBaseUrl}`;
      console.log(`使用全局变量中的API端点: ${apiEndpoint}`);
    } else {
      console.log(`使用默认API端点: ${apiEndpoint}`);
    }

    const response = await fetch(apiEndpoint, requestOptions);

    if (!response.ok) {
      console.error(`API 请求失败: ${response.status} ${response.statusText}`);

      // 尝试获取错误响应体
      try {
        const errorText = await response.text();
        console.error(`错误响应内容: ${errorText}`);
      } catch (textError) {
        console.error(`无法获取错误响应内容: ${textError.message}`);
      }

      // 如果是401错误，尝试使用备用方案
      if (response.status === 401) {
        console.log('尝试使用备用图片生成方案...');
        // 返回一个静态图片URL作为备用
        const backupImageUrl = 'https://placehold.co/600x400/orange/white?text=' + encodeURIComponent(imageName);
        console.log(`使用备用图片URL: ${backupImageUrl}`);
        imageLinks[imageName] = backupImageUrl;
        return backupImageUrl;
      }

      return null;
    }

    console.log(`收到响应，状态码: ${response.status}`);

    // 获取响应文本
    const responseText = await response.text();
    console.log(`响应文本长度: ${responseText.length} 字节`);

    let imageUrl = null;
    let downloadUrl = null;
    let accumulatedContent = ""; // 累积的内容

    // 处理流式响应
    const lines = responseText.split('\n');
    console.log(`响应包含 ${lines.length} 行数据`);

    let dataLineCount = 0;
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        dataLineCount++;
        const jsonStr = line.substring(6).trim();
        if (jsonStr === '[DONE]') {
          console.log(`检测到流结束标记 [DONE]`);
          continue;
        }

        try {
          const jsonData = JSON.parse(jsonStr);
          if (jsonData.choices && jsonData.choices[0].delta && jsonData.choices[0].delta.content) {
            const content = jsonData.choices[0].delta.content;

            // 打印完整的内容以便调试
            console.log(`收到内容片段: ${content}`);

            // 累积内容
            accumulatedContent += content;

            // 从当前片段中提取图片链接
            const contentLinks = extractImageLinks(content);

            // 如果当前片段中没有找到，尝试在累积的内容中查找
            const accumulatedLinks = extractImageLinks(accumulatedContent);

            // 优先使用当前片段中的链接，如果没有再使用累积内容中的链接
            if (contentLinks.imageUrl) {
              imageUrl = contentLinks.imageUrl;
              console.log(`在当前片段中找到图片URL: ${imageUrl}`);
            } else if (accumulatedLinks.imageUrl && !imageUrl) {
              imageUrl = accumulatedLinks.imageUrl;
              console.log(`在累积内容中找到图片URL: ${imageUrl}`);
            }

            if (contentLinks.downloadUrl) {
              downloadUrl = contentLinks.downloadUrl;
              console.log(`在当前片段中找到下载链接: ${downloadUrl}`);
            } else if (accumulatedLinks.downloadUrl && !downloadUrl) {
              downloadUrl = accumulatedLinks.downloadUrl;
              console.log(`在累积内容中找到下载链接: ${downloadUrl}`);
            }

            // 将链接添加到存储对象中
            if (downloadUrl) {
              imageLinks[imageName] = downloadUrl;
              console.log(`将下载链接添加到存储对象: ${imageName} -> ${downloadUrl}`);
            } else if (imageUrl && !imageLinks[imageName]) {
              imageLinks[imageName] = imageUrl;
              console.log(`将图片URL添加到存储对象: ${imageName} -> ${imageUrl}`);
            }
          }
        } catch (e) {
          console.error(`解析 JSON 数据时出错: ${e.message}`);
          console.error(`出错的 JSON 字符串: ${jsonStr.substring(0, 100)}...`);
        }
      }
    }

    console.log(`处理了 ${dataLineCount} 行 data 数据`);

    if (dataLineCount === 0) {
      console.log(`警告: 没有找到任何 data 行，这可能不是流式响应`);
      console.log(`响应的前 200 个字符: ${responseText.substring(0, 200)}...`);
    }

    // 在处理完所有行后，再次检查累积的内容中是否包含图片链接
    console.log(`处理完所有行，再次检查累积的内容`);
    console.log(`累积内容长度: ${accumulatedContent.length} 字符`);

    // 如果还没有找到图片URL或下载链接，再次尝试在累积的内容中查找
    if (!imageUrl && !downloadUrl) {
      console.log(`还没有找到图片链接，再次尝试在累积的内容中查找`);

      // 打印累积内容的一部分以便调试
      console.log(`累积内容的前 500 个字符: ${accumulatedContent.substring(0, 500)}...`);

      // 使用提取函数再次尝试提取链接
      const finalLinks = extractImageLinks(accumulatedContent);

      if (finalLinks.imageUrl) {
        imageUrl = finalLinks.imageUrl;
        console.log(`最终找到图片URL: ${imageUrl}`);

        // 将图片URL添加到存储对象中
        if (!imageLinks[imageName]) {
          imageLinks[imageName] = imageUrl;
          console.log(`将图片URL添加到存储对象: ${imageName} -> ${imageUrl}`);
        }
      }

      if (finalLinks.downloadUrl) {
        downloadUrl = finalLinks.downloadUrl;
        console.log(`最终找到下载链接: ${downloadUrl}`);

        // 将下载链接添加到存储对象中
        imageLinks[imageName] = downloadUrl;
        console.log(`将下载链接添加到存储对象: ${imageName} -> ${downloadUrl}`);
      }
    }

    // 如果找到了图片URL但没有下载链接，使用图片URL
    if (!downloadUrl && imageUrl) {
      imageLinks[imageName] = imageUrl;
    }

    console.log(`图片生成完成: ${imageName}`);
    return downloadUrl || imageUrl;
  } catch (error) {
    console.error(`生成图片时出错: ${imageName}`, error);
    return null;
  }
}

// 按顺序处理所有图片
async function processAllImages(imageList, teacherId, pptOutline) {
  console.log('开始处理所有图片...');

  for (const item of imageList) {
    const imageName = item.插图名称;
    const prompt = item.提示词;

    console.log(`处理图片: ${imageName}`);
    await generateImage(imageName, prompt);

    // 添加延迟，避免API限制
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // 将图片链接保存到数据库
  const saved = await saveImageLinksToDatabase(teacherId, pptOutline);

  console.log('所有图片处理完成!');
  if (saved) {
    console.log('图片链接已保存到数据库');
  } else {
    console.log('图片链接未能保存到数据库');
  }
}

// 将图片链接保存到数据库
async function saveImageLinksToDatabase(teacherId, pptOutline) {
  try {
    // 检查是否有图片链接
    const linkCount = Object.keys(imageLinks).length;
    if (linkCount === 0) {
      console.log('警告: 没有找到任何图片链接，不保存到数据库');
      return false;
    }

    // 将图片链接转换为JSON字符串
    const jsonContent = JSON.stringify(imageLinks, null, 2);

    // 导入数据库模块 - 直接使用已导入的pool
    const pool = (await import('../config/db.js')).default;

    // 尝试查找匹配的PPT模板
    let matchedTemplate = null;

    try {
      // 准备大纲内容的JSON字符串表示，用于精确匹配
      const outlineContent = JSON.stringify(pptOutline);

      // 1. 先尝试精确匹配：根据大纲内容完全匹配
      if (pptOutline) {
        const [exactMatches] = await pool.execute(
          'SELECT * FROM ppt_templates WHERE content = ? AND teacher_id = ?',
          [outlineContent, teacherId]
        );

        if (exactMatches.length > 0) {
          matchedTemplate = exactMatches[0];
          console.log(`根据大纲内容精确匹配到模板: ID=${matchedTemplate.id}`);
        }
      }

      // 2. 如果没有精确匹配，尝试根据标题匹配
      if (!matchedTemplate && pptOutline && pptOutline.title) {
        const [titleMatches] = await pool.execute(
          'SELECT * FROM ppt_templates WHERE title = ? AND teacher_id = ? ORDER BY create_time DESC LIMIT 1',
          [pptOutline.title, teacherId]
        );

        if (titleMatches.length > 0) {
          matchedTemplate = titleMatches[0];
          console.log(`根据标题找到匹配的模板: ID=${matchedTemplate.id}`);
        }
      }

      // 3. 如果还是没有匹配，尝试模糊匹配：大纲内容部分匹配
      if (!matchedTemplate && pptOutline && pptOutline.title) {
        // 获取所有该教师的模板
        const [allTemplates] = await pool.execute(
          'SELECT * FROM ppt_templates WHERE teacher_id = ? ORDER BY create_time DESC',
          [teacherId]
        );

        // 对每个模板进行内容匹配度评分
        let bestMatch = null;
        let bestScore = 0;

        for (const template of allTemplates) {
          try {
            // 解析模板内容
            const templateOutline = JSON.parse(template.content);

            // 计算匹配分数
            let score = 0;

            // 标题匹配分数
            if (templateOutline.title === pptOutline.title) {
              score += 50;
            }

            // 章节匹配分数
            if (templateOutline.chapters && pptOutline.chapters) {
              // 计算章节标题匹配数量
              const templateChapterTitles = templateOutline.chapters.map(c => c.chapterTitle);
              const outlineChapterTitles = pptOutline.chapters.map(c => c.chapterTitle);

              // 计算交集大小
              const matchingChapters = templateChapterTitles.filter(title =>
                outlineChapterTitles.includes(title)
              );

              // 每个匹配的章节得10分
              score += matchingChapters.length * 10;

              // 如果匹配率超过50%，额外加分
              if (matchingChapters.length > outlineChapterTitles.length * 0.5) {
                score += 30;
              }
            }

            // 更新最佳匹配
            if (score > bestScore) {
              bestScore = score;
              bestMatch = template;
            }
          } catch (parseError) {
            console.error(`解析模板内容失败 (ID=${template.id}):`, parseError);
            // 继续检查下一个模板
          }
        }

        // 如果有足够好的匹配（分数超过50），使用它
        if (bestMatch && bestScore >= 50) {
          matchedTemplate = bestMatch;
          console.log(`根据内容相似度找到匹配的模板: ID=${matchedTemplate.id}, 分数=${bestScore}`);
        }
      }

      // 4. 如果还是没有匹配，尝试获取最新的模板
      if (!matchedTemplate) {
        const [templates] = await pool.execute(
          'SELECT * FROM ppt_templates WHERE teacher_id = ? ORDER BY create_time DESC LIMIT 1',
          [teacherId]
        );

        if (templates.length > 0) {
          matchedTemplate = templates[0];
          console.log(`未找到匹配的模板，使用最新的模板: ID=${matchedTemplate.id}`);
        }
      }

      // 如果找到了匹配的模板，更新它
      if (matchedTemplate) {
        // 检查模板是否已经有图片数据
        if (matchedTemplate.ppt_picture_generate) {
          try {
            // 尝试解析现有数据
            const existingData = JSON.parse(matchedTemplate.ppt_picture_generate);
            console.log('模板已有图片数据:', Object.keys(existingData).length, '张');

            // 检查是否有重复的图片名称
            for (const name in existingData) {
              if (imageLinks[name] && existingData[name] !== imageLinks[name]) {
                console.log(`发现重复的图片名称: ${name}, 使用新生成的图片`);
              }
            }
          } catch (parseError) {
            console.error('解析现有图片数据失败:', parseError);
          }
        }

        // 更新数据库，使用新的图片数据替换旧的
        await pool.execute(
          'UPDATE ppt_templates SET ppt_picture_generate = ?, update_time = NOW() WHERE id = ?',
          [jsonContent, matchedTemplate.id]
        );

        console.log(`成功将 ${linkCount} 个图片链接保存到数据库，模板ID=${matchedTemplate.id}`);

        // 输出每个图片的链接
        console.log('各个图片的链接:');
        for (const [name, url] of Object.entries(imageLinks)) {
          console.log(`- ${name}: ${url}`);
        }

        return true;
      } else {
        // 如果没有找到匹配的模板，创建一个新的模板
        console.log('未找到匹配的PPT模板，创建新模板');

        // 准备模板数据
        const title = pptOutline && pptOutline.title ? pptOutline.title : '新建模板';
        const content = JSON.stringify(pptOutline || {});

        // 插入新模板
        const [result] = await pool.execute(
          'INSERT INTO ppt_templates (teacher_id, title, content, ppt_picture_generate, create_time, update_time) VALUES (?, ?, ?, ?, NOW(), NOW())',
          [teacherId, title, content, jsonContent]
        );

        if (result && result.insertId) {
          console.log(`成功创建新模板并保存图片链接，模板ID=${result.insertId}`);
          return true;
        } else {
          console.log('创建新模板失败');
          return false;
        }
      }
    } catch (dbError) {
      console.error('数据库操作失败:', dbError);

      // 尝试创建一个新的连接池
      try {
        console.log('尝试创建新的数据库连接...');
        const mysql = await import('mysql2/promise');
        const dbConfig = {
          host: '**************',
          user: 'root',
          password: 'mydb123',
          database: 'teaching_assistant',
          waitForConnections: true,
          connectionLimit: 10,
          queueLimit: 0
        };

        // 创建新的连接池
        const newPool = mysql.createPool(dbConfig);

        // 尝试使用新连接池执行查询
        if (pptOutline && pptOutline.title) {
          const [templates] = await newPool.execute(
            'SELECT * FROM ppt_templates WHERE title = ? AND teacher_id = ? ORDER BY create_time DESC LIMIT 1',
            [pptOutline.title, teacherId]
          );

          if (templates.length > 0) {
            matchedTemplate = templates[0];
            console.log(`使用新连接池找到匹配的模板: ID=${matchedTemplate.id}`);
          }
        }

        // 如果没有找到匹配的模板，尝试获取最新的模板
        if (!matchedTemplate) {
          const [templates] = await newPool.execute(
            'SELECT * FROM ppt_templates WHERE teacher_id = ? ORDER BY create_time DESC LIMIT 1',
            [teacherId]
          );

          if (templates.length > 0) {
            matchedTemplate = templates[0];
            console.log(`使用新连接池找到最新的模板: ID=${matchedTemplate.id}`);
          }
        }

        // 如果找到了匹配的模板，更新它
        if (matchedTemplate) {
          await newPool.execute(
            'UPDATE ppt_templates SET ppt_picture_generate = ?, update_time = NOW() WHERE id = ?',
            [jsonContent, matchedTemplate.id]
          );

          console.log(`使用新连接池成功将图片链接保存到数据库，模板ID=${matchedTemplate.id}`);
          return true;
        } else {
          // 如果没有找到匹配的模板，创建一个新的模板
          console.log('使用新连接池创建新模板');

          // 准备模板数据
          const title = pptOutline && pptOutline.title ? pptOutline.title : '新建模板';
          const content = JSON.stringify(pptOutline || {});

          // 插入新模板
          const [result] = await newPool.execute(
            'INSERT INTO ppt_templates (teacher_id, title, content, ppt_picture_generate, create_time, update_time) VALUES (?, ?, ?, ?, NOW(), NOW())',
            [teacherId, title, content, jsonContent]
          );

          if (result && result.insertId) {
            console.log(`使用新连接池成功创建新模板并保存图片链接，模板ID=${result.insertId}`);
            return true;
          } else {
            console.log('使用新连接池创建新模板失败');
            return false;
          }
        }
      } catch (newPoolError) {
        console.error('创建新连接池失败:', newPoolError);
        return false;
      }
    }
  } catch (error) {
    console.error('保存图片链接到数据库时出错:', error);
    return false;
  }
}

// 从数据库加载AI生成的插图
export async function loadPPTPictures(req, res) {
  try {
    const { outline } = req.body;
    const teacherId = req.user.system_teacher_id;

    if (!outline) {
      return res.json({
        flag: false,
        desc: '缺少PPT大纲内容'
      });
    }

    if (!teacherId) {
      return res.json({
        flag: false,
        desc: '未登录或无法获取教师ID'
      });
    }

    console.log(`教师ID: ${teacherId} 请求加载PPT插图，输入大纲:`, JSON.stringify(outline, null, 2));

    // 导入数据库模块
    const pool = (await import('../config/db.js')).default;

    // 准备大纲内容的JSON字符串表示，用于精确匹配
    const outlineContent = JSON.stringify(outline);

    // 尝试查找匹配的PPT模板
    let matchedTemplate = null;

    try {
      // 1. 先尝试精确匹配：根据大纲内容完全匹配
      const [exactMatches] = await pool.execute(
        'SELECT * FROM ppt_templates WHERE content = ? AND teacher_id = ? ORDER BY create_time DESC LIMIT 1',
        [outlineContent, teacherId]
      );

      if (exactMatches.length > 0) {
        matchedTemplate = exactMatches[0];
        console.log(`根据大纲内容精确匹配到模板: ID=${matchedTemplate.id}`);
      }

      // 2. 如果没有精确匹配，尝试根据标题匹配
      if (!matchedTemplate && outline.title) {
        const [titleMatches] = await pool.execute(
          'SELECT * FROM ppt_templates WHERE title = ? AND teacher_id = ? ORDER BY create_time DESC LIMIT 1',
          [outline.title, teacherId]
        );

        if (titleMatches.length > 0) {
          matchedTemplate = titleMatches[0];
          console.log(`根据标题找到匹配的模板: ID=${matchedTemplate.id}`);
        }
      }

      // 3. 如果还是没有匹配，尝试模糊匹配：大纲内容部分匹配
      if (!matchedTemplate && outline.title) {
        // 获取所有该教师的模板
        const [allTemplates] = await pool.execute(
          'SELECT * FROM ppt_templates WHERE teacher_id = ? ORDER BY create_time DESC',
          [teacherId]
        );

        // 对每个模板进行内容匹配度评分
        let bestMatch = null;
        let bestScore = 0;

        for (const template of allTemplates) {
          try {
            // 解析模板内容
            const templateOutline = JSON.parse(template.content);

            // 计算匹配分数
            let score = 0;

            // 标题匹配分数
            if (templateOutline.title === outline.title) {
              score += 50;
            }

            // 章节匹配分数
            if (templateOutline.chapters && outline.chapters) {
              // 计算章节标题匹配数量
              const templateChapterTitles = templateOutline.chapters.map(c => c.chapterTitle);
              const outlineChapterTitles = outline.chapters.map(c => c.chapterTitle);

              // 计算交集大小
              const matchingChapters = templateChapterTitles.filter(title =>
                outlineChapterTitles.includes(title)
              );

              // 每个匹配的章节得10分
              score += matchingChapters.length * 10;

              // 如果匹配率超过50%，额外加分
              if (matchingChapters.length > outlineChapterTitles.length * 0.5) {
                score += 30;
              }
            }

            // 更新最佳匹配
            if (score > bestScore) {
              bestScore = score;
              bestMatch = template;
            }
          } catch (parseError) {
            console.error(`解析模板内容失败 (ID=${template.id}):`, parseError);
            // 继续检查下一个模板
          }
        }

        // 如果有足够好的匹配（分数超过50），使用它
        if (bestMatch && bestScore >= 50) {
          matchedTemplate = bestMatch;
          console.log(`根据内容相似度找到匹配的模板: ID=${matchedTemplate.id}, 分数=${bestScore}`);
        }
      }

      // 如果找到了匹配的模板，检查是否有AI生成的插图
      if (matchedTemplate && matchedTemplate.ppt_picture_generate) {
        try {
          // 解析插图数据
          const imageData = JSON.parse(matchedTemplate.ppt_picture_generate);

          // 返回插图数据
          return res.json({
            flag: true,
            desc: '成功加载AI生成的PPT插图',
            data: imageData,
            fromDatabase: true
          });
        } catch (parseError) {
          console.error('解析插图数据失败:', parseError);
          return res.json({
            flag: false,
            desc: '解析插图数据失败',
            needGenerate: true
          });
        }
      } else {
        // 没有找到匹配的模板或模板中没有插图数据
        return res.json({
          flag: false,
          desc: '未找到AI生成的PPT插图',
          needGenerate: true
        });
      }
    } catch (error) {
      console.error('从数据库加载AI生成的插图时出错:', error);
      return res.json({
        flag: false,
        desc: '从数据库加载AI生成的插图时出错: ' + error.message,
        needGenerate: true
      });
    }
  } catch (error) {
    console.error('加载PPT插图时出错:', error);
    return res.json({
      flag: false,
      desc: '加载PPT插图时出错: ' + error.message,
      needGenerate: true
    });
  }
}

// 导出生成PPT插图的主函数
export async function generatePPTPictures(req, res) {
  try {
    const { outline } = req.body;
    const teacherId = req.user.system_teacher_id; // 从请求中获取教师ID

    if (!outline) {
      return res.json({
        flag: false,
        desc: '缺少PPT大纲内容'
      });
    }

    if (!teacherId) {
      return res.json({
        flag: false,
        desc: '未登录或无法获取教师ID'
      });
    }

    console.log(`教师ID: ${teacherId} 收到生成PPT插图请求，输入大纲:`, JSON.stringify(outline, null, 2));

    // 清空之前的图片链接
    Object.keys(imageLinks).forEach(key => delete imageLinks[key]);

    // 1. 获取插图列表
    const jsonResponse = await generateImageList(outline);
    console.log("从 deepseek 获取的原始数据:");
    console.log(jsonResponse);

    try {
      // 2. 清理 JSON 数据
      const cleanedJson = cleanJsonResponse(jsonResponse);
      console.log("清理后的 JSON 数据:");
      console.log(cleanedJson);

      // 3. 解析 JSON 数据
      const imageData = JSON.parse(cleanedJson);

      if (imageData && imageData.插图列表 && Array.isArray(imageData.插图列表)) {
        console.log(`成功解析 JSON 数据，找到 ${imageData.插图列表.length} 个插图`);

        // 4. 处理每个插图，传递教师ID和大纲
        await processAllImages(imageData.插图列表, teacherId, outline);

        // 5. 返回生成的图片链接
        return res.json({
          flag: true,
          desc: '成功生成PPT插图',
          data: imageLinks
        });
      } else {
        console.error("JSON 数据格式不正确，无法找到插图列表");
        return res.json({
          flag: false,
          desc: 'JSON 数据格式不正确，无法找到插图列表'
        });
      }
    } catch (error) {
      console.error("解析 JSON 数据时出错:", error);
      return res.json({
        flag: false,
        desc: '解析 JSON 数据时出错: ' + error.message
      });
    }
  } catch (error) {
    console.error('生成PPT插图时出错:', error);
    return res.json({
      flag: false,
      desc: '生成PPT插图时出错: ' + error.message
    });
  }
}

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import * as videoNoteModel from '../models/videoNoteModel.js';
import axios from 'axios';
import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';
import { getXfyunConfig, createOpenAIConfig, API_USAGES } from '../config/modelService.js';

// 获取 __dirname 的替代方案
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 定义常量
const UPLOAD_DIR = path.join(__dirname, '../../public/note_videos');
const AUDIO_DIR = path.join(__dirname, '../uploads/audios');
const TRANSCRIPT_DIR = path.join(__dirname, '../uploads/transcripts');
const SUMMARY_DIR = path.join(__dirname, '../uploads/summaries');

// 确保目录存在
[UPLOAD_DIR, AUDIO_DIR, TRANSCRIPT_DIR, SUMMARY_DIR].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Python脚本路径
const PY_SCRIPT_PATH = path.join(__dirname, '../scripts/video_processor.py');

// 环境变量设置 - 从数据库获取配置
async function loadXfyunConfig() {
  try {
    const config = await getXfyunConfig();
    
    // 设置环境变量，供Python脚本使用
    process.env.XFYUN_APPID = config.appId;
    process.env.XFYUN_SECRET_KEY = config.secretKey;
    
    return config;
  } catch (error) {
    console.error('加载讯飞配置失败:', error);
    
    // 检查环境变量
    if (!process.env.XFYUN_APPID || !process.env.XFYUN_SECRET_KEY) {
      console.warn('讯飞语音识别API配置缺失，请检查环境变量或数据库配置');
    }
    
    return {
      appId: process.env.XFYUN_APPID,
      secretKey: process.env.XFYUN_SECRET_KEY
    };
  }
}

// 确保启动时加载配置
loadXfyunConfig().catch(error => {
  console.error('初始化讯飞配置失败:', error);
});

// 检查环境设置
async function checkEnvironment() {
  console.log('检查Python环境...');
  
  try {
    // 检查Python版本
    const pythonVersionProcess = spawn('python', ['--version']);
    
    pythonVersionProcess.stdout.on('data', (data) => {
      console.log(`Python版本: ${data.toString().trim()}`);
    });
    
    pythonVersionProcess.stderr.on('data', (data) => {
      console.error(`检查Python版本时出错: ${data.toString().trim()}`);
    });
    
    // 检查脚本文件
    if (fs.existsSync(PY_SCRIPT_PATH)) {
      console.log(`Python脚本存在: ${PY_SCRIPT_PATH}`);
    } else {
      console.error(`Python脚本不存在: ${PY_SCRIPT_PATH}`);
    }
    
    // 检查目录权限
    [UPLOAD_DIR, AUDIO_DIR, TRANSCRIPT_DIR, SUMMARY_DIR].forEach(dir => {
      try {
        const testFile = path.join(dir, `test_${Date.now()}.txt`);
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        console.log(`目录 ${dir} 权限正常`);
      } catch (error) {
        console.error(`目录 ${dir} 权限检查失败: ${error.message}`);
      }
    });
    
    // 检查you-get是否存在
    try {
      const yougetProcess = spawn('you-get', ['--version']);
      
      yougetProcess.on('error', (error) => {
        console.error(`you-get 未安装或无法执行: ${error.message}`);
      });
      
      yougetProcess.stdout.on('data', (data) => {
        console.log(`you-get 版本: ${data.toString().trim()}`);
      });
    } catch (error) {
      console.error(`检查 you-get 时出错: ${error.message}`);
    }
    
    // 检查ffmpeg是否存在
    try {
      const ffmpegProcess = spawn('ffmpeg', ['-version']);
      
      ffmpegProcess.on('error', (error) => {
        console.error(`ffmpeg 未安装或无法执行: ${error.message}`);
      });
      
      ffmpegProcess.stdout.on('data', (data) => {
        console.log(`ffmpeg 已安装`);
      });
    } catch (error) {
      console.error(`检查 ffmpeg 时出错: ${error.message}`);
    }
  } catch (error) {
    console.error(`环境检查失败: ${error.message}`);
  }
}

// 在应用启动时检查环境
checkEnvironment().catch(error => {
  console.error(`环境检查出错: ${error.message}`);
});

// 从URL下载视频
async function downloadVideo(videoUrl, videoId) {
  return new Promise((resolve, reject) => {
    // 创建以视频ID为基础的文件夹
    const videoDir = path.join(UPLOAD_DIR, videoId);
    
    // 确保目录存在
    if (!fs.existsSync(videoDir)) {
      fs.mkdirSync(videoDir, { recursive: true });
    }
    
    const outputPath = path.join(videoDir, `${videoId}.mp4`);
    
    console.log(`正在下载视频: ${videoUrl}`);
    console.log(`Python脚本路径: ${PY_SCRIPT_PATH}`);
    console.log(`输出路径: ${outputPath}`);
    
    // 检查脚本是否存在
    if (!fs.existsSync(PY_SCRIPT_PATH)) {
      console.error(`Python脚本不存在: ${PY_SCRIPT_PATH}`);
      reject(new Error(`Python脚本不存在: ${PY_SCRIPT_PATH}`));
      return;
    }
    
    // 检查是否已经下载过
    if (fs.existsSync(outputPath)) {
      console.log(`视频已存在，跳过下载: ${outputPath}`);
      resolve(outputPath);
      return;
    }
    
    try {
      // 使用Python脚本下载视频
      const pythonProcess = spawn('python', [
        PY_SCRIPT_PATH,
        'download',
        videoUrl,
        outputPath
      ], {
        env: {
          ...process.env,
          PYTHONIOENCODING: 'utf-8',
        }
      });
      
      let errorData = '';
      let outputData = '';
      
      pythonProcess.stdout.on('data', (data) => {
        outputData += data.toString();
        console.log(`Python输出: ${data.toString()}`);
      });
      
      pythonProcess.stderr.on('data', (data) => {
        errorData += data.toString();
        console.error(`Python错误: ${data.toString()}`);
      });
      
      pythonProcess.on('close', (code) => {
        console.log(`Python进程退出，状态码 ${code}, 输出: ${outputData}`);
        if (code !== 0) {
          console.error(`Python进程退出，状态码 ${code}, 错误: ${errorData}`);
          reject(new Error(`视频下载失败，状态码: ${code}, 错误: ${errorData}`));
          return;
        }
        
        if (!fs.existsSync(outputPath)) {
          // 检查目录中是否有匹配的文件
          const files = fs.readdirSync(videoDir);
          const matchingFiles = files.filter(file => file.endsWith('.mp4') || file.endsWith('.flv'));
          
          if (matchingFiles.length > 0) {
            const foundFile = path.join(videoDir, matchingFiles[0]);
            console.log(`找到替代文件: ${foundFile}`);
            // 重命名为标准格式
            fs.renameSync(foundFile, outputPath);
            resolve(outputPath);
          } else {
            console.error(`下载完成但文件不存在: ${outputPath}, 目录内容: ${files.join(', ')}`);
            reject(new Error(`下载完成但文件不存在: ${outputPath}`));
          }
          return;
        }
        
        resolve(outputPath);
      });
      
      pythonProcess.on('error', (err) => {
        console.error(`启动Python进程失败: ${err.message}`);
        reject(new Error(`启动Python进程失败: ${err.message}`));
      });
    } catch (err) {
      console.error(`尝试执行Python脚本时发生异常: ${err.message}`);
      reject(new Error(`尝试执行Python脚本时发生异常: ${err.message}`));
    }
  });
}

// 从视频提取音频
async function extractAudio(videoPath, videoId) {
  return new Promise((resolve, reject) => {
    // 获取视频所在目录
    const videoDir = path.dirname(videoPath);
    const audioPath = path.join(videoDir, `${videoId}.mp3`);
    
    console.log(`正在从视频提取音频: ${videoPath}`);
    console.log(`音频输出路径: ${audioPath}`);
    
    // 检查是否已经提取过
    if (fs.existsSync(audioPath)) {
      console.log(`音频已存在，跳过提取: ${audioPath}`);
      resolve(audioPath);
      return;
    }
    
    // 使用Python脚本提取音频
    const pythonProcess = spawn('python', [
      PY_SCRIPT_PATH,
      'extract_audio',
      videoPath,
      audioPath
    ], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
      }
    });
    
    let errorData = '';
    let outputData = '';
    
    pythonProcess.stdout.on('data', (data) => {
      outputData += data.toString();
      console.log(`Python输出: ${data.toString()}`);
    });
    
    pythonProcess.stderr.on('data', (data) => {
      errorData += data.toString();
      console.error(`Python错误: ${data.toString()}`);
    });
    
    pythonProcess.on('close', (code) => {
      console.log(`Python进程退出，状态码 ${code}, 输出: ${outputData}`);
      if (code !== 0) {
        console.error(`Python进程退出，状态码 ${code}, 错误: ${errorData}`);
        reject(new Error(`音频提取失败: ${errorData}`));
        return;
      }
      
      if (!fs.existsSync(audioPath)) {
        reject(new Error('提取完成但文件不存在'));
        return;
      }
      
      resolve(audioPath);
    });
    
    pythonProcess.on('error', (err) => {
      console.error(`启动Python进程失败: ${err.message}`);
      reject(new Error(`启动Python进程失败: ${err.message}`));
    });
  });
}

// 转录音频为文本
async function transcribeAudio(audioPath, videoId) {
  return new Promise((resolve, reject) => {
    // 获取音频所在目录
    const videoDir = path.dirname(audioPath);
    const transcriptPath = path.join(videoDir, `${videoId}_transcript.txt`);
    
    console.log(`正在转录音频: ${audioPath}`);
    console.log(`转录输出路径: ${transcriptPath}`);
    
    // 检查是否已经转录过
    if (fs.existsSync(transcriptPath)) {
      console.log(`转录文件已存在，跳过转录: ${transcriptPath}`);
      // 读取转录结果
      fs.readFile(transcriptPath, 'utf8', (err, data) => {
        if (err) {
          reject(new Error(`读取转录结果失败: ${err.message}`));
          return;
        }
        
        resolve({
          transcript: data,
          duration: 300 // 使用默认时长
        });
      });
      return;
    }
    
    // 使用Python脚本进行转录
    const pythonProcess = spawn('python', [
      PY_SCRIPT_PATH,
      'transcribe',
      audioPath,
      transcriptPath
    ], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
      },
      timeout: 600000 // 设置10分钟超时
    });
    
    let errorData = '';
    let outputData = '';
    
    pythonProcess.stdout.on('data', (data) => {
      outputData += data.toString();
      console.log(`Python输出: ${data.toString()}`);
    });
    
    pythonProcess.stderr.on('data', (data) => {
      errorData += data.toString();
      console.error(`Python错误: ${data.toString()}`);
    });
    
    pythonProcess.on('close', (code) => {
      console.log(`Python进程退出，状态码 ${code}, 输出: ${outputData}`);
      if (code !== 0) {
        console.error(`Python进程退出，状态码 ${code}, 错误: ${errorData}`);
        
        // 如果是讯飞API余额不足错误，则使用模拟转录
        if (errorData.includes("讯飞API余额不足")) {
          console.log("检测到讯飞API余额不足，使用模拟转录");
          mockTranscription(videoId, "").then(mockResult => {
            resolve({
              transcript: mockResult.transcript,
              duration: mockResult.duration
            });
          }).catch(mockError => {
            reject(mockError);
          });
          return;
        }
        
        reject(new Error(`音频转录失败: ${errorData}`));
        return;
      }
      
      if (!fs.existsSync(transcriptPath)) {
        reject(new Error('转录完成但文件不存在'));
        return;
      }
      
      // 读取转录结果
      fs.readFile(transcriptPath, 'utf8', (err, data) => {
        if (err) {
          reject(new Error(`读取转录结果失败: ${err.message}`));
          return;
        }
        
        // 获取视频时长
        const durationProcess = spawn('python', [
          PY_SCRIPT_PATH,
          'get_duration',
          audioPath
        ], {
          env: {
            ...process.env,
            PYTHONIOENCODING: 'utf-8',
          }
        });
        
        let durationOutput = '';
        
        durationProcess.stdout.on('data', (chunk) => {
          durationOutput += chunk.toString();
        });
        
        durationProcess.on('close', (durationCode) => {
          let duration = 0;
          
          if (durationCode === 0 && durationOutput.trim()) {
            try {
              duration = parseInt(durationOutput.trim());
            } catch (e) {
              console.error('无法解析视频时长:', e);
              // 使用默认估算方法
              duration = data.length / 5; // 粗略估计：每5个字符1秒
            }
          } else {
            // 使用默认估算方法
            duration = data.length / 5;
          }
          
          resolve({
            transcript: data,
            duration: duration
          });
        });
        
        durationProcess.on('error', (err) => {
          console.error(`启动Python进程获取时长失败: ${err.message}`);
          // 仍然返回转录结果，但使用默认时长
          resolve({
            transcript: data,
            duration: 300 // 默认5分钟
          });
        });
      });
    });
    
    pythonProcess.on('error', (err) => {
      console.error(`启动Python进程失败: ${err.message}`);
      reject(new Error(`启动Python进程失败: ${err.message}`));
    });
  });
}

// 生成摘要
async function generateSummary(transcript, videoId, title) {
  return new Promise((resolve, reject) => {
    const summaryPath = path.join(SUMMARY_DIR, `${videoId}_summary.txt`);
    
    console.log(`正在生成摘要，视频标题: ${title}`);
    
    // 使用Python脚本生成摘要
    const pythonProcess = spawn('python', [
      PY_SCRIPT_PATH,
      'summarize',
      transcript,
      summaryPath,
      title || ''
    ]);
    
    let errorData = '';
    
    pythonProcess.stdout.on('data', (data) => {
      console.log(`Python输出: ${data}`);
    });
    
    pythonProcess.stderr.on('data', (data) => {
      errorData += data.toString();
      console.error(`Python错误: ${data}`);
    });
    
    pythonProcess.on('close', (code) => {
      if (code !== 0) {
        console.error(`Python进程退出，状态码 ${code}`);
        reject(new Error(`摘要生成失败: ${errorData}`));
        return;
      }
      
      if (!fs.existsSync(summaryPath)) {
        reject(new Error('摘要生成完成但文件不存在'));
        return;
      }
      
      // 读取摘要结果
      fs.readFile(summaryPath, 'utf8', (err, data) => {
        if (err) {
          reject(new Error(`读取摘要结果失败: ${err.message}`));
          return;
        }
        
        resolve(data);
      });
    });
  });
}

// 模拟转录功能，当真实下载失败时使用
async function mockTranscription(videoId, videoUrl) {
  console.log(`使用模拟转录功能，视频ID: ${videoId}, URL: ${videoUrl}`);
  
  // 提取视频标题或有关信息
  let videoTitle = "";
  try {
    if (videoUrl.includes('bilibili.com')) {
      videoTitle = await extractBilibiliTitle(videoUrl);
    }
  } catch (error) {
    console.error("提取视频标题失败:", error);
  }
  
  if (!videoTitle) {
    // 从videoId中提取一些信息
    videoTitle = `视频${videoId.slice(0, 5)}`;
  }
  
  console.log(`视频标题: ${videoTitle}`);
  
  // 根据视频ID和可能的标题选择更合适的模拟文本
  let mockTranscript = '';
  
  // 基于视频ID和标题，选择适当的模拟文本
  if (videoTitle.includes("人工智能") || videoTitle.includes("AI") || videoId.includes("AI")) {
    mockTranscript = `这节课我们讨论人工智能的发展历程和基础原理。人工智能作为计算机科学的一个重要分支，经历了从符号主义到连接主义，再到如今的深度学习的发展过程。

在课程的第一部分，我们回顾了AI的发展历史。从1956年达特茅斯会议提出"人工智能"概念开始，经历了几次起伏的发展周期。20世纪50至60年代是AI的黄金时期，科学家们对AI充满乐观；70年代进入第一次AI寒冬；80年代专家系统的出现又带来了AI的复苏；90年代再次进入低谷；直到21世纪，随着大数据、计算能力和算法的发展，AI迎来了爆发式增长。

第二部分，我们介绍了人工智能的基本方法论。包括符号主义（基于规则和逻辑推理），连接主义（神经网络），以及统计学习方法。特别是深度学习的出现，彻底改变了AI的发展轨迹。我们讨论了深度学习的核心思想：通过多层神经网络自动学习数据特征，实现端到端的学习。

第三部分探讨了AI的主要应用领域。包括计算机视觉（图像分类、目标检测、图像生成）、自然语言处理（机器翻译、文本生成、情感分析）、推荐系统、自动驾驶等。我们分析了每个领域的技术挑战和最新进展。

最后，我们讨论了AI的未来发展趋势。包括从感知智能到认知智能的发展，通用人工智能的可能性，以及AI技术带来的社会伦理问题。我们特别强调了AI技术应当以人为本，解决实际问题，并需要关注数据隐私、算法偏见、就业冲击等社会问题。

通过本次课程，希望同学们能够了解人工智能的基本概念、发展历程、关键技术和应用领域，为后续更深入的学习打下基础。人工智能正日益融入我们的生活和工作，了解并掌握这一技术将对未来的学习和职业发展带来重要影响。`;
  }
  else if (videoTitle.includes("编程") || videoTitle.includes("开发") || videoTitle.includes("程序") || videoId.includes("code")) {
    mockTranscript = `今天我们将深入探讨软件开发方法论和最佳实践。作为一名优秀的软件工程师，不仅需要掌握编程语言和工具，更需要了解如何设计、构建和维护高质量的软件系统。

我们首先讨论了软件开发的基本原则。包括SOLID原则：单一职责原则、开放封闭原则、里氏替换原则、接口隔离原则和依赖倒置原则。这些原则帮助我们创建更加灵活、可维护的代码结构。同时我们也强调了DRY（不要重复自己）和KISS（保持简单）等实用原则，这些都是构建优质软件的基石。

接下来，我们探讨了不同的软件开发方法论。从传统的瀑布模型，到更加灵活的敏捷开发和精益开发。我们详细介绍了敏捷方法中的Scrum和看板方法，包括如何进行sprint规划、每日站会、回顾会议等实践活动。我特别强调了用户故事的重要性，以及如何通过持续集成和持续部署来提高开发效率。

第三部分，我们讨论了代码质量保障措施。包括代码审查、单元测试、集成测试和端到端测试的编写方法。我们演示了如何使用测试驱动开发（TDD）来提高代码质量，以及如何设置自动化测试流程。还介绍了代码覆盖率、静态代码分析等工具如何帮助我们发现潜在问题。

在版本控制方面，我们深入讲解了Git的工作流程，包括特性分支开发、分支管理策略、合并与变基操作，以及如何编写有意义的提交信息。我们还讨论了如何处理合并冲突，以及团队协作的最佳实践。

最后，我们讨论了软件架构设计的重要性。从MVC、MVP到MVVM等常见的架构模式，从单体应用到微服务架构的演变，以及如何根据项目需求选择合适的架构。我们特别强调了，好的架构应该考虑到可扩展性、可维护性、性能和安全等多方面因素。

通过本节课，希望大家能够意识到，优秀的软件不仅仅是能够运行的代码，而是经过精心设计、严格测试、易于维护且能持续演进的系统。掌握这些软件工程方法论和最佳实践，将帮助你成为一名真正的专业软件工程师。`;
  }
  else if (videoTitle.includes("数据") || videoTitle.includes("分析") || videoId.includes("data")) {
    mockTranscript = `本节课我们讨论数据分析的基础方法和实践应用。在当今数据驱动的世界中，数据分析已成为各行各业必不可少的技能。

首先，我们介绍了数据分析的基本流程。这个流程包括：明确问题、收集数据、清洗数据、探索性分析、建模与验证、以及结果解释与展示。每一步都至关重要，缺一不可。特别是明确问题这一步，它决定了整个分析的方向和价值。

在数据收集部分，我们讨论了不同的数据来源：结构化数据（如数据库、Excel表格）、半结构化数据（如JSON、XML）和非结构化数据（如文本、图像）。我们强调了数据质量对分析结果的影响，以及如何评估数据的可靠性和代表性。

数据清洗是数据分析中最耗时但最关键的环节之一。我们详细讲解了如何处理缺失值、异常值和重复值，以及数据标准化和转换的方法。我们通过实例展示了常见的数据质量问题及其解决方案。

在探索性数据分析部分，我们介绍了统计描述、数据可视化和假设检验等工具。通过直方图、散点图、箱线图等可视化方法，我们可以直观地了解数据分布和变量关系。我们特别强调了如何从数据中发现模式和洞见，这是数据分析的核心价值所在。

接下来，我们讨论了常用的数据分析方法，包括回归分析、分类分析、聚类分析和时间序列分析等。我们不仅介绍了这些方法的基本原理，还通过实例展示了如何在实际问题中选择合适的分析方法，以及如何解释分析结果。

在工具选择方面，我们比较了Excel、Python、R和SQL等常用数据分析工具的优缺点和适用场景。我们演示了如何使用Python的pandas、numpy和matplotlib等库进行数据处理和可视化。

最后，我们讨论了数据分析在商业决策中的应用。通过案例分析，我们展示了如何将数据分析结果转化为可操作的商业洞见，以及如何有效沟通分析结果。我们强调了数据分析不仅仅是技术问题，更是一个商业问题，其目的是支持决策和创造价值。

通过本节课，希望同学们能够掌握数据分析的基本方法和流程，了解如何在实际工作中应用这些知识，从数据中挖掘有价值的信息，支持更明智的决策制定。`;
  }
  else {
    // 默认模拟文本，适用于一般教育内容
    mockTranscript = `在今天的课程中，我们探讨了学习方法和教育理念的创新发展。现代教育正经历着前所未有的变革，传统的填鸭式教学逐渐被更加注重思维培养和能力发展的方法所替代。

首先，我们讨论了有效学习的科学基础。研究表明，主动学习远比被动接受知识更有效。这包括提问法、教学相长法、实践学习法等。特别是间隔重复和检索练习这两种方法，已被认为是提高记忆效率的最佳实践。我们还介绍了认知负荷理论，它解释了为什么适当的挑战能促进学习，而过度的信息则会阻碍学习。

其次，我们探讨了批判性思维的培养。在信息爆炸的时代，辨别信息的真伪、理解复杂问题的能力变得尤为重要。我们介绍了CATLICE模型（澄清问题、分析论点、透视假设、逻辑推理、整合信息、创造性思考、评估结果）作为培养批判性思维的框架。通过案例分析，我们演示了如何将这一框架应用到实际问题中。

第三部分，我们讨论了个性化学习的重要性。每个学习者都有独特的学习风格、背景知识和学习节奏。现代教育技术使个性化学习成为可能，自适应学习系统可以根据学习者的表现实时调整内容难度和教学策略。我们也强调了学习动机和情感因素对学习效果的影响，以及如何通过设计有意义的学习体验来提高学习参与度。

接下来，我们探讨了协作学习的价值。通过小组讨论、项目合作和同伴评估，学习者不仅能获取知识，还能发展沟通、协作和领导等社会技能。我们介绍了如何设计有效的协作学习活动，以及如何评估协作过程和结果。

最后，我们讨论了终身学习的理念。在快速变化的世界中，学习不再局限于正规教育阶段，而是贯穿整个职业生涯。我们探讨了如何培养学习的元认知技能，即"学会学习"的能力。这包括设定学习目标、监控学习进度、反思学习过程等。我们还讨论了如何利用在线学习资源和社区支持持续学习。

通过本课程，希望大家能够认识到学习不仅是知识的积累，更是思维方式的转变和能力的提升。掌握科学的学习方法，将帮助我们在这个知识经济时代保持竞争力，实现个人和职业的持续成长。`;
  }
  
  // 根据videoId创建视频目录，确保存储转录结果
  const videoDir = path.join(UPLOAD_DIR, videoId);
  if (!fs.existsSync(videoDir)) {
    fs.mkdirSync(videoDir, { recursive: true });
  }
  
  // 保存到文件以便后续处理
  const transcriptPath = path.join(videoDir, `${videoId}_transcript.txt`);
  
  try {
    fs.writeFileSync(transcriptPath, mockTranscript, 'utf8');
    console.log(`模拟转录文本已保存到: ${transcriptPath}`);
    
    return {
      transcript: mockTranscript,
      duration: 600 // 模拟10分钟视频
    };
  } catch (error) {
    console.error(`保存模拟转录文本失败: ${error.message}`);
    throw error;
  }
}

// 提取B站视频标题
async function extractBilibiliTitle(url) {
  return new Promise((resolve, reject) => {
    try {
      // 简单方法：使用Python脚本获取标题
      const pythonProcess = spawn('python', [
        PY_SCRIPT_PATH,
        'get_title',
        url
      ], {
        env: {
          ...process.env,
          PYTHONIOENCODING: 'utf-8',
        }
      });
      
      let outputData = '';
      
      pythonProcess.stdout.on('data', (data) => {
        outputData += data.toString();
      });
      
      pythonProcess.on('close', (code) => {
        if (code === 0 && outputData.trim()) {
          resolve(outputData.trim());
        } else {
          console.log(`无法获取视频标题，使用默认值`);
          resolve("");
        }
      });
      
      pythonProcess.on('error', () => {
        resolve("");
      });
    } catch (error) {
      console.error(`获取视频标题失败: ${error.message}`);
      resolve("");
    }
  });
}

// 模拟摘要生成功能
async function mockSummary(transcript, videoId, title) {
  console.log(`使用模拟摘要功能，视频ID: ${videoId}`);
  
  // 生成一个基于转录文本的摘要
  const summaryIntro = `《${title || '视频内容'}》学习笔记\n\n`;
  
  // 提取关键句子
  const sentences = transcript.split(/[。！？.!?]\s*/).filter(s => s.length > 0);
  
  // 生成标题
  const sections = [
    "核心知识点",
    "要点总结",
    "重要概念",
    "学习收获",
    "应用思考"
  ];
  
  let mockSummary = summaryIntro;
  
  // 为每个标题添加2-3个要点
  const sentencesPerSection = Math.min(Math.max(1, Math.floor(sentences.length / sections.length)), 3);
  
  for (let i = 0; i < sections.length; i++) {
    mockSummary += sections[i] + "\n\n";
    
    const startIdx = i * sentencesPerSection;
    if (startIdx < sentences.length) {
      // 添加摘要点
      for (let j = 0; j < sentencesPerSection && startIdx + j < sentences.length; j++) {
        mockSummary += `${j+1}. ${sentences[startIdx + j]}。\n`;
      }
      mockSummary += "\n";
    } else {
      mockSummary += "此部分内容暂无相关信息。\n\n";
    }
  }
  
  // 添加总结
  mockSummary += "总结\n\n";
  mockSummary += "本次学习了" + (title || "相关内容") + "，掌握了多个关键概念和应用方法。通过深入理解这些知识点，可以更好地应用到实际工作和学习中。\n\n";
  
  // 保存到文件以便后续处理
  const summaryPath = path.join(SUMMARY_DIR, `${videoId}_summary.txt`);
  
  try {
    fs.writeFileSync(summaryPath, mockSummary);
    return mockSummary;
  } catch (error) {
    console.error(`保存模拟摘要失败: ${error.message}`);
    throw error;
  }
}

/**
 * 使用DeepSeek API直接生成摘要
 * @param {string} transcript 转录文本
 * @param {string} title 视频标题
 * @returns {Promise<string>} 生成的摘要
 */
async function generateSummaryWithDeepseek(transcript, title) {
  try {
    console.log(`使用DeepSeek API生成摘要，视频标题: ${title}`);
    
    // 从数据库/环境变量获取API配置
    const openaiConfig = await createOpenAIConfig(API_USAGES.EXERCISE);
    const apiKey = openaiConfig.apiKey;
    
    // 检查API密钥是否配置
    if (!apiKey) {
      throw new Error('缺少DeepSeek API密钥配置，请检查环境变量或数据库');
    }
    
    // 构建请求数据
    const payload = {
      model: "deepseek-chat",
      messages: [
        {
          role: "system", 
          content: "你是一个擅长笔记整理和知识提炼的助手。请按照以下格式生成结构化笔记：\n1. 不要使用Markdown格式的#号标题，改用中文标题如'核心知识点'、'要点总结'等\n2. 使用清晰的段落和分点，用数字序号或中文顿号标记要点\n3. 避免使用过多特殊符号\n4. 生成的内容应该是纯文本格式，易于阅读"
        },
        {
          role: "user", 
          content: `请帮我总结以下《${title}》的内容要点，制作成便于阅读的笔记形式。不要使用markdown格式，使用中文标题和序号：\n\n${transcript}`
        }
      ],
      stream: false
    };
    
    // 设置请求选项
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      timeout: 60000 // 60秒超时
    };
    
    console.log('发送DeepSeek API请求...');
    const response = await axios.post('https://api.deepseek.com/chat/completions', payload, config);
    
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      let summary = response.data.choices[0].message.content;
      
      // 格式化处理
      summary = formatSummary(summary, title);
      
      console.log(`摘要生成成功，长度: ${summary.length}字符`);
      return summary;
    } else {
      throw new Error('DeepSeek API返回格式异常，未找到生成内容');
    }
  } catch (error) {
    console.error('调用DeepSeek API生成摘要失败:', error);
    
    // 记录详细错误信息
    if (error.response) {
      // 服务器响应但状态码不是2xx
      console.error('API错误状态码:', error.response.status);
      console.error('API错误响应:', error.response.data);
    } else if (error.request) {
      // 请求发送但没有收到响应
      console.error('未收到API响应:', error.request);
    } else {
      // 设置请求时发生的错误
      console.error('API请求错误:', error.message);
    }
    
    throw new Error(`调用DeepSeek API生成摘要失败: ${error.message}`);
  }
}

/**
 * 格式化摘要内容，去除多余Markdown标记
 * @param {string} summary 原始摘要内容
 * @param {string} title 视频标题
 * @returns {string} 格式化后的摘要
 */
function formatSummary(summary, title) {
  // 添加课程标题
  let formattedSummary = `《${title}》学习笔记\n\n`;
  
  // 去除多余的Markdown标记
  let cleanSummary = summary
    // 替换Markdown标题，如"# 标题"、"## 标题"
    .replace(/^#+ (.+)$/gm, '$1')
    // 替换星号和下划线的强调，如"**文本**"、"__文本__"
    .replace(/(\*\*|__)(.*?)\1/g, '$2')
    // 替换星号和下划线的斜体，如"*文本*"、"_文本_"
    .replace(/(\*|_)(.*?)\1/g, '$2');
  
  // 添加清理后的内容
  formattedSummary += cleanSummary;
  
  return formattedSummary;
}

// 转录视频
export const transcribeVideo = async (req, res) => {
  try {
    const { videoUrl, videoId } = req.body;
    const userId = req.user.system_teacher_id; // 从auth中间件获取用户ID
    let videoTitle = req.body.title || '';
    
    if (!videoUrl || !videoId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    // 如果没有提供标题且URL是B站链接，尝试获取标题
    if ((!videoTitle || videoTitle === '未命名视频') && videoUrl.includes('bilibili')) {
      try {
        console.log('尝试从B站获取视频标题...');
        const bilibiliTitle = await extractBilibiliTitle(videoUrl);
        if (bilibiliTitle) {
          console.log(`成功获取B站视频标题: ${bilibiliTitle}`);
          videoTitle = bilibiliTitle;
        }
      } catch (titleError) {
        console.error('获取B站视频标题失败:', titleError);
      }
    }
    
    // 创建处理任务
    const task = await videoNoteModel.createProcessTask({
      userId,
      videoId,
      videoUrl,
      videoTitle
    });
    
    let transcript, duration;
    
    try {
      console.log('开始处理视频转录，视频ID:', videoId);
      
      // 1. 下载视频
      const videoPath = await downloadVideo(videoUrl, videoId);
      console.log(`视频下载成功: ${videoPath}`);
      
      // 2. 提取音频
      const audioPath = await extractAudio(videoPath, videoId);
      console.log(`音频提取成功: ${audioPath}`);
      
      // 3. 使用讯飞API转录音频
      console.log('开始转录音频，这可能需要10-15分钟...');
      
      // 使用更长的超时时间
      const transcriptionPromise = transcribeAudio(audioPath, videoId);
      
      // 设置超时保护
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('讯飞API转录超时，请稍后再试'));
        }, 900000); // 15分钟超时
      });
      
      // 使用Promise.race让两个Promise竞争
      const result = await Promise.race([
        transcriptionPromise,
        timeoutPromise
      ]);
      
      transcript = result.transcript;
      duration = result.duration;
      console.log(`转录完成，获取到文本内容 ${transcript.length} 字符`);
      
    } catch (processingError) {
      console.error('视频处理失败:', processingError);
      
      // 更详细的错误日志
      console.error('错误详情:', processingError.stack);
      console.error('错误消息:', processingError.message);
      
      // 检查是否特定错误
      const errorMsg = processingError.message || '';
      const isBalanceError = errorMsg.includes('余额不足') || errorMsg.includes('26625');
      const isTimeoutError = errorMsg.includes('超时') || errorMsg.includes('timeout');
      
      // 更新任务状态为警告
      if (task && task.id) {
        await videoNoteModel.updateTaskStatus(task.id, 'processing', 
          '视频处理失败: ' + processingError.message);
      }
      
      // 特定错误消息处理
      let mockMessage = '常规错误，使用模拟转录作为备选';
      if (isBalanceError) {
        mockMessage = '讯飞API余额不足，使用模拟转录';
      } else if (isTimeoutError) {
        mockMessage = '转录超时，使用模拟转录';
      }
      
      console.log(mockMessage);
      
      // 使用模拟数据作为备选
      try {
        console.log('使用模拟转录作为备选');
        const mockResult = await mockTranscription(videoId, videoUrl);
        transcript = mockResult.transcript;
        duration = mockResult.duration;
        
        console.log('使用模拟数据成功');
      } catch (mockError) {
        console.error('模拟数据生成失败:', mockError);
        
        // 更新任务状态为失败
        if (task && task.id) {
          await videoNoteModel.updateTaskStatus(task.id, 'failed', mockError.message);
        }
        
        throw mockError;
      }
    }
    
    // 保存转录结果到数据库
    await videoNoteModel.saveTranscript({
      userId,
      videoId,
      videoUrl,
      videoTitle,
      transcript
    });
    
    return res.json({
      success: true,
      data: {
        transcript,
        duration,
        videoTitle
      }
    });
  } catch (error) {
    console.error('视频转录失败:', error);
    return res.status(500).json({
      success: false,
      message: `视频转录失败: ${error.message}`
    });
  }
};

// 生成笔记
export const generateNote = async (req, res) => {
  try {
    const { videoId, videoUrl } = req.body;
    let { title, transcript } = req.body;
    const userId = req.user.system_teacher_id;
    
    if (!videoId || !transcript) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    // 如果没有提供标题且URL是B站链接，尝试获取标题
    if ((!title || title === '未命名视频') && videoUrl && videoUrl.includes('bilibili')) {
      try {
        console.log('尝试从B站获取视频标题...');
        const bilibiliTitle = await extractBilibiliTitle(videoUrl);
        if (bilibiliTitle) {
          console.log(`成功获取B站视频标题: ${bilibiliTitle}`);
          title = bilibiliTitle;
          
          // 更新数据库中的视频标题
          try {
            await videoNoteModel.updateVideoTitle(userId, videoId, title);
            console.log(`视频标题已更新: ${title}`);
          } catch (updateError) {
            console.error('更新视频标题失败:', updateError);
          }
        }
      } catch (titleError) {
        console.error('获取B站视频标题失败:', titleError);
      }
    }
    
    // 获取视频所在目录
    const videoDir = path.join(UPLOAD_DIR, videoId);
    const summaryPath = path.join(videoDir, `${videoId}_summary.txt`);
    
    let summary;
    
    try {
      console.log('尝试使用DeepSeek API生成摘要');
      
      // 使用JS版本的DeepSeek API调用替代Python脚本
      summary = await generateSummaryWithDeepseek(transcript, title || '视频');
      
      // 保存摘要到文件
      if (!fs.existsSync(videoDir)) {
        fs.mkdirSync(videoDir, { recursive: true });
      }
      
      fs.writeFileSync(summaryPath, summary, 'utf8');
      console.log(`摘要已保存到文件: ${summaryPath}`);
      
    } catch (summaryError) {
      console.error('DeepSeek API生成摘要失败，切换到模拟模式:', summaryError);
      
      // 使用模拟摘要作为备选
      console.log('使用模拟摘要功能，视频ID:', videoId);
      try {
        const mockResult = await mockSummary(transcript, videoId, title);
        summary = mockResult;
        console.log('使用模拟摘要成功');
      } catch (mockError) {
        console.error('模拟摘要生成失败:', mockError);
        throw mockError;
      }
    }
    
    // 保存笔记到数据库
    await videoNoteModel.saveSummary({
      userId,
      videoId,
      videoUrl,
      title: title || '未命名视频',
      summary
    });
    
    return res.json({
      success: true,
      data: {
        summary,
        title
      }
    });
  } catch (error) {
    console.error('生成笔记失败:', error);
    return res.status(500).json({
      success: false,
      message: `生成笔记失败: ${error.message}`
    });
  }
};

// 保存笔记
export const saveNote = async (req, res) => {
  try {
    const { videoId, videoUrl, videoTitle, title, content } = req.body;
    const userId = req.user.system_teacher_id; // 从auth中间件获取用户ID
    
    if (!videoId || !content) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    // 保存笔记到数据库
    const result = await videoNoteModel.saveNote({
      userId,
      videoId,
      videoUrl,
      videoTitle,
      title,
      content
    });
    
    return res.json({
      success: true,
      data: {
        id: result.id
      }
    });
  } catch (error) {
    console.error('保存笔记失败:', error);
    return res.status(500).json({
      success: false,
      message: `保存笔记失败: ${error.message}`
    });
  }
};

// 获取用户的所有笔记
export const getUserNotes = async (req, res) => {
  try {
    const userId = req.user.system_teacher_id;
    const notes = await videoNoteModel.getUserNotes(userId);
    
    return res.json({
      success: true,
      data: notes
    });
  } catch (error) {
    console.error('获取笔记列表失败:', error);
    return res.status(500).json({
      success: false,
      message: `获取笔记列表失败: ${error.message}`
    });
  }
};

// 获取笔记详情
export const getNoteDetail = async (req, res) => {
  try {
    const noteId = req.params.id;
    const userId = req.user.system_teacher_id;
    
    const note = await videoNoteModel.getNoteById(noteId, userId);
    
    if (!note) {
      return res.status(404).json({
        success: false,
        message: '笔记不存在'
      });
    }
    
    return res.json({
      success: true,
      data: note
    });
  } catch (error) {
    console.error('获取笔记详情失败:', error);
    return res.status(500).json({
      success: false,
      message: `获取笔记详情失败: ${error.message}`
    });
  }
};

// 删除笔记
export const deleteNote = async (req, res) => {
  try {
    const noteId = req.params.id;
    const userId = req.user.system_teacher_id;
    
    const result = await videoNoteModel.deleteNote(noteId, userId);
    
    if (!result) {
      return res.status(404).json({
        success: false,
        message: '笔记不存在或已删除'
      });
    }
    
    return res.json({
      success: true,
      message: '笔记已删除'
    });
  } catch (error) {
    console.error('删除笔记失败:', error);
    return res.status(500).json({
      success: false,
      message: `删除笔记失败: ${error.message}`
    });
  }
};

/**
 * 检查视频是否已有转录和笔记数据
 * @param {object} req - 请求对象
 * @param {object} res - 响应对象
 * @returns {Promise<void>}
 */
export const checkVideoData = async (req, res) => {
  const { videoId } = req.params;
  const userId = req.user.system_teacher_id;
  
  console.log(`检查视频数据，用户ID: ${userId}, 视频ID: ${videoId}`);
  
  try {
    // 查询video_summaries表中是否有此视频的数据
    const connection = await mysql.createConnection(dbConfig);
    
    // 查询视频摘要表中是否有此视频的数据
    const [summaryRows] = await connection.execute(
      `SELECT id, system_teacher_id, resource_id, title, url, transcription, summary, status 
       FROM video_summaries 
       WHERE system_teacher_id = ? AND resource_id = ? AND status = 1`,
      [userId, videoId]
    );
    
    console.log(`在video_summaries表中查找结果：找到 ${summaryRows.length} 条记录`);
    
    if (summaryRows.length === 0) {
      // 尝试用数字ID查询
      const numericUserId = parseInt(String(userId).replace(/\D/g, '')) || 1;
      console.log(`尝试使用数字ID ${numericUserId} 再次查询`);
      
      const [numericRows] = await connection.execute(
        `SELECT id, system_teacher_id, resource_id, title, url, transcription, summary, status 
         FROM video_summaries 
         WHERE system_teacher_id = ? AND resource_id = ? AND status = 1`,
        [numericUserId, videoId]
      );
      
      console.log(`数字ID查询结果：找到 ${numericRows.length} 条记录`);
      
      if (numericRows.length === 0) {
        // 没有找到数据
        await connection.end();
        return res.json({
          success: true,
          exists: false,
          message: '未找到该视频的笔记数据'
        });
      }
      
      // 处理找到的数据
      const data = numericRows[0];
      const transcriptText = data.transcription || '';
      const summaryText = data.summary || '';
      
      // 解析转录文本，按句子或段落分割
      const transcriptSegments = [];
      if (transcriptText) {
        // 简单实现：按句号分割，添加时间戳
        const sentences = transcriptText.split(/(?<=[。！？.!?])\s*/);
        const duration = sentences.length * 5; // 估计时长
        
        for (let i = 0; i < sentences.length; i++) {
          const start = Math.floor((duration / sentences.length) * i);
          const end = Math.floor((duration / sentences.length) * (i + 1));
          transcriptSegments.push({ start, end, text: sentences[i] });
        }
      }
      
      await connection.end();
      return res.json({
        success: true,
        exists: true,
        data: {
          videoTitle: data.title,
          hasTranscript: !!transcriptText,
          transcript: transcriptText,
          transcriptSegments,
          hasSummary: !!summaryText,
          summary: summaryText
        }
      });
    }
    
    // 处理找到的数据
    const data = summaryRows[0];
    const transcriptText = data.transcription || '';
    const summaryText = data.summary || '';
    
    // 解析转录文本，按句子或段落分割
    const transcriptSegments = [];
    if (transcriptText) {
      // 简单实现：按句号分割，添加时间戳
      const sentences = transcriptText.split(/(?<=[。！？.!?])\s*/);
      const duration = sentences.length * 5; // 估计时长
      
      for (let i = 0; i < sentences.length; i++) {
        const start = Math.floor((duration / sentences.length) * i);
        const end = Math.floor((duration / sentences.length) * (i + 1));
        transcriptSegments.push({ start, end, text: sentences[i] });
      }
    }
    
    await connection.end();
    return res.json({
      success: true,
      exists: true,
      data: {
        videoTitle: data.title,
        hasTranscript: !!transcriptText,
        transcript: transcriptText,
        transcriptSegments,
        hasSummary: !!summaryText,
        summary: summaryText
      }
    });
  } catch (error) {
    console.error('检查视频数据失败:', error);
    res.status(500).json({
      success: false,
      message: `检查视频数据时出错: ${error.message}`
    });
  }
};

// 使用LLM生成笔记摘要
export const generateNoteSummary = async (req, res) => {
  try {
    const { transcript, title } = req.body;
    
    if (!transcript) {
      return res.status(400).json({
        success: false,
        message: '缺少转录文本'
      });
    }
    
    // 设置OpenAI API
    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey) {
      return res.status(500).json({
        success: false,
        message: '缺少API密钥配置，请联系管理员'
      });
    }
    
    // 构建请求数据
    const payload = {
      model: "deepseek-chat",
      messages: [
        {
          role: "system", 
          content: "你是一个擅长笔记整理和知识提炼的助手。请按照以下格式生成结构化笔记：\n1. 不要使用Markdown格式的#号标题，改用中文标题如'核心知识点'、'要点总结'等\n2. 使用清晰的段落和分点，用数字序号或中文顿号标记要点\n3. 避免使用过多特殊符号\n4. 生成的内容应该是纯文本格式，易于阅读"
        },
        {
          role: "user", 
          content: `请帮我总结以下《${title}》的内容要点，制作成便于阅读的笔记形式。不要使用markdown格式，使用中文标题和序号：\n\n${transcript}`
        }
      ],
      stream: false
    };
    
    // 设置请求选项
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      timeout: 60000 // 60秒超时
    };
    
    console.log('发送DeepSeek API请求...');
    const response = await axios.post('https://api.deepseek.com/chat/completions', payload, config);
    
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      let summary = response.data.choices[0].message.content;
      
      // 格式化处理
      summary = formatSummary(summary, title);
      
      console.log(`摘要生成成功，长度: ${summary.length}字符`);
      return res.json({
        success: true,
        data: {
          summary
        }
      });
    } else {
      throw new Error('DeepSeek API返回格式异常，未找到生成内容');
    }
  } catch (error) {
    console.error('生成笔记摘要失败:', error);
    res.status(500).json({
      success: false,
      message: `生成笔记摘要失败: ${error.message}`
    });
  }
};

export default {
  transcribeVideo,
  generateNote,
  saveNote,
  getUserNotes,
  getNoteDetail,
  deleteNote,
  checkVideoData,
  generateNoteSummary
}; 
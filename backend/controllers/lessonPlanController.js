import { OpenAI } from 'openai';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { pool } from '../config/db.js';
import db from '../config/db.js';
import { createOpenAIConfig, API_USAGES } from '../config/modelService.js';
import { clearSyllabusCache } from './syllabusController.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 初始化火山引擎 AI 客户端（异步）
let volcano_client;
(async () => {
  try {
    const config = await createOpenAIConfig(API_USAGES.LESSON_PLAN);
    volcano_client = new OpenAI(config);
    console.log('火山引擎 AI 客户端初始化成功');
  } catch (error) {
    console.error('火山引擎 AI 客户端初始化失败:', error);
  }
})();

// 获取教师基本信息和课程列表
export const getTeacherAndCourses = async (req, res) => {
    let connection;
    try {
        connection = await pool.getConnection();

        // 从req.user中获取教师信息
        const { system_teacher_id, name } = req.user;

        // 获取该教师的所有课程信息，包括课时数
        const [courseRows] = await connection.execute(
            `SELECT
                c.id,
                c.course_code,
                c.course_name,
                c.description,
                c.credit,
                c.course_type,
                c.status,
                c.semester,
                cs.total_periods
            FROM courses c
            LEFT JOIN class_schedules cs ON cs.course_id = c.course_code AND cs.user_id = c.system_teacher_id AND cs.is_current = 1
            WHERE c.system_teacher_id = ?
            AND c.status = 1
            ORDER BY c.semester DESC, c.course_code ASC`,
            [system_teacher_id]
        );

        res.json({
            success: true,
            data: {
                teacher: {
                    name: name
                },
                courses: courseRows
            }
        });
    } catch (error) {
        console.error('获取教师课程列表失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误',
            error: error.message
        });
    } finally {
        if (connection) {
            connection.release();
        }
    }
};

// 提取最小单位并生成ID
async function extractMinimalUnits(syllabusContent, courseId, teacherId) {
  const minimalUnits = [];
  let currentId = 1;

  function traverse(obj) {
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        // 如果是对象，继续遍历子节点
        traverse(value);
      } else if (typeof value === 'string' || typeof value === 'number') {
        // 将所有非对象的值转换为空字符串，并记录ID映射
        minimalUnits.push({
          id: currentId,
          title: key,
          teacher_id: teacherId,
          course_code: courseId
        });
        // 在原对象中保持空字符串格式
        obj[key] = "";
        currentId++;
      }
    }
  }

  // 创建原始内容的深拷贝
  const formattedContent = JSON.parse(JSON.stringify(syllabusContent));

  // 遍历并处理内容
  traverse(formattedContent);

  return {
    minimalUnits,
    formattedContent
  };
}

// 处理大纲识别和课时分配
export const processSyllabus = async (req, res) => {
  if (!req.files || req.files.length === 0) {
    return res.status(400).json({
      success: false,
      message: '未接收到文件'
    });
  }

  const { totalHours, courseId, actionType, existingSyllabus, originalTotalPeriods } = req.body;
  const teacherId = req.user?.system_teacher_id;

  try {
    // 确保AI客户端已初始化
    if (!volcano_client) {
      console.log('AI客户端尚未初始化，正在尝试初始化...');
      const config = await createOpenAIConfig(API_USAGES.LESSON_PLAN);
      volcano_client = new OpenAI(config);
      console.log('成功初始化AI客户端');
    }

    // 1. 识别目录结构 - 处理所有上传的图片
    let syllabusContent = {};

    // 如果是叠加模式，首先解析现有大纲
    if (actionType === 'append' && existingSyllabus) {
      try {
        syllabusContent = JSON.parse(existingSyllabus);
      } catch (error) {
        console.error('解析现有大纲失败:', error);
        syllabusContent = {};
      }
    }

    // 从上传的所有图片中构建内容数组
    let contentArray = [];
    let imageUrls = [];
    
    // 准备所有图片
    for (const file of req.files) {
      const imageBuffer = fs.readFileSync(file.path);
      const base64Image = imageBuffer.toString('base64');
      imageUrls.push(`data:image/jpeg;base64,${base64Image}`);
      
      // 清理临时文件
      fs.unlink(file.path, (err) => {
        if (err) console.error(`临时文件 ${file.path} 删除失败:`, err);
      });
    }
    
    // 一次性调用AI API处理所有图片
    if (imageUrls.length > 0) {
      // 构建包含所有图片的messages内容
      const messagesContent = [
        {
          type: "text",
          text: `请识别并整理以下${imageUrls.length}张图片中展示的课程目录，整理成如下格式的嵌套字典：
{
  "绪论": "",
  "第1章 xxx": {
    "1.1 xxx": "",
    "1.2 xxx": ""
  }
}

非常重要的处理规则：
1. 严格遵循章节号唯一性原则：每个章节号（如"第一章"、"第二章"）在整个目录中只能出现一次
2. 合并相同章节号的内容：如果多张图片中出现相同章节号（例如两张图片都有"第四章"），必须合并为同一章节，完整保留所有子节点
3. 章节标题可能不完整：若某章节出现多次但标题不同，如"第四章"和"第四章 刚体转动和流体运动"，应取最完整的标题
4. 相邻图片处理：相邻图片通常是连续的目录部分，应正确识别衔接关系
5. 保持原有的章节编号和完整标题
6. 所有最小单位必须使用空字符串("")作为值
7. 必须排除与课程内容无关的项目，包括但不限于：习题、习题解答、练习、测试题、思考题、问题、复习题、小结等
8. 只保留真正的课程内容章节，不要包含如"习题1-1全解"、"问题集"、"复习资料"等辅助材料
9. 直接返回JSON格式，不要包含其他说明文字`
        }
      ];
      
      // 添加所有图片URL
      imageUrls.forEach(url => {
        messagesContent.push({
          type: "image_url",
          image_url: { url }
        });
      });
      
      try {
        // 一次性发送所有图片到API
        const volcano_response = await volcano_client.chat.completions.create({
          model: "ep-20250310134707-kcdj5",
          messages: [
            {
              role: "system",
              content: "你是一个专业的擅长备课和写教案的老师，能够从教材目录图片中识别并组织完整的章节结构"
            },
            {
              role: "user",
              content: messagesContent
            }
          ]
        });
        
        try {
          syllabusContent = JSON.parse(volcano_response.choices[0].message.content.trim());
        } catch (error) {
          console.error('解析AI响应内容失败:', error);
          throw new Error('无法解析识别结果，请检查上传的图片质量');
        }
      } catch (error) {
        console.error('调用AI API失败:', error);
        throw new Error('识别目录失败，请重试或上传更清晰的图片');
      }
    }

    // 如果没有成功解析任何内容
    if (Object.keys(syllabusContent).length === 0) {
      return res.status(400).json({
        success: false,
        message: '未能成功识别大纲内容'
      });
    }

    // 2. 为每个最小单位分配ID
    let currentId = 1;

    function assignIds(obj) {
      const result = {};
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'object' && value !== null) {
          result[key] = assignIds(value);
        } else {
          result[key] = currentId.toString();
          currentId++;
        }
      }
      return result;
    }

    // 分配ID
    syllabusContent = assignIds(syllabusContent);

    // 3. 生成课时分配
    const unitsArray = [];
    const skipKeywords = [
      '附录', '参考文献', '附件', '索引', '致谢', '前言', '序言', 
      '习题', '练习', '问题', '思考题', '测试题', '复习题', '小结', 
      '思考与练习', '习题解答', '课后习题', '测验', '自测题', '课后作业',
      '案例分析', '练习集', '参考答案', '思考与讨论'
    ];

    function collectUnits(obj, parentTitle = '', level = 0) {
      // 检查当前节点是否应该跳过
      const shouldSkip = (title) => {
        return skipKeywords.some(keyword => title.includes(keyword));
      };

      for (const [key, value] of Object.entries(obj)) {
        // 如果当前节点标题包含跳过关键词，则跳过该节点及其子节点
        if (shouldSkip(key)) {
          console.log(`跳过节点: ${key}`);
          continue;
        }

        if (typeof value === 'object' && value !== null) {
          collectUnits(value, key, level + 1);
        } else {
          unitsArray.push({
            title: key,
            id: value,
            parentTitle,
            level // 添加层级信息
          });
        }
      }
    }
    collectUnits(syllabusContent);

    const content_response = await volcano_client.chat.completions.create({
      model: "ep-20250310135629-zgwb7",
      messages: [
        {
          role: "system",
          content: "你是一个专业的教学专家。请将课程内容分配到各个课时中，直接返回JSON格式，不要包含任何其他说明文字。"
        },
        {
          role: "user",
          content: `请将以下课程内容分配到${totalHours}个课时中：

${JSON.stringify(unitsArray.map(unit => ({
  title: unit.title,
  parentTitle: unit.parentTitle,
  level: unit.level
})), null, 2)}

要求：
1. 必须分配到正好${totalHours}个课时，每一节课都必须有内容
2. 每个内容后面标注[简单]/[中等]/[困难]难度
3. 必须按照目录顺序分配，不能打乱顺序
4. 一个课时最多只能有一个[困难]内容
5. 考虑内容的层级和重要性，主要章节应该分配更多课时
6. 同一章节的内容应尽量安排在相邻的课时
7. 返回格式必须是JSON，格式如下：
{
  "课时1": ["xxx[简单]", "xxx[中等]"],
  "课时2": ["xxx[中等]"],
  "课时3": ["xxx[困难]"]
}`
        }
      ]
    });

    const courseContent = JSON.parse(content_response.choices[0].message.content.trim());

    // 4. 保存到数据库
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // 查询是否已存在该课程的大纲
      const [existingRows] = await connection.execute(
        `SELECT id FROM syllabus WHERE course_code = ? AND teacher_id = ?`,
        [courseId, teacherId]
      );

      // 获取课程的实际信息（课程名称）
      const [courseRows] = await connection.execute(
        `SELECT course_name FROM courses WHERE course_code = ?`,
        [courseId]
      );
      
      // 如果没有找到课程，抛出错误
      if (courseRows.length === 0) {
        throw new Error(`找不到课程代码为 ${courseId} 的课程信息`);
      }
      
      const courseName = courseRows[0].course_name;

      console.log(`覆盖/更新大纲 - 课程ID: ${courseId}, 课程名称: ${courseName}, 教师ID: ${teacherId}, 操作类型: ${actionType}, 当前存在记录: ${existingRows.length > 0}`);

      let result;
      if (existingRows.length > 0) {
        if (actionType === 'replace' || actionType === undefined) {
          // 如果已存在且是替换模式，完全覆盖现有记录
          console.log('执行覆盖更新操作');
          [result] = await connection.execute(
            `UPDATE syllabus
            SET content = ?, allocation = ?, total = ?, update_time = NOW()
            WHERE course_code = ? AND teacher_id = ?`,
            [
              JSON.stringify(syllabusContent, null, 2),
              JSON.stringify(courseContent, null, 2),
              parseInt(totalHours),
              courseId,
              teacherId
            ]
          );
        } else {
          // 叠加模式，已在之前的代码中处理并合并了内容
          console.log('执行叠加更新操作');
          [result] = await connection.execute(
            `UPDATE syllabus
            SET content = ?, allocation = ?, total = ?, update_time = NOW()
            WHERE course_code = ? AND teacher_id = ?`,
            [
              JSON.stringify(syllabusContent, null, 2),
              JSON.stringify(courseContent, null, 2),
              parseInt(totalHours),
              courseId,
              teacherId
            ]
          );
        }
      } else {
        // 如果不存在记录，则插入新记录
        console.log('插入新记录');
        [result] = await connection.execute(
          `INSERT INTO syllabus
          (teacher_id, course_code, subject, content, allocation, total, create_time)
          VALUES (?, ?, ?, ?, ?, ?, NOW())`,
          [
            teacherId,
            courseId,
            courseName, // 使用从数据库获取的实际课程名称，而不是硬编码的"物理"
            JSON.stringify(syllabusContent, null, 2),
            JSON.stringify(courseContent, null, 2),
            parseInt(totalHours)
          ]
        );
      }

      await connection.commit();

      // 如果提供了原始课时数，则一并返回
      res.json({
        success: true,
        data: {
          syllabus: syllabusContent,
          allocation: courseContent,
          total: parseInt(totalHours),
          originalTotalPeriods: originalTotalPeriods ? parseInt(originalTotalPeriods) : null
        }
      });

      // 清除该课程的缓存
      clearSyllabusCache(teacherId, courseId);

      console.log(`[大纲处理] 成功完成处理, 课程ID: ${courseId}, 教师ID: ${teacherId}, 已清除相关缓存`);

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('处理大纲失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '处理失败'
    });
  }
};

// 生成教案
export const generateLessonPlan = async (req, res) => {
    try {
        const { content, hour, totalHours } = req.body;
        if (!content || !hour) {
            throw new Error('缺少必要参数');
        }

        // 根据课时总数计算平均每课时时间，默认按照45分钟一课时计算
        const totalMinutes = 45;
        // 按比例分配时间，讲解新知识占总时间的40%左右
        const lectureMinutes = Math.round(totalMinutes * 0.4);

        // TODO: 调用大模型API生成教案
        // 这里暂时返回模拟数据
        const mockLessonPlan = `
# 第${hour}课时教案

## 教学目标
1. 理解本节课的核心概念
2. 掌握相关知识点的应用

## 教学重点
${content.join('\n')}

## 教学过程
1. 导入新课 (5分钟)
   - 复习上节课内容
   - 引入新课题

2. 讲解新知识 (${lectureMinutes}分钟)
   - 详细讲解各知识点
   - 举例说明

3. 课堂练习 (${totalMinutes - lectureMinutes - 10}分钟)
   - 布置练习题
   - 巡视指导

4. 总结 (5分钟)
   - 归纳本节课重点
   - 布置作业
    `;

        res.json({
            success: true,
            message: '生成成功',
            lessonPlan: mockLessonPlan
        });

    } catch (error) {
        console.error('生成教案失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '生成失败'
        });
    }
};

// 导出路由处理
export const setupLessonPlanRoutes = (router) => {
    router.get('/teacher-info', getTeacherAndCourses);
    router.post('/process-syllabus', processSyllabus);
    router.post('/generate-lesson-plan', generateLessonPlan);
};
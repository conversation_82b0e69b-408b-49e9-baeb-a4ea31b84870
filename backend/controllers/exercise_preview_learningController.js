import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';
import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';
import 'dotenv/config';

// 配置OpenAI，与课后习题相同
const apiKey = process.env.ARK_API_KEY || '1dfa2f7b-c0f7-486c-bd5a-068ff9c8d677';

// 配置OpenAI客户端
const openai = new OpenAI({
  apiKey: apiKey,
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
});

// 检查图片格式
function checkImageFormat(buffer) {
  if (buffer.length < 8) return false;
  
  // 检查PNG格式 (89 50 4E 47 0D 0A 1A 0A)
  if (buffer[0] === 0x89 && 
      buffer[1] === 0x50 && 
      buffer[2] === 0x4E && 
      buffer[3] === 0x47 && 
      buffer[4] === 0x0D && 
      buffer[5] === 0x0A && 
      buffer[6] === 0x1A && 
      buffer[7] === 0x0A) {
    return true;
  }
  
  // 检查JPEG格式 (FF D8)
  if (buffer[0] === 0xFF && buffer[1] === 0xD8) {
    return true;
  }
  
  return false;
}

// AI评分函数 - 与课后习题相同
async function gradeWithAI(question, studentAnswer, questionType) {
  try {
    let prompt = '';
    let isImageAnswer = false;
    
    // 检查是否是图片路径
    if (studentAnswer && typeof studentAnswer === 'string' && studentAnswer.startsWith('/uploads/')) {
      isImageAnswer = true;
    }
    
    if (questionType === 4) { // 简答题
      prompt = `
请评分以下学生的简答题回答，并以JSON格式返回结果。

问题: ${question}

学生答案: ${studentAnswer}

请根据以下标准进行评分：
- 答案的完整性和准确性（4分）
- 概念理解的深度（3分）
- 表达的清晰度和逻辑性（3分）

总分10分。

请以下面的JSON格式返回评分结果：
{
  "score": [分数，数值类型，0-10之间],
  "evaluation": [详细的评分理由]
}

你必须严格按照上述JSON格式返回，不要有任何额外文字。对于质量很差或完全无关的答案，请给予0分并说明原因。
`;

      // 文本评分
      const response = await openai.chat.completions.create({
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        model: 'ep-20250310134707-kcdj5',
        response_format: { type: "json_object" }
      });
      
      try {
        // 解析JSON响应
        const result = JSON.parse(response.choices[0].message.content);
        
        return {
          score: result.score !== undefined ? result.score : 0,
          evaluation: result.evaluation || "无评分理由"
        };
      } catch (parseError) {
        console.error('解析JSON响应失败:', parseError);
        // 解析失败给0分
        return {
          score: 0,
          evaluation: "解析评分结果失败，可能是系统错误"
        };
      }
      
    } else if (questionType === 5) { // 计算题
      // 对于计算题，如果没有图片就返回错误
      if (!isImageAnswer) {
        return {
          score: 5, // 默认给5分
          evaluation: "未提供计算过程图片，无法进行详细评分。请上传清晰的计算过程图片获取更准确的评分。"
        };
      }

      // 获取图片路径
      const uploadDir = path.join(process.cwd(), 'uploads', 'student_exercise');
      const fileName = path.basename(studentAnswer);
      let imagePath = path.join(uploadDir, fileName);
      
      console.log('计算题图片评分 - 图片路径:', imagePath);
      
      // 简化图片检查 - 如果主路径不存在，尝试备用路径
      if (!fs.existsSync(imagePath)) {
        console.log('主路径图片不存在，尝试备用路径');
        imagePath = path.join(process.cwd(), studentAnswer);
      }
      
      // 读取图片 - 如果不存在则返回默认评分
      let imageData, base64Image, imageUrl;
      try {
        imageData = fs.readFileSync(imagePath);
        base64Image = imageData.toString('base64');
        
        // 检查图片头部字节以验证格式
        const isValidImage = checkImageFormat(imageData);
        if (!isValidImage) {
          console.error('图片格式无效或不支持');
          return {
            score: 0,
            evaluation: "上传的图片格式无效或不受支持。请确保上传的是标准的JPG或PNG图片。"
          };
        }
        
        // 设置MIME类型
        const fileExtension = path.extname(imagePath).toLowerCase();
        const mimeType = fileExtension === '.png' ? 'image/png' : 'image/jpeg';
        imageUrl = `data:${mimeType};base64,${base64Image}`;
      } catch (error) {
        console.error('读取图片失败:', error);
        return {
          score: 0,
          evaluation: "系统无法读取计算题图片。请确保上传了有效的图片文件，或联系教师获取更准确的评分。"
        };
      }
      
      // 构建评分提示
      prompt = `
请评分以下学生的计算题解答，并以JSON格式返回结果。

问题: ${question}

请查看学生提供的计算过程图片。
警告：无论图片内容是什么（即使是不相关内容如猫、风景等），你必须以指定的JSON格式返回。
如果图片内容与计算过程无关，请给0分并在评价中说明原因。

请根据以下标准进行评分：
- 基本思路和解题方法是否正确（3分）
- 公式使用是否准确（3分）
- 计算过程是否有错误（2分）
- 最终结果是否正确（2分）

总分10分。

请以下面的JSON格式返回评分结果：
{
  "score": [分数，数值类型，0-10之间],
  "evaluation": [详细的评分理由]
}

你必须严格按照上述JSON格式返回，不要有任何额外文字，不管图片是什么内容都必须遵守这个要求。
`;
      
      try {
        // 使用多模态API调用，传递图片
        const response = await openai.chat.completions.create({
          messages: [
            {
              role: 'user',
              content: [
                { type: 'text', text: prompt },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageUrl
                  }
                }
              ]
            }
          ],
          model: 'ep-20250310134707-kcdj5',
          response_format: { type: "json_object" }
        });
        
        const responseText = response.choices[0].message.content;
        console.log('计算题图片评分结果:', responseText.substring(0, 100) + '...');
        
        try {
          // 解析JSON响应
          const result = JSON.parse(responseText);
          
          return {
            score: result.score !== undefined ? result.score : 0,
            evaluation: result.evaluation || "无评分理由"
          };
        } catch (parseError) {
          console.error('解析JSON响应失败:', parseError);
          // 解析失败给0分
          return {
            score: 0,
            evaluation: "解析评分结果失败，可能是系统错误"
          };
        }
      } catch (error) {
        console.error('API调用失败:', error);
        
        let errorMessage = "评分系统无法处理您的请求。请确保上传了包含计算过程的清晰图片。";
        
        // 针对不同类型的错误提供具体的提示
        if (error.error && error.error.code) {
          if (error.error.code === 'InvalidParameter.UnsupportedImageFormat') {
            errorMessage = "图片格式不受支持。请确保上传标准的JPG或PNG格式图片。";
          } else if (error.error.code === 'InvalidParameter.InvalidImageDimension') {
            errorMessage = "图片尺寸不符合要求。请确保图片尺寸适中，不要太大或太小。";
          } else if (error.error.code === 'InvalidParameter.ImageTooLarge') {
            errorMessage = "图片文件过大。请压缩图片或使用较小的图片。";
          } else if (error.error.code.includes('RateLimitExceeded')) {
            errorMessage = "系统当前请求过多。请稍后再试。";
          }
        }
        
        return {
          score: 0,
          evaluation: errorMessage
        };
      }
    }
    
    // 默认情况
    return {
      score: 5,
      evaluation: "无法确定题型，默认给予5分。"
    };
  } catch (error) {
    console.error('AI评分过程中出错:', error);
    return {
      score: 5,
      evaluation: '系统评分错误，请联系老师手动评分'
    };
  }
}

// 获取预习题列表
export const getExercises = async (req, res) => {
  try {
    const { classId, chapterId } = req.params;
    const studentId = req.user?.student_id || req.user?.id;
    
    if (!classId || !chapterId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    const conn = await mysql.createConnection(dbConfig);
    
    // 获取预习题列表，注意study_phase=1表示预习题
    let sql = `SELECT 
        pe.id as publish_id,
        eb.exercise_id,
        eb.content,
        eb.question_type,
        eb.option,
        eb.title_id,
        eb.difficulty,
        eb.title,
        ser.student_answer,
        ser.is_correct,
        ser.score,
        ser.evaluation,
        ser.time_spent
      FROM publish_exercise pe
      JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
      LEFT JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
      WHERE pe.class_id = ? AND eb.title_id = ? AND pe.study_phase = 1
      ORDER BY pe.id ASC`;
    
    const [exercises] = await conn.query(sql, [studentId.toString(), classId, chapterId]);
    
    // 处理题目选项和学生答案
    const processedExercises = exercises.map(exercise => {
      let options = [];
      if (exercise.option) {
        try {
          const optionObj = JSON.parse(exercise.option);
          options = Object.entries(optionObj).map(([key, value]) => ({
            key,
            value
          }));
        } catch (e) {
          console.error(`解析题目选项出错 (ID:${exercise.publish_id}):`, e);
        }
      }
      
      switch (exercise.question_type) {
        case 1: // 单选题
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
          
        case 2: // 填空题
          return {
            ...exercise,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
          
        case 3: // 判断题
          options = [
            { key: 'true', value: '正确' },
            { key: 'false', value: '错误' }
          ];
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
          
        case 4: // 简答题
        case 5: // 计算题
          return {
            ...exercise,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
          
        default:
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || ''
          };
      }
    });
    
    // 获取预习题发布信息
    const [previewInfo] = await conn.query(
      `SELECT 
        pe.created_at as release_time,
        pe.deadline
       FROM publish_exercise pe
       WHERE pe.class_id = ? AND pe.title_id = ? AND pe.study_phase = 1
       LIMIT 1`,
      [classId, chapterId]
    );
    
    await conn.end();
    
    res.json({
      success: true,
      data: {
        exercises: processedExercises,
        release_time: previewInfo.length > 0 ? previewInfo[0].release_time : null,
        deadline: previewInfo.length > 0 ? previewInfo[0].deadline : null
      }
    });
  } catch (error) {
    console.error('获取预习题列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习题列表失败: ' + error.message
    });
  }
};

// 保存单个答案（临时保存，不记录完成状态）
export const saveAnswer = async (req, res) => {
  try {
    const { publishId, studentAnswer } = req.body;
    
    if (!publishId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    // 发送成功响应，但不实际保存到数据库
    res.json({
      success: true,
      message: '答案暂存成功',
      data: {
        publish_id: publishId,
        student_answer: studentAnswer
      }
    });
  } catch (error) {
    console.error('暂存答案失败:', error);
    res.status(500).json({
      success: false,
      message: '暂存答案失败: ' + error.message
    });
  }
};

// 上传计算题图片
export const uploadImage = async (req, res) => {
  try {
    // 检查请求中是否包含文件
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未找到上传的图片'
      });
    }
    
    // 获取文件信息
    const file = req.file;
    
    // 打印完整文件信息进行调试
    console.log('接收到的文件信息:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      destination: file.destination,
      filename: file.filename
    });
    
    // 使用相对路径返回给前端
    const relativePath = `/uploads/student_exercise/${file.filename}`;
    
    console.log('计算题图片上传成功:', {
      filename: file.filename,
      relativePath,
      size: file.size,
      path: file.path
    });
    
    // 返回文件路径给前端
    res.json({
      success: true,
      data: {
        url: relativePath,
        filename: file.filename
      }
    });
  } catch (error) {
    console.error('上传图片失败:', error);
    res.status(500).json({
      success: false,
      message: '上传图片失败: ' + error.message
    });
  }
};

// 提交预习题答案
export const submitPreview = async (req, res) => {
  try {
    const { classId, chapterId } = req.params;
    const { answers, studentId } = req.body;
    // 从令牌中获取学号而不是ID
    const userIdToUse = studentId || req.user?.student_id || req.user?.id;
    
    console.log('批量提交 - 使用的学生ID:', userIdToUse);
    console.log('从请求体获取的studentId:', studentId);
    console.log('从令牌获取的信息:', req.user);
    
    if (!classId || !chapterId || !userIdToUse || !answers) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    let conn = null;
    try {
      conn = await mysql.createConnection(dbConfig);
      await conn.beginTransaction();
      
      let totalCorrect = 0;
      let totalScore = 0;
      let totalQuestions = answers.length;
      
      for (const answer of answers) {
        // 跳过没有答案的题目
        if (!answer.student_answer && answer.student_answer !== '0' && answer.student_answer !== false) {
          continue;
        }
        
        // 获取题目信息
        const [exerciseInfo] = await conn.query(
          `SELECT eb.exercise_id, eb.answer, eb.question_type, eb.content
           FROM exercise_bank eb
           JOIN publish_exercise pe ON eb.exercise_id = pe.exercise_id 
           WHERE pe.id = ?`,
          [answer.publish_id]
        );
        
        if (exerciseInfo.length === 0) continue;
        
        const correctAnswer = exerciseInfo[0].answer;
        const questionType = exerciseInfo[0].question_type;
        const questionContent = exerciseInfo[0].content;
        const studentAnswer = answer.student_answer || '';
        const timeSpent = answer.time_spent || 0;
        
        let isCorrect = 0;
        let score = 0;
        let evaluation = "";
        
        // 根据题型进行评分
        switch (questionType) {
          case 1: // 选择题
            isCorrect = studentAnswer.trim() === correctAnswer.trim() ? 1 : 0;
            score = isCorrect ? 5 : 0;
            evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
            break;
            
          case 2: // 填空题
            const normalizedCorrect = correctAnswer.trim().toLowerCase();
            const normalizedStudent = studentAnswer.trim().toLowerCase();
            isCorrect = normalizedStudent === normalizedCorrect ? 1 : 0;
            score = isCorrect ? 5 : 0;
            evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
            break;
            
          case 3: // 判断题
            let standardizedCorrect = correctAnswer.trim().toUpperCase();
            let standardizedStudent = studentAnswer.trim().toUpperCase();
            
            if (standardizedCorrect === "T" || standardizedCorrect === "TRUE" || standardizedCorrect === "对" || standardizedCorrect === "正确" || standardizedCorrect === "1") {
              standardizedCorrect = "T";
            } else if (standardizedCorrect === "F" || standardizedCorrect === "FALSE" || standardizedCorrect === "错" || standardizedCorrect === "错误" || standardizedCorrect === "0") {
              standardizedCorrect = "F";
            }
            
            if (standardizedStudent === "T" || standardizedStudent === "TRUE" || standardizedStudent === "对" || standardizedStudent === "正确" || standardizedStudent === "1") {
              standardizedStudent = "T";
            } else if (standardizedStudent === "F" || standardizedStudent === "FALSE" || standardizedStudent === "错" || standardizedStudent === "错误" || standardizedStudent === "0") {
              standardizedStudent = "F";
            }
            
            isCorrect = standardizedStudent === standardizedCorrect ? 1 : 0;
            score = isCorrect ? 5 : 0;
            evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
            break;
            
          case 4: // 简答题
          case 5: // 计算题
            // 使用AI评分
            console.log(`正在评分${questionType === 4 ? '简答题' : '计算题'}: 题目ID=${answer.publish_id}`);
            console.log(`学生答案: ${studentAnswer.substring(0, 100)}${studentAnswer.length > 100 ? '...' : ''}`);
            
            const aiResult = await gradeWithAI(questionContent, studentAnswer, questionType);
            isCorrect = aiResult.score >= 6 ? 1 : 0; // 6分以上认为是正确的
            score = aiResult.score;
            evaluation = aiResult.evaluation;
            
            console.log(`AI评分结果: 分数=${aiResult.score}, 评价=${aiResult.evaluation.substring(0, 100)}...`);
            break;
            
          default:
            isCorrect = null;
            score = null;
            evaluation = "未知题型";
        }
        
        if (isCorrect === 1) {
          totalCorrect++;
          totalScore += score || 0;
        }
        
        // 首先检查是否已存在记录
        const [existingRecord] = await conn.query(
          `SELECT * FROM student_exercise_records 
           WHERE student_id = ? AND publish_exercise_id = ?`,
          [userIdToUse, answer.publish_id]
        );
        
        if (existingRecord.length > 0) {
          // 更新现有记录
          await conn.query(
            `UPDATE student_exercise_records 
             SET student_answer = ?, is_correct = ?, score = ?, evaluation = ?, time_spent = ? 
             WHERE id = ?`,
            [studentAnswer, isCorrect, score, evaluation, timeSpent, existingRecord[0].id]
          );
        } else {
          // 插入新记录 - 这里需要使用学生的student_id，而不是自增ID
          await conn.query(
            `INSERT INTO student_exercise_records 
             (student_id, publish_exercise_id, student_answer, is_correct, score, evaluation, time_spent) 
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [userIdToUse, answer.publish_id, studentAnswer, isCorrect, score, evaluation, timeSpent]
          );
        }
      }
      
      // 提交事务
      await conn.commit();
      
      // 返回提交结果
      res.json({
        success: true,
        message: '预习题提交成功',
        data: {
          total: totalQuestions,
          correct: totalCorrect,
          score: totalScore
        }
      });
    } catch (error) {
      // 回滚事务
      if (conn) {
        await conn.rollback();
      }
      throw error;
    } finally {
      // 关闭数据库连接
      if (conn) {
        await conn.end();
      }
    }
  } catch (error) {
    console.error('提交预习题失败:', error);
    res.status(500).json({
      success: false,
      message: '提交预习题失败: ' + (error.message || '未知错误')
    });
  }
};

// 提交单个题目的答案
export const submitOne = async (req, res) => {
  try {
    const { classId, chapterId } = req.params;
    const { exercise, studentId } = req.body;
    // 从令牌中获取学号而不是ID
    const userIdToUse = studentId || req.user?.student_id || req.user?.id;
    
    console.log('提交单题 - 使用的学生ID:', userIdToUse);
    console.log('从请求体获取的studentId:', studentId);
    console.log('从令牌获取的信息:', req.user);
    
    if (!classId || !chapterId || !userIdToUse || !exercise || !exercise.publish_id) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    let conn = null;
    try {
      conn = await mysql.createConnection(dbConfig);
      await conn.beginTransaction();
      
      // 获取题目信息
      const [exerciseInfo] = await conn.query(
        `SELECT eb.exercise_id, eb.answer, eb.question_type, eb.content
         FROM exercise_bank eb
         JOIN publish_exercise pe ON eb.exercise_id = pe.exercise_id 
         WHERE pe.id = ?`,
        [exercise.publish_id]
      );
      
      if (exerciseInfo.length === 0) {
        return res.status(404).json({
          success: false,
          message: '题目不存在'
        });
      }
      
      const correctAnswer = exerciseInfo[0].answer;
      const questionType = exerciseInfo[0].question_type;
      const questionContent = exerciseInfo[0].content;
      const studentAnswer = exercise.student_answer || '';
      const timeSpent = exercise.time_spent || 0;
      
      let isCorrect = 0;
      let score = 0;
      let evaluation = "";
      
      // 根据题型进行评分
      switch (questionType) {
        case 1: // 选择题
          isCorrect = studentAnswer.trim() === correctAnswer.trim() ? 1 : 0;
          score = isCorrect ? 5 : 0;
          evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
          break;
          
        case 2: // 填空题
          const normalizedCorrect = correctAnswer.trim().toLowerCase();
          const normalizedStudent = studentAnswer.trim().toLowerCase();
          isCorrect = normalizedStudent === normalizedCorrect ? 1 : 0;
          score = isCorrect ? 5 : 0;
          evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
          break;
          
        case 3: // 判断题
          let standardizedCorrect = correctAnswer.trim().toUpperCase();
          let standardizedStudent = studentAnswer.trim().toUpperCase();
          
          if (standardizedCorrect === "T" || standardizedCorrect === "TRUE" || standardizedCorrect === "对" || standardizedCorrect === "正确" || standardizedCorrect === "1") {
            standardizedCorrect = "T";
          } else if (standardizedCorrect === "F" || standardizedCorrect === "FALSE" || standardizedCorrect === "错" || standardizedCorrect === "错误" || standardizedCorrect === "0") {
            standardizedCorrect = "F";
          }
          
          if (standardizedStudent === "T" || standardizedStudent === "TRUE" || standardizedStudent === "对" || standardizedStudent === "正确" || standardizedStudent === "1") {
            standardizedStudent = "T";
          } else if (standardizedStudent === "F" || standardizedStudent === "FALSE" || standardizedStudent === "错" || standardizedStudent === "错误" || standardizedStudent === "0") {
            standardizedStudent = "F";
          }
          
          isCorrect = standardizedStudent === standardizedCorrect ? 1 : 0;
          score = isCorrect ? 5 : 0;
          evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
          break;
          
        case 4: // 简答题
        case 5: // 计算题
          // 使用AI评分
          console.log(`正在评分${questionType === 4 ? '简答题' : '计算题'}: 题目ID=${exercise.publish_id}`);
          console.log(`学生答案: ${studentAnswer.substring(0, 100)}${studentAnswer.length > 100 ? '...' : ''}`);
          
          const aiResult = await gradeWithAI(questionContent, studentAnswer, questionType);
          isCorrect = aiResult.score >= 6 ? 1 : 0; // 6分以上认为是正确的
          score = aiResult.score;
          evaluation = aiResult.evaluation;
          
          console.log(`AI评分结果: 分数=${aiResult.score}, 评价=${aiResult.evaluation.substring(0, 100)}...`);
          break;
          
        default:
          isCorrect = null;
          score = null;
          evaluation = "未知题型";
      }
      
      // 首先检查是否已存在记录
      const [existingRecord] = await conn.query(
        `SELECT * FROM student_exercise_records 
         WHERE student_id = ? AND publish_exercise_id = ?`,
        [userIdToUse, exercise.publish_id]
      );
      
      if (existingRecord.length > 0) {
        // 更新现有记录
        await conn.query(
          `UPDATE student_exercise_records 
           SET student_answer = ?, is_correct = ?, score = ?, evaluation = ?, time_spent = ? 
           WHERE id = ?`,
          [studentAnswer, isCorrect, score, evaluation, timeSpent, existingRecord[0].id]
        );
      } else {
        // 插入新记录 - 这里需要使用学生的student_id，而不是自增ID
        await conn.query(
          `INSERT INTO student_exercise_records 
           (student_id, publish_exercise_id, student_answer, is_correct, score, evaluation, time_spent) 
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [userIdToUse, exercise.publish_id, studentAnswer, isCorrect, score, evaluation, timeSpent]
        );
      }
      
      // 提交事务
      await conn.commit();
      
      // 返回提交结果
      res.json({
        success: true,
        message: '答案提交成功',
        data: {
          publish_id: exercise.publish_id,
          is_correct: isCorrect === 1,
          score: score,
          evaluation: evaluation,
          correct_answer: correctAnswer
        }
      });
    } catch (error) {
      // 回滚事务
      if (conn) {
        await conn.rollback();
      }
      throw error;
    } finally {
      // 关闭数据库连接
      if (conn) {
        await conn.end();
      }
    }
  } catch (error) {
    console.error('提交答案失败:', error);
    res.status(500).json({
      success: false,
      message: '提交答案失败: ' + (error.message || '未知错误')
    });
  }
};

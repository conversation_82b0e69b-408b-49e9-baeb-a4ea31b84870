import { OpenAI } from 'openai';
import pool from '../config/db.js';
import { createOpenAIConfig, API_USAGES } from '../config/modelService.js';

// 初始化 DeepSeek AI 客户端（异步）
let ai_client;
(async () => {
  try {
    const config = await createOpenAIConfig(API_USAGES.PREVIEW);
    ai_client = new OpenAI(config);
    console.log('DeepSeek AI 客户端初始化成功');
  } catch (error) {
    console.error('DeepSeek AI 客户端初始化失败:', error);
  }
})();

// 生成预习资料
export const generatePreviewMaterial = async (req, res) => {
  try {
    console.log('\n=== 开始生成预习资料 ===');
    
    // 确保AI客户端已初始化
    if (!ai_client) {
      console.log('AI客户端尚未初始化，正在尝试初始化...');
      const config = await createOpenAIConfig(API_USAGES.PREVIEW);
      ai_client = new OpenAI(config);
      console.log('成功初始化AI客户端');
    }
    
    const { courseCode, titleId, chapterTitle } = req.body;
    const teacherId = req.user?.system_teacher_id;

    console.log('参数信息:', {
      courseCode,
      titleId,
      chapterTitle,
      teacherId
    });

    if (!courseCode || !titleId) {
      console.log('❌ 参数验证失败: 缺少必要参数');
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 构建提示词
    console.log('\n1. 构建AI提示词...');
    const prompt = `作为一名专业的教育工作者，请根据以下内容生成一份详细的预习资料：

章节：${chapterTitle}

要求：
1. 预习资料应包含本章节的核心概念和基础知识点
2. 预习资料应该对学生有指导性，帮助他们为课堂学习做好准备
3. 预习资料应该包含一些例子或实际应用场景
4. 预习资料应该结构清晰，语言简洁明了

请按照以下结构输出：

# ${chapterTitle} - 预习资料

## 核心概念
[列出本章节的核心概念和定义]

## 基础知识
[详细介绍相关的基础知识]

## 重点难点
[指出本章节的重点和可能的难点]

## 预习任务
[给出2-3个预习任务或思考问题]

## 学习资源
[推荐1-2个学习资源或参考资料]

## 预计阅读时间
[请评估这份预习资料的合理阅读时间（不用太长），以分钟为单位，只需提供一个数字，例如：10]`;

    console.log('✓ 提示词构建完成');

    // 调用AI生成预习资料
    console.log('\n2. 调用AI生成预习资料...');
    const completion = await ai_client.chat.completions.create({
      model: "deepseek-chat",
      messages: [
        {
          role: "system",
          content: "你是一名专业的教育工作者，擅长编写预习资料和学习指导。请按照指定格式输出预习资料，并给出合理的预计阅读时间。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 3000
    });

    // 获取AI返回的内容
    const previewContent = completion.choices[0].message.content.trim();
    console.log('✓ AI响应成功');
    
    // 提取预计阅读时间
    let expectedDuration = 10; // 默认30分钟
    const durationMatch = previewContent.match(/## 预计阅读时间\s*\n*([0-9]+)/);
    if (durationMatch && durationMatch[1]) {
      expectedDuration = parseInt(durationMatch[1], 10);
    }
    console.log(`✓ 提取的预计阅读时间: ${expectedDuration}分钟`);

    // 保存预习资料
    console.log('\n3. 保存预习资料到数据库...');
    // 获取大纲ID
    const [syllabusResult] = await pool.execute(
      'SELECT id FROM syllabus WHERE course_code = ? AND teacher_id = ? ORDER BY create_time DESC LIMIT 1',
      [courseCode, teacherId]
    );

    if (syllabusResult.length === 0) {
      throw new Error('未找到相关大纲信息');
    }

    const syllabusId = syllabusResult[0].id;
    console.log('✓ 获取大纲ID:', syllabusId);

    // 检查是否已存在预习资料
    const [existingMaterial] = await pool.execute(
      'SELECT id FROM preview_materials WHERE syllabus_id = ? AND title_id = ? AND teacher_id = ?',
      [syllabusId, titleId, teacherId]
    );

    if (existingMaterial.length > 0) {
      // 更新现有预习资料
      await pool.execute(
        'UPDATE preview_materials SET content = ?, expected_duration = ?, update_time = CURRENT_TIMESTAMP WHERE id = ?',
        [previewContent, expectedDuration, existingMaterial[0].id]
      );
      console.log('✓ 更新预习资料成功');
    } else {
      // 插入新预习资料
      await pool.execute(
        'INSERT INTO preview_materials (syllabus_id, teacher_id, title_id, content, expected_duration, course_code, chapter_title) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [syllabusId, teacherId, titleId, previewContent, expectedDuration, courseCode, chapterTitle]
      );
      console.log('✓ 保存预习资料成功');
    }

    console.log('\n✓ 预习资料生成完成!');
    return res.json({
      success: true,
      data: previewContent
    });

  } catch (error) {
    console.log('\n❌ 生成预习资料失败:', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({
      success: false,
      message: error.message || '生成预习资料失败'
    });
  } finally {
    console.log('\n=== 预习资料生成结束 ===\n');
  }
};

// 获取预习资料
export const getPreviewMaterial = async (req, res) => {
  try {
    console.log('\n=== 获取预习资料 ===');
    const { courseCode, titleId } = req.params;
    const teacherId = req.user?.system_teacher_id;

    console.log('查询参数:', {
      courseCode,
      titleId,
      teacherId
    });

    // 验证参数
    if (!courseCode || !titleId) {
      console.log('❌ 参数验证失败: 缺少必要参数');
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 获取课程大纲内容以验证 titleId
    console.log('\n1. 验证章节...');
    const [syllabusRows] = await pool.execute(
      `SELECT content
       FROM syllabus 
       WHERE course_code = ? AND teacher_id = ?
       ORDER BY create_time DESC LIMIT 1`,
      [courseCode, teacherId]
    );

    if (syllabusRows.length === 0) {
      console.log('❌ 未找到课程大纲');
      return res.status(404).json({
        success: false,
        message: '未找到课程大纲'
      });
    }

    // 解析大纲内容并验证 titleId
    const syllabusContent = JSON.parse(syllabusRows[0].content);
    let isValidTitleId = false;
    
    function validateTitleId(obj) {
      for (const [, value] of Object.entries(obj)) {
        if (typeof value === 'object') {
          validateTitleId(value);
        } else if (value === titleId.toString()) {
          isValidTitleId = true;
          return;
        }
      }
    }
    
    validateTitleId(syllabusContent);
    
    if (!isValidTitleId) {
      console.log('❌ 未找到对应章节');
      return res.status(404).json({
        success: false,
        message: '未找到对应的章节'
      });
    }

    console.log('✓ 章节验证通过');

    // 获取预习资料
    const [materials] = await pool.execute(
      `SELECT 
        id, content, chapter_title, expected_duration, create_time, update_time
       FROM preview_materials 
       WHERE course_code = ? 
         AND title_id = ? 
         AND teacher_id = ?
       ORDER BY update_time DESC LIMIT 1`,
      [courseCode, titleId, teacherId]
    );

    if (materials.length === 0) {
      return res.json({
        success: true,
        data: null,
        message: '未找到预习资料'
      });
    }

    console.log('✓ 获取预习资料成功');

    res.json({
      success: true,
      data: materials[0]
    });

  } catch (error) {
    console.log('\n❌ 获取预习资料失败:', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({
      success: false,
      message: '获取预习资料失败'
    });
  } finally {
    console.log('\n=== 获取预习资料结束 ===\n');
  }
};

// 获取班级预习任务列表（包含完成率）
export const getClassPreviews = async (req, res) => {
  try {
    const { classId } = req.params;
    
    // 获取班级所有预习任务
    const [previews] = await pool.query(
      `SELECT pp.*, pm.title, pm.content, pm.expected_duration 
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       WHERE pp.class_id = ?
       ORDER BY pp.deadline DESC`,
      [classId]
    );

    // 获取班级总学生数（通过course_students表）
    const [classInfo] = await pool.query(
      `SELECT COUNT(DISTINCT cs.student_id) as total_students 
       FROM course_students cs
       JOIN preview_publish pp ON cs.course_code = pp.course_code
       WHERE pp.class_id = ? AND cs.status = 1`,
      [classId]
    );

    const totalStudents = classInfo[0].total_students || 0;

    // 获取每个预习任务的完成情况
    for (let preview of previews) {
      const [submissions] = await pool.query(
        `SELECT COUNT(*) as submitted_count
         FROM preview_student
         WHERE preview_publish_id = ? AND status = 1`,
        [preview.id]
      );

      const submittedCount = submissions[0].submitted_count || 0;
      
      // 计算完成率
      preview.completion = totalStudents > 0 ? Math.round((submittedCount / totalStudents) * 100) : 0;
      preview.submittedCount = submittedCount;
      preview.totalStudents = totalStudents;
    }

    res.json({
      success: true,
      data: previews
    });
  } catch (error) {
    console.error('获取班级预习任务列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取班级预习任务列表失败'
    });
  }
};

// 获取预习任务的学生提交情况
export const getPreviewSubmissions = async (req, res) => {
  try {
    const { previewId } = req.params;
    
    const [submissions] = await pool.query(
      `SELECT ps.*, s.name as student_name
       FROM preview_student ps
       LEFT JOIN students s ON ps.student_id = s.student_id
       WHERE ps.preview_publish_id = ?
       ORDER BY ps.submit_time DESC`,
      [previewId]
    );

    // 格式化提交数据
    const formattedSubmissions = submissions.map(submission => ({
      id: submission.id,
      student_id: submission.student_id,
      student_name: submission.student_name,
      status: submission.status,
      submit_time: submission.submit_time,
      content: submission.content
    }));

    res.json({
      success: true,
      data: formattedSubmissions
    });
  } catch (error) {
    console.error('获取预习任务提交情况失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习任务提交情况失败'
    });
  }
};

// 默认导出
export default {
  getClassPreviews,
  getPreviewSubmissions
};

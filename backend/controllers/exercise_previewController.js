import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 获取学生所在的班级列表
export const getStudentClasses = async (req, res) => {
  const studentId = req.params.studentId;
  console.log('接收到的学生ID:', studentId);
  
  // 检查学生ID是否需要格式化
  let formattedStudentId = studentId;
  if (!isNaN(studentId) && !studentId.startsWith('S')) {
    // 如果是纯数字，转换为S格式
    formattedStudentId = `S${studentId.padStart(5, '0')}`;
    console.log('格式化后的学生ID:', formattedStudentId);
  }
  
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 查询学生所在的班级 - 使用格式化后的ID和原始ID都查询一次
    const [classes] = await conn.execute(
      `SELECT cc.id as class_id, cc.class_name, cc.course_code, cc.semester
       FROM course_classes cc
       JOIN student_course_registration scr ON cc.id = scr.class_id
       WHERE (scr.student_id = ? OR scr.student_id = ?) AND scr.status = 1
       ORDER BY cc.semester DESC`,
      [formattedStudentId, studentId]
    );
    
    await conn.end();
    
    console.log('查询到的班级数量:', classes.length);
    if (classes.length === 0) {
      console.log('未查询到班级, 使用的学生ID:', formattedStudentId, studentId);
    }
    
    res.json({
      success: true,
      data: classes
    });
  } catch (error) {
    console.error('获取学生班级失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学生班级失败'
    });
  }
};

// 获取班级的章节列表（有预习题的章节）
export const getClassChapters = async (req, res) => {
  const { classId } = req.params;
  
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 先获取班级对应的课程代码和最新的大纲
    const [classRows] = await conn.execute(
      `SELECT cc.course_code, s.content 
       FROM course_classes cc 
       LEFT JOIN syllabus s ON s.course_code = cc.course_code 
       WHERE cc.id = ? 
       ORDER BY s.create_time DESC LIMIT 1`,
      [classId]
    );

    if (classRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }

    const courseCode = classRows[0].course_code;
    console.log("查询到课程代码:", courseCode);
    
    let syllabusContent = {};
    try {
      syllabusContent = JSON.parse(classRows[0].content || '{}');
      console.log("解析大纲内容成功");
    } catch (error) {
      console.error('解析大纲内容失败:', error);
      syllabusContent = {};
    }

    // 获取已发布预习题的章节ID列表（从publish_exercise表中获取）
    const [exerciseRows] = await conn.execute(
      `SELECT DISTINCT eb.title_id 
       FROM publish_exercise pe
       JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id 
       WHERE pe.class_id = ? AND pe.study_phase = 1`,
      [classId]
    );

    console.log("已发布预习题的章节ID:", exerciseRows.map(row => row.title_id));
    if (exerciseRows.length === 0) {
      console.log("未找到该班级已发布的预习题");
      return res.json({
        success: true,
        data: []
      });
    }
    
    const publishedTitleIds = new Set(exerciseRows.map(row => row.title_id.toString()));
    const chapters = [];

    // 如果大纲为空但有发布的习题，至少返回这些章节的基本信息
    if (Object.keys(syllabusContent).length === 0 && publishedTitleIds.size > 0) {
      console.log("大纲为空，但找到了已发布的章节，将返回基本章节信息");
      exerciseRows.forEach(row => {
        chapters.push({
          id: row.title_id.toString(),
          title: `章节 ${row.title_id}`
        });
      });
      
      console.log("返回基本章节列表:", chapters);
      await conn.end();
      
      return res.json({
        success: true,
        data: chapters
      });
    }

    // 辅助函数：从大纲内容中查找title_id对应的完整标题路径
    const findTitlePathInSyllabus = (titleId, syllabusObj, prefix = "") => {
      for (const [key, value] of Object.entries(syllabusObj)) {
        const currentPath = prefix ? `${prefix} - ${key}` : key;
        
        if (typeof value === 'string' && value === titleId.toString()) {
          // 只返回当前节点的名称，而不是完整路径
          return { found: true, path: key, fullPath: currentPath };
        } else if (typeof value === 'object' && value !== null) {
          const result = findTitlePathInSyllabus(titleId, value, currentPath);
          if (result.found) {
            return result;
          }
        }
      }
      
      return { found: false, path: "", fullPath: "" };
    };

    // 处理每个已发布的章节ID
    for (const titleId of publishedTitleIds) {
      // 在大纲中查找该title_id
      const { found, path, fullPath } = findTitlePathInSyllabus(titleId, syllabusContent);
      
      if (found) {
        chapters.push({
          id: titleId,
          title: path,        // 只使用最小单元名称
          fullPath: fullPath  // 保留完整路径用于其他目的
        });
      } else {
        // 如果在大纲中找不到，使用通用标题
        chapters.push({
          id: titleId,
          title: `章节 ${titleId}`,
          fullPath: `章节 ${titleId}`
        });
      }
    }

    // 按照 id 排序
    chapters.sort((a, b) => parseInt(a.id) - parseInt(b.id));
    
    console.log("返回章节列表:", chapters);
    await conn.end();

    res.json({
      success: true,
      data: chapters
    });
  } catch (error) {
    console.error('获取班级章节列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取班级章节列表失败'
    });
  }
};

// 获取章节预习题
export const getChapterPreview = async (req, res) => {
  const { classId, titleId } = req.params;
  const studentId = req.query.studentId;
  
  if (!studentId) {
    return res.status(400).json({
      success: false,
      message: '缺少学生ID参数'
    });
  }
  
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 查询该章节的预习题信息
    const [preview] = await conn.execute(
      `SELECT 
         COUNT(pe.exercise_id) as exercise_count,
         MAX(pe.created_at) as release_time,
         MAX(pe.deadline) as deadline
       FROM publish_exercise pe
       JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
       WHERE pe.class_id = ? AND eb.title_id = ? AND pe.study_phase = 1
       GROUP BY pe.class_id, eb.title_id`,
      [classId, titleId]
    );
    
    // 如果没有找到预习题
    if (preview.length === 0) {
      return res.json({
        success: true,
        data: []
      });
    }
    
    const previewData = preview[0];
    
    // 查询学生完成情况
    const [completion] = await conn.execute(
      `SELECT 
         COUNT(pe.id) as total,
         SUM(CASE WHEN ser.id IS NOT NULL THEN 1 ELSE 0 END) as completed
       FROM publish_exercise pe
       JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
       LEFT JOIN student_exercise_records ser 
         ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
       WHERE pe.class_id = ? AND eb.title_id = ? AND pe.study_phase = 1`,
      [studentId, classId, titleId]
    );
    
    if (completion.length > 0) {
      const stats = completion[0];
      previewData.completion_status = {
        total: parseInt(stats.total || 0),
        completed: parseInt(stats.completed || 0),
        percentage: stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0,
        is_completed: stats.total > 0 && stats.completed >= stats.total
      };
    } else {
      previewData.completion_status = {
        total: 0,
        completed: 0,
        percentage: 0,
        is_completed: false
      };
    }
    
    // 查询预习题内容
    const [exercises] = await conn.execute(
      `SELECT 
        pe.id as publish_id,
        eb.exercise_id,
        eb.title,
        eb.content,
        eb.difficulty,
        eb.question_type,
        pe.created_at as publish_time,
        pe.deadline,
        ser.student_answer,
        ser.is_correct
      FROM exercise_bank eb
      JOIN publish_exercise pe ON eb.exercise_id = pe.exercise_id
      LEFT JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
      WHERE pe.class_id = ? AND eb.title_id = ? AND pe.study_phase = 1
      ORDER BY pe.id ASC`,
      [studentId, classId, titleId]
    );
    
    previewData.exercises = exercises;
    
    await conn.end();
    
    res.json({
      success: true,
      data: [previewData] // 返回一个数组，以保持与作业接口的一致性
    });
  } catch (error) {
    console.error('获取章节预习题失败:', error);
    res.status(500).json({
      success: false,
      message: '获取章节预习题失败'
    });
  }
};

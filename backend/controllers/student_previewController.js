import pool from '../config/db.js';

// 获取学生所在的班级列表
export const getStudentClasses = async (req, res) => {
  try {
    console.log('\n=== 获取学生班级列表 ===');
    const studentId = req.user?.student_id;
    
    if (!studentId) {
      return res.status(404).json({
        success: false,
        message: '未找到学生信息'
      });
    }
    
    // 查询学生所在的班级
    const [classes] = await pool.execute(
      `SELECT 
        cc.id, 
        cc.class_name, 
        cc.course_code, 
        cc.teacher_id, 
        cc.semester, 
        cc.max_students,
        c.course_name
       FROM student_course_registration scr
       JOIN course_classes cc ON scr.class_id = cc.id
       JOIN courses c ON cc.course_code = c.course_code
       WHERE scr.student_id = ?
       AND scr.status = 1
       ORDER BY cc.created_at DESC`,
      [studentId]
    );

    console.log('学生班级查询结果:', { 
      班级数量: classes.length, 
      学生ID: studentId 
    });

    res.json({
      success: true,
      data: classes
    });

  } catch (error) {
    console.log('\n❌ 获取学生班级列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学生班级列表失败'
    });
  }
};

// 获取学生的预习资料列表
export const getStudentPreviewMaterials = async (req, res) => {
  try {
    console.log('\n=== 获取学生预习资料列表 ===');
    const studentId = req.user?.student_id;
    const { classId } = req.query;
    
    console.log('查询参数:', { studentId, classId });

    if (!studentId) {
      return res.status(400).json({
        success: false,
        message: '未找到学生ID'
      });
    }

    let query = `
      SELECT 
        pp.id, pp.title, pm.chapter_title, 
        pm.content, pp.create_time as publish_time, pp.deadline,
        cc.class_name, cc.course_code, c.course_name,
        CASE WHEN ps.id IS NOT NULL THEN 1 ELSE 0 END AS completed,
        ps.submit_time
      FROM preview_publish pp
      JOIN preview_materials pm ON pp.preview_material_id = pm.id
      JOIN course_classes cc ON pp.class_id = cc.id
      JOIN courses c ON cc.course_code = c.course_code
      JOIN student_course_registration scr ON cc.id = scr.class_id AND scr.student_id = ? AND scr.status = 1
      LEFT JOIN preview_student ps ON pp.id = ps.preview_publish_id AND ps.student_id = ?
      WHERE 1=1
    `;
    
    const params = [studentId, studentId];
    
    if (classId) {
      query += ' AND cc.id = ?';
      params.push(classId);
    }
    
    query += ' ORDER BY pp.create_time DESC';

    const [previews] = await pool.execute(query, params);

    console.log('学生预习资料查询结果:', { 
      资料数量: previews.length, 
      学生ID: studentId,
      班级ID: classId || '所有班级'
    });

    // 格式化数据以适应前端展示
    const formattedPreviews = previews.map(preview => ({
      id: preview.id,
      title: preview.title,
      chapter: preview.chapter_title,
      class: preview.class_name,
      courseName: preview.course_name,
      publishTime: new Date(preview.publish_time).toLocaleString(),
      deadline: new Date(preview.deadline).toLocaleString(),
      content: preview.content,
      completed: preview.completed === 1,
      submitTime: preview.submit_time ? new Date(preview.submit_time).toLocaleString() : null
    }));

    res.json({
      success: true,
      data: formattedPreviews
    });

  } catch (error) {
    console.log('\n❌ 获取学生预习资料列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学生预习资料列表失败'
    });
  }
};

// 获取预习资料详情
export const getPreviewDetail = async (req, res) => {
  try {
    console.log('\n=== 获取预习资料详情 ===');
    const { previewId } = req.params;
    const studentId = req.user?.student_id;
    
    console.log('查询参数:', { previewId, studentId });

    if (!previewId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 查询预习详情，包括验证学生是否有权访问
    const [previews] = await pool.execute(
      `SELECT 
        pp.id, pp.title, pm.chapter_title, 
        pm.content, pp.create_time as publish_time, pp.deadline,
        cc.class_name, cc.course_code
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       JOIN course_classes cc ON pp.class_id = cc.id
       JOIN student_course_registration scr ON cc.id = scr.class_id AND scr.student_id = ? AND scr.status = 1
       WHERE pp.id = ?`,
      [studentId, previewId]
    );

    if (previews.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到预习资料或无权访问'
      });
    }

    // 查询学生完成状态，包括观看总时长和次数
    const [status] = await pool.execute(
      `SELECT 
        id, submit_time, status, content AS answer,
        total_view_duration, total_view_count
       FROM preview_student
       WHERE preview_publish_id = ? AND student_id = ?`,
      [previewId, studentId]
    );

    // 获取总观看次数（不管是否已提交）
    const [viewCounts] = await pool.execute(
      `SELECT COUNT(*) as view_count 
       FROM preview_view_logs 
       WHERE preview_id = ? AND student_id = ?`,
      [previewId, studentId]
    );
    
    const totalViewCount = viewCounts[0].view_count || 0;

    const previewDetail = {
      ...previews[0],
      publish_time: new Date(previews[0].publish_time).toLocaleString(),
      deadline: new Date(previews[0].deadline).toLocaleString(),
      completed: status.length > 0 && status[0].status === 1,
      submitTime: status.length > 0 ? new Date(status[0].submit_time).toLocaleString() : null,
      answer: status.length > 0 ? status[0].answer : null,
      totalViewDuration: status.length > 0 ? status[0].total_view_duration : 0,
      totalViewCount: status.length > 0 ? status[0].total_view_count : totalViewCount
    };

    console.log('预习详情查询结果:', { 
      预习ID: previewId, 
      学生ID: studentId,
      状态: previewDetail.completed ? '已完成' : '未完成',
      总时长: previewDetail.totalViewDuration,
      总次数: previewDetail.totalViewCount
    });

    res.json({
      success: true,
      data: previewDetail
    });

  } catch (error) {
    console.log('\n❌ 获取预习资料详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习资料详情失败'
    });
  }
};

// 记录预习查看
export const recordPreviewView = async (req, res) => {
  try {
    const { previewId } = req.params;
    const { viewDuration } = req.body;
    const studentId = req.user?.student_id;

    // 验证查看时长
    if (!viewDuration || viewDuration <= 0) {
      return res.status(400).json({
        success: false,
        message: '查看时长必须大于0',
      });
    }

    // 验证预习ID和学生ID
    if (!previewId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数',
      });
    }

    console.log(`记录预习查看: 学生=${studentId}, 预习=${previewId}, 时长=${viewDuration}秒`);

    // 添加一条查看记录
    await pool.query(
      'INSERT INTO preview_view_logs (preview_id, student_id, view_duration) VALUES (?, ?, ?)',
      [previewId, studentId, viewDuration]
    );

    // 获取该学生对该预习的总查看次数和总时长
    const [totalStats] = await pool.query(
      'SELECT COUNT(*) as totalCount, SUM(view_duration) as totalDuration FROM preview_view_logs WHERE preview_id = ? AND student_id = ?',
      [previewId, studentId]
    );

    const totalViewCount = totalStats[0].totalCount || 0;
    const totalViewDuration = totalStats[0].totalDuration || 0;

    // 检查是否存在提交记录
    const [submissionRows] = await pool.query(
      'SELECT id FROM preview_student WHERE preview_publish_id = ? AND student_id = ?',
      [previewId, studentId]
    );

    // 如果有提交记录，更新总观看次数和总时长
    if (submissionRows.length > 0) {
      await pool.query(
        'UPDATE preview_student SET total_view_count = ?, total_view_duration = ? WHERE preview_publish_id = ? AND student_id = ?',
        [totalViewCount, totalViewDuration, previewId, studentId]
      );
    }

    return res.json({
      success: true,
      message: '记录预习查看成功',
      data: {
        totalViewCount,
        totalViewDuration
      }
    });
  } catch (error) {
    console.error('Error recording preview view:', error);
    return res.status(500).json({
      success: false,
      message: '记录预习查看失败',
      error: error.message,
    });
  }
};

// 获取学习时长
export const getStudyDuration = async (req, res) => {
  try {
    console.log('\n=== 获取预习学习时长 ===');
    const { previewId } = req.params;
    const studentId = req.user?.student_id;
    
    console.log('查询参数:', { previewId, studentId });

    if (!previewId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 检查预习是否存在且学生有权访问
    const [previews] = await pool.execute(
      `SELECT pp.id
       FROM preview_publish pp
       JOIN course_classes cc ON pp.class_id = cc.id
       JOIN student_course_registration scr ON cc.id = scr.class_id AND scr.student_id = ? AND scr.status = 1
       WHERE pp.id = ?`,
      [studentId, previewId]
    );

    if (previews.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到预习资料或无权访问'
      });
    }

    // 从preview_view_logs表中计算总学习时长
    const [durationData] = await pool.execute(
      `SELECT SUM(view_duration) as total_duration 
       FROM preview_view_logs 
       WHERE preview_id = ? AND student_id = ?`,
      [previewId, studentId]
    );

    const totalDuration = durationData[0].total_duration || 0;
    
    console.log('获取时长结果:', { 
      学生ID: studentId,
      预习ID: previewId,
      时长: totalDuration
    });
    
    res.json({
      success: true,
      data: {
        duration: totalDuration
      }
    });

  } catch (error) {
    console.log('\n❌ 获取学习时长失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习时长失败'
    });
  }
};

// 提交预习作业
export const submitPreview = async (req, res) => {
  try {
    console.log('\n=== 提交预习作业 ===');
    const { previewId } = req.params;
    const { content } = req.body;
    const studentId = req.user?.student_id;
    
    console.log('提交参数:', { previewId, studentId, contentLength: content?.length });

    if (!previewId || !studentId || !content) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 检查预习是否存在且学生有权访问
    const [previews] = await pool.execute(
      `SELECT pp.id, pp.deadline, cc.id AS class_id
       FROM preview_publish pp
       JOIN course_classes cc ON pp.class_id = cc.id
       JOIN student_course_registration scr ON cc.id = scr.class_id AND scr.student_id = ? AND scr.status = 1
       WHERE pp.id = ?`,
      [studentId, previewId]
    );

    if (previews.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到预习资料或无权访问'
      });
    }

    // 检查是否已过截止日期
    const deadline = new Date(previews[0].deadline);
    const now = new Date();
    if (now > deadline) {
      return res.status(400).json({
        success: false,
        message: '已超过提交截止时间'
      });
    }

    // 从preview_view_logs表计算总学习时长
    const [durationData] = await pool.execute(
      `SELECT SUM(view_duration) as total_duration 
       FROM preview_view_logs 
       WHERE preview_id = ? AND student_id = ?`,
      [previewId, studentId]
    );
    
    const totalDuration = durationData[0].total_duration || 0;
    
    // 获取总观看次数
    const [viewCounts] = await pool.execute(
      `SELECT COUNT(*) as view_count 
       FROM preview_view_logs 
       WHERE preview_id = ? AND student_id = ?`,
      [previewId, studentId]
    );
    
    const totalViewCount = viewCounts[0].view_count || 0;

    // 检查是否已经提交过
    const [existing] = await pool.execute(
      `SELECT id FROM preview_student WHERE preview_publish_id = ? AND student_id = ?`,
      [previewId, studentId]
    );

    let result;
    if (existing.length > 0) {
      // 更新现有提交，同时更新学习时长和观看次数
      [result] = await pool.execute(
        `UPDATE preview_student
         SET status = 1, submit_time = NOW(), content = ?, 
             total_view_duration = ?, total_view_count = ?
         WHERE preview_publish_id = ? AND student_id = ?`,
        [content, totalDuration, totalViewCount, previewId, studentId]
      );
      
      console.log('更新提交结果:', { 
        类型: '更新', 
        affectedRows: result.affectedRows,
        总时长: totalDuration,
        总观看次数: totalViewCount
      });
      
      res.json({
        success: true,
        message: '预习更新成功'
      });
    } else {
      // 创建新提交，包含学习时长和观看次数
      [result] = await pool.execute(
        `INSERT INTO preview_student 
         (preview_publish_id, student_id, status, submit_time, content, total_view_duration, total_view_count)
         VALUES (?, ?, 1, NOW(), ?, ?, ?)`,
        [previewId, studentId, content, totalDuration, totalViewCount]
      );
      
      console.log('创建提交结果:', { 
        类型: '新建', 
        insertId: result.insertId,
        总时长: totalDuration,
        总观看次数: totalViewCount
      });
      
      res.json({
        success: true,
        message: '预习提交成功'
      });
    }
  } catch (error) {
    console.log('\n❌ 提交预习作业失败:', error);
    res.status(500).json({
      success: false,
      message: '提交预习作业失败'
    });
  }
};

// 更新学习时长 - 保留函数以兼容前端旧代码
export const updateStudyDuration = async (req, res) => {
  try {
    console.log('\n=== 更新预习学习时长（已废弃） ===');
    res.json({
      success: true,
      message: '操作已完成'
    });
  } catch (error) {
    console.log('\n❌ 更新学习时长失败:', error);
    res.json({
      success: true,
      message: '操作完成'
    });
  }
};

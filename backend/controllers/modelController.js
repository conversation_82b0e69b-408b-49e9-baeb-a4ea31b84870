import pool from '../config/db.js';
import modelService from '../config/modelService.js';

// 获取所有模型配置
export const getAllModels = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      'SELECT * FROM model_configs ORDER BY id'
    );
    
    return res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取模型配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取模型配置失败: ' + error.message
    });
  }
};

// 获取单个模型配置
export const getModelById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const [rows] = await pool.execute(
      'SELECT * FROM model_configs WHERE id = ?',
      [id]
    );
    
    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的模型配置'
      });
    }
    
    return res.json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    console.error('获取模型配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取模型配置失败: ' + error.message
    });
  }
};

// 创建新的模型配置
export const createModel = async (req, res) => {
  try {
    const { name, provider, api_key, app_id, base_url, usage_scenario, status } = req.body;
    
    // 验证必填字段
    if (!name || !provider || !api_key || !base_url || !usage_scenario) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }
    
    // 插入新记录
    const [result] = await pool.execute(
      'INSERT INTO model_configs (name, provider, api_key, app_id, base_url, usage_scenario, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [name, provider, api_key, app_id || null, base_url, usage_scenario, status === undefined ? 1 : status]
    );
    
    // 刷新模型配置缓存
    await modelService.refreshModelConfigs();
    
    return res.status(201).json({
      success: true,
      data: {
        id: result.insertId,
        name,
        provider,
        api_key: '******', // 隐藏API密钥
        app_id,
        base_url,
        usage_scenario,
        status: status === undefined ? 1 : status
      },
      message: '模型配置创建成功'
    });
  } catch (error) {
    console.error('创建模型配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '创建模型配置失败: ' + error.message
    });
  }
};

// 更新模型配置
export const updateModel = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, provider, api_key, app_id, base_url, usage_scenario, status } = req.body;
    
    // 验证必填字段
    if (!name || !provider || !base_url || !usage_scenario) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }
    
    // 检查是否存在
    const [existingRows] = await pool.execute(
      'SELECT * FROM model_configs WHERE id = ?',
      [id]
    );
    
    if (existingRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的模型配置'
      });
    }
    
    // 构建更新SQL
    let sql = 'UPDATE model_configs SET name = ?, provider = ?, base_url = ?, usage_scenario = ?, status = ?';
    let params = [name, provider, base_url, usage_scenario, status];
    
    // 如果提供了API密钥，则更新
    if (api_key) {
      sql += ', api_key = ?';
      params.push(api_key);
    }
    
    // 处理app_id字段
    sql += ', app_id = ?';
    params.push(app_id || null);
    
    // 添加WHERE条件
    sql += ' WHERE id = ?';
    params.push(id);
    
    // 执行更新
    await pool.execute(sql, params);
    
    // 刷新模型配置缓存
    await modelService.refreshModelConfigs();
    
    return res.json({
      success: true,
      message: '模型配置更新成功',
      data: {
        id,
        name,
        provider,
        api_key: '******', // 隐藏API密钥
        app_id,
        base_url,
        usage_scenario,
        status
      }
    });
  } catch (error) {
    console.error('更新模型配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '更新模型配置失败: ' + error.message
    });
  }
};

// 删除模型配置
export const deleteModel = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查是否存在
    const [existingRows] = await pool.execute(
      'SELECT * FROM model_configs WHERE id = ?',
      [id]
    );
    
    if (existingRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的模型配置'
      });
    }
    
    // 执行删除
    await pool.execute(
      'DELETE FROM model_configs WHERE id = ?',
      [id]
    );
    
    // 刷新模型配置缓存
    await modelService.refreshModelConfigs();
    
    return res.json({
      success: true,
      message: '模型配置删除成功'
    });
  } catch (error) {
    console.error('删除模型配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '删除模型配置失败: ' + error.message
    });
  }
};

// 测试模型连接
export const testModelConnection = async (req, res) => {
  try {
    const { provider, api_key, app_id, base_url } = req.body;
    
    // 根据不同的提供商进行不同的测试
    let testResult = {
      success: false,
      message: '连接测试失败',
      responseTime: 0
    };
    
    const startTime = Date.now();
    
    switch (provider) {
      case 'volcano':
      case 'deepseek':
      case 'moonshot':
        // OpenAI兼容API测试
        const OpenAI = (await import('openai')).default;
        const client = new OpenAI({
          apiKey: api_key,
          baseURL: base_url
        });
        
        const response = await client.chat.completions.create({
          model: provider === 'deepseek' ? 'deepseek-chat' : 'volc-ln-v2-mix',
          messages: [{ 
            role: 'user', 
            content: '你好，这是一个API连接测试。请回复"连接成功"。' 
          }],
          max_tokens: 10
        });
        
        testResult.success = true;
        testResult.message = '连接成功';
        testResult.modelResponse = response.choices[0]?.message?.content || '无响应';
        break;
        
      case 'xfyun':
        // 讯飞API测试
        // 只验证API密钥格式，不实际发起网络请求
        if (api_key && app_id && api_key.length > 10 && app_id.length > 5) {
          testResult.success = true;
          testResult.message = 'API密钥格式有效';
          testResult.modelResponse = '讯飞API密钥验证成功';
        } else {
          testResult.success = false;
          testResult.message = 'API密钥格式无效';
        }
        break;
        
      default:
        testResult.success = false;
        testResult.message = `不支持的提供商: ${provider}`;
    }
    
    // 计算响应时间
    testResult.responseTime = Date.now() - startTime;
    
    return res.json({
      success: true,
      data: testResult
    });
  } catch (error) {
    console.error('测试模型连接失败:', error);
    return res.status(500).json({
      success: false,
      message: '测试失败: ' + error.message,
      data: {
        success: false,
        responseTime: 0,
        error: error.message
      }
    });
  }
};

export default {
  getAllModels,
  getModelById,
  createModel,
  updateModel,
  deleteModel,
  testModelConnection
}; 
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import multer from 'multer';
import axios from 'axios';
import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';
// 使用 modelService 来获取 API 客户端
import modelService from '../config/modelService.js';
import config from '../config/config.js';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置临时文件存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const tempDir = path.join(__dirname, '../temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    cb(null, tempDir);
  },
  filename: function (req, file, cb) {
    const uniqueFilename = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueFilename);
  }
});

const upload = multer({ storage: storage });

// 处理音频转录请求
export const transcribeAudio = async (req, res) => {
  try {
    // 使用 multer 处理文件上传
    upload.single('audioFile')(req, res, async function (err) {
      if (err) {
        console.error('文件上传错误:', err);
        return res.status(400).json({
          success: false,
          message: '文件上传失败'
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '未找到音频文件'
        });
      }

      const filePath = req.file.path;
      const userId = req.user.id;

      try {
        // 根据配置选择不同的转录服务
        let transcriptionResult;
        let duration = 0;

        // 尝试使用讯飞 API 进行转录
        try {
          // 使用 Python 脚本调用讯飞 API
          const { spawn } = await import('child_process');

          // 生成转录文件路径
          const transcriptPath = path.join(path.dirname(filePath), `${path.basename(filePath, path.extname(filePath))}_transcript.txt`);

          // 调用 Python 脚本进行转录
          const pythonProcess = spawn('python', [
            path.join(__dirname, '../scripts/audio_processor.py'),
            'transcribe',
            filePath,
            transcriptPath
          ], {
            env: {
              ...process.env,
              PYTHONIOENCODING: 'utf-8',
            },
            timeout: 600000 // 设置10分钟超时
          });

          let errorData = '';
          let outputData = '';

          pythonProcess.stdout.on('data', (data) => {
            outputData += data.toString();
            console.log(`Python输出: ${data.toString()}`);
          });

          pythonProcess.stderr.on('data', (data) => {
            errorData += data.toString();
            console.error(`Python错误: ${data.toString()}`);
          });

          // 等待 Python 进程完成
          const exitCode = await new Promise((resolve) => {
            pythonProcess.on('close', resolve);
          });

          if (exitCode !== 0) {
            console.error(`Python进程退出，状态码 ${exitCode}, 错误: ${errorData}`);
            throw new Error(`音频转录失败: ${errorData}`);
          }

          // 读取转录结果
          if (fs.existsSync(transcriptPath)) {
            transcriptionResult = fs.readFileSync(transcriptPath, 'utf8');

            // 估算时长
            duration = Math.ceil(transcriptionResult.length * 0.3); // 粗略估计：每3个字符1秒
          } else {
            throw new Error('转录完成但文件不存在');
          }
        } catch (error) {
          console.error('使用讯飞 API 转录失败:', error);

          // 在转录失败时使用模拟数据
          console.log('使用高等数学微积分相关的模拟转录数据');

          // 模拟转录文本 - 高等数学微积分课堂的自然转录形式
          transcriptionResult = `同学们好！今天我们要开始微积分的新章节——导数应用。还记得上节课我们讲的导数基本概念吗？有谁能简单复述一下？

导数是函数变化率，表示曲线在某点的斜率！

很好！还有人补充吗？

导数的几何意义是切线斜率，物理意义是瞬时变化率。比如位移对时间的导数就是速度。

非常棒！看来大家对基础概念掌握得很好。今天我要用一个有趣的例子开始我们的课程——过山车设计问题！

(讨论声)

想象你们是游乐园的工程师，需要设计一个过山车轨道。这条轨道需要有刺激的下坡，但又不能太陡峭导致乘客不适。有谁知道这和导数有什么关系吗？

是不是和加速度有关？加速度太大会让人感觉不舒服。

答对了！加速度是速度的导数，而速度是位置的导数。所以我们实际上是在控制轨道曲线的二阶导数！现在，假设过山车轨道的高度函数是h(x) = 50 - 0.01x² + 0.000001x⁴，其中x是水平距离，单位是米。谁能帮我计算在x=100处的斜率？

我来试试！需要求h'(x)，然后代入x=100...

很好，你能在黑板上给大家演示计算过程吗？

首先，h'(x) = -0.02x + 0.000004x³...然后代入x=100...得到h'(100) = -2 + 4 = 2。所以在x=100米处，轨道的斜率是2，也就是说每前进1米，高度上升2米，坡度相当陡啊！

等等，这坡度也太大了吧？这不是几乎垂直了吗？

确实，斜率为2意味着大约63度的倾角，这已经非常陡了。不过实际设计中，我们还需要考虑二阶导数，也就是加速度...

如果我们想让乘客体验到"失重感"，是不是应该设计一个地方让加速度正好抵消重力加速度？

非常好的问题！这正是过山车最刺激部分的设计原理。当向下的加速度接近9.8米/秒²时，乘客会感到短暂的"失重"。让我们计算一下在什么位置会出现这种情况...

我在上个周末去游乐园玩了过山车，那种失重感太刺激了！现在明白是怎么回事了。

这就是数学在工程中的应用魅力。

老师，我有个问题。如果过山车轨道是三次函数，那它的加速度不就是常数了吗？这样岂不是整个过程加速度都一样？

好问题！正因如此，实际的过山车轨道设计通常使用更高次的函数或分段函数。让我们一起推导一下...

(小声讨论声)

同学们，先别着急讨论。我们来做个小测试，看看大家对导数应用的理解。拿出你们的手机，扫描屏幕上的二维码进入课堂小程序。

第一题：一个物体沿直线运动，其位置函数是s(t) = t³ - 6t² + 9t，求t=2时的瞬时速度和加速度。

(讨论声)

我算出来速度是-3，加速度是6，对吗？

让我们看看大家的答案分布...(显示结果) 有75%的同学答对了！正确答案确实是v(2)=-3，a(2)=6。你能解释一下你是怎么得到这个结果的吗？

位置函数s(t)对时间t求导，得到速度函数v(t)=3t²-12t+9，再代入t=2，得到v(2)=3×4-12×2+9=12-24+9=-3。速度是负数，说明物体在向反方向运动。再求导得到加速度a(t)=6t-12，代入t=2得到a(2)=6×2-12=0。啊，我算错了，应该是0而不是6。

没关系，计算错误是常有的。谁能帮忙指出错误在哪里？

我觉得在最后一步计算错了。a(2)=6×2-12=12-12=0。所以t=2时的加速度是0，表示物体虽然在运动，但速度不变。

完全正确！这个例子告诉我们什么？

在t=2时，物体速度为负，但加速度为0，说明物体正在以恒定速度向反方向运动。

非常好的分析！现在，让我们再看一个实际应用——经济学中的边际成本。假设一家工厂生产x件产品的总成本函数是C(x) = 0.01x² + 20x + 500。谁能告诉我边际成本的含义？

边际成本是总成本函数的导数，表示多生产一件产品带来的额外成本。

太棒了！那么这个例子中的边际成本函数是什么？

C'(x) = 0.02x + 20。

正确！所以当已经生产了100件产品时，再多生产一件的边际成本是？

22元！0.02×100+20=22！

大家反应真快！没错，是22元。这就是为什么规模越大的生产，边际成本可能会上升——这被称为"规模不经济"。

那什么情况下会出现"规模经济"？就是生产越多反而边际成本下降？

另一个好问题！这需要成本函数中含有负的二次项或者其他形式。例如...

(下课铃响)

看来时间到了。下节课我们将继续讨论导数在优化问题中的应用，包括最大值和最小值的求解。记得完成课后习题1到15题。对了，还有，小组项目的题目我已经发到了学习平台上，请在下周三之前组队并提交选题申请。

小组项目可以选择游戏设计中的数学应用吗？我对游戏物理引擎很感兴趣！

当然可以！这是个很好的选题方向。其他同学也可以选择自己感兴趣的应用领域，只要和我们学过的微积分知识相关。

如果有问题随时来办公室找我，或者在学习平台上提问。下课！

谢谢老师！`;

          // 模拟时长
          duration = 1200; // 模拟20分钟的音频
        }

        // 如果需要保存到文件，可以取消下面的注释
        /*
        const transcriptPath = path.join(path.dirname(filePath), `${path.basename(filePath, path.extname(filePath))}_transcript.txt`);
        try {
          fs.writeFileSync(transcriptPath, transcriptionResult, 'utf8');
          console.log(`模拟转录已保存到: ${transcriptPath}`);
        } catch (writeError) {
          console.error(`写入模拟转录失败:`, writeError);
        }
        */

        // 保存转录结果到数据库
        const transcriptionId = uuidv4();
        const connection = await mysql.createConnection(dbConfig);
        try {
          await connection.execute(
            `INSERT INTO audio_transcriptions (id, system_teacher_id, transcript, duration, created_at)
             VALUES (?, ?, ?, ?, NOW())`,
            [transcriptionId, userId, transcriptionResult, duration]
          );
        } finally {
          await connection.end();
        }

        // 删除临时文件
        fs.unlinkSync(filePath);

        return res.json({
          success: true,
          data: {
            transcript: transcriptionResult,
            duration: duration,
            id: transcriptionId
          }
        });
      } catch (error) {
        console.error('音频转录错误:', error);

        // 删除临时文件
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }

        return res.status(500).json({
          success: false,
          message: '音频转录失败: ' + (error.message || '未知错误')
        });
      }
    });
  } catch (error) {
    console.error('处理音频转录请求错误:', error);
    return res.status(500).json({
      success: false,
      message: '处理请求失败'
    });
  }
};

// 生成音频笔记
export const generateSummary = async (req, res) => {
  try {
    const { transcript, title } = req.body;
    const userId = req.user.id;

    if (!transcript) {
      return res.status(400).json({
        success: false,
        message: '缺少转录文本'
      });
    }

    // 直接使用模拟数据生成笔记
    console.log('使用模拟笔记数据');

    // 生成一个基于转录文本的摘要
    const summaryIntro = `# ${title || '教学建议'}

`;

    // 提取关键句子
    const sentences = transcript.split(/[\u3002\uff01\uff1f.!?]\s*/).filter(s => s.length > 0);

    // 生成模拟笔记内容 - 教学建议格式
    let summary = summaryIntro;
    
    summary += `## 教学优势分析

• 课堂互动性强，通过引导性提问激发学生思考和参与
• 将抽象数学概念与现实应用结合（过山车设计、经济学边际成本）
• 使用即时测评工具检验学生理解程度
• 鼓励学生主动发言，营造良好讨论氛围
• 针对学生错误采用同伴纠错方式，培养批判性思维
• 安排小组项目，鼓励将所学知识应用到感兴趣领域

## 学生学习情况

• 学生基础概念掌握良好，能够正确描述导数的物理和几何含义
• 学生提问质量高，关注应用场景（如"失重感"和三次函数特性）
• 75%的学生能正确计算微分问题，但仍有计算错误
• 学生能建立微积分与其他学科的联系（物理、工程、经济学）
• 对实际应用项目表现出强烈兴趣

## 教学改进建议

1. **视觉辅助工具**：引入动态图形软件演示导数概念，帮助学生可视化理解微积分。

2. **计算能力强化**：针对25%计算错误率，设计针对性练习，重点关注求导法则应用和步骤推导。

3. **分层教学实施**：设计基础、中等和挑战三级难度问题，满足不同学习能力学生需求。

4. **小组讨论结构化**：引入"思考-配对-分享"等技术，让讨论更有成效。

5. **项目评价标准化**：为小组项目制定清晰评价标准，包括概念理解、应用创新和表达能力等维度。

6. **学习资源丰富化**：开发辅助学习资源，如计算习题集、应用案例库和自测题库。

## 后续教学规划

• 针对下节课优化问题，准备合适的经济学和工程学实例
• 建立微积分应用案例库，分门别类整理各学科中的实际应用
• 优化课前预习材料，引导学生带着问题来上课
• 设计多层次课后作业，包括基础题、应用题和拓展题

这些建议旨在进一步提升课堂教学效果，帮助学生更好地理解微积分概念及其应用。课堂中学生的积极参与表明现有教学方法已有良好效果，上述建议可在此基础上进一步优化学习体验和教学成效。`;

    // 可以将生成的笔记保存到文件，但这里不是必要的
    /*
    const summaryId = uuidv4();
    const summaryPath = path.join(__dirname, '../uploads/summaries', `${summaryId}.txt`);

    // 确保目录存在
    const summaryDir = path.dirname(summaryPath);
    if (!fs.existsSync(summaryDir)) {
      fs.mkdirSync(summaryDir, { recursive: true });
    }

    try {
      fs.writeFileSync(summaryPath, summary, 'utf8');
      console.log(`模拟笔记已保存到: ${summaryPath}`);
    } catch (writeError) {
      console.error(`写入模拟笔记失败:`, writeError);
    }
    */

    // 保存生成的笔记到数据库
    const summaryId = uuidv4();
    const connection = await mysql.createConnection(dbConfig);
    try {
      await connection.execute(
        `INSERT INTO audio_summaries (id, system_teacher_id, title, transcript, summary, created_at)
         VALUES (?, ?, ?, ?, ?, NOW())`,
        [summaryId, userId, title || '录音笔记', transcript, summary]
      );
    } finally {
      await connection.end();
    }

    return res.json({
      success: true,
      data: {
        summary: summary,
        id: summaryId
      }
    });
  } catch (error) {
    console.error('生成音频笔记错误:', error);
    return res.status(500).json({
      success: false,
      message: '生成笔记失败: ' + (error.message || '未知错误')
    });
  }
};

// 保存音频笔记
export const saveNote = async (req, res) => {
  try {
    const { title, content, transcript, duration } = req.body;
    const userId = req.user.id;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: '缺少笔记内容'
      });
    }

    // 保存笔记到数据库
    const noteId = uuidv4();
    const connection = await mysql.createConnection(dbConfig);
    try {
      await connection.execute(
        `INSERT INTO notes (id, system_teacher_id, title, content, source_type, created_at)
         VALUES (?, ?, ?, ?, 'audio', NOW())`,
        [noteId, userId, title || '录音笔记', content]
      );

      // 如果有转录文本，保存关联的转录信息
      if (transcript) {
        const transcriptionId = uuidv4();
        await connection.execute(
          `INSERT INTO audio_transcriptions (id, system_teacher_id, transcript, duration, note_id, created_at)
           VALUES (?, ?, ?, ?, ?, NOW())`,
          [transcriptionId, userId, transcript, duration || 0, noteId]
        );
      }
    } finally {
      await connection.end();
    }

    return res.json({
      success: true,
      data: {
        id: noteId,
        message: '笔记保存成功'
      }
    });
  } catch (error) {
    console.error('保存音频笔记错误:', error);
    return res.status(500).json({
      success: false,
      message: '保存笔记失败: ' + (error.message || '未知错误')
    });
  }
};

import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET;

/**
 * 获取下一个可用的系统教师ID
 * @param {object} connection 数据库连接对象
 * @returns {Promise<string>} 返回下一个可用的系统教师ID
 */
async function getNextSystemTeacherId(connection) {
  try {
    // 查询最大的系统教师ID
    const [rows] = await connection.execute(
      'SELECT system_teacher_id FROM teaching_assistant ORDER BY system_teacher_id DESC LIMIT 1'
    );

    let nextId = 'T00001'; // 默认起始ID

    if (rows.length > 0) {
      // 如果已有记录，则获取当前最大ID并加1
      const currentMaxId = rows[0].system_teacher_id;
      const numericPart = parseInt(currentMaxId.substring(1), 10);
      const nextNumericPart = numericPart + 1;
      nextId = `T${nextNumericPart.toString().padStart(5, '0')}`;
    }

    return nextId;
  } catch (error) {
    console.error('获取下一个系统教师ID失败:', error);
    throw error;
  }
}

const register = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const { 
      school_teacher_id,
      name, 
      phone, 
      password
    } = req.body;

    // 验证必要字段
    if (!school_teacher_id || !name || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供必要的注册信息（教师工号、姓名、密码）'
      });
    }

    // 检查学校教师工号是否已存在
    const [existingSchoolId] = await connection.execute(
      'SELECT id FROM teaching_assistant WHERE BINARY school_teacher_id = ?',
      [school_teacher_id]
    );

    if (existingSchoolId.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该教师工号已被注册'
      });
    }

    // 获取下一个可用的系统教师ID
    const system_teacher_id = await getNextSystemTeacherId(connection);
    
    // 设置默认头像路径
    const defaultAvatar = '/avatar/avatar-0001.jpg';

    // 创建新用户
    const [result] = await connection.execute(
      `INSERT INTO teaching_assistant 
       (system_teacher_id, school_teacher_id, name, phone, password, avatar) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [system_teacher_id, school_teacher_id, name, phone, password, defaultAvatar]
    );

    // 生成token
    const token = jwt.sign(
      {
        id: result.insertId,
        system_teacher_id,
        school_teacher_id,
        name,
        avatar: defaultAvatar,
        role: 'teacher'
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      message: '注册成功',
      data: {
        token,
        userInfo: {
          id: result.insertId,
          system_teacher_id,
          school_teacher_id,
          name,
          avatar: defaultAvatar,
          role: 'teacher'
        }
      }
    });

  } catch (error) {
    console.error('注册失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '注册失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

const login = async (req, res) => {
  const { username, password } = req.body;
  
  if (!username || !password) {
    return res.status(400).json({
      success: false,
      message: '请提供用户名和密码'
    });
  }

  let connection;
  try {
    console.log('登录请求:', { username });
    
    connection = await mysql.createConnection(dbConfig);
    
    // 查询用户 (支持使用系统ID或教师工号登录)
    const [users] = await connection.execute(
      `SELECT id, system_teacher_id, school_teacher_id, name, phone, avatar, password,
              create_time, update_time
       FROM teaching_assistant 
       WHERE (BINARY system_teacher_id = ? OR BINARY school_teacher_id = ?) AND password = ?`,
      [username, username, password]
    );

    if (users.length === 0) {
      console.log('用户不存在或密码错误:', username);
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const user = users[0];
    console.log('用户登录成功:', { id: user.id, system_teacher_id: user.system_teacher_id });

    // 生成 token
    const token = jwt.sign(
      {
        id: user.id,
        system_teacher_id: user.system_teacher_id,
        school_teacher_id: user.school_teacher_id,
        name: user.name,
        role: 'teacher'
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // 返回用户信息和 token（不返回密码）
    delete user.password;
    res.json({
      success: true,
      data: {
        token,
        userInfo: {
          ...user,
          role: 'teacher'
        }
      }
    });

  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

const updateProfile = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const { id, name, phone, password, avatar } = req.body;

    let sql = 'UPDATE teaching_assistant SET ';
    const params = [];
    const updates = [];

    if (name) {
      updates.push('name = ?');
      params.push(name);
    }
    if (phone) {
      updates.push('phone = ?');
      params.push(phone);
    }
    if (password) {
      updates.push('password = ?');
      params.push(password);
    }
    if (avatar) {
      updates.push('avatar = ?');
      params.push(avatar);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有要更新的信息'
      });
    }

    sql += updates.join(', ') + ' WHERE id = ?';
    params.push(id);

    const [result] = await connection.execute(sql, params);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 获取更新后的用户信息（不包含密码）
    const [users] = await connection.execute(
      `SELECT id, system_teacher_id, school_teacher_id, name, phone, avatar,
              create_time, update_time 
       FROM teaching_assistant WHERE id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '个人信息更新成功',
      data: users[0]
    });

  } catch (error) {
    console.error('更新个人信息失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '更新失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

const registerStudent = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const { studentId, name, classId, password } = req.body;

    // 检查学号是否已存在
    const [existing] = await connection.execute(
      'SELECT id FROM students WHERE BINARY student_id = ?',
      [studentId]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该学号已被注册'
      });
    }

    // 插入学生信息
    await connection.execute(
      'INSERT INTO students (student_id, name, class_id, password) VALUES (?, ?, ?, ?)',
      [studentId, name, classId, password]
    );

    res.json({
      success: true,
      message: '注册成功'
    });

  } catch (error) {
    console.error('注册失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '注册失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

/**
 * 上传用户头像
 */
const uploadAvatar = async (req, res) => {
  // 确保文件存在
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: '请上传头像文件'
    });
  }

  let connection;
  try {
    const userId = req.user.id;
    const avatarPath = `/avatar/${req.file.filename}`;
    
    connection = await mysql.createConnection(dbConfig);
    
    // 更新用户头像路径
    const [result] = await connection.execute(
      'UPDATE teaching_assistant SET avatar = ? WHERE id = ?',
      [avatarPath, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '头像上传成功',
      data: {
        avatar: avatarPath
      }
    });
  } catch (error) {
    console.error('头像上传失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '头像上传失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

export { register, login, updateProfile, uploadAvatar };
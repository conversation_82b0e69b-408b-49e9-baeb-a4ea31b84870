import pool from '../config/db.js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// 获取学生列表
export const getStudents = async (req, res) => {
  const { classId } = req.query;
  let query = `
    SELECT s.*, c.class_name 
    FROM students s
    LEFT JOIN class_info c ON s.class_id = c.id
  `;
  
  const params = [];
  if (classId) {
    query += ' WHERE s.class_id = ?';
    params.push(classId);
  }
  
  try {
    const [rows] = await pool.query(query, params);
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取学生列表失败:', error);
    res.status(500).json({ success: false, message: '获取学生列表失败' });
  }
};

// 获取教师任课班级的学生
export const getTeacherStudents = async (req, res) => {
  const teacherId = req.params.teacherId;
  const { classId } = req.query;
  
  try {
    let query = `
      SELECT s.*, c.class_name, tcr.subject
      FROM students s
      JOIN class_info c ON s.class_id = c.id
      JOIN teacher_class_relation tcr ON c.id = tcr.class_id
      WHERE tcr.teacher_id = ?
    `;
    
    const params = [teacherId];
    
    if (classId) {
      query += ' AND s.class_id = ?';
      params.push(classId);
    }
    
    const [rows] = await pool.query(query, params);
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取学生列表失败:', error);
    res.status(500).json({ success: false, message: '获取学生列表失败' });
  }
};

// 添加学生（只能添加到自己任教的班级）
export const addStudent = async (req, res) => {
  const { student_id, name, class_id, password, phone, email } = req.body;
  const teacherId = req.user?.id;
  
  if (!teacherId) {
    return res.status(401).json({ success: false, message: '请先登录' });
  }
  
  if (!student_id || !name || !class_id || !password) {
    return res.status(400).json({ success: false, message: '学号、姓名、班级和密码不能为空' });
  }
  
  try {
    // 检查是否教授该班级
    const [classPermission] = await pool.query(
      'SELECT 1 FROM teacher_class_relation WHERE teacher_id = ? AND class_id = ?', 
      [teacherId, class_id]
    );
    
    if (classPermission.length === 0) {
      return res.status(403).json({ 
        success: false, 
        message: '您没有权限向此班级添加学生' 
      });
    }
    
    // 检查学号是否已存在
    const [existing] = await pool.query('SELECT id FROM students WHERE student_id = ?', [student_id]);
    if (existing.length > 0) {
      return res.status(400).json({ success: false, message: '学号已存在' });
    }
    
    const [result] = await pool.query(
      'INSERT INTO students (student_id, name, class_id, password, phone, email) VALUES (?, ?, ?, ?, ?, ?)',
      [student_id, name, class_id, password, phone || null, email || null]
    );
    
    res.status(201).json({ 
      success: true, 
      message: '学生添加成功',
      data: { id: result.insertId, student_id, name, class_id }
    });
  } catch (error) {
    console.error('添加学生失败:', error);
    res.status(500).json({ success: false, message: '添加学生失败' });
  }
};

// 删除学生
export const deleteStudent = async (req, res) => {
  const studentId = req.params.id;
  
  try {
    // 删除学生的练习记录和学习进度
    await pool.query('DELETE FROM student_exercise_records WHERE student_id = ?', [studentId]);
    await pool.query('DELETE FROM learning_progress WHERE student_id = ?', [studentId]);
    
    // 删除学生
    await pool.query('DELETE FROM students WHERE id = ?', [studentId]);
    
    res.json({ success: true, message: '学生删除成功' });
  } catch (error) {
    console.error('删除学生失败:', error);
    res.status(500).json({ success: false, message: '删除学生失败' });
  }
};

// 生成学生ID
const generateStudentId = async () => {
  const [result] = await pool.execute(
    'SELECT student_id FROM students ORDER BY student_id DESC LIMIT 1'
  );
  
  if (result.length === 0) {
    return 'S001';  // 改为3位数字格式
  }
  
  const lastId = result[0].student_id;
  const numPart = parseInt(lastId.replace(/[^0-9]/g, '')); // 提取所有数字
  const newNumPart = (numPart + 1).toString().padStart(3, '0'); // 使用3位数字
  return `S${newNumPart}`;
};

// 学生注册
export const register = async (req, res) => {
  const { name, password, phone, email } = req.body;
  
  if (!name || !password) {
    return res.status(400).json({
      success: false,
      message: '姓名和密码不能为空'
    });
  }

  let connection;
  try {
    connection = await pool.getConnection();
    await connection.beginTransaction();

    // 生成学生ID
    const studentId = await generateStudentId();

    // 插入学生记录 - 直接使用明文密码
    const [result] = await connection.execute(
      `INSERT INTO students (
        student_id, name, password, phone, email
      ) VALUES (?, ?, ?, ?, ?)`,
      [studentId, name, password, phone || null, email || null]
    );

    await connection.commit();

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        id: result.insertId,
        studentId,
        name
      }
    });

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('学生注册失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '注册失败'
    });
  } finally {
    if (connection) {
      connection.release();
    }
  }
};

// 学生登录
export const login = async (req, res) => {
  const { student_id, password } = req.body;

  console.log('登录请求参数:', { student_id, password });

  if (!student_id || !password) {
    return res.status(400).json({
      success: false,
      message: '学号和密码不能为空'
    });
  }

  try {
    // 查找学生 - 移除前导零以匹配不同格式
    const formattedStudentId = student_id.replace(/^S0+/, 'S');
    console.log('格式化后的学号:', formattedStudentId);

    const [students] = await pool.execute(
      'SELECT * FROM students WHERE student_id = ? OR student_id = ?',
      [student_id, formattedStudentId]
    );

    console.log('查询结果:', students);

    if (students.length === 0) {
      return res.status(401).json({
        success: false,
        message: '学号或密码错误'
      });
    }

    const student = students[0];
    console.log('数据库中的密码:', student.password);
    console.log('用户输入的密码:', password);

    // 直接比对明文密码
    if (password !== student.password) {
      return res.status(401).json({
        success: false,
        message: '学号或密码错误'
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      { 
        id: student.id, 
        student_id: student.student_id,
        role: 'student'
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // 返回用户信息和token
    res.json({
      success: true,
      data: {
        token,
        userInfo: {
          id: student.id,
          student_id: student.student_id,
          name: student.name,
          phone: student.phone,
          email: student.email,
          avatar: student.avatar,
          role: 'student'
        }
      }
    });

  } catch (error) {
    console.error('学生登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
};

// 获取学生信息
export const getProfile = async (req, res) => {
  try {
    const studentId = req.user?.student_id;
    
    if (!studentId) {
      return res.status(401).json({
        success: false,
        message: '未登录'
      });
    }

    const [students] = await pool.execute(
      `SELECT id, student_id, name, phone, email, avatar, create_time 
       FROM students WHERE student_id = ?`,
      [studentId]
    );

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到学生信息'
      });
    }

    res.json({
      success: true,
      data: students[0]
    });

  } catch (error) {
    console.error('获取学生信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取信息失败'
    });
  }
};

// 获取单个学生信息
export const getStudentById = async (req, res) => {
  const { studentId } = req.params;
  
  try {
    const [rows] = await pool.query(`
      SELECT student_id, name, phone, email, avatar
      FROM students 
      WHERE student_id = ?
    `, [studentId]);

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到学生信息'
      });
    }

    res.json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    console.error('获取学生信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学生信息失败'
    });
  }
};

// 更新学生信息
export const updateProfile = async (req, res) => {
  const { name, phone, email, avatar } = req.body;
  const studentId = req.user?.student_id;

  if (!studentId) {
    return res.status(401).json({
      success: false,
      message: '未登录'
    });
  }

  try {
    const [result] = await pool.execute(
      `UPDATE students 
       SET name = ?, phone = ?, email = ?, avatar = ?, update_time = NOW()
       WHERE student_id = ?`,
      [name, phone || null, email || null, avatar || null, studentId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到学生信息'
      });
    }

    res.json({
      success: true,
      message: '信息更新成功'
    });

  } catch (error) {
    console.error('更新学生信息失败:', error);
    res.status(500).json({
      success: false,
      message: '更新信息失败'
    });
  }
};

// 修改密码
export const changePassword = async (req, res) => {
  const { oldPassword, newPassword } = req.body;
  const studentId = req.user?.student_id;

  if (!studentId || !oldPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数'
    });
  }

  try {
    // 查找学生
    const [students] = await pool.execute(
      'SELECT * FROM students WHERE student_id = ?',
      [studentId]
    );

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到学生信息'
      });
    }

    // 验证旧密码
    const isMatch = await bcrypt.compare(oldPassword, students[0].password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: '原密码错误'
      });
    }

    // 加密新密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // 更新密码
    await pool.execute(
      'UPDATE students SET password = ? WHERE student_id = ?',
      [hashedPassword, studentId]
    );

    res.json({
      success: true,
      message: '密码修改成功'
    });

  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({
      success: false,
      message: '修改密码失败'
    });
  }
};
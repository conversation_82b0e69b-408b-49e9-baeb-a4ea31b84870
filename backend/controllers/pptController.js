import xfyunHelper from '../utils/xfyunHelper.js';
import pool from '../config/db.js';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 生成PPT
export const generatePPT = async (req, res) => {
    try {
        const { courseId, hour } = req.params;
        const { content, templateId } = req.body;
        const teacherId = req.user?.system_teacher_id;

        console.log('生成PPT - 参数:', { courseId, hour, teacherId, templateId });

        // 检查是否是临时ID（直接从PPT制作页面生成，不通过课程）
        // 临时ID的格式应该像 "T123"
        const isTemporary = courseId.startsWith('temp_') || courseId.startsWith('tmp_') || 
                            (courseId.startsWith('T') && courseId.length === 4);
        
        // 对临时ID不做特殊处理，直接使用客户端提供的ID
        // 对非临时ID，确保只取前4个字符来满足数据库字段长度限制
        const safeCourseId = isTemporary ? 
            courseId : 
            courseId.substring(0, 4);
        
        // 保证课时名称长度适当（title字段是varchar(50)）
        let safeHour = hour.substring(0, 45);
        
        // 如果标题格式为 "xxx - 课时Y"，提取为简单的 "课时Y" 格式
        if (safeHour && safeHour.includes(' - ')) {
            const parts = safeHour.split(' - ');
            if (parts.length > 1 && parts[parts.length-1].startsWith('课时')) {
                safeHour = parts[parts.length-1];
                console.log('从复合标题中提取课时部分:', safeHour);
            }
        }
        
        // 从课时名称中提取titleId（如"课时1"提取为1）
        let titleId = 0;
        if (!isTemporary) {
            const titleMatch = safeHour.match(/\d+/);
            if (titleMatch) {
                titleId = parseInt(titleMatch[0]);
                console.log('从课时名称提取的titleId:', titleId);
            }
        }

        // 只在非临时ID时验证课程存在，避免验证失败
        let courseExists = false;
        if (!isTemporary) {
            // 1. 获取课程信息
            const [courseRows] = await pool.execute(
                'SELECT course_name FROM courses WHERE course_code = ? AND teacher_id = ?',
                [safeCourseId, teacherId]
            );

            courseExists = courseRows.length > 0;
            
            if (!courseExists) {
                console.log('警告: 提供的课程ID未找到匹配记录:', safeCourseId);
                // 我们不再立即返回错误，而是继续处理
            }
        }

        // 2. 处理PPT内容
        let pptContent = content;
        let templateExists = false;

        // 尝试查找已有模板（无论是临时ID还是真实课程ID）
        try {
            const [templateRows] = await pool.execute(
                `SELECT 
                    id,
                    title,
                    content
                 FROM ppt_templates 
                 WHERE course_code = ? 
                   AND teacher_id = ? 
                   AND title = ?
                 ORDER BY create_time DESC
                 LIMIT 1`,
                [safeCourseId, teacherId, safeHour]
            );
            
            if (templateRows.length > 0) {
                // 使用数据库中的现有模板
                templateExists = true;
                console.log('找到现有模板:', templateRows[0].title, '(ID:', templateRows[0].id, ')');
                try {
                    pptContent = JSON.parse(templateRows[0].content);
                } catch (parseError) {
                    console.error('解析模板内容失败，将使用新内容:', parseError);
                }
            }
        } catch (queryError) {
            console.error('查询模板失败:', queryError);
            // 继续使用新提供的内容
        }

        // 保存内容到模板表（无论是新模板还是更新现有模板）
        try {
            // 检查是否有前端传来的现有模板ID
            if (templateId) {
                console.log('使用前端提供的模板ID更新:', templateId);
                
                // 更新现有模板
                await pool.execute(
                    `UPDATE ppt_templates SET
                        content = ?,
                        update_time = NOW()
                     WHERE id = ? AND teacher_id = ?`,
                    [JSON.stringify(content), templateId, teacherId]
                );
                console.log('更新现有模板成功，ID:', templateId);
            } else {
                // 先检查是否已经存在相同的模板记录
                const [existingTemplates] = await pool.execute(
                    `SELECT id FROM ppt_templates 
                     WHERE course_code = ? AND teacher_id = ? AND title = ?`,
                    [safeCourseId, teacherId, safeHour]
                );
                
                if (existingTemplates.length > 0) {
                    // 已存在相同模板，执行更新
                    await pool.execute(
                        `UPDATE ppt_templates SET
                            content = ?,
                            update_time = NOW()
                         WHERE id = ?`,
                        [JSON.stringify(content), existingTemplates[0].id]
                    );
                    console.log('更新已有PPT模板，ID:', existingTemplates[0].id);
                } else {
                    // 不存在相同模板，执行插入
                    await pool.execute(
                        `INSERT INTO ppt_templates (
                            teacher_id, title, content, course_code, title_id, create_time
                        ) VALUES (?, ?, ?, ?, ?, NOW())`,
                        [teacherId, safeHour, JSON.stringify(content), safeCourseId, titleId]
                    );
                    console.log('创建新PPT模板，使用titleId:', titleId);
                }
            }
        } catch (templateError) {
            console.error('保存PPT模板失败:', templateError);
            // 继续执行，不中断流程
        }
        
        // 3. 调用讯飞API生成PPT
        const data = {
            query: pptContent.title || safeHour,
            outline: pptContent,
            language: "cn",
            isCardNote: true,
            search: true,
            author: '智能PPT',  // 所有情况都使用同一个作者名，避免问题
            isFigure: true,
            aiImage: "normal"
        };

        console.log('开始调用讯飞API生成PPT，参数:', data);
        const result = await xfyunHelper.createPptByOutline(data);

        // 4. 保存生成记录到数据库
        if (result.flag) {
            try {
                await pool.execute(
                    `INSERT INTO ppt_records (
                        teacher_id, course_code, title, sid, status, View_url, create_time
                    ) VALUES (?, ?, ?, ?, ?, ?, NOW())`,
                    [teacherId, safeCourseId, safeHour, result.data.sid, 'processing', null]
                );
                console.log('成功保存PPT生成记录');
            } catch (recordError) {
                console.error('保存PPT生成记录失败:', recordError);
                // 继续执行，不中断流程
            }
        }

        res.json({
            flag: result.flag,
            desc: result.desc,
            data: result.data
        });

    } catch (error) {
        console.error('生成PPT失败:', error);
        res.json({
            flag: false,
            desc: error.message || '生成PPT失败',
            data: null
        });
    }
};

// 查询PPT生成进度
export const getPPTProgress = async (req, res) => {
    try {
        const { sid } = req.query;
        if (!sid) {
            return res.json({
                flag: false,
                desc: '缺少必要参数sid'
            });
        }

        console.log('开始查询PPT生成进度, sid:', sid);
        const result = await xfyunHelper.getPptProgress(sid);
        console.log('讯飞返回的进度信息:', result);
        
        // 如果API调用成功
        if (result.flag) {
            // 根据讯飞API返回的状态更新数据库
            let status = 'processing';
            let downloadUrl = null;
            let percent = 0;
            let message = '生成中...';
            let View_url = null;

            // 检查PPT生成状态
            if (result.data.pptStatus === 'done' && 
                result.data.aiImageStatus === 'done' && 
                result.data.cardNoteStatus === 'done') {
                
                // 下载PPT文件到本地
                if (result.data.pptUrl) {
                    try {
                        // 创建public/PPTS目录（如果不存在）
                        const publicDir = path.join(__dirname, '../../public/PPTS');
                        if (!fs.existsSync(publicDir)) {
                            fs.mkdirSync(publicDir, { recursive: true });
                        }

                        // 下载文件
                        const response = await axios({
                            method: 'get',
                            url: result.data.pptUrl,
                            responseType: 'stream'
                        });
                        
                        // 生成文件名
                        const timestamp = new Date().getTime();
                        const filename = `ppt_${sid}_${timestamp}.pptx`;
                        const filepath = path.join(publicDir, filename);

                        // 保存文件
                        const writer = fs.createWriteStream(filepath);
                        response.data.pipe(writer);

                        await new Promise((resolve, reject) => {
                            writer.on('finish', resolve);
                            writer.on('error', reject);
                        });

                        // 更新下载地址为本地路径
                        downloadUrl = `/public/PPTS/${filename}`;
                        View_url = result.data.pptUrl;
                        status = 'completed';
                        percent = 100;
                        message = '生成完成';
                        
                        console.log('PPT文件已保存到:', filepath);
                    } catch (downloadError) {
                        console.error('下载PPT文件失败:', downloadError);
                        status = 'failed';
                        message = '下载PPT文件失败';
                    }
                }
            } else if (result.data.errMsg) {
                status = 'failed';
                message = result.data.errMsg;
            } else {
                // 计算进度
                const totalSteps = 3; // ppt、图片、讲稿三个步骤
                let completedSteps = 0;
                if (result.data.pptStatus === 'done') completedSteps++;
                if (result.data.aiImageStatus === 'done') completedSteps++;
                if (result.data.cardNoteStatus === 'done') completedSteps++;
                percent = Math.floor((completedSteps / totalSteps) * 100);
            }

            // 更新数据库
            await pool.execute(
                `UPDATE ppt_records 
                 SET status = ?, 
                     download_url = ?,
                     View_url = ?,
                     update_time = NOW()
                 WHERE sid = ?`,
                [status, downloadUrl, View_url, sid]
            );

            // 查询更新后的记录
            const [records] = await pool.execute(
                'SELECT status, download_url, View_url FROM ppt_records WHERE sid = ?',
                [sid]
            );
            console.log('数据库中的记录:', records[0]);

            res.json({
                flag: true,
                desc: 'success',
                data: {
                    status,
                    percent,
                    message,
                    downloadUrl,
                    pptUrl: result.data.pptUrl
                }
            });
        } else {
            // API调用失败
            console.error('讯飞API返回失败:', result);
            await pool.execute(
                `UPDATE ppt_records 
                 SET status = 'failed',
                     update_time = NOW()
                 WHERE sid = ?`,
                [sid]
            );

            res.json({
                flag: false,
                desc: result.desc || '查询进度失败',
                data: {
                    status: 'failed',
                    percent: 0,
                    message: result.desc || '生成失败'
                }
            });
        }

    } catch (error) {
        console.error('查询PPT进度失败:', error);
        res.json({
            flag: false,
            desc: error.message || '查询进度失败',
            data: {
                status: 'failed',
                percent: 0,
                message: '查询进度时发生错误'
            }
        });
    }
};

// 获取PPT模板
export const getPPTTemplate = async (req, res) => {
    try {
        const { courseId, hour, titleId } = req.params;
        const teacherId = req.user?.system_teacher_id;

        console.log('获取PPT模板 - 参数:', { courseId, hour, titleId, teacherId });

        // 检查是否使用titleId查询
        const usesTitleId = req.path.includes('/byTitleId/');
        
        if (usesTitleId) {
            if (!courseId || !titleId) {
                console.log('缺少必要参数');
                return res.json({
                    flag: false,
                    desc: '缺少必要参数'
                });
            }
            
            // 使用titleId查询
            const [templateRows] = await pool.execute(
                `SELECT 
                    id,
                    title,
                    content,
                    create_time
                 FROM ppt_templates 
                 WHERE course_code = ? 
                   AND teacher_id = ? 
                   AND title_id = ?
                 ORDER BY create_time DESC
                 LIMIT 1`,
                [courseId, teacherId, titleId]
            );
            
            console.log('通过titleId查询结果:', templateRows.length > 0);
            
            if (templateRows.length === 0) {
                console.log('未找到PPT模板');
                return res.json({
                    flag: false,
                    desc: '未找到PPT模板'
                });
            }
            
            let content;
            try {
                content = JSON.parse(templateRows[0].content);
                console.log('成功解析PPT内容');
            } catch (parseError) {
                console.error('PPT内容解析失败:', parseError);
                return res.json({
                    flag: false,
                    desc: 'PPT模板内容格式错误'
                });
            }
            
            const result = {
                flag: true,
                data: {
                    id: templateRows[0].id,
                    title: templateRows[0].title,
                    content: content,
                    createTime: templateRows[0].create_time
                }
            };
            
            console.log('返回数据:', result);
            return res.json(result);
        }
        else {
            // 原有的通过课时名称查询
            if (!courseId || !hour) {
                console.log('缺少必要参数');
                return res.json({
                    flag: false,
                    desc: '缺少必要参数'
                });
            }

            // 获取最新的PPT模板
            const [templateRows] = await pool.execute(
                `SELECT 
                    id,
                    title,
                    content,
                    create_time
                 FROM ppt_templates 
                 WHERE course_code = ? 
                   AND teacher_id = ? 
                   AND title = ?
                 ORDER BY create_time DESC
                 LIMIT 1`,
                [courseId, teacherId, hour]
            );

            console.log('数据库查询结果:', {
                rowCount: templateRows.length,
                firstRow: templateRows[0] ? {
                    id: templateRows[0].id,
                    title: templateRows[0].title,
                    createTime: templateRows[0].create_time
                } : null
            });

            if (templateRows.length === 0) {
                console.log('未找到PPT模板');
                return res.json({
                    flag: false,
                    desc: '未找到PPT模板'
                });
            }

            let content;
            try {
                content = JSON.parse(templateRows[0].content);
                console.log('成功解析PPT内容');
            } catch (parseError) {
                console.error('PPT内容解析失败:', parseError);
                return res.json({
                    flag: false,
                    desc: 'PPT模板内容格式错误'
                });
            }

            const result = {
                flag: true,
                data: {
                    id: templateRows[0].id,
                    title: templateRows[0].title,
                    content: content,
                    createTime: templateRows[0].create_time
                }
            };

            console.log('返回数据:', result);
            res.json(result);
        }
    } catch (error) {
        console.error('获取PPT模板失败:', error);
        res.json({
            flag: false,
            desc: error.message || '获取PPT模板失败'
        });
    }
};

// 保存PPT大纲
export const savePPTTemplate = async (req, res) => {
    try {
        const { courseId, hour, titleId } = req.params;
        const { title, content } = req.body;
        const teacherId = req.user?.system_teacher_id;

        console.log('保存PPT模板 - 参数:', { courseId, hour, titleId, teacherId });

        // 检查是否使用titleId保存
        const usesTitleId = req.path.includes('/byTitleId/');
        
        if (!title && !content) {
            return res.json({
                flag: false,
                desc: '标题和内容不能为空'
            });
        }

        // 确定保存的标题和titleId
        let saveTitle = hour || title;
        let saveTitleId = 0;
        
        // 如果标题格式为 "xxx - 课时Y"，提取为简单的 "课时Y" 格式
        if (saveTitle && saveTitle.includes(' - ')) {
            const parts = saveTitle.split(' - ');
            if (parts.length > 1 && parts[parts.length-1].startsWith('课时')) {
                saveTitle = parts[parts.length-1];
                console.log('从复合标题中提取课时部分:', saveTitle);
            }
        }
        
        if (usesTitleId) {
            saveTitleId = parseInt(titleId) || 0;
        } else {
            // 提取title_id
            if (saveTitle) {
                const match = saveTitle.match(/\d+/);
                if (match) {
                    saveTitleId = parseInt(match[0]);
                }
            }
        }
        
        // 保存后的模板ID
        let templateId = null;
        
        // 检查是否已经存在相同的模板记录
        const [existingTemplates] = await pool.execute(
            `SELECT id FROM ppt_templates 
             WHERE course_code = ? AND teacher_id = ? AND title = ?`,
            [courseId, teacherId, saveTitle]
        );
        
        if (existingTemplates.length > 0) {
            // 已存在相同模板，执行更新
            templateId = existingTemplates[0].id;
            
            await pool.execute(
                `UPDATE ppt_templates SET
                    content = ?,
                    update_time = NOW()
                 WHERE id = ?`,
                [JSON.stringify(content), templateId]
            );
            console.log('更新已有PPT模板，ID:', templateId);
        } else {
            // 不存在相同模板，执行插入
            const [result] = await pool.execute(
                `INSERT INTO ppt_templates (
                    teacher_id, title, content, course_code, title_id, create_time
                ) VALUES (?, ?, ?, ?, ?, NOW())`,
                [teacherId, saveTitle, JSON.stringify(content), courseId, saveTitleId]
            );
            
            // 获取插入的ID
            templateId = result.insertId;
            console.log('创建新PPT模板，ID:', templateId);
        }

        res.json({
            flag: true,
            desc: 'PPT大纲保存成功',
            templateId: templateId
        });

    } catch (error) {
        console.error('保存PPT大纲失败:', error);
        res.json({
            flag: false,
            desc: error.message || '保存PPT大纲失败'
        });
    }
};

// 修改智能模式生成方法，使用已有接口
export const generatePPTAuto = async (req, res) => {
    try {
        const { content } = req.body;
        const file = req.file;
        const teacherId = req.user?.system_teacher_id;

        // 使用原有createPptByOutline方法
        const data = {
            query: "智能生成PPT",
            outline: {
                title: "智能生成PPT",
                subTitle: "自动生成",
                chapters: []
            },
            language: "cn",
            isCardNote: true,
            search: true,
            author: "系统用户",
            isFigure: true,
            aiImage: "normal"
        };

        let result;
        if (file) {
            // 使用文件上传模式
            result = await xfyunHelper.createPptByFile({
                ...data,
                file: file.buffer,
                fileName: file.originalname
            });
        } else {
            // 使用文本模式
            result = await xfyunHelper.createPptByOutline({
                ...data,
                outline: {
                    ...data.outline,
                    chapters: [
                        {
                            chapterTitle: "自动生成章节",
                            chapterContents: [
                                {
                                    chapterTitle: content || "自动生成内容",
                                    chapterContents: null
                                }
                            ]
                        }
                    ]
                }
            });
        }

        // 保存记录逻辑保持不变...
        if (result.flag) {
            // 完善数据库插入语句
            await pool.execute(
                `INSERT INTO ppt_records (
                    teacher_id, 
                    course_code,
                    title, 
                    sid, 
                    status, 
                    download_url, 
                    View_url, 
                    create_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
                [
                    teacherId,
                    'AUTO',
                    '智能生成PPT',
                    result.data.sid,
                    'processing',
                    null,
                    null
                ]
            );
            
            // 返回完整的响应数据
            res.json({
                ...result,
                data: {
                    ...result.data,
                    status: 'processing',
                    percent: 0
                }
            });
        } else {
            res.json(result);
        }
    } catch (error) {
        console.error('智能模式生成失败:', error);
        res.json({ flag: false, desc: error.message });
    }
}; 
// 获取所有PPT模板列表
export const getAllPPTTemplates = async (req, res) => {
    try {
        const teacherId = req.user?.system_teacher_id;

        console.log('获取所有PPT模板 - 参数:', { teacherId });

        if (!teacherId) {
            return res.json({
                flag: false,
                desc: '未登录或无效的教师ID'
            });
        }

        // 获取所有PPT模板，不再使用GROUP BY，而是按创建时间降序排序
        const [templateRows] = await pool.execute(
            `SELECT 
                ppt.id,
                ppt.title,
                ppt.course_code,
                ppt.title_id,
                ppt.create_time,
                JSON_EXTRACT(ppt.content, '$.title') as content_title,
                c.course_name
             FROM ppt_templates ppt
             LEFT JOIN courses c ON ppt.course_code = c.course_code
             WHERE ppt.teacher_id = ? 
             ORDER BY ppt.create_time DESC
             LIMIT 100`,
            [teacherId]
        );

        console.log('查询到的模板数量:', templateRows.length);

        if (templateRows.length === 0) {
            return res.json({
                flag: true,
                desc: '暂无PPT模板',
                data: []
            });
        }

        // 格式化返回数据
        const templates = templateRows.map(template => {
            // 从JSON内容中提取的标题可能包含引号，需要清理
            let contentTitle = template.content_title;
            if (contentTitle) {
                contentTitle = contentTitle.replace(/^"|"$/g, '');
            }
            
            return {
                id: template.id,
                title: template.title,
                contentTitle: contentTitle || '未命名PPT',
                courseCode: template.course_code,
                courseName: template.course_name || '未知课程',
                titleId: template.title_id,
                createTime: template.create_time
            };
        });

        res.json({
            flag: true,
            desc: '获取成功',
            data: templates
        });

    } catch (error) {
        console.error('获取所有PPT模板失败:', error);
        res.json({
            flag: false,
            desc: error.message || '获取所有PPT模板失败',
            data: []
        });
    }
};

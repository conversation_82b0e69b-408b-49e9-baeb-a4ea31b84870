import pool from '../config/db.js';
import xlsx from 'xlsx';
const { readFile, utils, write } = xlsx;
import { dbConfig } from '../config/db.js';
import mysql from 'mysql2/promise';
import { createRequire } from 'module';
import nodeXlsx from 'node-xlsx';
import db from '../config/db.js';
import { validateSchedule } from '../utils/validation.js';
import path from 'path';
import fs from 'fs';

const require = createRequire(import.meta.url);

const uploadSchedule = async (req, res) => {
  let connection;
  try {
    // 检查文件和用户ID
    if (!req.files || !req.files.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    const file = req.files.file;
    const userId = req.body.userId;

    console.log('Upload request:', {
      fileName: file.name,
      fileSize: file.size,
      mimeType: file.mimetype,
      userId: userId
    });

    try {
      // 读取Excel文件
      const sheets = nodeXlsx.parse(file.data);
      const worksheet = sheets[0];
      const rawData = worksheet.data;

      console.log('Raw Excel data:', {
        sheetName: worksheet.name,
        range: worksheet['!ref'],
        firstCell: worksheet['A1'],
        data: rawData
      });

      // 移除空行
      const filteredData = rawData.filter(row => 
        row && row.some(cell => cell !== null && cell !== '')
      );

      if (!filteredData || filteredData.length < 2) {
        return res.status(400).json({
          success: false,
          message: '课表数据为空或格式不正确'
        });
      }

      // 验证表头
      const headers = filteredData[0].map(h => String(h || '').toLowerCase().trim());
      const requiredHeaders = ['weekday', 'period', 'class_name', 'classroom', 'class_group'];

      console.log('Headers comparison:', {
        found: headers,
        required: requiredHeaders
      });

      if (!requiredHeaders.every(h => headers.includes(h))) {
        return res.status(400).json({
          success: false,
          message: '表头格式不正确，请使用正确的模板'
        });
      }

      // 处理数据行
      const validData = [];
      for (let i = 1; i < filteredData.length; i++) {
        const row = filteredData[i];
        if (row && row.length >= 5) {
          const weekday = parseInt(row[0]);
          const period = parseInt(row[1]);
          
          if (!isNaN(weekday) && !isNaN(period) && weekday > 0 && weekday <= 7 && period > 0 && period <= 8) {
            validData.push({
              weekday,
              period,
              class_name: String(row[2] || '').trim(),
              classroom: String(row[3] || '').trim(),
              class_group: String(row[4] || '').trim(),
              total_weeks: 20,
              current_week: 1,
              start_week: 1,
              end_week: 20,
              total_hours: 0,
              completed_hours: 0,
              course_type: 'required'
            });
          }
        }
      }

      if (validData.length === 0) {
        return res.status(400).json({
          success: false,
          message: '未找到有效的课表数据，请检查数据格式'
        });
      }

      console.log('有效数据条数:', validData.length);

      // 数据库操作
      connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        // 更新当前课表状态
        await connection.execute(
          'UPDATE class_schedules SET is_current = 0 WHERE user_id = ?',
          [userId]
        );

        // 创建新课表记录
        const [result] = await connection.execute(
          'INSERT INTO class_schedules (user_id, schedule_name, semester, is_current) VALUES (?, ?, ?, ?)',
          [
            userId,
            `课表_${new Date().toISOString().split('T')[0]}`,
            '2024春季',
            1
          ]
        );

        const scheduleId = result.insertId;

        // 批量插入课程数据
        for (const item of validData) {
          await connection.execute(
            `INSERT INTO schedule_details (
              schedule_id, user_id, weekday, period, class_name, classroom, class_group,
              total_weeks, current_week, start_week, end_week, total_hours, completed_hours, course_type
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              scheduleId,
              userId,
              item.weekday,
              item.period,
              item.class_name || '',
              item.classroom || '',
              item.class_group || '',
              item.total_weeks,
              item.current_week,
              item.start_week,
              item.end_week,
              item.total_hours,
              item.completed_hours,
              item.course_type
            ]
          );
        }

        await connection.commit();
        
        res.json({
          success: true,
          message: '课表数据导入成功',
          data: {
            scheduleId,
            itemCount: validData.length
          }
        });

      } catch (error) {
        console.error('Database operation error:', error);
        if (connection) {
          await connection.rollback();
        }
        throw error;
      }

    } catch (error) {
      console.error('Excel processing error:', error);
      throw error;
    }

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: error.message || '导入失败',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  } finally {
    if (connection) {
      await connection.release();
    }
  }
};

// 获取当前课表
const getCurrentSchedule = async (req, res) => {
    try {
        const teacherId = req.user?.id || req.query.teacherId;
        console.log('获取当前课表，教师ID:', teacherId);

        const [schedules] = await db.query(
            `SELECT * FROM class_schedules 
             WHERE user_id = ? AND is_current = 1
             ORDER BY created_at DESC LIMIT 1`,
            [teacherId]
        );

        // 如果找到课表，返回第一个；否则返回默认值
        const currentSchedule = schedules[0] || {
            id: null,
            user_id: teacherId,
            semester: '2023-1',
            is_current: 1,
            current_week: 1,
            total_weeks: 20,
            start_date: new Date(),
            end_date: new Date(new Date().setMonth(new Date().getMonth() + 4)),
            total_hours: 0
        };

        res.json({
            success: true,
            data: currentSchedule
        });
    } catch (error) {
        console.error('获取当前课表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取当前课表失败',
            error: error.message
        });
    }
};

// 获取教师的所有课表
const getTeacherSchedules = async (req, res) => {
    try {
        const teacherId = req.user?.id || req.query.teacherId;
        console.log('获取教师课表列表，教师ID:', teacherId);

        const [schedules] = await db.query(
            `SELECT cs.*, c.course_name, c.course_code
             FROM class_schedules cs
             LEFT JOIN courses c ON cs.course_id = c.id
             WHERE cs.user_id = ?
             ORDER BY cs.created_at DESC`,
            [teacherId]
        );

        res.json({
            success: true,
            data: schedules
        });
    } catch (error) {
        console.error('获取教师课表列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取教师课表列表失败',
            error: error.message
        });
    }
};

// 获取课表详情
const getScheduleDetails = async (req, res) => {
  try {
    const teacherId = req.params.teacherId;
    console.log('获取课表详情，教师ID:', teacherId);

    // 1. 获取当前课表
    const [schedules] = await pool.query(
      `SELECT cs.*, c.course_name 
       FROM class_schedules cs
       LEFT JOIN courses c ON cs.course_id = c.id
       WHERE cs.user_id = ? AND cs.is_current = 1
       LIMIT 1`,
      [teacherId]
    );

    if (schedules.length === 0) {
      console.log('未找到当前课表');
      return res.json({
        success: true,
        data: []
      });
    }

    const schedule = schedules[0];
    console.log('当前课表:', schedule);

    // 2. 获取课程安排详情
    const [details] = await pool.query(
      `SELECT 
        sd.id,
        sd.weekday,
        sd.period,
        c.course_name as class_name,
        sd.classroom,
        sd.class_group,
        sd.start_week,
        sd.end_week,
        cs.current_week,
        cs.total_weeks,
        c.id as course_id
       FROM schedule_details sd
       JOIN class_schedules cs ON sd.schedule_id = cs.id
       JOIN courses c ON cs.course_id = c.id
       WHERE sd.schedule_id = ?
       ORDER BY sd.weekday, sd.period`,
      [schedule.id]
    );

    console.log('查询到的课表详情数量:', details.length);

    res.json({
      success: true,
      data: {
        schedule,
        details: details.map(detail => ({
          id: detail.id,
          weekday: detail.weekday,
          period: detail.period,
          class_name: detail.class_name,
          classroom: detail.classroom,
          class_group: detail.class_group,
          start_week: detail.start_week,
          end_week: detail.end_week,
          current_week: detail.current_week,
          total_weeks: detail.total_weeks,
          course_id: detail.course_id
        }))
      }
    });
  } catch (error) {
    console.error('获取课表详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课表详情失败',
      error: error.message
    });
  }
};

// 更新课表信息
const updateSchedule = async (req, res) => {
    try {
        const { id } = req.params;
        const {
            current_week,
            total_weeks,
            semester,
            start_date,
            end_date
        } = req.body;
        const teacherId = req.user?.id;

        await db.query(
            `UPDATE class_schedules 
             SET current_week = ?,
                 total_weeks = ?,
                 semester = ?,
                 start_date = ?,
                 end_date = ?
             WHERE id = ? AND user_id = ?`,
            [current_week, total_weeks, semester, start_date, end_date, id, teacherId]
        );

        res.json({
            success: true,
            message: '课表更新成功'
        });
    } catch (error) {
        console.error('更新课表失败:', error);
        res.status(500).json({
            success: false,
            message: '更新课表失败',
            error: error.message
        });
    }
};

const getScheduleHistory = async (req, res) => {
  try {
    const userId = req.params.userId;
    const [rows] = await pool.execute(
      'SELECT * FROM class_schedules WHERE user_id = ? ORDER BY create_time DESC',
      [userId]
    );
    res.json(rows);
  } catch (error) {
    console.error('Get history error:', error);
    res.status(500).json({ message: '获取历史课表失败' });
  }
};

// 添加下载课表的方法
const downloadSchedule = async (req, res) => {
  const connection = await pool.getConnection();
  try {
    const { userId, scheduleId } = req.params;

    // 获取课表数据
    const [scheduleDetails] = await connection.execute(
      'SELECT weekday, period, class_name, classroom, class_group FROM schedule_details WHERE schedule_id = ? AND user_id = ? ORDER BY weekday, period',
      [scheduleId, userId]
    );

    // 获取课表基本信息
    const [scheduleInfo] = await connection.execute(
      'SELECT schedule_name, semester FROM class_schedules WHERE id = ? AND user_id = ?',
      [scheduleId, userId]
    );

    if (!scheduleInfo.length) {
      return res.status(404).json({ message: '课表不存在' });
    }

    // 创建工作簿
    const wb = utils.book_new();
    
    // 添加表头
    const headers = ['星期', '节次', '课程名称', '教室', '班级'];
    const data = [
      headers,
      ...scheduleDetails.map(row => [
        row.weekday,
        row.period,
        row.class_name,
        row.classroom,
        row.class_group
      ])
    ];

    // 创建工作表
    const ws = utils.aoa_to_sheet(data);

    // 设置列宽
    ws['!cols'] = [
      { wch: 8 },  // 星期
      { wch: 8 },  // 节次
      { wch: 15 }, // 课程名称
      { wch: 10 }, // 教室
      { wch: 12 }  // 班级
    ];

    // 将工作表添加到工作簿
    utils.book_append_sheet(wb, ws, '课表');

    // 添加说明页
    const instructionData = [
      ['课表填写说明'],
      [''],
      ['1. 每个单元格的格式为：'],
      ['   课程名称'],
      ['   教室'],
      ['   班级'],
      [''],
      ['2. 示例：'],
      ['   数学'],
      ['   301教室'],
      ['   高一(1)班'],
      [''],
      ['3. 注意事项：'],
      ['   - 三行信息之间用回车分隔'],
      ['   - 不要添加额外的空格'],
      ['   - 不要修改表头和节次'],
      ['   - 空课时保持单元格为空']
    ];

    const wsInstructions = utils.aoa_to_sheet(instructionData);

    // 设置说明页的列宽
    wsInstructions['!cols'] = [{ wch: 50 }];

    // 添加说明页到工作簿
    utils.book_append_sheet(wb, wsInstructions, 'Instructions');

    // 生成二进制数据
    const buffer = write(wb, { type: 'buffer', bookType: 'xlsx' });

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=schedule_${scheduleId}.xlsx`);

    // 发送文件
    res.send(buffer);

  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({ 
      success: false,
      message: '下载失败',
      error: error.message 
    });
  } finally {
    connection.release();
  }
};

// 删除课程
const deleteClass = async (req, res) => {
  let connection;
  try {
    const { userId, weekday, period } = req.body;
    
    // 参数验证和转换
    const params = {
      userId: parseInt(userId),
      weekday: parseInt(weekday),
      period: parseInt(period)
    };

    // 验证参数
    if (isNaN(params.userId) || isNaN(params.weekday) || isNaN(params.period)) {
      throw new Error('无效的参数格式');
    }

    // 获取数据库连接
    connection = await pool.getConnection();

    // 开启事务
    await connection.beginTransaction();

    try {
      // 获取当前课表ID
      const [schedules] = await connection.execute(
        'SELECT id FROM class_schedules WHERE user_id = ? AND is_current = 1',
        [params.userId]
      );

      if (!schedules || schedules.length === 0) {
        throw new Error('未找到当前课表');
      }

      const scheduleId = schedules[0].id;

      // 删除课程
      const [result] = await connection.execute(
        'DELETE FROM schedule_details WHERE schedule_id = ? AND user_id = ? AND weekday = ? AND period = ?',
        [scheduleId, params.userId, params.weekday, params.period]
      );

      // 提交事务
      await connection.commit();

      res.json({
        success: true,
        message: result.affectedRows > 0 ? '课程删除成功' : '课程不存在',
        debug: {
          params,
          result
        }
      });

    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    }

  } catch (error) {
    console.error('Delete class error:', error);
    res.status(500).json({
      success: false,
      message: error.message || '删除失败'
    });
  } finally {
    if (connection) {
      await connection.release();
    }
  }
};

// 添加下载模板的方法
const downloadTemplate = async (req, res) => {
  try {
    const wb = utils.book_new();
    
    // 表头和示例数据
    const data = [
      ['weekday', 'period', 'class_name', 'classroom', 'class_group'],
      ['1', '1', '高二数学', '301', '1班'],  // 示例数据
      ['1', '2', '高二英语', '302', '2班'],
      ['2', '1', '', '', ''],  // 空课时示例
    ];

    // 创建工作表
    const ws = utils.aoa_to_sheet(data);
    
    // 设置列宽
    ws['!cols'] = [
      { wch: 10 },  // weekday
      { wch: 10 },  // period
      { wch: 15 },  // class_name
      { wch: 12 },  // classroom
      { wch: 12 }   // class_group
    ];

    // 添加工作表到工作簿
    utils.book_append_sheet(wb, ws, 'Schedule');

    // 添加说明页
    const instructionData = [
      ['课表填写说明'],
      [''],
      ['1. 各列说明：'],
      ['   weekday: 星期几（1-7）'],
      ['   period: 第几节课（1-8）'],
      ['   class_name: 课程名称'],
      ['   classroom: 教室'],
      ['   class_group: 班级'],
      [''],
      ['2. 注意事项：'],
      ['   - 星期和节次必须为数字'],
      ['   - 其他字段可以为空'],
      ['   - 不要修改表头']
    ];

    const wsInstructions = utils.aoa_to_sheet(instructionData);
    utils.book_append_sheet(wb, wsInstructions, '填写说明');

    // 生成buffer
    const buffer = write(wb, { type: 'buffer', bookType: 'xlsx' });

    res.attachment('schedule_template.xlsx');
    res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.send(buffer);

  } catch (error) {
    console.error('Template download error:', error);
    res.status(500).json({ 
      success: false,
      message: '模板下载失败'
    });
  }
};

// 获取教师的课程相关信息
const getTeacherCourses = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const userId = req.query.userId;

    console.log('Fetching courses for user:', userId);

    // 1. 获取用户信息从 teaching_assistant 表
    const [users] = await connection.execute(
      'SELECT name FROM teaching_assistant WHERE id = ?',
      [userId]
    );

    const teacherName = users.length > 0 ? users[0].name : '未知教师';
    console.log('Found teacher:', teacherName);

    // 2. 获取课程详细信息
    const [details] = await connection.execute(
      `SELECT DISTINCT 
        sd.class_name, 
        sd.class_group, 
        sd.classroom,
        sd.total_weeks,
        sd.current_week,
        sd.total_hours,
        sd.course_type,
        cs.semester,
        cs.schedule_name
       FROM schedule_details sd
       JOIN class_schedules cs ON sd.schedule_id = cs.id
       WHERE sd.user_id = ? AND cs.is_current = 1`,
      [userId]
    );

    console.log('Found course details:', details);

    // 如果没有课程信息，返回基本用户信息
    if (details.length === 0) {
      return res.json({
        success: true,
        data: {
          teacherName: teacherName,
          semester: '',
          schedule_name: '',
          total_weeks: '',
          current_week: '',
          total_hours: '',
          course_type: '',
          suggestions: {
            courses: [],
            classes: [],
            classrooms: []
          },
          message: '请输入课程信息' // 提示用户输入
        }
      });
    }

    // 3. 整理课程信息
    const uniqueCourses = new Set();
    const uniqueClasses = new Set();
    const uniqueClassrooms = new Set();

    details.forEach(record => {
      if (record.class_name) uniqueCourses.add(record.class_name);
      if (record.class_group) uniqueClasses.add(record.class_group);
      if (record.classroom) uniqueClassrooms.add(record.classroom);
    });

    // 4. 整理返回数据
    const responseData = {
      teacherName: teacherName,
      semester: details[0].semester || '',
      schedule_name: details[0].schedule_name || '',
      total_weeks: details[0].total_weeks || '',
      current_week: details[0].current_week || '',
      total_hours: details[0].total_hours || '',
      course_type: details[0].course_type || '',
      suggestions: {
        courses: Array.from(uniqueCourses),
        classes: Array.from(uniqueClasses),
        classrooms: Array.from(uniqueClassrooms)
      }
    };

    console.log('Sending response:', responseData);

    res.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('获取教师课程信息失败:', error);
    // 即使出错也返回一个可用的响应
    res.json({
      success: true,
      data: {
        teacherName: '未知教师',
        semester: '',
        schedule_name: '',
        total_weeks: '',
        current_week: '',
        total_hours: '',
        course_type: '',
        suggestions: {
          courses: [],
          classes: [],
          classrooms: []
        },
        message: '获取信息失败，请手动输入'
      }
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 导出所有函数
export {
  uploadSchedule,
  getCurrentSchedule,
  getTeacherSchedules,
  getScheduleDetails,
  updateSchedule,
  getScheduleHistory,
  downloadSchedule,
  deleteClass,
  downloadTemplate,
  getTeacherCourses
} 
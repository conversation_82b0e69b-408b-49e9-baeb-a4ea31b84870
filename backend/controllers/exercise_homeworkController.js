import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 获取学生所在的班级列表
export const getStudentClasses = async (req, res) => {
  const studentId = req.params.studentId;
  console.log('接收到的学生ID:', studentId);
  
  // 检查学生ID是否需要格式化
  let formattedStudentId = studentId;
  if (!isNaN(studentId) && !studentId.startsWith('S')) {
    // 如果是纯数字，转换为S格式
    formattedStudentId = `S${studentId.padStart(5, '0')}`;
    console.log('格式化后的学生ID:', formattedStudentId);
  }
  
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 查询学生所在的班级 - 使用格式化后的ID和原始ID都查询一次
    const [classes] = await conn.execute(
      `SELECT cc.id as class_id, cc.class_name, cc.course_code, cc.semester
       FROM course_classes cc
       JOIN student_course_registration scr ON cc.id = scr.class_id
       WHERE (scr.student_id = ? OR scr.student_id = ?) AND scr.status = 1
       ORDER BY cc.semester DESC`,
      [formattedStudentId, studentId]
    );
    
    // 如果还是没有数据，尝试直接查询所有学生的选课记录 (仅用于调试)
    if (classes.length === 0) {
      const [allRegistrations] = await conn.execute(
        `SELECT student_id FROM student_course_registration LIMIT 10`
      );
      console.log('数据库中的学生ID样例:', allRegistrations.map(r => r.student_id).join(', '));
    }
    
    await conn.end();
    
    console.log('查询到的班级数量:', classes.length);
    if (classes.length === 0) {
      console.log('未查询到班级, 使用的学生ID:', formattedStudentId, studentId);
    }
    
    res.json({
      success: true,
      data: classes
    });
  } catch (error) {
    console.error('获取学生班级失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学生班级失败'
    });
  }
};

// 获取班级的章节列表
export const getClassChapters = async (req, res) => {
  const { classId } = req.params;
  
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 先获取班级对应的课程代码和最新的大纲
    const [classRows] = await conn.execute(
      `SELECT cc.course_code, s.content 
       FROM course_classes cc 
       LEFT JOIN syllabus s ON s.course_code = cc.course_code 
       WHERE cc.id = ? 
       ORDER BY s.create_time DESC LIMIT 1`,
      [classId]
    );

    if (classRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }

    const courseCode = classRows[0].course_code;
    console.log("查询到课程代码:", courseCode);
    
    let syllabusContent = {};
    try {
      syllabusContent = JSON.parse(classRows[0].content || '{}');
      console.log("解析大纲内容成功");
    } catch (error) {
      console.error('解析大纲内容失败:', error);
      syllabusContent = {};
    }

    // 获取已发布作业的章节ID列表（从publish_exercise表中获取）
    const [exerciseRows] = await conn.execute(
      `SELECT DISTINCT eb.title_id 
       FROM publish_exercise pe
       JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id 
       WHERE pe.class_id = ? AND pe.study_phase = 2`,
      [classId]
    );

    console.log("已发布作业的章节ID:", exerciseRows.map(row => row.title_id));
    if (exerciseRows.length === 0) {
      console.log("未找到该班级已发布的作业");
      return res.json({
        success: true,
        data: []
      });
    }
    
    const publishedTitleIds = new Set(exerciseRows.map(row => row.title_id.toString()));
    const chapters = [];

    // 如果大纲为空但有发布的习题，至少返回这些章节的基本信息
    if (Object.keys(syllabusContent).length === 0 && publishedTitleIds.size > 0) {
      console.log("大纲为空，但找到了已发布的章节，将返回基本章节信息");
      exerciseRows.forEach(row => {
        chapters.push({
          id: row.title_id.toString(),
          title: `章节 ${row.title_id}`
        });
      });
      
      console.log("返回基本章节列表:", chapters);
      await conn.end();
      
      return res.json({
        success: true,
        data: chapters
      });
    }

    // 辅助函数：从大纲内容中查找title_id对应的完整标题路径
    const findTitlePathInSyllabus = (titleId, syllabusObj, prefix = "") => {
      for (const [key, value] of Object.entries(syllabusObj)) {
        const currentPath = prefix ? `${prefix} - ${key}` : key;
        
        if (typeof value === 'string' && value === titleId.toString()) {
          // 只返回当前节点的名称，而不是完整路径
          return { found: true, path: key, fullPath: currentPath };
        } else if (typeof value === 'object' && value !== null) {
          const result = findTitlePathInSyllabus(titleId, value, currentPath);
          if (result.found) {
            return result;
          }
        }
      }
      
      return { found: false, path: "", fullPath: "" };
    };

    // 处理每个已发布的章节ID
    for (const titleId of publishedTitleIds) {
      // 在大纲中查找该title_id
      const { found, path, fullPath } = findTitlePathInSyllabus(titleId, syllabusContent);
      
      if (found) {
        chapters.push({
          id: titleId,
          title: path,        // 只使用最小单元名称
          fullPath: fullPath  // 保留完整路径用于其他目的
        });
      } else {
        // 如果在大纲中找不到，使用通用标题
        chapters.push({
          id: titleId,
          title: `章节 ${titleId}`,
          fullPath: `章节 ${titleId}`
        });
      }
    }

    // 按照 id 排序
    chapters.sort((a, b) => parseInt(a.id) - parseInt(b.id));
    
    console.log("返回章节列表:", chapters);
    await conn.end();

    res.json({
      success: true,
      data: chapters
    });
  } catch (error) {
    console.error('获取班级章节列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取班级章节列表失败'
    });
  }
};

// 获取章节的习题列表
export const getChapterExercises = async (req, res) => {
  const { classId, titleId } = req.params;
  const studentId = req.query.studentId;
  
  if (!studentId) {
    return res.status(400).json({
      success: false,
      message: '缺少学生ID参数'
    });
  }
  
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 获取班级对应的课程代码
    const [classRows] = await conn.execute(
      `SELECT course_code FROM course_classes WHERE id = ?`,
      [classId]
    );
    
    if (classRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }
    
    const courseCode = classRows[0].course_code;
    
    // 获取章节下的习题并包含学生的答题记录
    const [exercises] = await conn.execute(
      `SELECT 
        pe.id as publish_id,
        eb.exercise_id,
        eb.title,
        eb.content,
        eb.difficulty,
        eb.question_type,
        pe.created_at as publish_time,
        pe.release_batch,
        pe.deadline,
        pe.time as time_limit,
        ser.student_answer,
        ser.is_correct
      FROM exercise_bank eb
      JOIN publish_exercise pe ON eb.exercise_id = pe.exercise_id
      LEFT JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
      WHERE pe.class_id = ? AND eb.title_id = ? AND pe.study_phase = 2
      ORDER BY pe.release_batch DESC, pe.id ASC`,
      [studentId, classId, titleId]
    );
    
    await conn.end();
    
    res.json({
      success: true,
      data: exercises
    });
  } catch (error) {
    console.error('获取章节习题失败:', error);
    res.status(500).json({
      success: false,
      message: '获取章节习题失败'
    });
  }
};

// 获取章节下的作业批次
export const getChapterHomework = async (req, res) => {
  const { classId, titleId } = req.params;
  const studentId = req.query.studentId;
  
  if (!studentId) {
    return res.status(400).json({
      success: false,
      message: '缺少学生ID参数'
    });
  }
  
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 查询该章节下的所有作业批次
    const [homeworks] = await conn.execute(
      `SELECT 
         pe.release_batch, 
         COUNT(pe.exercise_id) as exercise_count,
         MAX(pe.created_at) as release_time,
         MAX(pe.deadline) as deadline,
         MAX(pe.time) as time_limit
       FROM publish_exercise pe
       JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
       WHERE pe.class_id = ? AND eb.title_id = ? AND pe.study_phase = 2
       GROUP BY pe.release_batch
       ORDER BY pe.release_batch DESC`,
      [classId, titleId]
    );
    
    // 查询学生对每个批次的完成情况
    for (const homework of homeworks) {
      const [completion] = await conn.execute(
        `SELECT 
           COUNT(pe.id) as total,
           SUM(CASE WHEN ser.id IS NOT NULL THEN 1 ELSE 0 END) as completed
         FROM publish_exercise pe
         JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
         LEFT JOIN student_exercise_records ser 
           ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
         WHERE pe.class_id = ? AND pe.release_batch = ? AND eb.title_id = ? AND pe.study_phase = 2`,
        [studentId, classId, homework.release_batch, titleId]
      );
      
      if (completion.length > 0) {
        const stats = completion[0];
        homework.completion_status = {
          total: parseInt(stats.total || 0),
          completed: parseInt(stats.completed || 0),
          percentage: stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0,
          is_completed: stats.total > 0 && stats.completed >= stats.total
        };
      } else {
        homework.completion_status = {
          total: 0,
          completed: 0,
          percentage: 0,
          is_completed: false
        };
      }
      
      // 查询计时状态
      if (homework.time_limit > 0) {
        const [timerRecords] = await conn.execute(
          `SELECT 
             start_time, end_time, is_completed, is_timeout, title_id,
             TIMESTAMPDIFF(MINUTE, start_time, NOW()) as elapsed_minutes,
             TIMESTAMPDIFF(SECOND, start_time, end_time) as elapsed_seconds
           FROM student_homework_timer
           WHERE student_id = ? AND class_id = ? AND release_batch = ? AND title_id = ?`,
          [studentId, classId, homework.release_batch, titleId]
        );
        
        if (timerRecords.length > 0) {
          const timerRecord = timerRecords[0];
          
          // 检查是否已超时但未标记
          let isTimeout = timerRecord.is_timeout;
          if (!timerRecord.is_completed && !timerRecord.is_timeout && 
              homework.time_limit > 0 && timerRecord.elapsed_minutes > homework.time_limit) {
            // 标记为超时
            await conn.execute(
              `UPDATE student_homework_timer 
               SET is_timeout = true 
               WHERE student_id = ? AND class_id = ? AND release_batch = ? AND title_id = ?`,
              [studentId, classId, homework.release_batch, titleId]
            );
            isTimeout = true;
          }
          
          homework.timer_status = {
            has_started: true,
            start_time: timerRecord.start_time,
            end_time: timerRecord.end_time,
            is_completed: timerRecord.is_completed,
            is_timeout: isTimeout,
            elapsed_minutes: timerRecord.elapsed_minutes,
            elapsed_seconds: timerRecord.elapsed_seconds,
            completion_time: timerRecord.end_time,
            title_id: timerRecord.title_id || titleId  // 优先使用数据库中的title_id，如果没有则使用查询参数
          };
        } else {
          homework.timer_status = {
            has_started: false
          };
        }
      }
      
      // 获取分数信息
      if (homework.completion_status?.is_completed) {
        // 计算得分情况 - 确保只计算当前章节的题目
        const [scoreResult] = await conn.execute(
          `SELECT 
             COUNT(ser.id) as answered_count,
             SUM(CASE WHEN ser.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
             SUM(ser.score) as total_score
           FROM publish_exercise pe
           JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id
           JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
           WHERE pe.class_id = ? AND pe.release_batch = ? AND ser.student_id = ? 
             AND eb.title_id = ?  
             AND pe.study_phase = 2`,
          [classId, homework.release_batch, studentId, titleId]
        );
        
        // 获取所有题目信息用于计算满分 - 确保只包含当前章节
        const [exercises] = await conn.execute(
          `SELECT 
            eb.question_type
           FROM publish_exercise pe
           JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
           WHERE pe.class_id = ? AND pe.release_batch = ? 
             AND eb.title_id = ?
             AND pe.study_phase = 2`,
          [classId, homework.release_batch, titleId]
        );
        
        // 计算满分
        let fullScore = 0;
        for (const exercise of exercises) {
          const questionType = exercise.question_type;
          // 1-3类型题5分，4-5类型题10分
          if (questionType >= 1 && questionType <= 3) {
            fullScore += 5;
          } else if (questionType >= 4 && questionType <= 5) {
            fullScore += 10;
          }
        }
        
        if (scoreResult.length > 0) {
          const scoreData = scoreResult[0];
          homework.score = {
            answered_count: parseInt(scoreData.answered_count || 0),
            correct_count: parseInt(scoreData.correct_count || 0),
            total_score: parseInt(scoreData.total_score || 0),
            full_score: fullScore
          };
        }
      }
    }
    
    await conn.end();
    
    res.json({
      success: true,
      data: homeworks
    });
  } catch (error) {
    console.error('获取章节作业批次失败:', error);
    res.status(500).json({
      success: false,
      message: '获取章节作业批次失败'
    });
  }
};

// 获取班级的作业列表
export const getClassHomework = async (req, res) => {
  const classId = req.params.classId;
  const studentId = req.query.studentId;
  
  if (!studentId) {
    return res.status(400).json({
      success: false,
      message: '缺少学生ID参数'
    });
  }
  
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 查询该班级的作业（按release_batch分组）
    const [homeworks] = await conn.execute(
      `SELECT 
         pe.release_batch, 
         COUNT(pe.exercise_id) as exercise_count,
         MAX(pe.created_at) as release_time,
         MAX(pe.deadline) as deadline,
         MAX(pe.time) as time_limit
       FROM publish_exercise pe
       WHERE pe.class_id = ? AND pe.study_phase = 2
       GROUP BY pe.release_batch
       ORDER BY pe.release_batch DESC`,
      [classId]
    );
    
    // 查询学生对每个批次的完成情况
    for (const homework of homeworks) {
      const [completion] = await conn.execute(
        `SELECT 
           COUNT(pe.id) as total,
           SUM(CASE WHEN ser.id IS NOT NULL THEN 1 ELSE 0 END) as completed
         FROM publish_exercise pe
         LEFT JOIN student_exercise_records ser 
           ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
         WHERE pe.class_id = ? AND pe.release_batch = ? AND pe.study_phase = 2`,
        [studentId, classId, homework.release_batch]
      );
      
      if (completion.length > 0) {
        const stats = completion[0];
        homework.completion_status = {
          total: parseInt(stats.total || 0),
          completed: parseInt(stats.completed || 0),
          percentage: stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0,
          is_completed: stats.total > 0 && stats.completed >= stats.total
        };
      } else {
        homework.completion_status = {
          total: 0,
          completed: 0,
          percentage: 0,
          is_completed: false
        };
      }
      
      // 查询计时状态
      if (homework.time_limit > 0) {
        // 获取这批次作业涉及的所有章节(title_id)
        const [exerciseTitles] = await conn.execute(
          `SELECT DISTINCT eb.title_id 
           FROM publish_exercise pe
           JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
           WHERE pe.class_id = ? AND pe.release_batch = ? AND pe.study_phase = 2`,
          [classId, homework.release_batch]
        );
        
        // 如果有多个章节，就记录章节列表
        if (exerciseTitles.length > 0) {
          homework.title_ids = exerciseTitles.map(t => t.title_id);
        }
        
        // 查询是否有任何章节的作业已经开始
        const [timerRecords] = await conn.execute(
          `SELECT 
             title_id, start_time, end_time, is_completed, is_timeout,
             TIMESTAMPDIFF(MINUTE, start_time, NOW()) as elapsed_minutes,
             TIMESTAMPDIFF(SECOND, start_time, end_time) as elapsed_seconds
           FROM student_homework_timer
           WHERE student_id = ? AND class_id = ? AND release_batch = ?
           ORDER BY start_time DESC`,
          [studentId, classId, homework.release_batch]
        );
        
        if (timerRecords.length > 0) {
          const timerRecord = timerRecords[0];
          
          // 检查是否已超时但未标记
          let isTimeout = timerRecord.is_timeout;
          if (!timerRecord.is_completed && !timerRecord.is_timeout && 
              homework.time_limit > 0 && timerRecord.elapsed_minutes > homework.time_limit) {
            // 标记为超时
            await conn.execute(
              `UPDATE student_homework_timer 
               SET is_timeout = true 
               WHERE student_id = ? AND class_id = ? AND release_batch = ? AND title_id = ?`,
              [studentId, classId, homework.release_batch, timerRecord.title_id]
            );
            isTimeout = true;
          }
          
          homework.timer_status = {
            has_started: true,
            title_id: timerRecord.title_id, // 记录开始了哪个章节的作业
            start_time: timerRecord.start_time,
            end_time: timerRecord.end_time,
            is_completed: timerRecord.is_completed,
            is_timeout: isTimeout,
            elapsed_minutes: timerRecord.elapsed_minutes,
            elapsed_seconds: timerRecord.elapsed_seconds,
            completion_time: timerRecord.end_time
          };
        } else {
          homework.timer_status = {
            has_started: false
          };
        }
      }
      
      // 获取分数信息
      if (homework.completion_status?.is_completed) {
        // 计算得分情况
        const [scoreResult] = await conn.execute(
          `SELECT 
             COUNT(ser.id) as answered_count,
             SUM(CASE WHEN ser.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
             SUM(ser.score) as total_score
           FROM publish_exercise pe
           JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id
           JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
           WHERE pe.class_id = ? AND pe.release_batch = ? AND ser.student_id = ? AND pe.study_phase = 2`,
          [classId, homework.release_batch, studentId]
        );
        
        // 获取所有题目信息用于计算满分
        const [exercises] = await conn.execute(
          `SELECT 
            eb.question_type
           FROM publish_exercise pe
           JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
           WHERE pe.class_id = ? AND pe.release_batch = ? AND pe.study_phase = 2`,
          [classId, homework.release_batch]
        );
        
        // 计算满分
        let fullScore = 0;
        for (const exercise of exercises) {
          const questionType = exercise.question_type;
          // 1-3类型题5分，4-5类型题10分
          if (questionType >= 1 && questionType <= 3) {
            fullScore += 5;
          } else if (questionType >= 4 && questionType <= 5) {
            fullScore += 10;
          }
        }
        
        if (scoreResult.length > 0) {
          const scoreData = scoreResult[0];
          homework.score = {
            answered_count: parseInt(scoreData.answered_count || 0),
            correct_count: parseInt(scoreData.correct_count || 0),
            total_score: parseInt(scoreData.total_score || 0),
            full_score: fullScore
          };
        }
      }
    }
    
    await conn.end();
    
    res.json({
      success: true,
      data: homeworks
    });
  } catch (error) {
    console.error('获取班级作业失败:', error);
    res.status(500).json({
      success: false,
      message: '获取班级作业失败'
    });
  }
};

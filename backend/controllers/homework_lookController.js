import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 获取已提交作业的详细信息
export const getHomeworkDetails = async (req, res) => {
  try {
    const { classId, batchId } = req.params;
    const { chapterId } = req.query; // 从查询参数中获取章节ID
    const studentId = req.user?.student_id || req.user?.id;
    
    if (!classId || !batchId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    const conn = await mysql.createConnection(dbConfig);
    const queryChapterId = chapterId ? parseInt(chapterId, 10) : null;
    
    console.log(`获取作业详情 - 班级:${classId}, 批次:${batchId}, 章节:${queryChapterId}`);
    
    // 构建基础SQL查询 - 始终使用章节ID筛选
    let sql = `SELECT 
        pe.id as publish_id,
        eb.exercise_id,
        eb.content,
        eb.answer,
        eb.analysis,
        eb.point,
        eb.question_type,
        eb.option,
        eb.title_id,
        ser.student_answer,
        ser.is_correct,
        ser.score,
        ser.evaluation,
        ser.create_time as submit_time
      FROM student_exercise_records ser
      JOIN publish_exercise pe ON pe.id = ser.publish_exercise_id
      JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
      WHERE ser.student_id = ? AND pe.class_id = ? AND pe.release_batch = ? AND pe.study_phase = 2`;
    
    // 如果提供了章节ID，添加章节过滤条件
    let params = [studentId.toString(), classId, batchId];
    if (queryChapterId) {
      sql += ` AND eb.title_id = ?`;
      params.push(queryChapterId);
    }
    
    sql += ` ORDER BY pe.id ASC`;
    
    // 获取本批次作业的所有习题和学生提交记录
    const [exercises] = await conn.query(sql, params);
    
    // 检查作业是否存在
    if (exercises.length === 0) {
      await conn.end();
      return res.status(404).json({
        success: false,
        message: '未找到作业记录'
      });
    }
    
    // 获取作业提交信息 - 添加章节ID过滤
    let timerSql = `SELECT 
        start_time, 
        end_time, 
        is_completed, 
        is_timeout
      FROM student_homework_timer
      WHERE student_id = ? AND class_id = ? AND release_batch = ?`;
    
    let timerParams = [studentId.toString(), classId, batchId];
    if (queryChapterId) {
      timerSql += ` AND title_id = ?`;
      timerParams.push(queryChapterId);
    }
    
    timerSql += ` LIMIT 1`;
    
    const [submissionInfo] = await conn.query(timerSql, timerParams);
    
    // 格式化习题数据
    const formattedExercises = exercises.map(exercise => {
      let options = [];
      try {
        if (exercise.option) {
          const optionObj = JSON.parse(exercise.option);
          options = Object.entries(optionObj).map(([key, value]) => ({
            key,
            value
          }));
        }
      } catch (e) {
        console.error('解析选项失败:', e);
      }
      
      // 为判断题设置标准选项
      if (exercise.question_type === 3) {
        options = [
          { key: 'true', value: '正确' },
          { key: 'false', value: '错误' }
        ];
      }
      
      // 处理知识点
      let points = [];
      try {
        if (exercise.point) {
          // 检查是否是JSON格式
          if (exercise.point.startsWith('[') || exercise.point.startsWith('{')) {
            try {
              points = JSON.parse(exercise.point);
              if (!Array.isArray(points)) {
                points = [points]; // 如果不是数组，转为数组
              }
            } catch (e) {
              // JSON解析失败，视为普通文本
              points = [exercise.point];
            }
          } else {
            // 不是JSON格式，直接作为文本处理
            points = [exercise.point];
          }
        }
      } catch (e) {
        console.error('解析知识点失败:', e);
        points = exercise.point ? [exercise.point] : [];
      }

      // 确保分数是有效的整数
      let score = 0;
      if (exercise.score !== null && exercise.score !== undefined) {
        const numScore = Number(exercise.score);
        score = !isNaN(numScore) ? Math.round(numScore) : 0;
      }

      return {
        ...exercise,
        options,
        points,
        student_answer: exercise.student_answer || '未作答',
        analysis: exercise.analysis || '暂无解析',
        point: points,
        score: score,
        evaluation: exercise.evaluation || '',
        title_id: exercise.title_id // 保留章节ID信息
      };
    });
    
    // 计算总分和满分
    const scoreInfo = formattedExercises.reduce((acc, exercise) => {
      // 判断题型设置满分
      const maxScore = (exercise.question_type <= 3) ? 5 : 10;
      
      // 累加总得分和满分（确保是有效数字）
      const exerciseScore = exercise.score || 0;
      
      return {
        earned: acc.earned + exerciseScore,
        total: acc.total + maxScore
      };
    }, { earned: 0, total: 0 });
    
    await conn.end();
    
    res.json({
      success: true,
      data: {
        exercises: formattedExercises,
        submission: submissionInfo.length > 0 ? submissionInfo[0] : null,
        score: scoreInfo,
        chapterId: queryChapterId // 返回章节ID以便前端使用
      }
    });
  } catch (error) {
    console.error('获取作业详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取作业详情失败: ' + error.message
    });
  }
};

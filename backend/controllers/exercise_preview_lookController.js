import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 获取预习题提交记录
export const getSubmissionRecords = async (req, res) => {
  try {
    const { classId, chapterId } = req.params;
    const { studentId } = req.query;
    const userIdToUse = studentId || req.user?.student_id || req.user?.id;
    
    console.log('获取提交记录参数:', { classId, chapterId, studentId: userIdToUse });
    
    if (!classId || !chapterId || !userIdToUse) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    const conn = await mysql.createConnection(dbConfig);
    
    // 修正字段名：改用create_time而不是created_at
    let sql = `
      SELECT 
        pe.id as publish_id,
        eb.exercise_id,
        eb.content,
        eb.question_type,
        eb.option,
        eb.title_id,
        eb.difficulty,
        eb.title,
        eb.answer,
        eb.point as knowledge_points,
        eb.analysis as explanation,
        ser.student_answer,
        ser.is_correct,
        ser.score,
        ser.evaluation,
        ser.time_spent,
        ser.create_time as submission_time
      FROM publish_exercise pe
      JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
      JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id
      WHERE pe.class_id = ? 
        AND eb.title_id = ? 
        AND ser.student_id = ?
        AND pe.study_phase = 1
      ORDER BY pe.id ASC`;
    
    console.log('执行SQL查询:', sql, [classId, chapterId, userIdToUse.toString()]);
    
    const [exercises] = await conn.query(sql, [classId, chapterId, userIdToUse.toString()]);
    
    console.log(`查询结果: 获取到 ${exercises.length} 条记录`);
    
    if (exercises.length === 0) {
      await conn.end();
      return res.json({
        success: true,
        data: {
          exercises: [],
          submission_time: null,
          total_score: 0
        }
      });
    }
    
    // 处理题目选项和学生答案
    const processedExercises = exercises.map(exercise => {
      let options = [];
      if (exercise.option) {
        try {
          const optionObj = JSON.parse(exercise.option);
          options = Object.entries(optionObj).map(([key, value]) => ({
            key,
            value
          }));
        } catch (e) {
          console.error(`解析题目选项出错 (ID:${exercise.publish_id}):`, e);
        }
      }
      
      const lastSubmissionTime = exercise.submission_time;
      
      switch (exercise.question_type) {
        case 1: // 单选题
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || '',
            submission_time: lastSubmissionTime
          };
          
        case 2: // 填空题
          return {
            ...exercise,
            student_answer: exercise.student_answer || '',
            submission_time: lastSubmissionTime
          };
          
        case 3: // 判断题
          options = [
            { key: 'true', value: '正确' },
            { key: 'false', value: '错误' }
          ];
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || '',
            submission_time: lastSubmissionTime
          };
          
        case 4: // 简答题
        case 5: // 计算题
          return {
            ...exercise,
            student_answer: exercise.student_answer || '',
            submission_time: lastSubmissionTime
          };
          
        default:
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || '',
            submission_time: lastSubmissionTime
          };
      }
    });
    
    // 计算总分
    const totalScore = processedExercises.reduce((sum, exercise) => sum + (exercise.score || 0), 0);
    
    // 获取最后提交时间
    const submissionTime = processedExercises.length > 0 ? 
      processedExercises[0].submission_time : null;
    
    await conn.end();
    
    res.json({
      success: true,
      data: {
        exercises: processedExercises,
        submission_time: submissionTime,
        total_score: totalScore
      }
    });
  } catch (error) {
    console.error('获取预习题提交记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习题提交记录失败: ' + error.message
    });
  }
};

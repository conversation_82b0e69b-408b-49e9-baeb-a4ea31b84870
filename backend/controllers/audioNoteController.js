import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 设置Python脚本路径
const PY_SCRIPT_PATH = path.join(__dirname, '../scripts/audio_processor.py');

// 设置上传目录
const UPLOAD_DIR = path.join(__dirname, '../uploads/audio');

// 确保上传目录存在
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOAD_DIR);
  },
  filename: function (req, file, cb) {
    const uniqueId = uuidv4();
    const ext = path.extname(file.originalname) || '.webm';
    cb(null, `${uniqueId}${ext}`);
  }
});

// 创建上传中间件
export const upload = multer({ 
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 } // 限制50MB
});

// 转录音频为文本
async function transcribeAudio(audioPath) {
  return new Promise((resolve, reject) => {
    // 获取音频所在目录
    const audioDir = path.dirname(audioPath);
    const audioId = path.basename(audioPath, path.extname(audioPath));
    const transcriptPath = path.join(audioDir, `${audioId}_transcript.txt`);
    
    console.log(`正在转录音频: ${audioPath}`);
    console.log(`转录输出路径: ${transcriptPath}`);
    
    // 检查是否已经转录过
    if (fs.existsSync(transcriptPath)) {
      console.log(`转录文件已存在，跳过转录: ${transcriptPath}`);
      // 读取转录结果
      fs.readFile(transcriptPath, 'utf8', (err, data) => {
        if (err) {
          reject(new Error(`读取转录结果失败: ${err.message}`));
          return;
        }
        
        resolve({
          transcript: data,
          duration: 300 // 使用默认时长
        });
      });
      return;
    }
    
    // 使用Python脚本进行转录
    const pythonProcess = spawn('python', [
      PY_SCRIPT_PATH,
      'transcribe',
      audioPath,
      transcriptPath
    ], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
      },
      timeout: 600000 // 设置10分钟超时
    });
    
    let errorData = '';
    let outputData = '';
    
    pythonProcess.stdout.on('data', (data) => {
      outputData += data.toString();
      console.log(`Python输出: ${data.toString()}`);
    });
    
    pythonProcess.stderr.on('data', (data) => {
      errorData += data.toString();
      console.error(`Python错误: ${data.toString()}`);
    });
    
    pythonProcess.on('close', (code) => {
      if (code !== 0) {
        console.error(`Python进程退出，状态码 ${code}, 错误: ${errorData}`);
        reject(new Error(`音频转录失败: ${errorData}`));
        return;
      }
      
      // 读取转录结果
      fs.readFile(transcriptPath, 'utf8', (err, data) => {
        if (err) {
          reject(new Error(`读取转录结果失败: ${err.message}`));
          return;
        }
        
        // 获取音频时长
        const durationProcess = spawn('python', [
          PY_SCRIPT_PATH,
          'get_duration',
          audioPath
        ], {
          env: {
            ...process.env,
            PYTHONIOENCODING: 'utf-8',
          }
        });
        
        let durationOutput = '';
        
        durationProcess.stdout.on('data', (chunk) => {
          durationOutput += chunk.toString();
        });
        
        durationProcess.on('close', (durationCode) => {
          let duration = 0;
          
          if (durationCode === 0 && durationOutput.trim()) {
            try {
              duration = parseFloat(durationOutput.trim());
            } catch (e) {
              console.error('解析音频时长失败:', e);
              duration = 0;
            }
          }
          
          resolve({
            transcript: data,
            duration: duration || 300 // 如果获取失败，使用默认时长
          });
        });
      });
    });
    
    pythonProcess.on('error', (err) => {
      console.error(`启动Python进程失败: ${err.message}`);
      reject(new Error(`启动Python进程失败: ${err.message}`));
    });
  });
}

// 使用模拟数据进行转录（当讯飞API不可用时）
async function mockTranscription(audioPath) {
  return new Promise((resolve) => {
    // 模拟处理延迟
    setTimeout(() => {
      const mockTranscript = `这是一段模拟的音频转录文本。
由于当前环境无法使用讯飞API，系统生成了这段模拟文本。
实际使用时，这里将是真实的音频转录内容。
音频内容可能包含重要信息、会议记录、课程内容等。
AI将帮助您分析这些内容并生成结构化的笔记。`;
      
      resolve({
        transcript: mockTranscript,
        duration: 120 // 模拟2分钟的音频
      });
    }, 2000);
  });
}

// 转录音频API
export const transcribeAudioFile = async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权的访问'
      });
    }
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未找到上传的音频文件'
      });
    }
    
    const audioPath = req.file.path;
    console.log(`接收到音频文件: ${audioPath}`);
    
    let transcript = '';
    let duration = 0;
    
    try {
      console.log('开始处理音频转录');
      
      // 使用更长的超时时间
      const transcriptionPromise = transcribeAudio(audioPath);
      
      // 设置超时保护
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('讯飞API转录超时，请稍后再试'));
        }, 600000); // 10分钟超时
      });
      
      // 使用Promise.race让两个Promise竞争
      const result = await Promise.race([
        transcriptionPromise,
        timeoutPromise
      ]);
      
      transcript = result.transcript;
      duration = result.duration;
      console.log(`转录完成，获取到文本内容 ${transcript.length} 字符`);
      
    } catch (transcriptionError) {
      console.error('转录失败，使用模拟数据:', transcriptionError);
      
      const mockMessage = `转录失败: ${transcriptionError.message}。使用模拟数据代替。`;
      console.log(mockMessage);
      
      // 使用模拟数据作为备选
      try {
        console.log('使用模拟转录作为备选');
        const mockResult = await mockTranscription(audioPath);
        transcript = mockResult.transcript;
        duration = mockResult.duration;
        
        console.log('使用模拟数据成功');
      } catch (mockError) {
        console.error('模拟数据生成失败:', mockError);
        throw mockError;
      }
    }
    
    // 解析转录文本，按句子或段落分割
    const sentences = transcript.split(/(?<=[。！？.!?])\s*/);
    const transcriptSegments = sentences.map((text, index) => {
      const start = Math.floor((duration / sentences.length) * index);
      const end = Math.floor((duration / sentences.length) * (index + 1));
      return { start, end, text };
    });
    
    return res.json({
      success: true,
      data: {
        transcript,
        transcriptSegments,
        duration,
        audioId: path.basename(audioPath, path.extname(audioPath))
      }
    });
    
  } catch (error) {
    console.error('音频转录失败:', error);
    return res.status(500).json({
      success: false,
      message: `音频转录失败: ${error.message}`
    });
  }
};

// 生成笔记API
export const generateNote = async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权的访问'
      });
    }
    
    const { transcript, title } = req.body;
    
    if (!transcript) {
      return res.status(400).json({
        success: false,
        message: '缺少转录文本'
      });
    }
    
    // 使用OpenAI API生成笔记
    // 这里使用模拟数据，实际应用中应调用AI API
    const summary = `# ${title || '录音笔记'}

## 主要内容

${transcript.split(/[。！？.!?]/).slice(0, 3).join('。')}...

## 要点总结

${transcript.split(/[。！？.!?]/).filter((s, i) => i % 3 === 0).map(s => `- ${s}`).join('\n')}

## 详细内容

${transcript}

---
*此笔记由AI自动生成，可能需要进一步编辑和完善。*`;
    
    return res.json({
      success: true,
      data: {
        summary
      }
    });
    
  } catch (error) {
    console.error('生成笔记失败:', error);
    return res.status(500).json({
      success: false,
      message: `生成笔记失败: ${error.message}`
    });
  }
};

// 保存笔记API
export const saveNote = async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权的访问'
      });
    }
    
    const { title, content, audioId } = req.body;
    
    if (!content) {
      return res.status(400).json({
        success: false,
        message: '缺少笔记内容'
      });
    }
    
    // 这里应该将笔记保存到数据库
    // 为简化示例，这里只返回成功消息
    
    return res.json({
      success: true,
      message: '笔记保存成功',
      data: {
        id: uuidv4(), // 模拟生成的笔记ID
        title,
        content: content.substring(0, 100) + '...' // 返回内容摘要
      }
    });
    
  } catch (error) {
    console.error('保存笔记失败:', error);
    return res.status(500).json({
      success: false,
      message: `保存笔记失败: ${error.message}`
    });
  }
};

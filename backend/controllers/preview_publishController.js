import pool from '../config/db.js';

// 获取班级的章节列表（包含预习资料信息）
export const getClassChaptersWithPreview = async (req, res) => {
  try {
    console.log('\n=== 获取班级章节列表（包含预习资料信息） ===');
    const { classId } = req.params;
    const teacherId = req.user?.system_teacher_id;

    console.log('查询参数:', { classId, teacherId });

    // 获取班级信息
    const [classInfo] = await pool.execute(
      `SELECT cc.*, c.course_code
       FROM course_classes cc
       JOIN courses c ON cc.course_code = c.course_code
       WHERE cc.id = ? AND cc.teacher_id = ?`,
      [classId, teacherId]
    );

    console.log('班级查询结果:', { 
      查询到的班级数量: classInfo.length, 
      班级ID: classId,
      老师ID: teacherId, 
      SQL: `SELECT cc.*, c.course_code FROM course_classes cc JOIN courses c ON cc.course_code = c.course_code WHERE cc.id = ${classId} AND cc.teacher_id = ${teacherId}`
    });

    if (classInfo.length === 0) {
      console.log('❌ 未找到班级信息');
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }

    const courseCode = classInfo[0].course_code;
    const className = classInfo[0].class_name || '未知班级';
    console.log('班级信息:', { 
      班级ID: classId, 
      班级名称: className, 
      课程代码: courseCode, 
      老师ID: teacherId 
    });

    // 获取课程大纲
    const [syllabusRows] = await pool.execute(
      `SELECT id, content, create_time
       FROM syllabus 
       WHERE course_code = ? AND teacher_id = ?
       ORDER BY create_time DESC LIMIT 1`,
      [courseCode, teacherId]
    );

    console.log('大纲查询结果:', { 
      查询到的大纲数量: syllabusRows.length, 
      课程代码: courseCode, 
      老师ID: teacherId, 
      大纲ID: syllabusRows.length > 0 ? syllabusRows[0].id : null,
      创建时间: syllabusRows.length > 0 ? syllabusRows[0].create_time : null
    });

    if (syllabusRows.length === 0) {
      console.log('❌ 未找到课程大纲');
      return res.status(404).json({
        success: false,
        message: '未找到课程大纲'
      });
    }

    // 解析大纲内容
    let syllabusContent;
    try {
      syllabusContent = JSON.parse(syllabusRows[0].content);
      console.log('大纲内容解析成功，大纲结构为:', Object.keys(syllabusContent));
    } catch (error) {
      console.error('❌ 大纲内容解析失败:', error);
      return res.status(500).json({
        success: false,
        message: '大纲内容格式错误'
      });
    }
    
    // 获取该课程的所有预习资料
    const [previewMaterials] = await pool.execute(
      `SELECT id, title_id, chapter_title, create_time
       FROM preview_materials 
       WHERE course_code = ? AND teacher_id = ?`,
      [courseCode, teacherId]
    );

    console.log('预习资料查询结果:', { 
      查询到的预习资料数量: previewMaterials.length, 
      课程代码: courseCode, 
      老师ID: teacherId,
      SQL: `SELECT id, title_id, chapter_title, create_time FROM preview_materials WHERE course_code = ${courseCode} AND teacher_id = ${teacherId}`
    });

    // 输出预习资料详情
    if (previewMaterials.length > 0) {
      console.log('预习资料列表:');
      previewMaterials.forEach((pm, index) => {
        console.log(`[${index + 1}] ID: ${pm.id}, 题目ID: ${pm.title_id}, 章节标题: ${pm.chapter_title}, 创建时间: ${pm.create_time}`);
      });
    } else {
      console.log('❌ 未找到预习资料');
    }

    // 将预习资料ID转为Set，方便快速查找
    const previewTitleIds = new Set(previewMaterials.map(pm => pm.title_id.toString()));
    
    // 提取章节列表，并标记有预习资料的章节
    const chapterList = [];
    
    const processNode = (node, parentTitle = '') => {
      Object.entries(node).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          // 如果是章节标题，略过
          processNode(value, key);
        } else {
          // 如果是具体课时
          const titleId = value.toString();
          const previewMaterial = previewMaterials.find(pm => pm.title_id === titleId);
          if (previewMaterial) {
            chapterList.push({
              id: titleId,
              title: previewMaterial.chapter_title || (parentTitle ? `${parentTitle} - ${key}` : key),
              hasPreview: true
            });
          }
        }
      });
    };
    
    processNode(syllabusContent);
    
    console.log(`✓ 找到 ${chapterList.length} 个章节，其中 ${previewTitleIds.size} 个有预习资料`);

    // 输出章节列表详情
    if (chapterList.length > 0) {
      console.log('章节列表:');
      chapterList.forEach((chapter, index) => {
        console.log(`[${index + 1}] ID: ${chapter.id}, 标题: ${chapter.title}, 有预习资料: ${chapter.hasPreview}`);
      });
    } else {
      console.log('❌ 没有找到具有预习资料的章节');
    }

    console.log('返回数据:', { success: true, data: chapterList });

    res.json({
      success: true,
      data: chapterList
    });

  } catch (error) {
    console.log('\n❌ 获取章节列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取章节列表失败'
    });
  }
};

// 发布预习资料
export const publishPreview = async (req, res) => {
  try {
    console.log('\n=== 发布预习资料 ===');
    const { classId } = req.params;
    const { titleId, title, deadline } = req.body;
    const teacherId = req.user?.system_teacher_id;

    console.log('发布参数:', { classId, titleId, title, deadline, teacherId });

    if (!titleId || !title || !deadline) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 获取班级信息
    const [classInfo] = await pool.execute(
      `SELECT cc.*, cc.course_code
       FROM course_classes cc
       WHERE cc.id = ? AND cc.teacher_id = ?`,
      [classId, teacherId]
    );

    if (classInfo.length === 0) {
      console.log('❌ 未找到班级信息');
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }

    const courseCode = classInfo[0].course_code;

    // 检查预习资料是否存在
    const [previewMaterials] = await pool.execute(
      `SELECT id
       FROM preview_materials
       WHERE course_code = ? AND title_id = ? AND teacher_id = ?`,
      [courseCode, titleId, teacherId]
    );

    if (previewMaterials.length === 0) {
      console.log('❌ 未找到预习资料');
      return res.status(404).json({
        success: false,
        message: '未找到预习资料'
      });
    }

    const previewMaterialId = previewMaterials[0].id;

    // 插入预习发布记录
    const [result] = await pool.execute(
      `INSERT INTO preview_publish (
        class_id, course_code, preview_material_id, title, deadline
      ) VALUES (?, ?, ?, ?, ?)`,
      [
        classId, 
        courseCode, 
        previewMaterialId, 
        title, 
        new Date(deadline)
      ]
    );

    console.log('✓ 发布预习资料成功');

    res.json({
      success: true,
      data: {
        id: result.insertId,
        title,
        deadline
      },
      message: '发布预习资料成功'
    });

  } catch (error) {
    console.log('\n❌ 发布预习资料失败:', error);
    res.status(500).json({
      success: false,
      message: '发布预习资料失败'
    });
  }
};

// 获取班级的预习列表
export const getClassPreviews = async (req, res) => {
  try {
    console.log('\n=== 获取班级预习列表 ===');
    const { classId } = req.params;
    const { chapter } = req.query;
    const teacherId = req.user?.system_teacher_id;

    console.log('查询参数:', { classId, chapter, teacherId });

    // 获取班级信息
    const [classInfo] = await pool.execute(
      `SELECT cc.*, c.course_code, c.course_name
       FROM course_classes cc
       JOIN courses c ON cc.course_code = c.course_code
       WHERE cc.id = ?`,
      [classId]
    );

    console.log('班级信息查询结果:', { 
      查询到的班级数量: classInfo.length, 
      班级ID: classId,
      SQL: `SELECT cc.*, c.course_code, c.course_name FROM course_classes cc JOIN courses c ON cc.course_code = c.course_code WHERE cc.id = ${classId}`
    });

    if (classInfo.length > 0) {
      const className = classInfo[0].class_name || '未知班级';
      const courseCode = classInfo[0].course_code;
      const courseName = classInfo[0].course_name;
      console.log('班级详情:', { 
        班级ID: classId, 
        班级名称: className, 
        课程代码: courseCode, 
        课程名称: courseName,
        教师ID: classInfo[0].teacher_id
      });
    } else {
      console.log('❌ 未找到班级信息');
    }

    // 先获取班级学生总数
    const [studentCount] = await pool.execute(
      `SELECT COUNT(*) as total 
       FROM student_course_registration 
       WHERE class_id = ? AND status = 1`,
      [classId]
    );
    
    const totalStudents = studentCount[0].total;
    console.log(`班级 ${classId} 总学生数: ${totalStudents}`);

    // 构建SQL查询
    let sql = `
      SELECT 
        pp.id, pp.title, pp.deadline, pp.create_time as publishTime,
        pm.title_id, pm.chapter_title, 
        (SELECT COUNT(*) FROM preview_student WHERE preview_publish_id = pp.id) as submittedCount,
        (SELECT SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) FROM preview_student WHERE preview_publish_id = pp.id) as completedStudents
      FROM preview_publish pp
      JOIN preview_materials pm ON pp.preview_material_id = pm.id
      JOIN course_classes cc ON pp.class_id = cc.id
      WHERE pp.class_id = ? AND cc.teacher_id = ?
    `;
    
    const params = [classId, teacherId];
    
    // 如果指定了章节，添加过滤条件
    if (chapter) {
      sql += ` AND pm.title_id = ?`;
      params.push(chapter);
    }
    
    sql += ` ORDER BY pp.create_time DESC`;

    console.log('预习列表查询SQL:', {
      SQL: sql.replace(/\s+/g, ' '),
      参数: params,
      条件: { 班级ID: classId, 老师ID: teacherId, 章节ID: chapter || '未指定' }
    });

    const [previews] = await pool.execute(sql, params);

    console.log('预习列表查询结果:', { 
      查询到的预习数量: previews.length,
      班级ID: classId,
      老师ID: teacherId,
      章节ID: chapter || '未指定'
    });

    if (previews.length > 0) {
      console.log('预习列表详情:');
      previews.forEach((p, index) => {
        console.log(`[${index + 1}] ID: ${p.id}, 标题: ${p.title}, 章节: ${p.chapter_title}, 发布时间: ${p.publishTime}`);
      });
    }

    // 计算完成率
    const formattedPreviews = previews.map(preview => {
      const completedCount = preview.completedStudents || 0;
      const submittedCount = preview.submittedCount || 0;
      
      // 使用班级总学生数作为分母
      const completion = totalStudents > 0 
        ? Math.round((completedCount / totalStudents) * 100) 
        : 0;
      
      return {
        id: preview.id,
        title: preview.title,
        chapter: preview.chapter_title,
        publishTime: new Date(preview.publishTime).toLocaleString(),
        deadline: new Date(preview.deadline).toLocaleString(),
        submittedCount: completedCount,  // 使用已完成学生数作为提交计数
        totalStudents: totalStudents,    // 使用班级总学生数
        completion: completion,          // 正确计算的完成率
        titleId: preview.title_id
      };
    });

    console.log(`✓ 找到 ${formattedPreviews.length} 条预习记录`);
    console.log('返回数据:', { success: true, data: formattedPreviews });

    res.json({
      success: true,
      data: formattedPreviews
    });

  } catch (error) {
    console.log('\n❌ 获取预习列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习列表失败'
    });
  }
};

// 获取预习详情
export const getPreviewDetail = async (req, res) => {
  try {
    console.log('\n=== 获取预习详情 ===');
    const { previewId } = req.params;
    const teacherId = req.user?.system_teacher_id;

    console.log('查询参数:', { previewId, teacherId });

    // 获取预习发布记录，同时获取class_id
    const [preview] = await pool.execute(
      `SELECT pp.*, pm.content, pm.chapter_title, pp.class_id
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       JOIN course_classes cc ON pp.class_id = cc.id
       WHERE pp.id = ? AND cc.teacher_id = ?`,
      [previewId, teacherId]
    );

    if (preview.length === 0) {
      console.log('❌ 未找到预习记录');
      return res.status(404).json({
        success: false,
        message: '未找到预习记录'
      });
    }

    const classId = preview[0].class_id;
    console.log('获取到班级ID:', classId);

    // 1. 获取班级所有学生
    const [allStudents] = await pool.execute(
      `SELECT s.student_id, s.name as student_name
       FROM student_course_registration scr
       JOIN students s ON scr.student_id = s.student_id
       WHERE scr.class_id = ? AND scr.status = 1`,
      [classId]
    );
    
    console.log(`班级 ${classId} 共有 ${allStudents.length} 名学生`);
    
    // 2. 获取所有提交记录（包括未完成和已完成）
    const [submissions] = await pool.execute(
      `SELECT 
        ps.id, ps.student_id, ps.status, ps.submit_time,
        ps.total_view_duration, ps.total_view_count,
        s.name as student_name
       FROM preview_student ps
       JOIN students s ON ps.student_id = s.student_id
       WHERE ps.preview_publish_id = ?`,
      [previewId]
    );
    
    console.log(`预习任务 ${previewId} 提交记录: ${submissions.length}条`);
    
    // 3. 创建提交记录映射，用于快速查找
    const submissionMap = new Map();
    submissions.forEach(s => {
      submissionMap.set(s.student_id, s);
    });
    
    // 4. 处理每个学生的状态
    const studentsList = [];
    
    for (const student of allStudents) {
      const submission = submissionMap.get(student.student_id);
      
      if (submission && submission.status === 1) {
        // 已完成
        studentsList.push({
          id: submission.id,
          studentId: student.student_id,
          studentName: student.student_name,
          status: 1,
          submitTime: submission.submit_time ? new Date(submission.submit_time).toLocaleString() : null,
          totalViewDuration: submission.total_view_duration || 0,
          totalViewCount: submission.total_view_count || 0
        });
      } else {
        // 未完成
        studentsList.push({
          studentId: student.student_id,
          studentName: student.student_name,
          status: 0,
          submitTime: null
        });
      }
    }
    
    // 统计已完成和未完成学生数量
    const completedCount = studentsList.filter(s => s.status === 1).length;
    const uncompletedCount = studentsList.filter(s => s.status === 0).length;
    
    console.log(`学生统计: 总数=${studentsList.length}, 已完成=${completedCount}, 未完成=${uncompletedCount}`);

    const previewDetail = {
      ...preview[0],
      publishTime: new Date(preview[0].create_time).toLocaleString(),
      deadline: new Date(preview[0].deadline).toLocaleString(),
      students: studentsList,
      stats: {
        totalStudents: studentsList.length,
        completedStudents: completedCount,
        uncompletedStudents: uncompletedCount,
        completionRate: studentsList.length > 0 ? Math.round((completedCount / studentsList.length) * 100) : 0
      }
    };

    console.log('✓ 获取预习详情成功');

    res.json({
      success: true,
      data: previewDetail
    });

  } catch (error) {
    console.log('\n❌ 获取预习详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习详情失败: ' + error.message
    });
  }
};

// 取消发布预习资料
export const cancelPreview = async (req, res) => {
  try {
    console.log('\n=== 取消发布预习资料 ===');
    const { previewId } = req.params;
    const teacherId = req.user?.system_teacher_id;

    console.log('取消参数:', { previewId, teacherId });

    // 检查预习是否存在
    const [preview] = await pool.execute(
      `SELECT pp.* 
       FROM preview_publish pp
       JOIN course_classes cc ON pp.class_id = cc.id
       WHERE pp.id = ? AND cc.teacher_id = ?`,
      [previewId, teacherId]
    );

    if (preview.length === 0) {
      console.log('❌ 未找到预习记录');
      return res.status(404).json({
        success: false,
        message: '未找到预习记录'
      });
    }

    // 删除预习记录
    await pool.execute(
      `DELETE FROM preview_publish WHERE id = ?`,
      [previewId]
    );

    // 删除相关的学生预习记录
    await pool.execute(
      `DELETE FROM preview_student WHERE preview_publish_id = ?`,
      [previewId]
    );

    console.log('✓ 取消发布预习资料成功');

    res.json({
      success: true,
      message: '取消发布预习资料成功'
    });

  } catch (error) {
    console.log('\n❌ 取消发布预习资料失败:', error);
    res.status(500).json({
      success: false,
      message: '取消发布预习资料失败'
    });
  }
};

// 获取预习资料内容
export const getPreviewMaterialContent = async (req, res) => {
  try {
    console.log('\n=== 获取预习资料内容 ===');
    const { titleId } = req.params;
    const teacherId = req.user?.system_teacher_id;

    console.log('查询参数:', { titleId, teacherId });

    if (!titleId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    // 从preview_materials表中获取预习资料内容
    const [material] = await pool.execute(
      `SELECT id, syllabus_id, title_id, content, chapter_title, course_code, create_time
       FROM preview_materials
       WHERE title_id = ? AND teacher_id = ?`,
      [titleId, teacherId]
    );

    console.log('预习资料查询结果:', { 
      查询到的资料数量: material.length, 
      标题ID: titleId, 
      教师ID: teacherId,
      SQL: `SELECT id, syllabus_id, title_id, content, chapter_title, course_code, create_time FROM preview_materials WHERE title_id = ${titleId} AND teacher_id = ${teacherId}`
    });

    if (material.length === 0) {
      console.log('❌ 未找到预习资料');
      return res.status(404).json({
        success: false,
        message: '未找到预习资料'
      });
    }
    
    const previewMaterial = material[0];
    console.log('✓ 成功获取预习资料');

    res.json({
      success: true,
      data: {
        id: previewMaterial.id,
        title_id: previewMaterial.title_id,
        chapter_title: previewMaterial.chapter_title,
        content: previewMaterial.content,
        course_code: previewMaterial.course_code,
        create_time: previewMaterial.create_time
      }
    });

  } catch (error) {
    console.log('\n❌ 获取预习资料内容失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习资料内容失败'
    });
  }
};

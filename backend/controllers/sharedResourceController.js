import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 获取教师的班级列表
export const getTeacherClasses = async (req, res) => {
  const pool = mysql.createPool(dbConfig);
  
  try {
    console.log('认证用户信息:', JSON.stringify(req.user));
    const teacherId = req.user?.system_teacher_id; // 从认证中间件中获取教师系统ID
    
    if (!teacherId) {
      console.error('未能获取到教师ID, req.user:', req.user);
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }
    
    console.log(`正在获取教师(系统ID: ${teacherId})的班级列表`);
    
    // 先查询所有班级的数量
    const [totalCount] = await pool.query(
      `SELECT COUNT(*) as total FROM course_classes`
    );
    console.log(`数据库中总共有 ${totalCount[0].total} 个班级`);
    
    // 查询该教师ID相关班级的数量
    const [teacherCount] = await pool.query(
      `SELECT COUNT(*) as count FROM course_classes WHERE teacher_id = ?`,
      [teacherId]
    );
    console.log(`教师ID "${teacherId}" 关联的班级数量: ${teacherCount[0].count}`);
    
    // 获取所有教师ID和对应的班级数量
    const [teacherStats] = await pool.query(
      `SELECT teacher_id, COUNT(*) as count FROM course_classes GROUP BY teacher_id LIMIT 10`
    );
    console.log('教师班级统计:', JSON.stringify(teacherStats));
    
    // 查询教师的班级列表
    const [classes] = await pool.query(
      `SELECT id, class_name, course_code, semester, current_students, max_students, status, teacher_id
       FROM course_classes 
       WHERE teacher_id = ? AND status = 1
       ORDER BY created_at DESC`,
      [teacherId]
    );
    
    console.log(`查询到${classes.length}个班级，SQL参数: "${teacherId}"`);
    console.log('班级数据示例:', JSON.stringify(classes.slice(0, 2)));
    
    res.json({
      success: true,
      data: classes
    });
  } catch (error) {
    console.error('获取班级列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取班级列表失败',
      error: error.message
    });
  } finally {
    pool.end();
  }
};

// 获取班级的学生列表
export const getClassStudents = async (req, res) => {
  const pool = mysql.createPool(dbConfig);
  
  try {
    const { classId } = req.params;
    const teacherId = req.user?.system_teacher_id; // 使用系统教师ID
    
    if (!teacherId) {
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }
    
    console.log(`正在获取班级(ID: ${classId})的学生列表，教师系统ID: ${teacherId}`);
    
    // 首先验证该班级是否属于当前教师
    const [classCheck] = await pool.query(
      'SELECT id FROM course_classes WHERE id = ? AND teacher_id = ?',
      [classId, teacherId]
    );
    
    if (classCheck.length === 0) {
      return res.status(403).json({
        success: false,
        message: '无权访问此班级的学生列表'
      });
    }
    
    // 查询班级的学生列表
    const [students] = await pool.query(
      `SELECT s.student_id as id, s.name
       FROM student_course_registration scr
       JOIN students s ON scr.student_id = s.student_id
       WHERE scr.class_id = ?
       ORDER BY s.name`,
      [classId]
    );
    
    console.log(`查询到${students.length}名学生`);
    
    res.json({
      success: true,
      data: students
    });
  } catch (error) {
    console.error('获取学生列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学生列表失败',
      error: error.message
    });
  } finally {
    pool.end();
  }
};

// 分享视频给学生
export const shareVideoToStudents = async (req, res) => {
  const pool = mysql.createPool(dbConfig);
  
  try {
    const teacherId = req.user?.system_teacher_id; // 使用系统教师ID
    
    if (!teacherId) {
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }
    
    const { 
      classId, 
      title,
      url: videoUrl, 
      coverUrl,
      resourceType,
      resourceId,
      studentIds,
      remark,
      required
    } = req.body;
    
    console.log('接收到视频分享请求:', {
      teacherId,
      classId,
      title,
      videoUrl,
      studentCount: studentIds ? studentIds.length : '全班',
      required
    });
    
    // 验证必须的字段
    if (!classId || !title || !videoUrl) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的参数: 班级ID、标题或视频URL'
      });
    }
    
    // 验证班级是否属于当前教师
    const [classCheck] = await pool.query(
      'SELECT id FROM course_classes WHERE id = ? AND teacher_id = ?',
      [classId, teacherId]
    );
    
    if (classCheck.length === 0) {
      return res.status(403).json({
        success: false,
        message: '无权向此班级分享视频'
      });
    }
    
    // 解析来源信息
    let source = '未知';
    let sourceType = '视频';
    
    if (videoUrl.includes('bilibili.com')) {
      source = 'bilibili';
    } else if (videoUrl.includes('youtube.com')) {
      source = 'youtube';
    }
    
    // 开始事务
    await pool.query('START TRANSACTION');
    
    // 1. 添加到shared_videos表
    const [result] = await pool.query(
      `INSERT INTO shared_videos 
      (teacher_id, class_id, title, video_url, cover_url, source, source_type, remark, status) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)`,
      [teacherId, classId, title, videoUrl, coverUrl || null, source, sourceType, remark || null]
    );
    
    const sharedVideoId = result.insertId;
    
    // 2. 如果指定了特定学生，则只分享给这些学生
    if (studentIds && studentIds.length > 0) {
      // 插入到学生分享关系表
      const studentValues = studentIds.map(studentId => [sharedVideoId, studentId, required ? 1 : 0]);
      
      await pool.query(
        `INSERT INTO shared_video_students 
        (shared_video_id, student_id, is_required) 
        VALUES ?`,
        [studentValues]
      );
      
      console.log(`已分享给${studentIds.length}名指定学生`);
    } else {
      // 获取班级所有学生
      const [students] = await pool.query(
        `SELECT s.student_id
         FROM student_course_registration scr
         JOIN students s ON scr.student_id = s.student_id
         WHERE scr.class_id = ?`,
        [classId]
      );
      
      if (students.length > 0) {
        const allStudentValues = students.map(student => [sharedVideoId, student.student_id, required ? 1 : 0]);
        
        await pool.query(
          `INSERT INTO shared_video_students 
          (shared_video_id, student_id, is_required) 
          VALUES ?`,
          [allStudentValues]
        );
        
        console.log(`已分享给班级所有${students.length}名学生`);
      }
    }
    
    // 提交事务
    await pool.query('COMMIT');
    
    res.json({
      success: true,
      message: '视频分享成功',
      data: { id: sharedVideoId }
    });
    
  } catch (error) {
    // 回滚事务
    if (pool) {
      await pool.query('ROLLBACK');
    }
    
    console.error('视频分享失败:', error);
    res.status(500).json({
      success: false,
      message: '视频分享失败',
      error: error.message
    });
  } finally {
    pool.end();
  }
};  
import pool from '../config/db.js';

// 获取可选课程列表
export const getAvailableCourses = async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT
        c.course_code,
        c.course_name,
        c.description,
        c.credit,
        c.course_type,
        c.semester,
        ta.name as teacher_name,
        ta.system_teacher_id as teacher_id,
        COALESCE(
          (SELECT SUM(cc.max_students - cc.current_students)
           FROM course_classes cc
           WHERE cc.course_code = c.course_code
           AND cc.status = 1), 0
        ) as available_seats
      FROM courses c
      JOIN teaching_assistant ta ON c.teacher_id = ta.system_teacher_id
      WHERE c.status = 1
      ORDER BY c.semester DESC, c.course_code
    `);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取可选课程失败:', error);
    res.status(500).json({
      success: false,
      message: '获取可选课程失败'
    });
  }
};

// 获取学生已选课程
export const getStudentCourses = async (req, res) => {
  const studentId = req.user?.student_id;

  try {
    const [rows] = await pool.query(`
      SELECT
        c.course_code,
        c.course_name,
        c.description,
        c.credit,
        c.semester,
        ta.name as teacher_name,
        cc.class_name,
        scr.join_time,
        scr.status as registration_status
      FROM student_course_registration scr
      JOIN courses c ON scr.course_code = c.course_code
      JOIN teaching_assistant ta ON c.teacher_id = ta.system_teacher_id
      LEFT JOIN course_classes cc ON cc.id = scr.class_id
      WHERE scr.student_id = ?
        AND scr.status = 1
      ORDER BY scr.join_time DESC
    `, [studentId]);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取已选课程失败:', error);
    res.status(500).json({
      success: false,
      message: '获取已选课程失败'
    });
  }
};

// 退课
export const dropCourse = async (req, res) => {
  const { course_code } = req.params;
  const studentId = req.user?.student_id;

  if (!course_code || !studentId) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数'
    });
  }

  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    // 查找选课记录和对应的班级
    const [registrations] = await connection.query(
      `SELECT
        scr.id,
        scr.class_id,
        cc.current_students,
        cc.class_name
       FROM student_course_registration scr
       JOIN courses c ON scr.course_code = c.course_code
       LEFT JOIN course_classes cc ON cc.id = scr.class_id
       WHERE scr.student_id = ?
       AND scr.course_code = ?
       AND scr.status = 1`,
      [studentId, course_code]
    );

    if (registrations.length === 0) {
      throw new Error('未找到选课记录');
    }

    const registration = registrations[0];

    // 更新选课状态为退课
    await connection.query(
      'UPDATE student_course_registration SET status = 0 WHERE id = ?',
      [registration.id]
    );

    // 更新班级人数
    if (registration.class_id && registration.current_students > 0) {
      await connection.query(
        'UPDATE course_classes SET current_students = current_students - 1 WHERE id = ?',
        [registration.class_id]
      );
      console.log(`退课成功: 班级 ${registration.class_name || registration.class_id} 人数减1，当前人数: ${registration.current_students - 1}`);
    }

    await connection.commit();

    res.json({
      success: true,
      message: '退课成功'
    });

  } catch (error) {
    await connection.rollback();
    console.error('退课失败:', error);
    res.status(400).json({
      success: false,
      message: error.message || '退课失败'
    });
  } finally {
    connection.release();
  }
};

// 选课并分配班级
export const registerCourse = async (req, res) => {
  const { course_code } = req.body;
  const studentId = req.user?.student_id;

  if (!course_code || !studentId) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数'
    });
  }

  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    // 检查是否已经选过这门课（包括已退课的记录）
    const [existingReg] = await connection.query(
      'SELECT id, status FROM student_course_registration WHERE student_id = ? AND course_code = ?',
      [studentId, course_code]
    );

    if (existingReg.length > 0) {
      if (existingReg[0].status === 1) {
        throw new Error('您已经选过这门课程了');
      } else {
        // 如果之前退过这门课，查找所有可用的班级
        const [availableClasses] = await connection.query(`
          SELECT id, class_name, current_students, max_students
          FROM course_classes
          WHERE course_code = ?
          AND status = 1
          AND current_students < max_students
          ORDER BY id ASC
        `, [course_code]);

        if (availableClasses.length === 0) {
          throw new Error('该课程暂无可用班级');
        }

        // 班级分配阈值
        const THRESHOLD = 30;

        // 分配班级的逻辑
        let targetClass;

        // 1. 查找未达到阈值的班级（按ID升序）
        const underThresholdClasses = availableClasses.filter(c => c.current_students < THRESHOLD);

        if (underThresholdClasses.length > 0) {
          // 如果有班级未达到阈值，选择ID最小的班级
          targetClass = underThresholdClasses[0];
          console.log(`重新选课: 分配到未达到阈值的班级: ${targetClass.class_name}, 当前人数: ${targetClass.current_students}`);
        } else {
          // 2. 如果所有班级都达到了阈值，随机分配
          const randomIndex = Math.floor(Math.random() * availableClasses.length);
          targetClass = availableClasses[randomIndex];
          console.log(`重新选课: 随机分配到班级: ${targetClass.class_name}, 当前人数: ${targetClass.current_students}`);
        }

        // 更新选课记录状态，包含班级ID
        await connection.query(
          'UPDATE student_course_registration SET status = 1, class_id = ?, join_time = NOW() WHERE id = ?',
          [targetClass.id, existingReg[0].id]
        );

        // 更新班级人数
        await connection.query(
          'UPDATE course_classes SET current_students = current_students + 1 WHERE id = ?',
          [targetClass.id]
        );

        await connection.commit();
        return res.json({
          success: true,
          message: '选课成功'
        });
      }
    }

    // 查找所有可用的班级
    const [availableClasses] = await connection.query(`
      SELECT id, class_name, current_students, max_students
      FROM course_classes
      WHERE course_code = ?
      AND status = 1
      AND current_students < max_students
      ORDER BY id ASC
    `, [course_code]);

    if (availableClasses.length === 0) {
      throw new Error('该课程暂无可用班级');
    }

    // 班级分配阈值
    const THRESHOLD = 30;

    // 分配班级的逻辑
    let targetClass;

    // 1. 查找未达到阈值的班级（按ID升序）
    const underThresholdClasses = availableClasses.filter(c => c.current_students < THRESHOLD);

    if (underThresholdClasses.length > 0) {
      // 如果有班级未达到阈值，选择ID最小的班级
      targetClass = underThresholdClasses[0];
      console.log(`分配到未达到阈值的班级: ${targetClass.class_name}, 当前人数: ${targetClass.current_students}`);
    } else {
      // 2. 如果所有班级都达到了阈值，随机分配
      const randomIndex = Math.floor(Math.random() * availableClasses.length);
      targetClass = availableClasses[randomIndex];
      console.log(`随机分配到班级: ${targetClass.class_name}, 当前人数: ${targetClass.current_students}`);
    }

    // 再次检查班级容量（防止并发问题）
    const [classInfo] = await connection.query(
      'SELECT current_students, max_students FROM course_classes WHERE id = ? FOR UPDATE',
      [targetClass.id]
    );

    if (classInfo[0].current_students >= classInfo[0].max_students) {
      throw new Error('该班级已满');
    }

    // 创建选课记录，包含班级ID
    await connection.query(
      'INSERT INTO student_course_registration (student_id, course_code, class_id, join_time) VALUES (?, ?, ?, NOW())',
      [studentId, course_code, targetClass.id]
    );

    // 更新班级人数
    await connection.query(
      'UPDATE course_classes SET current_students = current_students + 1 WHERE id = ?',
      [targetClass.id]
    );

    await connection.commit();

    res.json({
      success: true,
      message: '选课成功'
    });

  } catch (error) {
    await connection.rollback();
    console.error('选课失败:', error);

    if (error.code === 'ER_DUP_ENTRY') {
      res.status(400).json({
        success: false,
        message: '您已经选过这门课程了'
      });
    } else {
      res.status(400).json({
        success: false,
        message: error.message || '选课失败'
      });
    }
  } finally {
    connection.release();
  }
};
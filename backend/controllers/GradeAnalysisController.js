import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

const pool = mysql.createPool(dbConfig);

/**
 * 获取班级的章节列表
 * @param {object} req - 请求对象，包含班级ID
 * @param {object} res - 响应对象
 */
export const getClassChapters = async (req, res) => {
  const { classId } = req.params;
  
  try {
    console.log(`获取班级 ${classId} 的章节数据`);
    
    // 先获取班级对应的课程代码和最新的大纲
    const [classRows] = await pool.query(`
      SELECT cc.course_code, s.content 
      FROM course_classes cc 
      LEFT JOIN syllabus s ON s.course_code = cc.course_code 
      WHERE cc.id = ? 
      ORDER BY s.create_time DESC LIMIT 1
    `, [classId]);

    if (classRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }

    const courseCode = classRows[0].course_code;
    const syllabusContent = JSON.parse(classRows[0].content || '{}');

    // 获取已发布测验习题的章节ID列表
    const [exerciseRows] = await pool.query(`
      SELECT DISTINCT eb.title_id 
      FROM publish_exercise pe
      JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id 
      WHERE pe.class_id = ? AND pe.study_phase = 2
    `, [classId]);

    // 确保所有title_id都转为字符串
    const publishedTitleIds = new Set(exerciseRows.map(row => String(row.title_id)));
    
    // 调试输出
    console.log('已发布的title_id列表:', Array.from(publishedTitleIds));
    
    const chapters = [];

    // 解析大纲内容，提取已发布作业的章节
    const processContent = (title, value) => {
      if (typeof value === 'string' || typeof value === 'number') {
        // 确保value转为字符串进行比较
        const valueStr = String(value);
        console.log(`检查章节: ${title}, 值: ${valueStr}, 是否匹配: ${publishedTitleIds.has(valueStr)}`);
        
        if (publishedTitleIds.has(valueStr)) {
          chapters.push({
            id: valueStr,
            title: title
          });
        }
      } else if (typeof value === 'object') {
        // 处理有子章节的情况
        for (const [subTitle, subValue] of Object.entries(value)) {
          // 确保subValue转为字符串进行比较
          const subValueStr = String(subValue);
          console.log(`检查子章节: ${subTitle}, 值: ${subValueStr}, 是否匹配: ${publishedTitleIds.has(subValueStr)}`);
          
          if (publishedTitleIds.has(subValueStr)) {
            chapters.push({
              id: subValueStr,
              title: subTitle
            });
          } else if (typeof subValue === 'object') {
            // 处理多级子章节的情况
            for (const [deepTitle, deepValue] of Object.entries(subValue)) {
              const deepValueStr = String(deepValue);
              console.log(`检查深层子章节: ${deepTitle}, 值: ${deepValueStr}, 是否匹配: ${publishedTitleIds.has(deepValueStr)}`);
              
              if (publishedTitleIds.has(deepValueStr)) {
                chapters.push({
                  id: deepValueStr,
                  title: `${subTitle} - ${deepTitle}`
                });
              }
            }
          }
        }
      }
    };

    // 处理所有章节
    for (const [title, value] of Object.entries(syllabusContent)) {
      processContent(title, value);
    }

    // 按照 id 排序
    chapters.sort((a, b) => parseInt(a.id) - parseInt(b.id));

    console.log(`找到 ${chapters.length} 个章节:`, chapters);
    
    return res.json({
      success: true,
      data: chapters
    });
    
  } catch (error) {
    console.error('获取班级章节列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取班级章节列表失败',
      error: error.message
    });
  }
};

/**
 * 获取章节的作业批次
 * @param {object} req - 请求对象，包含班级ID和章节ID
 * @param {object} res - 响应对象
 */
export const getChapterBatches = async (req, res) => {
  const { classId, chapterId } = req.params;
  
  try {
    console.log(`获取班级 ${classId} 章节 ${chapterId} 的批次数据`);
    
    // 将chapterId统一转换为字符串，用于日志输出
    console.log(`查询章节ID: ${chapterId}, 类型: ${typeof chapterId}`);
    
    // 查询该章节下的所有批次
    const [batchesResult] = await pool.query(`
      SELECT DISTINCT 
        pe.release_batch as id,
        CONCAT('第', pe.release_batch, '批') as batch_name
      FROM 
        publish_exercise pe
      JOIN 
        exercise_bank eb ON pe.exercise_id = eb.exercise_id
      WHERE 
        pe.class_id = ?
        AND eb.title_id = ?
        AND pe.study_phase = 2
      ORDER BY 
        pe.release_batch ASC
    `, [classId, chapterId]);
    
    if (batchesResult.length === 0) {
      console.log(`未找到章节 ${chapterId} 的批次`);
      
      // 尝试转换章节ID类型后再次查询
      const numericChapterId = parseInt(chapterId, 10);
      if (!isNaN(numericChapterId) && String(numericChapterId) === chapterId) {
        console.log(`尝试使用数字类型章节ID ${numericChapterId} 重新查询`);
        
        const [retryResult] = await pool.query(`
          SELECT DISTINCT 
            pe.release_batch as id,
            CONCAT('第', pe.release_batch, '批') as batch_name
          FROM 
            publish_exercise pe
          JOIN 
            exercise_bank eb ON pe.exercise_id = eb.exercise_id
          WHERE 
            pe.class_id = ?
            AND eb.title_id = ?
            AND pe.study_phase = 2
          ORDER BY 
            pe.release_batch ASC
        `, [classId, numericChapterId]);
        
        if (retryResult.length > 0) {
          console.log(`使用数字类型章节ID找到 ${retryResult.length} 个批次`);
          return res.json({
            success: true,
            data: retryResult
          });
        }
      }
      
      return res.status(404).json({
        success: false,
        message: '未找到该章节的测验批次'
      });
    }
    
    console.log(`找到 ${batchesResult.length} 个批次:`, batchesResult);
    
    return res.json({
      success: true,
      data: batchesResult
    });
    
  } catch (error) {
    console.error('获取章节批次失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取章节批次失败',
      error: error.message
    });
  }
};

/**
 * 获取班级的作业批次
 * @param {object} req - 请求对象，包含班级ID
 * @param {object} res - 响应对象
 */
export const getClassHomeworkBatches = async (req, res) => {
  const { classId } = req.params;
  
  try {
    console.log(`获取班级 ${classId} 的作业批次数据`);
    
    // 只查询课后测验题(study_phase=2)的批次
    const [batchesResult] = await pool.query(`
      SELECT DISTINCT 
        release_batch as id,
        CONCAT('第', release_batch, '批') as batch_name
      FROM 
        publish_exercise
      WHERE 
        class_id = ?
        AND study_phase = 2
      ORDER BY 
        release_batch ASC
    `, [classId]);
    
    if (batchesResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该班级的测验批次'
      });
    }
    
    // 返回批次数据
    console.log(`找到 ${batchesResult.length} 个批次`);
    
    return res.json({
      success: true,
      data: batchesResult
    });
    
  } catch (error) {
    console.error('获取作业批次失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取作业批次失败',
      error: error.message
    });
  }
};

/**
 * 获取作业批次的学生成绩分析数据
 * @param {object} req - 请求对象，包含班级ID、章节ID和批次ID
 * @param {object} res - 响应对象
 */
export const getHomeworkAnalysis = async (req, res) => {
  const { classId, chapterId, batchId } = req.params;
  
  try {
    console.log(`获取班级 ${classId} 章节 ${chapterId} 批次 ${batchId} 的作业分析数据`);
    console.log('参数类型 - classId:', typeof classId, 'chapterId:', typeof chapterId, 'batchId:', typeof batchId);
    
    // 1. 获取班级中所有学生 - 修正字段名
    const [studentsResult] = await pool.query(`
      SELECT 
        s.id as student_id, 
        s.name as student_name, 
        s.student_id as student_number
      FROM 
        students s
      JOIN 
        student_course_registration scr ON s.student_id = scr.student_id
      WHERE 
        scr.class_id = ?
    `, [classId]);
    
    if (studentsResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级学生信息'
      });
    }
    
    console.log(`找到 ${studentsResult.length} 个学生`);
    
    // 2. 查询批次中的所有习题 - 限制只查询study_phase=2的习题和指定章节
    const [exercisesResult] = await pool.query(`
      SELECT 
        pb.id as publish_id,
        eb.exercise_id,
        eb.title as exercise_title,
        eb.question_type,
        pb.title_id
      FROM 
        publish_exercise pb
      JOIN 
        exercise_bank eb ON pb.exercise_id = eb.exercise_id
      WHERE 
        pb.class_id = ? 
        AND pb.release_batch = ?
        AND pb.study_phase = 2
        AND eb.title_id = ?
    `, [classId, batchId, chapterId]);
    
    if (exercisesResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到相关测验习题'
      });
    }
    
    console.log(`找到 ${exercisesResult.length} 个习题`);
    
    // 3. 获取学生的提交记录
    const studentIds = studentsResult.map(student => student.student_number); // 使用student_number而不是student_id
    const publishIds = exercisesResult.map(exercise => exercise.publish_id);
    const titleIds = [...new Set(exercisesResult.map(exercise => exercise.title_id))]; // 获取唯一的title_id
    
    // 使用IN操作符进行批量查询
    const [recordsResult] = await pool.query(`
      SELECT 
        ser.student_id,
        ser.publish_exercise_id,
        ser.score,
        ser.is_correct,
        ser.create_time
      FROM 
        student_exercise_records ser
      WHERE 
        ser.student_id IN (?) 
        AND ser.publish_exercise_id IN (?)
    `, [studentIds, publishIds]);
    
    console.log(`找到 ${recordsResult.length} 条提交记录`);
    
    // 4. 获取习题计时器数据
    const [timerResult] = await pool.query(`
      SELECT 
        sht.student_id,
        sht.title_id,
        sht.release_batch,
        sht.start_time,
        sht.end_time,
        sht.is_completed,
        TIMESTAMPDIFF(SECOND, sht.start_time, IFNULL(sht.end_time, NOW())) as time_spent_seconds
      FROM 
        student_homework_timer sht
      WHERE 
        sht.student_id IN (?) 
        AND sht.class_id = ?
        AND sht.release_batch = ?
        AND sht.title_id IN (?)
    `, [studentIds, classId, batchId, titleIds]);
    
    console.log(`找到 ${timerResult.length} 条计时器记录`);
    
    // 5. 处理数据，为每个学生统计完成情况
    const studentData = studentsResult.map(student => {
      // 获取该学生的所有记录
      const studentRecords = recordsResult.filter(record => 
        record.student_id === student.student_number
      );
      
      // 获取该学生的计时器记录
      const studentTimers = timerResult.filter(timer => 
        timer.student_id === student.student_number
      );
      
      // 计算完成习题数量和总分
      const completedCount = studentRecords.length;
      const totalPossible = exercisesResult.length;
      const isCompleted = completedCount === totalPossible;
      
      // 计算总分和平均分
      let totalScore = 0;
      let possibleTotalScore = 0; // 可能的最高分数
      studentRecords.forEach(record => {
        // 添加该记录对应的习题信息
        const exercise = exercisesResult.find(e => e.publish_id === record.publish_exercise_id);
        if (exercise) {
          // 根据题目类型确定满分
          const maxScore = (exercise.question_type >= 1 && exercise.question_type <= 3) ? 5 : 
                          (exercise.question_type >= 4 && exercise.question_type <= 5) ? 10 : 5; // 默认5分
          
          possibleTotalScore += maxScore;
          totalScore += parseFloat(record.score || 0);
          
          console.log(`题目ID: ${record.publish_exercise_id}, 类型: ${exercise.question_type}, 满分: ${maxScore}, 得分: ${record.score}`);
        } else {
          console.log(`未找到习题信息，题目ID: ${record.publish_exercise_id}`);
        }
      });
      
      // 查找最后一次提交时间
      let completionTime = null;
      if (studentRecords.length > 0) {
        const lastRecord = studentRecords.reduce((latest, record) => {
          if (!latest.create_time) return record;
          return new Date(record.create_time) > new Date(latest.create_time) ? record : latest;
        }, {});
        completionTime = lastRecord.create_time;
      }
      
      // 计算总用时 - 从student_homework_timer表获取
      let totalTimeSpent = 0;
      if (studentTimers.length > 0) {
        // 找出最后完成的计时器记录
        const completedTimer = studentTimers.find(timer => timer.is_completed) || studentTimers[0];
        if (completedTimer) {
          totalTimeSpent = completedTimer.time_spent_seconds * 1000; // 转换为毫秒
        }
      }
      
      // 如果没有计时器记录，则使用student_exercise_records表中的time_spent
      if (totalTimeSpent === 0) {
        studentRecords.forEach(record => {
          if (record.time_spent) {
            totalTimeSpent += parseInt(record.time_spent || 0);
          }
        });
      }
      
      // 计算学生的正确率和总分（不再计算平均分）
      const correctCount = studentRecords.filter(r => r.is_correct === 1).length;
      const correctRate = completedCount > 0 ? (correctCount / completedCount) * 100 : 0;
      
      // 直接使用总分，不再计算平均分
      console.log(`学生 ${student.student_name} 总分: ${totalScore}, 完成题目: ${completedCount}`);
      
      return {
        ...student,
        completed_count: completedCount,
        total_exercises: totalPossible,
        is_completed: isCompleted,
        score: totalScore, // 使用总分而不是平均分
        completion_time: completionTime,
        time_spent: totalTimeSpent,
        is_correct: correctCount > 0 ? 1 : 0,
        correct_count: correctCount,
        correct_rate: correctRate
      };
    });
    
    // 6. 计算整体统计数据
    const totalStudents = studentData.length;
    const completedStudents = studentData.filter(s => s.is_completed).length;
    const completionRate = totalStudents > 0 ? Math.round((completedStudents / totalStudents) * 100) : 0;
    
    // 计算平均分 - 考虑所有有答题记录的学生
    let totalScores = 0;
    let scoredStudents = 0;
    let totalCorrectCount = 0;
    studentData.forEach(student => {
      if (student.completed_count > 0) {
        totalScores += student.score || 0;
        totalCorrectCount += student.correct_count || 0;
        scoredStudents++;
      }
    });
    
    // 计算班级整体的平均得分（每位学生的总分之和除以学生数）
    const averageScore = scoredStudents > 0 ? totalScores / scoredStudents : 0;
    const averageCorrectRate = scoredStudents > 0 ? (totalCorrectCount / (scoredStudents * exercisesResult.length)) * 100 : 0;
    
    console.log('统计数据:', {
      totalStudents,
      completedStudents,
      scoredStudents,
      totalScores,
      averageScore,
      totalCorrectCount,
      averageCorrectRate
    });
    
    // 返回处理后的数据
    return res.json({
      success: true,
      data: {
        students: studentData,
        exercises: exercisesResult, // 返回当前测验的习题数据
        statsData: {
          totalStudents,
          completedStudents,
          completionRate,
          averageScore,
          averageCorrectRate,
          exerciseCount: exercisesResult.length // 添加当前测验题目数量
        }
      }
    });
    
  } catch (error) {
    console.error('获取作业分析数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取作业分析数据失败',
      error: error.message
    });
  }
};

/**
 * 获取作业知识点错误率统计
 * @param {object} req - 请求对象，包含班级ID、章节ID和批次ID
 * @param {object} res - 响应对象
 */
export const getHomeworkKnowledgePoints = async (req, res) => {
  const { classId, chapterId, batchId } = req.params;
  const useAiAnalysis = req.query.ai === 'true';
  
  try {
    console.log(`获取班级 ${classId} 章节 ${chapterId} 批次 ${batchId} 的知识点错误率数据`);
    
    // 先检查point字段是否存在
    const [checkField] = await pool.query(`
      SHOW COLUMNS FROM exercise_bank LIKE 'point'
    `);
    
    if (checkField.length === 0) {
      console.log('数据库中exercise_bank表没有point字段，尝试使用knowledge_points字段');
      
      // 检查是否有knowledge_points字段
      const [checkAlternativeField] = await pool.query(`
        SHOW COLUMNS FROM exercise_bank LIKE 'knowledge_points'
      `);
      
      if (checkAlternativeField.length === 0) {
        return res.json({
          success: true,
          message: '数据库中没有存储知识点的字段，请确认数据库结构',
          data: {
            knowledgePoints: [],
            studentCount: 0
          }
        });
      }
    }
    
    // 检查表结构
    console.log('检查数据库表结构...');
    
    try {
      await pool.query('SELECT 1 FROM publish_exercise LIMIT 1');
      await pool.query('SELECT 1 FROM exercise_bank LIMIT 1');
      await pool.query('SELECT 1 FROM student_exercise_records LIMIT 1');
    } catch (error) {
      console.error('数据库表结构检查失败:', error);
      return res.status(500).json({
        success: false,
        message: '数据库表结构检查失败，请确认数据库中存在必要的表',
        error: error.message
      });
    }
    
    // 确定知识点字段名称
    const pointFieldName = checkField.length > 0 ? 'point' : 'knowledge_points';
    console.log(`使用 ${pointFieldName} 字段获取知识点数据`);
    
    // 1. 获取习题及其知识点信息
    console.log('查询习题数据...');
    const [exercises] = await pool.query(`
      SELECT 
        pe.id as publish_id,
        eb.exercise_id,
        eb.title as exercise_title,
        eb.${pointFieldName} as knowledge_points,
        eb.content
      FROM 
        publish_exercise pe
      JOIN 
        exercise_bank eb ON pe.exercise_id = eb.exercise_id
      WHERE 
        pe.class_id = ? 
        AND pe.title_id = ?
        AND pe.release_batch = ?
        AND pe.study_phase = 2
        AND eb.${pointFieldName} IS NOT NULL 
        AND eb.${pointFieldName} != ''
    `, [classId, chapterId, batchId]);
    
    console.log(`找到 ${exercises.length} 道习题`);
    
    if (exercises.length === 0) {
      return res.json({
        success: true,
        message: '该章节没有含有知识点的习题',
        data: {
          knowledgePoints: [],
          studentCount: 0
        }
      });
    }
    
    // 2. 获取班级学生数量
    console.log('获取班级学生数量...');
    const [studentsResult] = await pool.query(`
      SELECT COUNT(*) as count
      FROM students s
      JOIN student_course_registration scr ON s.student_id = scr.student_id
      WHERE scr.class_id = ?
    `, [classId]);
    
    const studentCount = studentsResult[0].count;
    console.log(`班级学生总数: ${studentCount}`);
    
    // 3. 解析所有习题的知识点
    console.log('解析习题知识点数据...');
    const knowledgePointsMap = new Map();
    
    for (const exercise of exercises) {
      let pointsData = [];
      console.log(`处理习题 ID: ${exercise.exercise_id}, 知识点数据: ${exercise.knowledge_points}`);
      
      // 尝试解析知识点数据 (可能是JSON字符串或逗号分隔的字符串)
      try {
        if (!exercise.knowledge_points) {
          console.log('该习题没有知识点数据，跳过');
          continue;
        }
        
        // 尝试解析为JSON
        try {
          const pointsJson = JSON.parse(exercise.knowledge_points);
          console.log('成功解析JSON格式知识点');
          
          if (Array.isArray(pointsJson)) {
            pointsData = pointsJson;
          } else if (typeof pointsJson === 'object') {
            pointsData = Object.values(pointsJson);
          } else {
            pointsData = [String(pointsJson)];
          }
        } catch (jsonError) {
          console.log('不是JSON格式，尝试作为逗号分隔字符串处理');
          // 如果不是有效的JSON，假设是逗号分隔的字符串
          pointsData = exercise.knowledge_points.split(',').map(p => p.trim());
        }
        
        console.log(`解析出 ${pointsData.length} 个知识点: ${pointsData.join(', ')}`);
      } catch (e) {
        console.error(`解析知识点失败: ${e.message}`);
        continue;
      }
      
      // 获取该习题的错误提交数据
      console.log(`获取习题 ${exercise.publish_id} 的错误提交数据...`);
      const [errorRecords] = await pool.query(`
        SELECT 
          COUNT(*) as error_count
        FROM 
          student_exercise_records ser
        WHERE 
          ser.publish_exercise_id = ?
          AND ser.is_correct = 0
      `, [exercise.publish_id]);
      
      const errorCount = errorRecords[0].error_count;
      
      // 获取该习题的总提交数
      console.log(`获取习题 ${exercise.publish_id} 的总提交数...`);
      const [totalRecords] = await pool.query(`
        SELECT 
          COUNT(*) as total_count
        FROM 
          student_exercise_records ser
        WHERE 
          ser.publish_exercise_id = ?
      `, [exercise.publish_id]);
      
      const totalCount = totalRecords[0].total_count;
      const errorRate = totalCount > 0 ? Math.round((errorCount / totalCount) * 100) : 0;
      
      console.log(`习题 ${exercise.publish_id} - 错误: ${errorCount}, 总数: ${totalCount}, 错误率: ${errorRate}%`);
      
      // 为每个知识点累加错误率数据
      for (const point of pointsData) {
        if (!point || point.trim() === '') continue;
        
        const pointName = point.trim();
        if (!knowledgePointsMap.has(pointName)) {
          knowledgePointsMap.set(pointName, {
            name: pointName,
            exercises: [],
            totalErrors: 0,
            totalSubmissions: 0
          });
        }
        
        const pointData = knowledgePointsMap.get(pointName);
        pointData.exercises.push({
          id: exercise.publish_id,
          title: exercise.exercise_title,
          content: exercise.content,
          errorCount,
          totalCount,
          errorRate
        });
        
        // 累加知识点的错误统计
        pointData.totalErrors += errorCount;
        pointData.totalSubmissions += totalCount;
      }
    }
    
    // 4. 计算每个知识点的错误率
    console.log('计算知识点错误率...');
    const knowledgePoints = Array.from(knowledgePointsMap.values()).map(point => {
      const errorRate = point.totalSubmissions > 0 
        ? Math.round((point.totalErrors / point.totalSubmissions) * 100)
        : 0;
        
      return {
        name: point.name,
        exercises: point.exercises,
        errorCount: point.totalErrors,
        totalCount: point.totalSubmissions,
        errorRate
      };
    });
    
    // 5. 按错误率降序排列
    knowledgePoints.sort((a, b) => b.errorRate - a.errorRate);
    console.log(`总共发现 ${knowledgePoints.length} 个知识点`);
    
    // 6. 如果需要，调用AI分析
    let aiSuggestions = null;
    if (useAiAnalysis && knowledgePoints.length > 0) {
      console.log('生成AI分析建议...');
      try {
        // 这里可以添加AI分析服务的调用
        // 暂时只添加一个示例建议
        aiSuggestions = `## 知识点错误分析
        
根据作业数据分析，学生在以下知识点上遇到了较多困难：

${knowledgePoints.slice(0, 3).map((point, index) => 
  `${index + 1}. **${point.name}** (错误率: ${point.errorRate}%)`
).join('\n')}

### 教学建议

- 针对错误率高的知识点，建议在课堂上进行针对性的复习
- 可以通过小组讨论或演示帮助学生理解这些概念
- 考虑提供额外的练习题巩固这些知识点`;
      } catch (error) {
        console.error('获取AI分析失败:', error);
      }
    }
    
    // 7. 返回数据
    console.log('返回知识点错误率数据');
    return res.json({
      success: true,
      data: {
        knowledgePoints,
        studentCount,
        aiSuggestions
      }
    });
    
  } catch (error) {
    console.error('获取作业知识点错误率失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取作业知识点错误率失败',
      error: error.message
    });
  }
};

/**
 * 测试数据库连接和表结构
 * @param {object} req - 请求对象
 * @param {object} res - 响应对象
 */
export const testDatabaseConnection = async (req, res) => {
  try {
    console.log('测试数据库连接...');
    
    const connection = await pool.getConnection();
    console.log('数据库连接成功');
    
    // 测试各表结构
    const tables = [
      'publish_exercise',
      'exercise_bank',
      'student_exercise_records',
      'students',
      'student_course_registration'
    ];
    
    const tableResults = {};
    
    for (const table of tables) {
      try {
        const [rows] = await connection.query(`SHOW COLUMNS FROM ${table}`);
        tableResults[table] = {
          exists: true,
          columns: rows.map(row => row.Field)
        };
      } catch (error) {
        tableResults[table] = {
          exists: false,
          error: error.message
        };
      }
    }
    
    // 释放连接
    connection.release();
    
    return res.json({
      success: true,
      message: '数据库连接测试成功',
      data: {
        dbConfig: {
          host: dbConfig.host,
          database: dbConfig.database,
          user: dbConfig.user
        },
        tables: tableResults
      }
    });
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    return res.status(500).json({
      success: false,
      message: '数据库连接测试失败',
      error: error.message
    });
  }
};

export default {
  getHomeworkAnalysis,
  getClassHomeworkBatches,
  getClassChapters,
  getChapterBatches,
  getHomeworkKnowledgePoints,
  testDatabaseConnection
};

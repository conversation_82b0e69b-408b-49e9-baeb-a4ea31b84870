import Favorite from '../models/favoriteModel.js';

// 添加收藏
export const addFavorite = async (req, res) => {
  try {
    console.log('\n=== 添加收藏 ===');
    const { system_teacher_id } = req.user;
    const favoriteData = {
      system_teacher_id,
      ...req.body
    };
    
    console.log('收藏数据:', favoriteData);

    // 检查是否已收藏
    const existingFavorite = await Favorite.checkFavorite(
      system_teacher_id,
      favoriteData.resource_type,
      favoriteData.resource_id
    );

    console.log('检查已存在收藏:', existingFavorite);

    if (existingFavorite) {
      if (existingFavorite.status === 1) {
        console.log('资源已被收藏');
        return res.status(400).json({
          success: false,
          message: '该资源已收藏'
        });
      } else {
        // 如果之前软删除了，则重新激活
        console.log('重新激活已删除的收藏');
        await Favorite.remove(existingFavorite.id, system_teacher_id);
        return res.json({
          success: true,
          message: '收藏成功'
        });
      }
    }

    // 添加新收藏
    console.log('添加新收藏');
    const result = await Favorite.create(favoriteData);
    console.log('收藏结果:', result);

    res.json({
      success: true,
      message: '收藏成功',
      data: result
    });

  } catch (error) {
    console.error('添加收藏失败:', error);
    res.status(500).json({
      success: false,
      message: '添加收藏失败',
      error: error.message
    });
  }
};

// 获取收藏列表
export const getFavorites = async (req, res) => {
  try {
    const { system_teacher_id } = req.user;
    const favorites = await Favorite.findByTeacherId(system_teacher_id);
    
    // 按类型分组
    const videoFavorites = favorites.filter(f => f.resource_type === 'video');
    const articleFavorites = favorites.filter(f => f.resource_type === 'article');

    res.json({
      success: true,
      data: {
        videos: videoFavorites,
        articles: articleFavorites
      }
    });

  } catch (error) {
    console.error('获取收藏列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收藏列表失败'
    });
  }
};

// 取消收藏
export const removeFavorite = async (req, res) => {
  try {
    const { id } = req.params;
    const { system_teacher_id } = req.user;

    await Favorite.remove(id, system_teacher_id);
    res.json({
      success: true,
      message: '取消收藏成功'
    });

  } catch (error) {
    console.error('取消收藏失败:', error);
    res.status(500).json({
      success: false,
      message: '取消收藏失败'
    });
  }
};

// 检查收藏状态
export const checkFavoriteStatus = async (req, res) => {
  try {
    const { system_teacher_id } = req.user;
    const { resource_type, resource_id } = req.query;

    const favorite = await Favorite.checkFavorite(
      system_teacher_id,
      resource_type,
      resource_id
    );

    res.json({
      success: true,
      isFavorited: favorite?.status === 1
    });

  } catch (error) {
    console.error('检查收藏状态失败:', error);
    res.status(500).json({
      success: false,
      message: '检查收藏状态失败'
    });
  }
}; 
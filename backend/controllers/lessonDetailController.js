import pool from '../config/db.js';
import { Document, Paragraph, TextRun, HeadingLevel, Packer } from 'docx';
import { OpenAI } from 'openai';
import { createOpenAIConfig, API_USAGES } from '../config/modelService.js';

// 初始化火山引擎 AI 客户端（异步）
let volcano_client;
(async () => {
  try {
    const config = await createOpenAIConfig(API_USAGES.LESSON_DETAIL);
    volcano_client = new OpenAI(config);
    console.log('火山引擎 AI 客户端初始化成功');
  } catch (error) {
    console.error('火山引擎 AI 客户端初始化失败:', error);
  }
})();

// 修改为函数形式的模板配置
const TEMPLATE_PROMPTS = {
  // 传统讲授型
  1: (courseType) => `作为一名专业的${courseType}课程教师，请根据传统讲授型教学模式生成一份详细的教案。这种模式以教师讲授为主，突出知识点讲解和课堂练习。

教学特点：
1. 教师主导，系统讲解知识点
2. 重视概念、原理的准确传授
3. 适当安排课堂练习和巩固
4. 注重知识的系统性和完整性

具体要求：
1. 教学目标要突出知识掌握的准确性
2. 教学过程应以讲解为主，配合板书和练习
3. 重视重难点的详细讲解
4. 课堂练习要有针对性`,

  // 探究实验型
  2: (courseType) => `作为一名专业的${courseType}课程教师，请根据探究实验型教学模式生成一份详细的教案。这种模式强调学生动手实践，培养探究能力和创新思维。

教学特点：
1. 以实验、实践为主要形式
2. 培养学生的动手能力和探究精神
3. 强调发现问题、解决问题的过程
4. 注重创新思维的培养

具体要求：
1. 教学目标要突出能力培养
2. 教学过程要设计探究性活动
3. 预设可能的问题和解决方案
4. 注重实验安全和操作规范`,

  // 互动讨论型
  3: (courseType) => `作为一名专业的${courseType}课程教师，请根据互动讨论型教学模式生成一份详细的教案。这种模式注重师生互动和生生互动，培养表达和思辨能力。

教学特点：
1. 以讨论、对话为主要形式
2. 强调学生的主动参与
3. 培养批判性思维和表达能力
4. 注重观点的交流和碰撞

具体要求：
1. 教学目标要突出能力培养和思维发展
2. 设计有价值的讨论话题和问题
3. 预设讨论的关键节点和引导策略
4. 注重课堂氛围的营造`,

  // 启发思考型
  4: (courseType) => `作为一名专业的${courseType}课程教师，请根据启发思考型教学模式生成一份详细的教案。这种模式以问题为导向，培养学生独立思考和解决问题的能力。

教学特点：
1. 以问题为导向，层层深入
2. 重视思维过程的训练
3. 培养独立思考的能力
4. 注重知识的迁移和应用

具体要求：
1. 教学目标要突出思维能力的培养
2. 设计递进式的问题链
3. 预设学生可能的思路和答案
4. 注重启发式教学方法的运用`
}

// 获取课时教案详情
export const getLessonDetail = async (req, res) => {
  const { courseId, titleId } = req.params;
  const teacherId = req.user?.system_teacher_id;

  try {
    // 1. 获取课程信息
    const [courseRows] = await pool.execute(
      'SELECT course_name FROM courses WHERE course_code = ? AND teacher_id = ?',
      [courseId, teacherId]
    );

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到课程信息'
      });
    }

    // 2. 获取最新的教案内容
    const [lessonRows] = await pool.execute(
      `SELECT
        id,
        title,
        content,
        create_time,
        update_time,
        title_id
       FROM lesson_plans
       WHERE course_code = ?
         AND teacher_id = ?
         AND title_id = ?
       ORDER BY create_time DESC
       LIMIT 1`,
      [courseId, teacherId, titleId]
    );

    // 如果找到教案，解析JSON内容
    if (lessonRows.length > 0) {
      const lessonPlan = lessonRows[0];
      try {
        // 解析存储在content中的JSON数据
        const planContent = JSON.parse(lessonPlan.content);

        return res.json({
          success: true,
          data: {
            id: lessonPlan.id,
            courseName: courseRows[0].course_name,
            title: lessonPlan.title,
            content: planContent.content || '',
            teaching_goals: planContent.objectives || '',
            teaching_methods: planContent.keyPoints || [],
            teaching_steps: planContent.teachingProcess || [],
            title_id: lessonPlan.title_id,
            createTime: lessonPlan.create_time,
            updateTime: lessonPlan.update_time
          }
        });
      } catch (e) {
        console.warn('解析教案内容失败:', e);
        return res.json({
          success: true,
          data: {
            id: lessonPlan.id,
            courseName: courseRows[0].course_name,
            title: lessonPlan.title,
            content: '',
            teaching_goals: '',
            teaching_methods: [],
            teaching_steps: [],
            title_id: lessonPlan.title_id,
            createTime: lessonPlan.create_time,
            updateTime: lessonPlan.update_time
          }
        });
      }
    }

    // 如果没有找到教案，返回空模板
    return res.json({
      success: true,
      data: {
        courseName: courseRows[0].course_name,
        content: '',
        teaching_goals: '',
        teaching_methods: [],
        teaching_steps: []
      }
    });

  } catch (error) {
    console.error('获取教案详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教案详情失败'
    });
  }
};

// 保存课时教案
export const saveLessonDetail = async (req, res) => {
  const { courseId, hour, plan } = req.body;
  const teacherId = req.user?.system_teacher_id;

  try {
    console.log('保存教案参数:', { courseId, hour, teacherId, plan });

    if (!courseId || !hour || !plan || !teacherId) {
      console.error('缺少必要参数:', { courseId, hour, teacherId, plan });
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 从hour中提取titleId (例如："课时1" -> 1)
    const titleId = parseInt(hour.match(/\d+/)?.[0]);
    if (!titleId) {
      console.error('无效的课时ID:', hour);
      return res.status(400).json({
        success: false,
        message: '无效的课时信息'
      });
    }

    // 检查课程是否存在
    const [courseRows] = await pool.execute(
      'SELECT course_code FROM courses WHERE course_code = ? AND teacher_id = ?',
      [courseId, teacherId]
    );

    if (courseRows.length === 0) {
      console.error('未找到课程:', { courseId, teacherId });
      return res.status(404).json({
        success: false,
        message: '未找到课程信息'
      });
    }

    // 将所有教案内容组合成一个对象并转换为JSON字符串
    const contentJson = JSON.stringify({
      content: plan.content || '',
      objectives: plan.objectives || '',
      keyPoints: Array.isArray(plan.methods) ? plan.methods :
                (plan.methods ? JSON.parse(plan.methods) : []),
      teachingProcess: Array.isArray(plan.teachingProcess) ? plan.teachingProcess :
                      (plan.teachingProcess ? JSON.parse(plan.teachingProcess) : [])
    });

    // 准备要保存的数据
    const planData = {
      teacher_id: teacherId,
      course_code: courseId,
      title: plan.title || hour,
      content: contentJson,
      title_id: titleId,
      create_time: new Date(),
      update_time: new Date()
    };

    console.log('准备保存的数据:', planData);

    // 插入新记录
    const [result] = await pool.execute(
      `INSERT INTO lesson_plans (
        teacher_id, course_code, title, content, title_id,
        create_time, update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        planData.teacher_id,
        planData.course_code,
        planData.title,
        planData.content,
        planData.title_id,
        planData.create_time,
        planData.update_time
      ]
    );

    console.log('保存结果:', result);

    res.json({
      success: true,
      message: '教案保存成功',
      data: {
        id: result.insertId
      }
    });

  } catch (error) {
    console.error('保存教案失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '保存教案失败'
    });
  }
};

// 修改提示词生成函数
const generatePrompt = (courseType, courseName, hour, content, templateId, customRequirements) => {
  // 获取模板提示词函数
  const templatePromptFn = TEMPLATE_PROMPTS[templateId] || TEMPLATE_PROMPTS[1]

  // 执行函数获取模板提示词
  const templatePrompt = templatePromptFn(courseType)

  // 构建完整提示词
  return `${templatePrompt}

课程基本信息：
课程名称：${courseName}
课时：${hour}
教学内容：${Array.isArray(content) ? content.join('\n') : content}
${customRequirements ? `教师特殊要求：${customRequirements}\n` : ''}

请严格按照以下JSON格式返回教案内容，不要包含任何其他内容：
{
  "content": "教学内容[难度]",
  "objectives": [
    "教学目标1",
    "教学目标2",
    "教学目标3"
  ],
  "keyPoints": [
    "教学重点1",
    "教学重点2",
    "教学重点3"
  ],
  "teachingProcess": [
    {
      "stage": "导入新课",
      "duration": "5",
      "content": "详细的教学过程描述，包括教学方法和具体活动(不少于100字)"
    },
    {
      "stage": "讲解新知识",
      "duration": "20",
      "content": "详细的教学过程描述(不少于300字)"
    },
    {
      "stage": "课堂练习",
      "duration": "15",
      "content": "详细的教学过程描述(不少于100字)"
    },
    {
      "stage": "总结",
      "duration": "5",
      "content": "详细的教学过程描述(不少于100字)"
    }
  ],
  "homework": "具体的课后作业安排"
}

要求：
1. 教学目标要具体、可衡量
2. 教学重点要突出本节课的核心内容
3. 教学过程要详细且具有可操作性
4. 每个阶段要标明具体时长(分钟)
5. 作业安排要与课堂内容相呼应
6. content字段必须包含[简单]/[中等]/[困难]难度标注
7. 严格按照JSON格式返回，确保格式完全正确`
}

// 生成课时教案
export const generateLessonDetail = async (req, res) => {
  // 添加 templateId 和 customRequirements 到解构中
  const { courseId, hour, content, titleId, templateId, customRequirements } = req.body;
  const teacherId = req.user?.system_teacher_id;

  try {
    // 确保AI客户端已初始化
    if (!volcano_client) {
      console.log('AI客户端尚未初始化，正在尝试初始化...');
      const config = await createOpenAIConfig(API_USAGES.LESSON_DETAIL);
      volcano_client = new OpenAI(config);
      console.log('成功初始化AI客户端');
    }

    console.log('生成教案参数:', {
      courseId,
      hour,
      titleId,
      content,
      teacherId,
      templateId,  // 添加到日志
      customRequirements  // 添加到日志
    });

    // 获取课程信息
    const [courseRows] = await pool.execute(
      'SELECT course_name, course_type FROM courses WHERE course_code = ? AND teacher_id = ?',
      [courseId, teacherId]
    );

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到课程信息'
      });
    }

    // 构建提示词
    const prompt = generatePrompt(
      courseRows[0].course_type,
      courseRows[0].course_name,
      hour,
      content,
      templateId,  // 现在这些参数都有值了
      customRequirements
    );

    console.log('发送到AI的提示词:', prompt);

    // 设置超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('AI接口响应超时')), 250000);
    });

    // 调用AI接口生成教案
    const aiPromise = volcano_client.chat.completions.create({
      model: "ep-20250310135629-zgwb7",
      messages: [
        {
          role: "system",
          content: "你是一个专业的教案编写专家，请严格按照JSON格式返回内容，确保返回的是合法的JSON字符串"
        },
        {
          role: "user",
          content: prompt
        }
      ]
    });

    // 使用 Promise.race 处理超时
    const response = await Promise.race([aiPromise, timeoutPromise]);

    // 解析AI返回的内容
    let lessonPlan;
    try {
      // 清理返回内容中的特殊字符
      const content = response.choices[0].message.content
        .trim()
        .replace(/\n\s*/g, ' ')  // 替换换行和多余空格
        .replace(/\\n/g, ' ')    // 替换转义的换行符
        .replace(/\s+/g, ' ');   // 合并多个空格

      console.log('AI返回的处理后内容:', content);

      // 解析JSON
      lessonPlan = JSON.parse(content);

      // 验证返回的数据结构
      if (!lessonPlan.content || !lessonPlan.objectives || !lessonPlan.keyPoints ||
          !lessonPlan.teachingProcess || !lessonPlan.homework) {
        throw new Error('AI返回的教案格式不完整');
      }

      // 验证难度标注
      if (!/\[(简单|中等|困难)\]/.test(lessonPlan.content)) {
        throw new Error('教学内容缺少难度标注');
      }

      // 验证教学过程格式
      lessonPlan.teachingProcess.forEach(process => {
        if (!process.stage || !process.duration || !process.content) {
          throw new Error('教学过程格式不正确');
        }
      });

      // 生成PPT大纲
      const pptPrompt = `作为一名专业的${courseRows[0].course_type}课程教师，请根据以下内容生成一份PPT大纲：

课程名称：${courseRows[0].course_name}
课时：${hour}
教学目标：${JSON.stringify(lessonPlan.objectives)}
教学重点：${JSON.stringify(lessonPlan.keyPoints)}

请生成一个结构清晰的PPT大纲，要求：
1. 内容完整但不冗余
2. 层次分明，便于学生理解
3. 符合教学内容和目标
4. 确保返回的是合法的JSON格式
5. 是给学生看的，所以需要面向学生，上课使用
6. 需要着重于教学学生知识点，层层深入，便于学生理解  
7. 请注意，教案只用于告诉你这节课是关于哪些知识点，你需要理解知识点，构建如何让学生的学习效果达到最大化的ppt大纲  
8. 不要按照教案的顺序来生成ppt大纲，而是要根据知识点的重要性来生成ppt大纲

请严格按照以下JSON格式返回：
{
  "title": "课时标题",
  "subTitle": "副标题",
  "chapters": [
    {
      "chapterTitle": "章节标题",
      "chapterContents": [
        {
          "chapterTitle": "子章节标题",
          "chapterContents": null
        }
      ]
    }
  ]
}`;

      console.log('发送PPT大纲生成提示词:', pptPrompt);

      const pptResponse = await volcano_client.chat.completions.create({
        model: "ep-20250310135947-rlxqx",
        messages: [
          {
            role: "system",
            content: "你是一个专业的PPT大纲设计专家，请严格按照JSON格式返回内容，确保返回的是合法的JSON字符串"
          },
          {
            role: "user",
            content: pptPrompt
          }
        ]
      });

      // 解析PPT大纲
      let pptOutline;
      try {
        // 清理PPT大纲返回内容中的特殊字符
        const pptContent = pptResponse.choices[0].message.content
          .trim()
          .replace(/\n\s*/g, ' ')
          .replace(/\\n/g, ' ')
          .replace(/\s+/g, ' ');

        console.log('AI返回的PPT大纲内容:', pptContent);
        pptOutline = JSON.parse(pptContent);

        // 保存PPT大纲到数据库
        await pool.execute(
          `INSERT INTO ppt_templates (
            teacher_id, title, content, course_code, title_id
          ) VALUES (?, ?, ?, ?, ?)`,
          [
            teacherId,
            hour,
            JSON.stringify(pptOutline),
            courseId,
            titleId
          ]
        );

        console.log('PPT大纲保存成功');
      } catch (pptError) {
        console.error('PPT大纲生成或保存失败:', pptError);
        console.error('PPT原始返回内容:', pptResponse.choices[0].message.content);
        // PPT大纲生成失败不影响教案的保存，继续执行
      }

      // 保存教案到数据库
      const planContent = JSON.stringify({
        content: lessonPlan.content,
        objectives: lessonPlan.objectives,
        keyPoints: lessonPlan.keyPoints,
        teachingProcess: lessonPlan.teachingProcess,
        homework: lessonPlan.homework
      });

      await pool.execute(
        `INSERT INTO lesson_plans (
          teacher_id, course_code, title, content, title_id,
          create_time, update_time
        ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          teacherId,
          courseId,
          hour,
          planContent,
          titleId
        ]
      );

      res.json({
        success: true,
        data: lessonPlan
      });

    } catch (error) {
      console.error('AI返回内容解析失败:', error);
      console.error('原始内容:', response.choices[0].message.content);
      res.status(500).json({
        success: false,
        message: 'AI返回内容解析失败'
      });
    }

  } catch (error) {
    console.error('生成教案失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '生成失败'
    });
  }
};

// 导出课时教案
// 获取课程所有课时的教案状态
export const getHoursStatus = async (req, res) => {
  const { courseId } = req.params;
  const teacherId = req.user?.system_teacher_id;

  try {
    // 查询该课程所有课时的教案状态
    const [rows] = await pool.execute(
      `SELECT
        title,
        title_id
       FROM lesson_plans
       WHERE course_code = ?
         AND teacher_id = ?
       GROUP BY title, title_id`,
      [courseId, teacherId]
    );

    // 构建状态对象
    const statusMap = {};
    rows.forEach(row => {
      // 将课时标题作为键，值为true表示已有教案
      statusMap[row.title] = true;
    });

    res.json({
      success: true,
      data: statusMap
    });

  } catch (error) {
    console.error('获取课时状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课时状态失败'
    });
  }
};

export const exportLessonDetail = async (req, res) => {
  const { courseId, titleId } = req.params;
  const teacherId = req.user?.system_teacher_id;

  try {
    // 获取课程和教案信息
    const [courseRows] = await pool.execute(
      'SELECT course_name FROM courses WHERE course_code = ? AND teacher_id = ?',
      [courseId, teacherId]
    );

    const [lessonRows] = await pool.execute(
      `SELECT
        title,
        content
       FROM lesson_plans
       WHERE course_code = ? AND teacher_id = ? AND title_id = ?
       ORDER BY create_time DESC
       LIMIT 1`,
      [courseId, teacherId, titleId]
    );

    if (lessonRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到教案'
      });
    }

    // 解析JSON格式的content字段
    let parsedContent;
    try {
      parsedContent = JSON.parse(lessonRows[0].content);
    } catch (error) {
      console.error('解析教案内容失败:', error);
      return res.status(500).json({
        success: false,
        message: '解析教案内容失败'
      });
    }

    // 创建Word文档
    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          new Paragraph({
            text: `${courseRows[0].course_name} - ${lessonRows[0].title}教案`,
            heading: HeadingLevel.HEADING_1
          }),
          new Paragraph({ text: '' }),
          new Paragraph({
            text: '教学目标',
            heading: HeadingLevel.HEADING_2
          }),
          new Paragraph({
            text: Array.isArray(parsedContent.objectives) ? parsedContent.objectives.join('\n') : parsedContent.objectives || ''
          }),
          new Paragraph({ text: '' }),
          new Paragraph({
            text: '教学重点',
            heading: HeadingLevel.HEADING_2
          }),
          ...(Array.isArray(parsedContent.keyPoints) ? parsedContent.keyPoints.filter(point => point.trim() !== '').map(point =>
            new Paragraph({ text: `• ${point}` })
          ) : []),
          new Paragraph({ text: '' }),
          new Paragraph({
            text: '教学过程',
            heading: HeadingLevel.HEADING_2
          }),
          ...(Array.isArray(parsedContent.teachingProcess) ? parsedContent.teachingProcess.map(step => [
            new Paragraph({
              text: `${step.stage} (${step.duration}分钟)`,
              heading: HeadingLevel.HEADING_3
            }),
            new Paragraph({ text: step.content }),
            new Paragraph({ text: '' })
          ]).flat() : []),
          new Paragraph({
            text: '课后作业',
            heading: HeadingLevel.HEADING_2
          }),
          new Paragraph({
            text: parsedContent.homework || ''
          })
        ]
      }]
    });

    // 生成文档buffer
    const buffer = await Packer.toBuffer(doc);

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    res.setHeader('Content-Disposition', `attachment; filename="lesson-plan.docx"; filename*=UTF-8''${encodeURIComponent(`${courseRows[0].course_name}-${lessonRows[0].title}教案.docx`)}`);

    // 发送文档
    res.send(buffer);

  } catch (error) {
    console.error('导出教案失败:', error);
    res.status(500).json({
      success: false,
      message: '导出教案失败'
    });
  }
};
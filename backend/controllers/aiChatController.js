import { OpenAI } from 'openai';
import pool from '../config/db.js';
import { createOpenAIConfig, API_USAGES } from '../config/modelService.js';

// 初始化火山引擎 AI 客户端（异步）
let volcano_client;
(async () => {
  try {
    const config = await createOpenAIConfig(API_USAGES.CHAT);
    volcano_client = new OpenAI(config);
    console.log('火山引擎 AI 客户端初始化成功');
  } catch (error) {
    console.error('火山引擎 AI 客户端初始化失败:', error);
  }
})();

export const chatWithAI = async (req, res) => {
  const { message, courseId, currentPlan } = req.body;
  const teacherId = req.user?.system_teacher_id;

  try {
    // 确保AI客户端已初始化
    if (!volcano_client) {
      console.log('AI客户端尚未初始化，正在尝试初始化...');
      const config = await createOpenAIConfig(API_USAGES.CHAT);
      volcano_client = new OpenAI(config);
      console.log('成功初始化AI客户端');
    }
    
    // 1. 获取课程信息（如果有）
    let courseInfo = null;
    if (courseId) {
      const [courseRows] = await pool.execute(
        'SELECT course_name, course_type FROM courses WHERE course_code = ? AND teacher_id = ?',
        [courseId, teacherId]
      );
      if (courseRows.length > 0) {
        courseInfo = courseRows[0];
      }
    }

    // 2. 构建系统提示词
    const systemPrompt = courseInfo 
      ? `你是一个专业的${courseInfo.course_type}课程教学助手。
         你的任务是帮助教师完善和改进教案，解答教学相关问题。
         请提供具体的建议和可以直接使用的内容。`
      : `你是一个专业的教学助手。
         你的任务是帮助教师解答教学相关问题，提供专业的教学建议。`;

    // 3. 构建用户提示词
    const userPrompt = courseInfo
      ? `课程：${courseInfo.course_name}
         ${currentPlan ? '当前教案内容：' + JSON.stringify(currentPlan) : ''}
         用户问题：${message}`
      : message;

    // 检查是否请求流式响应
    const isStreamRequest = req.query.stream === 'true';
    
    if (isStreamRequest) {
      // 设置SSE响应头
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');

      // 4. 调用AI接口（流式）
      const stream = await volcano_client.chat.completions.create({
        model: "ep-20250310135947-rlxqx",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        stream: true
      });
      
      // 处理数据流
      for await (const chunk of stream) {
        // 获取当前块中的文本
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          // 发送SSE格式的数据
          res.write(`data: ${JSON.stringify({ content })}\n\n`);
        }
      }
      
      // 发送完成信号
      res.write(`data: ${JSON.stringify({ done: true })}\n\n`);
      res.end();
    } else {
      // 4. 调用AI接口（非流式 - 保持原有逻辑）
      const response = await volcano_client.chat.completions.create({
        model: "ep-20250310135947-rlxqx",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ]
      });

      // 5. 返回AI回复
      res.json({
        success: true,
        data: {
          content: response.choices[0].message.content.trim()
        }
      });
    }
  } catch (error) {
    console.error('AI聊天失败:', error);
    
    // 如果是流式请求，发送错误事件
    if (req.query.stream === 'true') {
      res.write(`event: error\ndata: ${JSON.stringify({error: error.message || 'AI聊天失败'})}\n\n`);
      res.end();
    } else {
      res.status(500).json({
        success: false, 
        message: error.message || 'AI聊天失败'
      });
    }
  }
}; 
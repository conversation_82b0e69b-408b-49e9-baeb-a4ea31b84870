import { createOpenAIConfig, API_USAGES } from '../config/modelService.js';
import { OpenAI } from 'openai';

// 初始化 Moonshot AI 客户端（异步）
let client;
(async () => {
  try {
    const config = await createOpenAIConfig(API_USAGES.SEARCH);
    client = new OpenAI(config);
    console.log('Moonshot AI 客户端初始化成功');
  } catch (error) {
    console.error('Moonshot AI 客户端初始化失败:', error);
  }
})();

const tools = [{
    "type": "builtin_function",
    "function": {
        "name": "$web_search",
    },
}];

// 搜索实现函数
function search_impl(searchArgs) {
    return searchArgs;
}

// 处理单个搜索结果
function processSearchResult(content) {
    try {
        // 匹配链接
        const linkMatch = content.match(/\[([^\]]+)\]\(([^)]+)\)/);
        if (!linkMatch) return null;

        // 提取描述（链接后的文本）
        const description = content.replace(linkMatch[0], '').trim();
        
        // 判断是否是购书链接
        const isBookLink = linkMatch[1].toLowerCase().includes('购买') || 
                          linkMatch[1].toLowerCase().includes('book') ||
                          linkMatch[2].includes('amazon') ||
                          linkMatch[2].includes('jd.com') ||
                          linkMatch[2].includes('dangdang');
        
        // 判断内容类型
        const urlLower = linkMatch[2].toLowerCase();
        let contentType = '其他资源';
        let sourceType = 'normal';

        // 技术类网站
        if (urlLower.includes('csdn.net') ||
            urlLower.includes('juejin.cn') ||
            urlLower.includes('github.com') ||
            urlLower.includes('gitee.com') ||
            urlLower.includes('stackoverflow.com')) {
            contentType = '技术文章';
            sourceType = 'tech';
        }
        // 教育类网站
        else if (urlLower.includes('zhihu.com') ||
                 urlLower.includes('jianshu.com') ||
                 urlLower.includes('cnblogs.com') ||
                 urlLower.includes('163.com') ||
                 urlLower.includes('sina.com.cn')) {
            contentType = '教育资源';
            sourceType = 'edu';
        }
        // 学术类网站
        else if (urlLower.includes('cnki.net') ||
                 urlLower.includes('researchgate.net') ||
                 urlLower.includes('sciencedirect.com') ||
                 urlLower.includes('springer.com') ||
                 urlLower.includes('edu.cn')) {
            contentType = '学术资源';
            sourceType = 'academic';
        }
        // 视频网站
        else if (urlLower.includes('bilibili.com') ||
                 urlLower.includes('youku.com') ||
                 urlLower.includes('youtube.com')) {
            contentType = '视频教程';
            sourceType = 'video';
        }

        // 过滤购书链接
        if (isBookLink) return null;

        return {
            title: linkMatch[1],
            url: linkMatch[2],
            description: description,
            source: contentType,
            sourceType: sourceType
        };
    } catch (error) {
        console.error('处理搜索结果时出错:', error);
        return null;
    }
}

// AI搜索控制器
export const searchContent = async (req, res) => {
    try {
        // 确保AI客户端已初始化
        if (!client) {
          console.log('AI客户端尚未初始化，正在尝试初始化...');
          const config = await createOpenAIConfig(API_USAGES.SEARCH);
          client = new OpenAI(config);
          console.log('成功初始化AI客户端');
        }
        
        const { keyword } = req.query;
        
        if (!keyword) {
            return res.status(400).json({
                success: false,
                message: '请提供搜索关键词'
            });
        }

        // 设置响应头，支持流式传输
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        console.log('\n=== AI全网搜索 ===');
        console.log(`搜索关键词: ${keyword}`);

        const messages = [
            {
                role: "system",
                content: "你是 Kimi，由 Moonshot AI 提供的人工智能助手。请帮助用户搜索相关的学习资源。确保内容的准确性和权威性，优先选择知名教师或机构的资源。"
            },
            {
                role: "user",
                content: `请搜索与"${keyword}"相关的学习资源，重点关注：
1. 权威的教程和课程
2. 专业的知识讲解
3. 优质的实践案例
4. 前沿的研究动态
请返回5-8个高质量的结果，并附上链接。`
            }
        ];

        let finishReason = null;

        while (finishReason === null || finishReason === "tool_calls") {
            const completion = await client.chat.completions.create({
                model: "moonshot-v1-32k",
                messages: messages,
                temperature: 0.3,
                tools: tools,
                max_tokens: 4000,
            });

            const choice = completion.choices[0];
            finishReason = choice.finish_reason;

            if (finishReason === "tool_calls") {
                messages.push(choice.message);
                
                for (const toolCall of choice.message.tool_calls) {
                    const tool_call_name = toolCall.function.name;
                    const tool_call_arguments = JSON.parse(toolCall.function.arguments);
                    
                    let tool_result;
                    if (tool_call_name === "$web_search") {
                        tool_result = search_impl(tool_call_arguments);
                    } else {
                        tool_result = 'no tool found';
                    }

                    messages.push({
                        "role": "tool",
                        "tool_call_id": toolCall.id,
                        "name": tool_call_name,
                        "content": JSON.stringify(tool_result),
                    });
                }
            } else if (choice.message.content) {
                const lines = choice.message.content.split('\n');
                let currentContent = '';
                
                for (const line of lines) {
                    if (line.trim() === '') {
                        if (currentContent) {
                            const result = processSearchResult(currentContent);
                            if (result) {
                                // 实时发送结果
                                res.write(`data: ${JSON.stringify({
                                    type: 'result',
                                    data: result
                                })}\n\n`);
                            }
                            currentContent = '';
                        }
                    } else {
                        currentContent += line + '\n';
                    }
                }
                
                if (currentContent) {
                    const result = processSearchResult(currentContent);
                    if (result) {
                        // 实时发送最后一个结果
                        res.write(`data: ${JSON.stringify({
                            type: 'result',
                            data: result
                        })}\n\n`);
                    }
                }
            }
        }

        // 发送搜索完成信号
        res.write(`data: ${JSON.stringify({
            type: 'complete',
            data: {
                message: '搜索完成'
            }
        })}\n\n`);
        res.end();

    } catch (error) {
        console.error('AI搜索出错:', error);
        
        res.write(`data: ${JSON.stringify({
            type: 'error',
            message: '搜索失败，请稍后重试',
            error: error.message
        })}\n\n`);
        res.end();
    }
}; 
import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

/**
 * 获取学生测验答案详情
 * 教师用于查看学生提交的测验答案
 */
export const getStudentAnswers = async (req, res) => {
  try {
    const { classId, chapterId, batchId, studentId } = req.params;
    
    if (!classId || !chapterId || !batchId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    const conn = await mysql.createConnection(dbConfig);
    
    // 1. 获取发布的习题ID和详情
    const exerciseSql = `
      SELECT 
        pe.id as publish_id,
        eb.exercise_id,
        eb.content,
        eb.answer,
        eb.analysis,
        eb.point,
        eb.question_type,
        eb.option,
        eb.title_id,
        ser.student_answer,
        ser.is_correct,
        ser.score,
        ser.evaluation,
        ser.create_time as submit_time
      FROM publish_exercise pe
      JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
      LEFT JOIN student_exercise_records ser 
        ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
      WHERE pe.class_id = ? 
        AND eb.title_id = ? 
        AND pe.release_batch = ?
        AND pe.study_phase = 2  -- 仅获取课后测验题
      ORDER BY pe.id ASC
    `;
    
    const [exercises] = await conn.query(exerciseSql, [
      studentId,
      classId,
      chapterId,
      batchId
    ]);
    
    // 2. 获取学生提交时间信息
    const timerSql = `
      SELECT 
        start_time, 
        end_time, 
        is_completed, 
        is_timeout
      FROM student_homework_timer
      WHERE student_id = ? 
        AND class_id = ? 
        AND title_id = ?
        AND release_batch = ?
      LIMIT 1
    `;
    
    const [submissionInfo] = await conn.query(timerSql, [
      studentId,
      classId,
      chapterId,
      batchId
    ]);
    
    // 3. 格式化习题数据
    const formattedExercises = exercises.map(exercise => {
      // 处理选项数据
      let options = [];
      try {
        if (exercise.option) {
          const optionObj = JSON.parse(exercise.option);
          options = Object.entries(optionObj).map(([key, value]) => ({
            key,
            value
          }));
        }
      } catch (e) {
        console.error('解析选项失败:', e);
      }
      
      // 为判断题设置标准选项
      if (exercise.question_type === 3) {
        options = [
          { key: 'true', value: '正确' },
          { key: 'false', value: '错误' }
        ];
      }
      
      // 处理知识点
      let points = [];
      try {
        if (exercise.point) {
          if (exercise.point.startsWith('[') || exercise.point.startsWith('{')) {
            try {
              points = JSON.parse(exercise.point);
              if (!Array.isArray(points)) {
                points = [points];
              }
            } catch (e) {
              points = [exercise.point];
            }
          } else {
            points = exercise.point.split(',').map(p => p.trim());
          }
        }
      } catch (e) {
        console.error('解析知识点失败:', e);
        points = exercise.point ? [exercise.point] : [];
      }

      // 确保分数是有效的数字
      let score = 0;
      if (exercise.score !== null && exercise.score !== undefined) {
        const numScore = Number(exercise.score);
        score = !isNaN(numScore) ? Math.round(numScore) : 0;
      }

      return {
        ...exercise,
        options,
        student_answer: exercise.student_answer || '未作答',
        analysis: exercise.analysis || '暂无解析',
        point: points,
        score: score,
        evaluation: exercise.evaluation || ''
      };
    });
    
    // 4. 计算总分和满分
    const scoreInfo = formattedExercises.reduce((acc, exercise) => {
      // 根据题型设置满分
      const maxScore = (exercise.question_type <= 3) ? 5 : 10;
      const exerciseScore = exercise.score || 0;
      
      return {
        earned: acc.earned + exerciseScore,
        total: acc.total + maxScore
      };
    }, { earned: 0, total: 0 });
    
    await conn.end();
    
    res.json({
      success: true,
      data: {
        exercises: formattedExercises,
        submission: submissionInfo.length > 0 ? submissionInfo[0] : null,
        score: scoreInfo
      }
    });
  } catch (error) {
    console.error('获取学生答案详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学生答案详情失败: ' + error.message
    });
  }
}; 
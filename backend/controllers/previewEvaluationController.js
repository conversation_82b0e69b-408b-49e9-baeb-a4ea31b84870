import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';
import OpenAI from 'openai';
import 'dotenv/config';

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.ARK_API_KEY || '1dfa2f7b-c0f7-486c-bd5a-068ff9c8d677',
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
});

const pool = mysql.createPool(dbConfig);

/**
 * 获取知识点教学建议
 * @param {Array} knowledgePoints - 错误率较高的知识点数组
 * @returns {Promise<string>} - AI生成的教学建议
 */
const getKnowledgePointSuggestions = async (knowledgePoints) => {
  try {
    // 准备提示词
    const prompt = `
我是一名教师，正在分析学生的预习情况。以下是学生在预习测试中表现较差的知识点及其错误率：
${knowledgePoints.map(kp => `- ${kp.name}：错误率 ${kp.errorRate}%`).join('\n')}

请针对这些知识点提供以下内容：
1. 这些知识点可能存在的学习难点分析
2. 针对性的教学建议（每个知识点给出具体的教学策略）
3. 可以推荐给学生的额外学习资源或练习方式

请以教学指导的形式回答，内容简洁明了，便于我直接采用。`;

    // 调用API
    const completion = await openai.chat.completions.create({
      messages: [
        { role: 'system', content: '你是一个专业的教学辅助顾问，擅长分析学生学习数据并提供针对性的教学建议。' },
        { role: 'user', content: prompt },
      ],
      model: 'ep-20250310135629-zgwb7',
    });

    return completion.choices[0]?.message?.content || '无法获取AI分析结果';
  } catch (error) {
    console.error('AI分析失败:', error);
    return '获取AI分析建议时出错，请稍后再试';
  }
};

/**
 * 计算预习资料得分
 * @param {number} viewDuration - 学生观看时间（秒）
 * @param {number} expectedDuration - 推荐阅读时间（秒）
 * @returns {number} - 得分（0-100）
 */
const calculateMaterialScore = (viewDuration, expectedDuration) => {
  if (!expectedDuration || expectedDuration <= 0) return 0;
  
  // 将观看时间转换为分钟
  const viewMinutes = viewDuration / 60;
  const expectedMinutes = expectedDuration / 60;
  
  // 计算时间差（绝对值）
  const timeDiff = Math.abs(viewMinutes - expectedMinutes);
  
  // 如果时间差超过预期时间的50%，得分开始降低
  const threshold = expectedMinutes * 0.5;
  
  if (timeDiff <= threshold) {
    // 在阈值范围内，得分为100
    return 100;
  } else {
    // 超过阈值，得分线性降低
    // 最大允许超过预期时间100%，超过则得分为0
    const maxExcess = expectedMinutes;
    const excessRatio = Math.min(timeDiff / maxExcess, 1);
    return Math.max(0, 100 * (1 - excessRatio));
  }
};

/**
 * 计算习题得分
 * @param {number} correctCount - 正确题目数
 * @param {number} totalCount - 总题目数
 * @returns {number} - 得分（0-100）
 */
const calculateExerciseScore = (correctCount, totalCount) => {
  if (!totalCount || totalCount <= 0) return 0;
  return (correctCount / totalCount) * 100;
};

/**
 * 计算综合得分
 * @param {number} materialScore - 预习资料得分
 * @param {number} exerciseScore - 习题得分
 * @param {number} materialWeight - 预习资料权重（默认0.4）
 * @param {number} exerciseWeight - 习题权重（默认0.6）
 * @returns {number} - 综合得分（0-100）
 */
const calculateTotalScore = (materialScore, exerciseScore, materialWeight = 0.4, exerciseWeight = 0.6) => {
  return materialScore * materialWeight + exerciseScore * exerciseWeight;
};

/**
 * 获取班级预习评估数据
 */
export const getClassPreviewEvaluation = async (req, res) => {
  const { classId, chapterId } = req.params;

  try {
    // 1. 获取预习资料信息
    const [previewMaterials] = await pool.query(
      `SELECT pm.id, pm.expected_duration
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       WHERE pp.class_id = ? AND pm.title_id = ?`,
      [classId, chapterId]
    );

    if (previewMaterials.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到预习资料'
      });
    }

    const previewMaterial = previewMaterials[0];
    const expectedDuration = previewMaterial.expected_duration || 0;

    // 2. 获取班级学生列表
    const [students] = await pool.query(
      `SELECT s.student_id, s.name
       FROM students s
       JOIN student_course_registration scr ON s.student_id = scr.student_id
       WHERE scr.class_id = ?`,
      [classId]
    );

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级学生'
      });
    }

    // 3. 获取预习发布ID
    const [previewPublishes] = await pool.query(
      `SELECT id FROM preview_publish
       WHERE class_id = ? AND preview_material_id = ?`,
      [classId, previewMaterial.id]
    );

    if (previewPublishes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到预习发布记录'
      });
    }

    const previewPublishId = previewPublishes[0].id;

    // 4. 获取预习题发布ID
    const [exercisePublishes] = await pool.query(
      `SELECT id FROM publish_exercise
       WHERE class_id = ? AND title_id = ? AND study_phase = 1`,
      [classId, chapterId]
    );

    if (exercisePublishes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到预习题发布记录'
      });
    }

    const exercisePublishIds = exercisePublishes.map(ep => ep.id);

    // 5. 计算每个学生的得分
    const studentScores = await Promise.all(students.map(async (student) => {
      // 5.1 获取学生预习资料观看时间
      const [previewStudent] = await pool.query(
        `SELECT total_view_duration
         FROM preview_student
         WHERE preview_publish_id = ? AND student_id = ?`,
        [previewPublishId, student.student_id]
      );

      const viewDuration = previewStudent.length > 0 ? (previewStudent[0].total_view_duration || 0) : 0;
      
      // 5.2 计算预习资料得分
      const materialScore = calculateMaterialScore(viewDuration, expectedDuration);

      // 5.3 获取学生预习题完成情况
      const [exerciseResults] = await pool.query(
        `SELECT 
           COUNT(*) as total_count,
           SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count
         FROM student_exercise_records
         WHERE publish_exercise_id IN (?) AND student_id = ?`,
        [exercisePublishIds, student.student_id]
      );

      const totalExercises = exerciseResults[0].total_count || 0;
      const correctExercises = exerciseResults[0].correct_count || 0;
      
      // 5.4 计算习题得分
      const exerciseScore = calculateExerciseScore(correctExercises, totalExercises);

      // 5.5 计算综合得分
      const totalScore = calculateTotalScore(materialScore, exerciseScore);

      return {
        student_id: student.student_id,
        name: student.name,
        material_score: Math.round(materialScore),
        exercise_score: Math.round(exerciseScore),
        total_score: Math.round(totalScore),
        view_duration: viewDuration,
        expected_duration: expectedDuration,
        correct_exercises: correctExercises,
        total_exercises: totalExercises
      };
    }));

    // 6. 计算班级平均分
    const avgMaterialScore = studentScores.reduce((sum, student) => sum + student.material_score, 0) / studentScores.length;
    const avgExerciseScore = studentScores.reduce((sum, student) => sum + student.exercise_score, 0) / studentScores.length;
    const avgTotalScore = studentScores.reduce((sum, student) => sum + student.total_score, 0) / studentScores.length;

    return res.json({
      success: true,
      data: {
        students: studentScores,
        averages: {
          material_score: Math.round(avgMaterialScore),
          exercise_score: Math.round(avgExerciseScore),
          total_score: Math.round(avgTotalScore)
        },
        expected_duration: expectedDuration
      }
    });

  } catch (error) {
    console.error('获取班级预习评估数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取班级预习评估数据失败',
      error: error.message
    });
  }
};

/**
 * 获取班级预习题错误知识点热力图数据
 */
export const getErrorKnowledgePoints = async (req, res) => {
  const { classId, chapterId } = req.params;
  const includeAiAnalysis = req.query.ai === 'true';

  try {
    // 1. 获取预习题发布ID
    const [exercisePublishes] = await pool.query(
      `SELECT id, exercise_id FROM publish_exercise
       WHERE class_id = ? AND title_id = ? AND study_phase = 1`,
      [classId, chapterId]
    );

    if (exercisePublishes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到预习题发布记录'
      });
    }

    const exercisePublishIds = exercisePublishes.map(ep => ep.id);
    
    // 2. 获取学生做错的题目ID（通过publish_exercise表关联）
    const wrongAnswersQuery = `
      SELECT pe.exercise_id, COUNT(*) as error_count
      FROM student_exercise_records ser
      JOIN publish_exercise pe ON ser.publish_exercise_id = pe.id
      WHERE ser.publish_exercise_id IN (?) AND ser.is_correct = 0
      GROUP BY pe.exercise_id
      ORDER BY error_count DESC
      LIMIT 10
    `;
    
    const [wrongExercises] = await pool.query(wrongAnswersQuery, [exercisePublishIds]);

    if (wrongExercises.length === 0) {
      return res.json({
        success: true,
        data: {
          knowledgePoints: [],
          errorRates: []
        }
      });
    }

    // 3. 获取这些题目的知识点
    const exerciseIds = wrongExercises.map(we => we.exercise_id);
    const [exerciseKnowledgePoints] = await pool.query(
      `SELECT e.exercise_id, e.title, e.point as knowledge_point, e.difficulty
       FROM exercise_bank e
       WHERE e.exercise_id IN (?)`,
      [exerciseIds]
    );

    // 4. 统计知识点错误次数
    const knowledgePointsMap = {};
    exerciseKnowledgePoints.forEach(ekp => {
      if (!ekp.knowledge_point) return;
      
      const kps = ekp.knowledge_point.split(',').map(kp => kp.trim());
      const wrongExercise = wrongExercises.find(we => we.exercise_id === ekp.exercise_id);
      const errorCount = wrongExercise ? wrongExercise.error_count : 0;
      
      kps.forEach(kp => {
        if (!knowledgePointsMap[kp]) {
          knowledgePointsMap[kp] = {
            name: kp,
            errorCount: 0,
            exercises: []
          };
        }
        
        knowledgePointsMap[kp].errorCount += errorCount;
        knowledgePointsMap[kp].exercises.push({
          id: ekp.exercise_id,
          title: ekp.title,
          difficulty: ekp.difficulty,
          errorCount: errorCount
        });
      });
    });

    // 5. 转换为数组并排序
    const knowledgePoints = Object.values(knowledgePointsMap)
      .sort((a, b) => b.errorCount - a.errorCount)
      .slice(0, 8); // 取前8个知识点
    
    // 6. 获取总答题人数，用于计算错误率
    const [totalAttempts] = await pool.query(
      `SELECT COUNT(DISTINCT student_id) as student_count
       FROM student_exercise_records
       WHERE publish_exercise_id IN (?)`,
      [exercisePublishIds]
    );
    
    const studentCount = totalAttempts[0]?.student_count || 0;
    
    // 7. 计算每个知识点的错误率
    const knowledgePointsWithRates = knowledgePoints.map(kp => ({
      ...kp,
      errorRate: studentCount > 0 ? Math.round((kp.errorCount / studentCount) * 100) : 0
    }));

    // 8. 如果请求中包含AI分析，则调用AI获取建议
    let aiSuggestions = null;
    if (includeAiAnalysis && knowledgePointsWithRates.length > 0) {
      try {
        aiSuggestions = await getKnowledgePointSuggestions(knowledgePointsWithRates);
      } catch (aiError) {
        console.error('AI分析失败:', aiError);
        // AI分析失败不影响主流程，继续返回知识点数据
      }
    }

    return res.json({
      success: true,
      data: {
        knowledgePoints: knowledgePointsWithRates,
        studentCount,
        aiSuggestions
      }
    });

  } catch (error) {
    console.error('获取知识点热力图数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取知识点热力图数据失败',
      error: error.message
    });
  }
}; 
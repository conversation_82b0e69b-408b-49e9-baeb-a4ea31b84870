import pool from '../config/db.js';

// 获取章节列表
export const getChapters = async (req, res) => {
  const { classId } = req.params;
  const { study_phase } = req.query; // 添加study_phase查询参数
  
  console.log(`获取章节列表 - 班级ID: ${classId}, 学习阶段: ${study_phase || '全部'}`);
  
  try {
    // 如果没有认证信息，创建一个虚拟的默认响应数据（演示模式）
    if (!req.user) {
      // 返回一些示例章节数据
      return res.json({
        success: true,
        data: [
          { id: '1', title: '第一章 - 第一节' },
          { id: '2', title: '第一章 - 第二节' },
          { id: '3', title: '第二章 - 第一节' },
          { id: '4', title: '第二章 - 第二节' }
        ],
        demo_mode: true
      });
    }
    
    // 以下是正常认证用户的逻辑
    // 先获取班级对应的课程代码和最新的大纲
    const [classRows] = await pool.query(
      'SELECT cc.course_code, s.content FROM course_classes cc ' +
      'LEFT JOIN syllabus s ON s.course_code = cc.course_code ' +
      'WHERE cc.id = ? ' +
      'ORDER BY s.create_time DESC LIMIT 1',
      [classId]
    );

    if (classRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }

    const courseCode = classRows[0].course_code;
    const syllabusContent = JSON.parse(classRows[0].content || '{}');

    // 获取有习题的章节ID列表，同时根据学习阶段筛选
    let query = `SELECT DISTINCT title_id 
                 FROM exercise_bank 
                 WHERE course_code = ?`;
    const params = [courseCode];
    
    // 如果指定了学习阶段，添加筛选条件
    if (study_phase) {
      query += ` AND study_phase = ?`;
      params.push(study_phase);
    }
    
    const [exerciseRows] = await pool.query(query, params);

    const hasExerciseTitleIds = new Set(exerciseRows.map(row => row.title_id.toString()));
    const chapters = [];

    // 解析大纲内容，提取有习题的章节
    const processContent = (title, value) => {
      if (typeof value === 'string') {
        if (hasExerciseTitleIds.has(value)) {
          chapters.push({
            id: value,
            title: title
          });
        }
      } else if (typeof value === 'object') {
        // 处理有子章节的情况
        for (const [subTitle, subValue] of Object.entries(value)) {
          if (typeof subValue === 'string') {
            if (hasExerciseTitleIds.has(subValue)) {
              chapters.push({
                id: subValue,
                title: `${title} - ${subTitle}`
              });
            }
          } else if (typeof subValue === 'object') {
            // 处理嵌套子章节
            for (const [subSubTitle, subSubValue] of Object.entries(subValue)) {
              if (typeof subSubValue === 'string' && hasExerciseTitleIds.has(subSubValue)) {
                chapters.push({
                  id: subSubValue,
                  title: `${title} - ${subTitle} - ${subSubTitle}`
                });
              }
            }
          }
        }
      }
    };

    // 处理所有章节
    for (const [title, value] of Object.entries(syllabusContent)) {
      processContent(title, value);
    }

    // 如果没有找到任何章节，通过直接查询习题获取 title_id
    if (chapters.length === 0) {
      console.log("未从大纲中找到匹配的章节，尝试直接从习题获取 title_id");
      const [titleIds] = await pool.query(
        `SELECT DISTINCT e.title_id, e.title, 
         (SELECT SUBSTRING_INDEX(GROUP_CONCAT(title ORDER BY exercise_id SEPARATOR '||'), '||', 1) 
          FROM exercise_bank WHERE title_id = e.title_id LIMIT 1) AS chapter_title
         FROM exercise_bank e 
         WHERE e.course_code = ? ${study_phase ? 'AND e.study_phase = ?' : ''}
         GROUP BY e.title_id`,
        study_phase ? [courseCode, study_phase] : [courseCode]
      );
      
      for (const row of titleIds) {
        chapters.push({
          id: row.title_id.toString(),
          title: row.chapter_title || `章节 ${row.title_id}`
        });
      }
    }

    // 按照 id 排序
    chapters.sort((a, b) => parseInt(a.id) - parseInt(b.id));

    console.log(`返回章节列表 - 找到 ${chapters.length} 个章节`);
    chapters.forEach((ch, idx) => {
      console.log(`  ${idx+1}. ID: ${ch.id}, 标题: ${ch.title}`);
    });

    res.json({
      success: true,
      data: chapters
    });
  } catch (error) {
    console.error('获取章节列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取章节列表失败'
    });
  }
};

// 获取习题列表
export const getExercises = async (req, res) => {
  const { classId } = req.params;
  const { chapter } = req.query;
  
  try {
    // 获取已发布的习题
    let query = `
      SELECT 
        eb.exercise_id,
        eb.title,
        eb.content,
        eb.difficulty,
        eb.question_type,
        eb.study_phase,
        pe.created_at as publish_time,
        pe.release_batch,
        pe.deadline,
        pe.time,
        eb.title_id
      FROM exercise_bank eb
      INNER JOIN publish_exercise pe ON eb.exercise_id = pe.exercise_id
      WHERE pe.class_id = ?
    `;
    
    const params = [classId];
    
    if (chapter) {
      query += ' AND eb.title_id = ?';
      params.push(chapter);
    }
    
    query += ' ORDER BY pe.created_at DESC';
    
    const [exercises] = await pool.query(query, params);

    res.json({
      success: true,
      data: exercises
    });
  } catch (error) {
    console.error('获取习题列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取习题列表失败'
    });
  }
};

// 添加一个新端点获取所有习题（已发布和未发布的）
export const getAllExercises = async (req, res) => {
  const { classId } = req.params;
  const { chapter, showStatus } = req.query;
  
  try {
    // 如果没有认证信息，创建一个虚拟的默认响应数据（演示模式）
    if (!req.user) {
      // 从数据库获取一些示例数据
      const [sampleExercises] = await pool.query(
        `SELECT 
          exercise_id,
          title,
          content,
          difficulty,
          question_type,
          study_phase,
          title_id
        FROM exercise_bank 
        LIMIT 10`
      );
      
      // 处理数据，添加一些模拟的发布信息
      const demoData = sampleExercises.map((exercise, index) => ({
        ...exercise,
        publish_time: index < 5 ? new Date().toISOString() : null,
        release_batch: index < 5 ? Math.floor(index / 2) + 1 : null,
        deadline: index < 5 ? new Date(Date.now() + ********).toISOString() : null,
        time: index < 5 ? 30 : null
      }));
      
      return res.json({
        success: true,
        data: demoData,
        demo_mode: true
      });
    }
    
    // 以下是正常认证用户的逻辑
    // 获取班级和课程信息
    const [classRows] = await pool.query(
      'SELECT course_code FROM course_classes WHERE id = ?',
      [classId]
    );

    if (classRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }

    const courseCode = classRows[0].course_code;
    
    // 使用LEFT JOIN获取所有习题（包括已发布和未发布的）
    let query = `
      SELECT 
        eb.exercise_id,
        eb.title,
        eb.content,
        eb.difficulty,
        eb.question_type,
        eb.study_phase,
        eb.title_id,
        pe.created_at as publish_time,
        pe.release_batch,
        pe.deadline,
        pe.time
      FROM exercise_bank eb
      LEFT JOIN publish_exercise pe ON eb.exercise_id = pe.exercise_id AND pe.class_id = ?
      WHERE eb.course_code = ?
    `;
    
    const params = [classId, courseCode];
    
    // 按章节筛选
    if (chapter) {
      query += ' AND eb.title_id = ?';
      params.push(chapter);
    }
    
    // 按学习阶段筛选（预习题/课后题）
    if (showStatus) {
      query += ' AND eb.study_phase = ?';
      params.push(showStatus);
    }
    
    query += ' ORDER BY IFNULL(pe.created_at, "9999-12-31") ASC, eb.create_time DESC';
    
    const [exercises] = await pool.query(query, params);

    res.json({
      success: true,
      data: exercises
    });
  } catch (error) {
    console.error('获取所有习题列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取所有习题列表失败'
    });
  }
};

// 创建（发布）习题
export const createExercise = async (req, res) => {
  const { classId } = req.params;
  const { exerciseId, deadline, time } = req.body;

  if (!exerciseId) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数'
    });
  }

  if (!deadline) {
    return res.status(400).json({
      success: false,
      message: '截止时间是必填项'
    });
  }

  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();

    // 1. 获取班级和课程信息
    const [classRows] = await connection.query(
      'SELECT course_code FROM course_classes WHERE id = ?',
      [classId]
    );

    if (classRows.length === 0) {
      throw new Error('未找到班级信息');
    }

    const courseCode = classRows[0].course_code;

    // 2. 检查习题是否存在且属于该课程
    const [exerciseRows] = await connection.query(
      'SELECT exercise_id, title_id, study_phase FROM exercise_bank WHERE exercise_id = ? AND course_code = ?',
      [exerciseId, courseCode]
    );

    if (exerciseRows.length === 0) {
      throw new Error('习题不存在或不属于该课程');
    }

    const titleId = exerciseRows[0].title_id;
    const studyPhase = exerciseRows[0].study_phase || 2; // 默认为课后题(2)

    // 检查预习题是否已经被发布过
    if (studyPhase === 1) {
      const [publishedCheck] = await connection.query(
        'SELECT COUNT(*) as count FROM publish_exercise WHERE class_id = ? AND exercise_id = ? AND study_phase = 1',
        [classId, exerciseId]
      );
      
      if (publishedCheck[0].count > 0) {
        throw new Error('该预习题已发布过，不能重复发布');
      }
    }

    // 4. 获取当前班级和章节的最大发布批次 (预习题固定为1)
    let releaseBatch = 1;
    
    // 课后题才需要计算批次
    if (studyPhase === 2) {
      const [batchRows] = await connection.query(
        'SELECT MAX(release_batch) as max_batch FROM publish_exercise WHERE class_id = ? AND title_id = ? AND study_phase = ?',
        [classId, titleId, studyPhase]
      );
      
      if (batchRows[0].max_batch) {
        releaseBatch = batchRows[0].max_batch + 1;
      }
    }

    // 设置time值，预习题time为null
    const timeValue = studyPhase === 1 ? null : Number(time);
    
    // 如果是课后题但time无效
    if (studyPhase === 2 && (timeValue === undefined || timeValue === null || isNaN(timeValue) || timeValue <= 0)) {
      throw new Error('课后题必须设置有效的答题时间');
    }

    // 5. 插入发布记录
    await connection.query(
      `INSERT INTO publish_exercise (course_code, class_id, exercise_id, title_id, release_batch, deadline, time, study_phase)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [courseCode, classId, exerciseId, titleId, releaseBatch, deadline, timeValue, studyPhase]
    );

    await connection.commit();

    res.status(201).json({
      success: true,
      message: '习题发布成功',
      data: {
        release_batch: releaseBatch
      }
    });
  } catch (error) {
    await connection.rollback();
    console.error('发布习题失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '发布习题失败'
    });
  } finally {
    connection.release();
  }
};

// 批量发布习题
export const batchCreateExercises = async (req, res) => {
  const { classId } = req.params;
  const { exerciseIds, deadline, time, study_phase } = req.body;
  const isPreviewOnly = study_phase === 1; // 是否只包含预习题
  const isHomeworkOnly = study_phase === 2; // 是否只包含课后题

  if (!exerciseIds || !Array.isArray(exerciseIds) || exerciseIds.length === 0) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数或参数格式错误'
    });
  }

  if (!deadline) {
    return res.status(400).json({
      success: false,
      message: '截止时间是必填项'
    });
  }

  // 只有不是纯预习题的情况才检查time参数
  if (!isPreviewOnly) {
    // 课后题才需要检查time
    if (time === undefined || time === null || time === '') {
      return res.status(400).json({
        success: false,
        message: '请为课后题设置答题时间'
      });
    }

    // 确保time是数字
    const timeValue = Number(time);
    if (isNaN(timeValue) || timeValue <= 0) {
      return res.status(400).json({
        success: false,
        message: '答题时间必须是正数'
      });
    }
  }

  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();

    // 1. 获取班级和课程信息
    const [classRows] = await connection.query(
      'SELECT id, course_code, class_name FROM course_classes WHERE id = ?',
      [classId]
    );

    if (classRows.length === 0) {
      throw new Error('未找到班级信息');
    }

    const courseCode = classRows[0].course_code;

    // 2. 检查所有习题是否存在且属于该课程
    const [exerciseRows] = await connection.query(
      'SELECT exercise_id, title, title_id, study_phase FROM exercise_bank WHERE exercise_id IN (?) AND course_code = ?',
      [exerciseIds, courseCode]
    );

    if (exerciseRows.length !== exerciseIds.length) {
      const foundIds = exerciseRows.map(row => row.exercise_id);
      const invalidIds = exerciseIds.filter(id => !foundIds.includes(id));
      throw new Error('部分习题不存在或不属于该课程');
    }

    // 验证所有习题类型是否一致，不允许同时发布预习题和课后题
    const previewExercises = exerciseRows.filter(ex => ex.study_phase === 1);
    const homeworkExercises = exerciseRows.filter(ex => ex.study_phase === 2 || ex.study_phase === null);
    
    if (previewExercises.length > 0 && homeworkExercises.length > 0) {
      throw new Error('不能同时发布预习题和课后题，请分开发布');
    }
    
    // 如果前端未指定study_phase，则根据实际习题类型判断
    const hasPreviewExercises = previewExercises.length > 0;
    const actualIsPreviewOnly = hasPreviewExercises && homeworkExercises.length === 0;
    
    // 获取习题所属的章节和预习题ID
    const titleIds = [...new Set(exerciseRows.map(row => row.title_id))];
    const previewExerciseIds = previewExercises.map(row => row.exercise_id);
    
    // 检查预习题是否已经被发布过
    if (previewExerciseIds.length > 0) {
      const [publishedCheck] = await connection.query(
        'SELECT exercise_id FROM publish_exercise WHERE class_id = ? AND exercise_id IN (?) AND study_phase = 1',
        [classId, previewExerciseIds]
      );
      
      if (publishedCheck.length > 0) {
        const publishedIds = publishedCheck.map(row => row.exercise_id);
        throw new Error(`以下预习题已发布过，不能重复发布: ${publishedIds.join(', ')}`);
      }
    }
    
    // 判断是否有课后题
    const hasHomework = homeworkExercises.length > 0;
    
    // 如果有课后题但没有time参数
    if (hasHomework && (time === undefined || time === null || time === '')) {
      throw new Error('请为课后题设置答题时间');
    }
    
    // 按章节和学习阶段分组习题
    const values = [];
    const batchNumbers = {};
    
    // 对课后题按章节分组
    const homeworkByTitleId = {};
    homeworkExercises.forEach(exercise => {
      const titleId = exercise.title_id;
      if (!homeworkByTitleId[titleId]) {
        homeworkByTitleId[titleId] = [];
      }
      homeworkByTitleId[titleId].push(exercise);
    });
    
    // 处理课后题，计算不同章节的批次号
    for (const titleId of Object.keys(homeworkByTitleId)) {
      const [batchRows] = await connection.query(
        'SELECT MAX(release_batch) as max_batch FROM publish_exercise WHERE class_id = ? AND title_id = ? AND study_phase = 2',
        [classId, titleId]
      );
      
      let releaseBatch = 1;
      if (batchRows[0].max_batch) {
        releaseBatch = batchRows[0].max_batch + 1;
      }
      
      batchNumbers[titleId] = releaseBatch;
      
      // 准备课后题的插入数据
      for (const exercise of homeworkByTitleId[titleId]) {
        values.push([
          courseCode, 
          classId, 
          exercise.exercise_id, 
          exercise.title_id, 
          releaseBatch, 
          deadline, 
          Number(time),
          2 // 课后题
        ]);
      }
    }
    
    // 处理预习题，批次号固定为1
    for (const exercise of exerciseRows.filter(row => row.study_phase === 1)) {
      values.push([
        courseCode, 
        classId, 
        exercise.exercise_id, 
        exercise.title_id, 
        1, // 预习题批次号固定为1
        deadline, 
        null, // 预习题不需要time
        1 // 预习题
      ]);
      
      // 记录预习题章节的批次号
      if (!batchNumbers[exercise.title_id]) {
        batchNumbers[exercise.title_id] = 1;
      }
    }

    // 批量插入发布记录
    await connection.query(
      `INSERT INTO publish_exercise (course_code, class_id, exercise_id, title_id, release_batch, deadline, time, study_phase)
       VALUES ?`,
      [values]
    );

    await connection.commit();

    // 如果只有一个章节，直接返回批次号；否则返回多个章节的批次号
    const batchInfo = Object.keys(batchNumbers).length === 1 
      ? { release_batch: batchNumbers[Object.keys(batchNumbers)[0]] }
      : { release_batches: batchNumbers };

    res.status(201).json({
      success: true,
      message: `成功发布 ${exerciseIds.length} 道习题`,
      data: {
        totalSelected: exerciseIds.length,
        published: exerciseIds.length,
        ...batchInfo
      }
    });
  } catch (error) {
    await connection.rollback();
    console.error('发布失败:', error.message);
    
    res.status(500).json({
      success: false,
      message: error.message || '批量发布习题失败',
      error: {
        code: error.code,
        sqlMessage: error.sqlMessage
      }
    });
  } finally {
    connection.release();
  }
};

// 获取习题详情
export const getExerciseDetail = async (req, res) => {
  const { exerciseId } = req.params;

  try {
    const [exercises] = await pool.query(
      `SELECT 
        exercise_id,
        title,
        content,
        answer,
        analysis,
        difficulty,
        CAST(question_type AS UNSIGNED) as question_type,
        study_phase
       FROM exercise_bank 
       WHERE exercise_id = ?`,
      [exerciseId]
    );

    if (exercises.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到习题'
      });
    }

    res.json({
      success: true,
      data: exercises[0]
    });
  } catch (error) {
    console.error('获取习题详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取习题详情失败'
    });
  }
};

// 获取习题统计信息
export const getExerciseStats = async (req, res) => {
  const { exerciseId } = req.params;

  try {
    // TODO: 实现习题统计信息的查询
    res.json({
      success: true,
      data: {
        completed_count: 0,
        incomplete_count: 0,
        avg_score: 0
      }
    });
  } catch (error) {
    console.error('获取习题统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取习题统计信息失败'
    });
  }
};

// 获取章节下的习题
export const getChapterExercises = async (req, res) => {
  const { titleId } = req.params;
  const { classId } = req.query;
  
  try {
    // 1. 先获取班级对应的课程代码和大纲
    const [classRows] = await pool.query(
      `SELECT cc.course_code, s.content 
       FROM course_classes cc 
       LEFT JOIN syllabus s ON s.course_code = cc.course_code 
       WHERE cc.id = ? 
       ORDER BY s.create_time DESC 
       LIMIT 1`,
      [classId]
    );

    if (classRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }

    const courseCode = classRows[0].course_code;
    const syllabusContent = JSON.parse(classRows[0].content || '{}');

    // 2. 验证章节是否属于当前课程大纲
    let chapterFound = false;
    const validateChapter = (value) => {
      if (typeof value === 'string' && value === titleId) {
        chapterFound = true;
      } else if (typeof value === 'object') {
        Object.values(value).forEach(v => {
          if (v === titleId) chapterFound = true;
        });
      }
    };

    Object.values(syllabusContent).forEach(value => validateChapter(value));

    if (!chapterFound) {
      return res.status(400).json({
        success: false,
        message: '该章节不属于当前课程'
      });
    }

    // 3. 获取符合条件的习题，确保question_type是整数
    const [exercises] = await pool.query(
      `SELECT 
        exercise_id,
        title,
        content,
        CAST(question_type AS UNSIGNED) as question_type,
        difficulty,
        study_phase
       FROM exercise_bank 
       WHERE title_id = ? 
       AND course_code = ?
       ORDER BY create_time DESC`,
      [titleId, courseCode]
    );

    res.json({
      success: true,
      data: exercises
    });
  } catch (error) {
    console.error('获取章节习题失败:', error);
    res.status(500).json({
      success: false,
      message: '获取章节习题失败'
    });
  }
};

// 获取习题发布次数
export const getExercisePublishCount = async (req, res) => {
  const { classId, exerciseId } = req.params;
  
  try {
    // 统计该习题在当前班级中的发布次数
    const [countRows] = await pool.query(
      'SELECT COUNT(*) as count FROM publish_exercise WHERE class_id = ? AND exercise_id = ?',
      [classId, exerciseId]
    );
    
    res.json({
      success: true,
      data: {
        count: countRows[0].count
      }
    });
  } catch (error) {
    console.error('获取习题发布次数失败:', error);
    res.status(500).json({
      success: false,
      message: '获取习题发布次数失败'
    });
  }
};

// 批量获取所有习题的发布次数
export const getAllExercisePublishCounts = async (req, res) => {
  const { classId } = req.params;
  
  try {
    // 统计所有习题在当前班级中的发布次数
    const [countRows] = await pool.query(
      `SELECT exercise_id, COUNT(*) as count 
       FROM publish_exercise 
       WHERE class_id = ? 
       GROUP BY exercise_id`,
      [classId]
    );
    
    // 转换为{exerciseId: count}格式的对象
    const countsMap = {};
    countRows.forEach(row => {
      countsMap[row.exercise_id] = row.count;
    });
    
    res.json({
      success: true,
      data: countsMap
    });
  } catch (error) {
    console.error('批量获取习题发布次数失败:', error);
    res.status(500).json({
      success: false,
      message: '批量获取习题发布次数失败'
    });
  }
};

// 获取下一个批次号
export const getNextBatch = async (req, res) => {
  const { classId } = req.params;
  const { titleId, studyPhase = 2 } = req.query; // 从查询参数获取章节ID和学习阶段，默认为课后题
  
  try {
    // 获取当前班级和章节的最大发布批次
    let query = 'SELECT MAX(release_batch) as max_batch FROM publish_exercise WHERE class_id = ? AND study_phase = ?';
    const params = [classId, studyPhase];
    
    // 如果提供了章节ID，则按章节ID筛选
    if (titleId) {
      query += ' AND title_id = ?';
      params.push(titleId);
    }
    
    const [batchRows] = await pool.query(query, params);
    
    let nextBatch = 1;
    if (batchRows[0].max_batch) {
      nextBatch = batchRows[0].max_batch + 1;
    }
    
    // 预习题永远是批次1
    if (parseInt(studyPhase) === 1) {
      nextBatch = 1;
    }
    
    res.json({
      success: true,
      data: {
        nextBatch: nextBatch
      }
    });
  } catch (error) {
    console.error('获取下一个批次号失败:', error);
    res.status(500).json({
      success: false,
      message: '获取下一个批次号失败'
    });
  }
};

// 获取章节批次列表
export const getChapterBatches = async (req, res) => {
  const { classId } = req.params;
  const { titleId, studyPhase = 2 } = req.query; // 默认获取课后题批次
  
  if (!titleId) {
    return res.status(400).json({
      success: false,
      message: '请指定章节ID'
    });
  }
  
  try {
    // 查询指定章节、班级和学习阶段的所有批次及每个批次的习题数量
    const [batchRows] = await pool.query(
      `SELECT release_batch as batch, COUNT(*) as count
       FROM publish_exercise
       WHERE class_id = ? AND title_id = ? AND study_phase = ?
       GROUP BY release_batch
       ORDER BY release_batch ASC`,
      [classId, titleId, studyPhase]
    );
    
    res.json({
      success: true,
      data: batchRows
    });
  } catch (error) {
    console.error('获取章节批次列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取章节批次列表失败'
    });
  }
};

// 获取指定批次的习题列表
export const getBatchExercises = async (req, res) => {
  const { classId, batchNumber } = req.params;
  const { titleId, studyPhase = 2 } = req.query; // 默认获取课后题
  
  if (!titleId) {
    return res.status(400).json({
      success: false,
      message: '请指定章节ID'
    });
  }
  
  try {
    // 查询指定批次的所有习题，关联习题库表获取详细信息
    const [exercises] = await pool.query(
      `SELECT pe.*, eb.title, eb.content, eb.difficulty, eb.question_type
       FROM publish_exercise pe
       INNER JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
       WHERE pe.class_id = ? 
         AND pe.title_id = ? 
         AND pe.release_batch = ? 
         AND pe.study_phase = ?
       ORDER BY pe.created_at ASC`,
      [classId, titleId, batchNumber, studyPhase]
    );
    
    res.json({
      success: true,
      data: exercises
    });
  } catch (error) {
    console.error('获取批次习题列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取批次习题列表失败'
    });
  }
};

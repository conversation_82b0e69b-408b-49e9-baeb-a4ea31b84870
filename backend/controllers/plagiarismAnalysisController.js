import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

/**
 * 查重分析控制器
 * 用于分析学生作业提交的相似度
 */

// 定义相似度阈值
const SIMILARITY_THRESHOLDS = {
  HIGH: 0.85,
  MEDIUM: 0.65,
  LOW: 0.45
};

// 中文停用词表
const chineseStopwords = new Set([
  '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都',
  '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会',
  '着', '没有', '看', '好', '自己', '这', '但', '那', '他', '还', '对',
  '能', '而', '被', '让', '此', '与', '之', '这些', '及', '等', '把',
  '她', '但是', '这个', '由', '从', '或', '给', '什么', '这样', '其',
  '时', '为', '可以', '使', '向', '并', '中'
]);

// 文本清洗与分词 (不使用 nodejieba，采用简单的字符分割)
function cleanText(text) {
  if (!text) return [];
  
  // 去掉常见的标点符号和空格
  text = text.replace(/[.,，。、？！""《》：；（）\[\]\s]/g, '');
  
  // 简单的中文分词：单字分词
  const words = [];
  for (let i = 0; i < text.length; i++) {
    // 把每个汉字作为一个词
    const char = text.charAt(i);
    if (char.trim() && !chineseStopwords.has(char)) {
      words.push(char);
    }
  }
  
  return words;
}

// 计算词频
function computeWordFrequencies(words) {
  const frequencies = {};
  words.forEach(word => {
    frequencies[word] = (frequencies[word] || 0) + 1;
  });
  return frequencies;
}

// 余弦相似度计算
function cosineSimilarity(text1, text2) {
  const words1 = cleanText(text1);
  const words2 = cleanText(text2);
  
  const freq1 = computeWordFrequencies(words1);
  const freq2 = computeWordFrequencies(words2);
  
  // 合并所有词，建立完整的向量空间
  const uniqueWords = new Set([...Object.keys(freq1), ...Object.keys(freq2)]);
  
  let dotProduct = 0;
  let norm1 = 0;
  let norm2 = 0;
  
  for (const word of uniqueWords) {
    const f1 = freq1[word] || 0;
    const f2 = freq2[word] || 0;
    
    dotProduct += f1 * f2;
    norm1 += f1 * f1;
    norm2 += f2 * f2;
  }
  
  if (norm1 === 0 || norm2 === 0) return 0;
  return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
}

// 根据相似度得分返回相似度等级
function getSimilarityLevel(score) {
  if (score >= SIMILARITY_THRESHOLDS.HIGH) return 'HIGH';
  if (score >= SIMILARITY_THRESHOLDS.MEDIUM) return 'MEDIUM';
  if (score >= SIMILARITY_THRESHOLDS.LOW) return 'LOW';
  return 'NONE';
}

// 获取查重分析数据
const getPlagiarismAnalysis = async (req, res) => {
  const { classId, chapterId, batchId } = req.params;
  const studyPhase = 2; // 固定study_phase为2
  let connection;
  
  try {
    connection = await pool.getConnection();
    
    console.log(`获取查重分析数据: 班级=${classId}, 章节=${chapterId}, 批次=${batchId}, study_phase=${studyPhase}`);
    
    // 1. 获取班级课程代码
    const [classInfo] = await connection.execute(`
      SELECT course_code FROM course_classes WHERE id = ?
    `, [classId]);
    
    if (classInfo.length === 0) {
      console.log(`警告：未找到班级ID=${classId}的信息`);
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }
    
    const courseCode = classInfo[0].course_code;
    console.log(`班级${classId}的课程代码: ${courseCode}`);
    
    // 2. 获取班级学生数量，用于统计
    const [students] = await connection.execute(`
      SELECT s.student_id, s.name
      FROM course_students cs
      JOIN students s ON cs.student_id = s.student_id
      WHERE cs.course_code = ?
    `, [courseCode]);
    
    console.log(`班级学生数量: ${students.length}`);
    
    // 3. 查询该班级、章节和批次下的简答题数量，用于统计
    const [essayQuestions] = await connection.execute(`
      SELECT e.exercise_id, e.question_type, e.content, e.title, p.id as publish_id
      FROM exercise_bank e
      JOIN publish_exercise p ON e.exercise_id = p.exercise_id
      WHERE p.class_id = ? 
      AND p.study_phase = ? 
      AND p.release_batch = ?
      AND p.title_id = ? -- 添加章节ID过滤
      AND e.question_type = 4 -- 简答题类型为4
    `, [classId, studyPhase, batchId, chapterId]);
    
    console.log(`查询到的简答题数量: ${essayQuestions.length}`);

    // 4. 查询发布的题目IDs用于进一步过滤
    if (essayQuestions.length === 0) {
      return res.json({
        success: true,
        data: {
          statsData: {
            totalQuestions: 0,
            totalStudents: students.length,
            highSimilarityCount: 0
          },
          similarities: []
        }
      });
    }
    
    const publishIds = essayQuestions.map(q => q.publish_id);
    console.log(`发布的题目IDs: ${publishIds.join(',')}`);

    // 5. 直接查询similarity表中符合条件的记录
    const [similarities] = await connection.execute(`
      SELECT 
        sim.id as similarity_id,
        sim.answer1_id, 
        sim.answer2_id, 
        sim.similarity_score, 
        sim.created_at,
        a1.student_id as student1_id,
        s1.name as student1_name,
        a1.student_answer as student1_answer,
        a1.publish_exercise_id,
        a2.student_id as student2_id,
        s2.name as student2_name,
        a2.student_answer as student2_answer,
        e.exercise_id,
        e.content as exercise_content,
        e.question_type,
        p.title_id
      FROM similarity sim
      JOIN student_exercise_records a1 ON sim.answer1_id = a1.id
      JOIN student_exercise_records a2 ON sim.answer2_id = a2.id
      JOIN students s1 ON a1.student_id = s1.student_id
      JOIN students s2 ON a2.student_id = s2.student_id
      JOIN publish_exercise p ON a1.publish_exercise_id = p.id
      JOIN exercise_bank e ON p.exercise_id = e.exercise_id
      WHERE sim.class_id = ? 
      AND sim.title_id = ? 
      AND sim.release_batch = ?
      AND p.title_id = ? -- 确保题目真的属于选定的章节
      ORDER BY sim.similarity_score DESC
    `, [classId, chapterId, batchId, chapterId]);
    
    console.log(`查询到的相似度记录数量: ${similarities.length}`);
    
    // 将结果格式化为前端需要的格式
    const allSimilarities = similarities.map(sim => ({
      exercise_id: sim.exercise_id,
      question_type_name: '简答题',
      student1_id: sim.student1_id,
      student1_name: sim.student1_name,
      student1_answer: sim.student1_answer,
      student2_id: sim.student2_id,
      student2_name: sim.student2_name,
      student2_answer: sim.student2_answer,
      similarity_score: sim.similarity_score,
      created_at: sim.created_at,
      exercise_content: sim.exercise_content
    }));
    
    // 统计高相似度数量
    const highSimilarityCount = allSimilarities.filter(
      sim => sim.similarity_score >= SIMILARITY_THRESHOLDS.HIGH
    ).length;
    
    // 计算涉及到的学生数量
    const involvedStudentIds = new Set();
    allSimilarities.forEach(sim => {
      involvedStudentIds.add(sim.student1_id);
      involvedStudentIds.add(sim.student2_id);
    });
    
    const involvedStudentCount = involvedStudentIds.size;
    console.log(`查重涉及的学生数量: ${involvedStudentCount}`);
    
    // 返回统计数据和查重结果
    return res.json({
      success: true,
      data: {
        statsData: {
          totalQuestions: essayQuestions.length,
          totalStudents: involvedStudentCount > 0 ? involvedStudentCount : students.length,
          highSimilarityCount: highSimilarityCount
        },
        similarities: allSimilarities
      }
    });
  } catch (error) {
    console.error('获取查重分析数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取查重分析数据失败',
      error: error.message
    });
  } finally {
    if (connection) {
      connection.release();
    }
  }
};

// 手动触发查重分析
const runPlagiarismCheck = async (req, res) => {
  const { classId, chapterId, batchId } = req.params;
  const studyPhase = 2; // 固定study_phase为2
  let connection;
  
  try {
    connection = await pool.getConnection();
    
    console.log(`开始查重分析: 班级=${classId}, 章节=${chapterId}, 批次=${batchId}, study_phase=${studyPhase}`);
    
    // 先获取该批次下的所有简答题
    const [essayQuestions] = await connection.execute(`
      SELECT e.exercise_id, e.question_type, e.content, e.title, p.id as publish_id, p.title_id
      FROM exercise_bank e
      JOIN publish_exercise p ON e.exercise_id = p.exercise_id
      WHERE p.class_id = ? 
      AND p.study_phase = ? 
      AND p.release_batch = ?
      AND p.title_id = ? -- 添加章节ID过滤
      AND e.question_type = 4 -- 简答题类型为4
    `, [classId, studyPhase, batchId, chapterId]);
    
    console.log(`查询到的简答题数量: ${essayQuestions.length}`);
    if(essayQuestions.length > 0) {
      console.log(`第一道题: exercise_id=${essayQuestions[0].exercise_id}, question_type=${essayQuestions[0].question_type}, publish_id=${essayQuestions[0].publish_id}`);
    }
    
    if (essayQuestions.length === 0) {
      // 调试: 查看是否有任何题目
      const [allQuestions] = await connection.execute(`
        SELECT e.exercise_id, e.question_type, p.id as publish_id
        FROM exercise_bank e
        JOIN publish_exercise p ON e.exercise_id = p.exercise_id
        WHERE p.class_id = ? 
        AND p.study_phase = ? 
        AND p.release_batch = ?
      `, [classId, studyPhase, batchId]);
      
      console.log(`总题目数量: ${allQuestions.length}`);
      if(allQuestions.length > 0) {
        console.log(`题目类型统计:`);
        const typeCounts = {};
        allQuestions.forEach(q => {
          typeCounts[q.question_type] = (typeCounts[q.question_type] || 0) + 1;
        });
        console.log(typeCounts);
      }
      
      return res.json({
        success: true,
        message: '没有找到需要检查的简答题',
        data: {
          checkedCount: 0,
          foundSimilarities: 0
        }
      });
    }
    
    // 检查班级学生数量
    const [classInfo] = await connection.execute(`
      SELECT course_code FROM course_classes WHERE id = ?
    `, [classId]);
    
    if (classInfo.length === 0) {
      console.log(`警告：未找到班级ID=${classId}的信息`);
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }
    
    const courseCode = classInfo[0].course_code;
    console.log(`班级${classId}的课程代码: ${courseCode}`);
    
    // 修改为使用课程代码查询学生
    const [students] = await connection.execute(`
      SELECT s.student_id, s.name
      FROM course_students cs
      JOIN students s ON cs.student_id = s.student_id
      WHERE cs.course_code = ?
    `, [courseCode]);
    
    console.log(`班级学生数量: ${students.length}`);
    if (students.length === 0) {
      console.log(`警告：班级 ${classId} (课程代码 ${courseCode}) 没有找到任何学生`);
    }
    
    let checkedCount = 0;
    let foundSimilarities = 0;
    let allSimilarityResults = [];
    
    // 处理每道简答题
    for (const question of essayQuestions) {
      console.log(`正在检查题目 publish_id=${question.publish_id}`);
      
      // 获取该题的所有学生回答，按创建时间排序
      const [answers] = await connection.execute(`
        SELECT 
          ser.id as answer_id, 
          ser.student_id, 
          s.name as student_name,
          ser.student_answer, 
          ser.create_time,
          p.title_id -- 获取真实的title_id
        FROM student_exercise_records ser
        JOIN students s ON ser.student_id = s.student_id
        JOIN publish_exercise p ON ser.publish_exercise_id = p.id
        WHERE ser.publish_exercise_id = ?
        ORDER BY ser.create_time ASC
      `, [question.publish_id]);
      
      console.log(`题目 publish_id=${question.publish_id} 的回答数量: ${answers.length}`);
      
      // 如果答案数量少于2，不需要做查重
      if (answers.length < 2) {
        console.log(`答案数量不足，跳过查重`);
        continue;
      }
      
      checkedCount += answers.length;
      
      // 存储之前已经处理过的答案
      const previousAnswers = [];
      
      // 按照时间顺序，对每个学生的答案与之前提交的答案进行比较
      for (let i = 0; i < answers.length; i++) {
        const currentAnswer = answers[i];
        
        console.log(`检查学生 ${currentAnswer.student_id} 的答案 (ID=${currentAnswer.answer_id})`);
        
        // 如果是第一个答案，没有之前的答案可比较
        if (i === 0) {
          previousAnswers.push(currentAnswer);
          console.log(`这是第一个答案，跳过比较`);
          continue;
        }
        
        let maxSimilarity = 0;
        let mostSimilarAnswer = null;
        
        // 与之前的所有回答比较
        for (const prevAnswer of previousAnswers) {
          // 跳过空答案比较
          if (!currentAnswer.student_answer || !prevAnswer.student_answer) {
            console.log(`跳过空答案比较`);
            continue;
          }
          
          const similarity = cosineSimilarity(currentAnswer.student_answer, prevAnswer.student_answer);
          
          console.log(`与学生 ${prevAnswer.student_id} 的答案 (ID=${prevAnswer.answer_id}) 相似度: ${similarity}`);
          
          // 只记录高于阈值的情况
          if (similarity >= SIMILARITY_THRESHOLDS.HIGH && similarity > maxSimilarity) {
            maxSimilarity = similarity;
            mostSimilarAnswer = prevAnswer;
          }
        }
        
        // 如果相似度超过高阈值，记录到数据库
        if (mostSimilarAnswer && maxSimilarity >= SIMILARITY_THRESHOLDS.HIGH) {
          foundSimilarities++;
          
          console.log(`发现高度相似: ${mostSimilarAnswer.answer_id} 和 ${currentAnswer.answer_id}, 相似度: ${maxSimilarity}`);
          
          // 记录到similarity表，使用真实的title_id确保数据一致性
          await connection.execute(`
            REPLACE INTO similarity 
              (answer1_id, answer2_id, similarity_score, created_at, class_id, title_id, release_batch)
            VALUES 
              (?, ?, ?, NOW(), ?, ?, ?)
          `, [
            mostSimilarAnswer.answer_id, 
            currentAnswer.answer_id, 
            maxSimilarity,
            classId,  // 班级ID
            question.title_id, // 使用真实的title_id而不是chapterId
            batchId   // 批次ID
          ]);
          
          // 将结果添加到列表
          allSimilarityResults.push({
            exercise_id: question.exercise_id,
            question_type_name: '简答题',
            student1_id: mostSimilarAnswer.student_id,
            student1_name: mostSimilarAnswer.student_name,
            student1_answer: mostSimilarAnswer.student_answer,
            student2_id: currentAnswer.student_id,
            student2_name: currentAnswer.student_name,
            student2_answer: currentAnswer.student_answer,
            similarity_score: maxSimilarity,
            created_at: new Date(),
            exercise_content: question.content
          });
        }
        
        // 将当前答案添加到已处理列表中
        previousAnswers.push(currentAnswer);
      }
    }
    
    // 获取最新的查重结果以供显示
    return res.json({
      success: true,
      message: `查重完成，检查了${checkedCount}条记录，发现${foundSimilarities}个高度相似的答案`,
      data: {
        checkedCount,
        foundSimilarities,
        statsData: {
          totalQuestions: essayQuestions.length,
          totalStudents: checkedCount,
          highSimilarityCount: foundSimilarities
        },
        similarities: allSimilarityResults
      }
    });
    
  } catch (error) {
    console.error('运行查重分析失败:', error);
    return res.status(500).json({
      success: false,
      message: '运行查重分析失败',
      error: error.message
    });
  } finally {
    if (connection) {
      connection.release();
    }
  }
};

export default {
  getPlagiarismAnalysis,
  runPlagiarismCheck
}; 
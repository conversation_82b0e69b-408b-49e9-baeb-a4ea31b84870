import pool from '../config/db.js';

/**
 * 获取班级预习资料统计数据
 * 包括学生总数、已完成学生数、完成率，以及各个预习任务的完成情况
 */
export const getPreviewStats = async (req, res) => {
  try {
    const { classId } = req.params;
    
    if (!classId) {
      return res.status(400).json({
        success: false,
        message: '缺少班级ID参数'
      });
    }
    
    // 1. 获取班级信息和课程代码
    const [classInfo] = await pool.query(
      `SELECT cc.id, cc.class_name, cc.course_code, c.course_name 
       FROM course_classes cc
       JOIN courses c ON cc.course_code = c.course_code 
       WHERE cc.id = ?`,
      [classId]
    );
    
    if (classInfo.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到班级信息'
      });
    }
    
    const courseCode = classInfo[0].course_code;
    
    // 2. 获取班级学生总数（通过student_course_registration表）
    const studentCountQuery = `
      SELECT COUNT(DISTINCT scr.student_id) as total_students 
      FROM student_course_registration scr 
      WHERE scr.class_id = ? AND scr.status = 1`;
    
    const [studentCountResult] = await pool.query(studentCountQuery, [classId]);
    
    const totalStudents = studentCountResult[0]?.total_students || 0;
    
    // 3. 获取班级已发布的预习任务列表
    const previewListQuery = `
      SELECT 
         pp.id, 
         pp.title, 
         pp.deadline, 
         pp.create_time,
         pm.chapter_title
       FROM preview_publish pp 
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       WHERE pp.class_id = ? 
       ORDER BY pp.deadline DESC`;
    
    const [previewList] = await pool.query(previewListQuery, [classId]);
    
    // 4. 获取每个预习任务的完成情况
    const formattedPreviewList = [];
    let totalCompletedCount = 0;
    
    for (const preview of previewList) {
      const [submissionCount] = await pool.query(
        `SELECT COUNT(DISTINCT ps.student_id) as completed_count 
         FROM preview_student ps 
         WHERE ps.preview_publish_id = ? AND ps.status = 1`,
        [preview.id]
      );
      
      const completedCount = submissionCount[0]?.completed_count || 0;
      const completion = totalStudents > 0 ? Math.round((completedCount / totalStudents) * 100) : 0;
      
      formattedPreviewList.push({
        id: preview.id,
        title: preview.title,
        chapter: preview.chapter_title || '未知章节',
        deadline: preview.deadline,
        publishTime: preview.create_time,
        completedCount,
        totalStudents,
        completion
      });
      
      // 累计已完成的学生数（使用最近一个预习任务的数据）
      if (formattedPreviewList.length === 1) {
        totalCompletedCount = completedCount;
      }
    }
    
    // 5. 构建统计数据
    const stats = {
      classId,
      className: classInfo[0].class_name,
      courseName: classInfo[0].course_name,
      totalStudents,
      completedStudents: totalCompletedCount,
      completionRate: totalStudents > 0 ? Math.round((totalCompletedCount / totalStudents) * 100) : 0,
      previewCount: previewList.length
    };
    
    const responseData = {
      success: true,
      data: {
        stats,
        previewList: formattedPreviewList
      }
    };
    
    res.json(responseData);
    
  } catch (error) {
    console.error('获取班级预习统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取班级预习统计数据失败: ' + error.message
    });
  }
};

/**
 * 获取单个预习任务的完成详情
 * 包括已完成和未完成的学生列表
 */
export const getPreviewDetail = async (req, res) => {
  try {
    const { previewId } = req.params;
    
    if (!previewId) {
      return res.status(400).json({
        success: false,
        message: '缺少预习任务ID参数'
      });
    }
    
    // 1. 获取预习任务信息
    const [preview] = await pool.query(
      `SELECT pp.*, pm.chapter_title, cc.class_name, c.course_name  
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       JOIN course_classes cc ON pp.class_id = cc.id
       JOIN courses c ON cc.course_code = c.course_code
       WHERE pp.id = ?`,
      [previewId]
    );
    
    if (preview.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到预习任务'
      });
    }
    
    const classId = preview[0].class_id;
    
    // 2. 获取班级所有学生 (通过student_course_registration表)
    const [students] = await pool.query(
      `SELECT s.student_id, s.name
       FROM student_course_registration scr
       JOIN students s ON scr.student_id = s.student_id
       WHERE scr.class_id = ? AND scr.status = 1`,
      [classId]
    );
    
    // 3. 获取已完成的学生
    const [completedStudents] = await pool.query(
      `SELECT ps.student_id, ps.submit_time, s.name
       FROM preview_student ps
       JOIN students s ON ps.student_id = s.student_id
       WHERE ps.preview_publish_id = ? AND ps.status = 1`,
      [previewId]
    );
    
    const completedStudentIds = new Set(completedStudents.map(s => s.student_id));
    
    // 4. 分类处理学生列表
    const completedList = completedStudents.map(student => ({
      studentId: student.student_id,
      name: student.name,
      submitTime: student.submit_time,
      status: 1
    }));
    
    const pendingList = students
      .filter(student => !completedStudentIds.has(student.student_id))
      .map(student => ({
        studentId: student.student_id,
        name: student.name,
        submitTime: null,
        status: 0
      }));
      
    const stats = {
      totalStudents: students.length,
      completedCount: completedList.length,
      pendingCount: pendingList.length,
      completionRate: students.length > 0 ? Math.round((completedList.length / students.length) * 100) : 0
    };
    
    res.json({
      success: true,
      data: {
        previewInfo: {
          ...preview[0],
          chapterTitle: preview[0].chapter_title
        },
        stats,
        students: {
          completed: completedList,
          pending: pendingList
        }
      }
    });
    
  } catch (error) {
    console.error('获取预习任务完成详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习任务完成详情失败: ' + error.message
    });
  }
};

/**
 * 获取学生预习完成趋势数据（按时间统计）
 */
export const getPreviewTrend = async (req, res) => {
  try {
    const { classId } = req.params;
    const { days = 7 } = req.query;
    
    if (!classId) {
      return res.status(400).json({
        success: false,
        message: '缺少班级ID参数'
      });
    }
    
    // 获取指定天数内的预习完成情况
    const [trendData] = await pool.query(
      `SELECT 
        DATE(ps.submit_time) as date,
        COUNT(DISTINCT ps.student_id) as completed_count
       FROM preview_student ps
       JOIN preview_publish pp ON ps.preview_publish_id = pp.id
       WHERE pp.class_id = ? AND ps.status = 1
         AND ps.submit_time >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
       GROUP BY DATE(ps.submit_time)
       ORDER BY DATE(ps.submit_time)`,
      [classId, days]
    );
    
    res.json({
      success: true,
      data: trendData
    });
    
  } catch (error) {
    console.error('获取预习完成趋势失败:', error);
    res.status(500).json({
      success: false,
      message: '获取预习完成趋势失败: ' + error.message
    });
  }
};

export default {
  getPreviewStats,
  getPreviewDetail,
  getPreviewTrend
};

import pool from '../config/db.js';

// 获取班级列表
export const getClasses = async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT cc.*, c.course_name, 
             cc.current_students as actual_student_count
      FROM course_classes cc
      LEFT JOIN courses c ON cc.course_code = c.course_code
      WHERE cc.status = 1
      ORDER BY cc.semester DESC, cc.class_name
    `);
    
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取班级列表失败:', error);
    res.status(500).json({ success: false, message: '获取班级列表失败' });
  }
};

// 获取教师的班级列表
export const getTeacherClasses = async (req, res) => {
  const teacherId = req.params.teacherId;
  
  try {
    // 获取教师的所有班级
    const [rows] = await pool.query(`
      SELECT cc.*, c.course_name,
             cc.current_students as student_count
      FROM course_classes cc
      JOIN courses c ON cc.course_code = c.course_code
      WHERE cc.teacher_id = ? AND cc.status = 1
      ORDER BY cc.semester DESC, cc.class_name
    `, [teacherId]);
    
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取教师班级失败:', error);
    res.status(500).json({ success: false, message: '获取教师班级失败' });
  }
};

// 获取班级详情
export const getClassDetails = async (req, res) => {
  const classId = req.params.id;
  
  try {
    const [rows] = await pool.query(`
      SELECT cc.*, c.course_name,
             cc.current_students as student_count
      FROM course_classes cc
      JOIN courses c ON cc.course_code = c.course_code
      WHERE cc.id = ?
    `, [classId]);

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '班级不存在'
      });
    }

    res.json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    console.error('获取班级详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取班级详情失败'
    });
  }
};

// 生成推荐的班级名称
export const generateClassName = async (req, res) => {
  const { courseCode, semester } = req.query;

  try {
    // 获取课程信息
    const [courseRows] = await pool.query(
      'SELECT course_name FROM courses WHERE course_code = ?',
      [courseCode]
    );

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到课程信息'
      });
    }

    const courseName = courseRows[0].course_name;
    
    // 查找当前学期该课程的所有班级，分析班级名称中的编号
    const [existingClasses] = await pool.query(
      'SELECT class_name FROM course_classes WHERE course_code = ? AND semester = ?',
      [courseCode, semester]
    );
    
    // 如果没有现有班级，返回"1班"
    if (existingClasses.length === 0) {
      const suggestedName = `${courseName} 1班`;
      return res.json({
        success: true,
        data: {
          suggestedName,
          nextClassNumber: 1
        }
      });
    }
    
    // 分析现有班级名称，找出最大编号
    let maxClassNumber = 0;
    
    existingClasses.forEach(classObj => {
      // 从班级名称中提取数字
      const match = classObj.class_name.match(/(\d+)班/);
      if (match && match[1]) {
        const classNumber = parseInt(match[1], 10);
        if (!isNaN(classNumber) && classNumber > maxClassNumber) {
          maxClassNumber = classNumber;
        }
      }
    });
    
    // 生成下一个班级编号
    const nextClassNumber = maxClassNumber + 1;
    const suggestedName = `${courseName} ${nextClassNumber}班`;

    res.json({
      success: true,
      data: {
        suggestedName,
        nextClassNumber
      }
    });
  } catch (error) {
    console.error('生成班级名称失败:', error);
    res.status(500).json({
      success: false,
      message: '生成班级名称失败'
    });
  }
};

// 创建班级
export const createClass = async (req, res) => {
  const { courseCode, className, teacherId, semester, maxStudents = 50 } = req.body;

  if (!courseCode || !className || !teacherId || !semester) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数'
    });
  }

  try {
    // 检查课程是否存在
    const [courseExists] = await pool.query(
      'SELECT course_code FROM courses WHERE course_code = ?',
      [courseCode]
    );

    if (courseExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    const [result] = await pool.query(`
      INSERT INTO course_classes (
        course_code, class_name, teacher_id, semester, 
        max_students, status, current_students
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [courseCode, className, teacherId, semester, maxStudents, 1, 0]);

    res.status(201).json({
      success: true,
      message: '班级创建成功',
      data: {
        id: result.insertId,
        courseCode,
        className,
        teacherId,
        semester,
        maxStudents,
        status: 1,
        currentStudents: 0
      }
    });
  } catch (error) {
    console.error('创建班级失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '创建班级失败'
    });
  }
};

// 更新班级
export const updateClass = async (req, res) => {
  const classId = req.params.id;
  const { course_code, class_name, semester, max_students, status } = req.body;

  try {
    // 检查班级是否存在
    const [existingClass] = await pool.query(
      'SELECT * FROM course_classes WHERE id = ?',
      [classId]
    );

    if (existingClass.length === 0) {
      return res.status(404).json({
        success: false,
        message: '班级不存在'
      });
    }

    // 更新班级信息
    await pool.query(`
      UPDATE course_classes 
      SET course_code = ?, 
          class_name = ?, 
          semester = ?, 
          max_students = ?, 
          status = ?
      WHERE id = ?
    `, [course_code, class_name, semester, max_students, status, classId]);

    res.json({
      success: true,
      message: '班级更新成功'
    });
  } catch (error) {
    console.error('更新班级失败:', error);
    res.status(500).json({
      success: false,
      message: '更新班级失败'
    });
  }
};

// 删除班级
export const deleteClass = async (req, res) => {
  const classId = req.params.id;

  try {
    // 检查班级是否存在
    const [existingClass] = await pool.query(
      'SELECT * FROM course_classes WHERE id = ?',
      [classId]
    );

    if (existingClass.length === 0) {
      return res.status(404).json({
        success: false,
        message: '班级不存在'
      });
    }

    // 删除班级
    await pool.query('DELETE FROM course_classes WHERE id = ?', [classId]);

    res.json({
      success: true,
      message: '班级删除成功'
    });
  } catch (error) {
    console.error('删除班级失败:', error);
    res.status(500).json({
      success: false,
      message: '删除班级失败'
    });
  }
};

// 获取班级学生列表
export const getClassStudents = async (req, res) => {
  const { classId } = req.params;
  const { page = 1, pageSize = 10, search = '' } = req.query;
  const offset = (page - 1) * pageSize;
  
  let retryCount = 0;
  const maxRetries = 3;

  const tryQuery = async () => {
    try {
      // 先获取班级信息
      const [classInfo] = await pool.query(
        'SELECT course_code FROM course_classes WHERE id = ?',
        [classId]
      );

      if (classInfo.length === 0) {
        return res.status(404).json({
          success: false,
          message: '未找到班级信息'
        });
      }

      // 获取总数
      const [countResult] = await pool.query(`
        SELECT COUNT(*) as total
        FROM student_course_registration scr
        JOIN students s ON s.student_id = scr.student_id
        WHERE scr.class_id = ?
          AND scr.status = 1
          AND (s.student_id LIKE ? OR s.name LIKE ?)
      `, [classId, `%${search}%`, `%${search}%`]);

      // 获取学生列表
      const [rows] = await pool.query(`
        SELECT 
          s.student_id,
          s.name,
          s.phone,
          s.email,
          s.avatar,
          scr.join_time,
          scr.status
        FROM student_course_registration scr
        JOIN students s ON s.student_id = scr.student_id
        WHERE scr.class_id = ?
          AND scr.status = 1
          AND (s.student_id LIKE ? OR s.name LIKE ?)
        ORDER BY scr.join_time DESC
        LIMIT ? OFFSET ?
      `, [classId, `%${search}%`, `%${search}%`, Number(pageSize), offset]);

      return res.json({
        success: true,
        data: {
          list: rows,
          total: countResult[0].total,
          page: Number(page),
          pageSize: Number(pageSize)
        }
      });
    } catch (error) {
      console.error('查询失败:', error);
      
      // 如果是连接错误且未超过重试次数，则重试
      if (error.code === 'ECONNRESET' && retryCount < maxRetries) {
        retryCount++;
        console.log(`第 ${retryCount} 次重试...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // 延迟重试
        return tryQuery();
      }
      
      throw error;
    }
  };

  try {
    await tryQuery();
  } catch (error) {
    console.error('获取班级学生列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取班级学生列表失败'
    });
  }
};

// 从班级移除学生
export const removeStudentFromClass = async (req, res) => {
  const { classId, studentId } = req.params;
  
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();

    // 获取班级信息
    const [classRows] = await connection.query(
      'SELECT course_code, semester FROM course_classes WHERE id = ?',
      [classId]
    );

    if (classRows.length === 0) {
      throw new Error('班级不存在');
    }

    const { course_code, semester } = classRows[0];

    // 更新选课状态
    await connection.query(`
      UPDATE student_course_registration 
      SET status = 0 
      WHERE student_id = ? 
        AND course_code = ? 
        AND semester = ?
        AND status = 1
    `, [studentId, course_code, semester]);

    // 更新班级人数
    await connection.query(`
      UPDATE course_classes 
      SET current_students = current_students - 1 
      WHERE id = ? 
        AND current_students > 0
    `, [classId]);

    await connection.commit();

    res.json({
      success: true,
      message: '学生已从班级移除'
    });

  } catch (error) {
    await connection.rollback();
    console.error('移除学生失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '移除学生失败'
    });
  } finally {
    connection.release();
  }
};
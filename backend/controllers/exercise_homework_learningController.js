import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';
import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import 'dotenv/config';
import { createOpenAIConfig, API_USAGES } from '../config/modelService.js';

// 确保加载dotenv配置，提前显式引入
import * as dotenv from 'dotenv';
// 尝试加载本地的.env文件
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({ path: path.resolve(process.cwd(), 'backend', '.env') });

// 配置OpenAI客户端(异步)
let openai;
(async () => {
  try {
    const config = await createOpenAIConfig(API_USAGES.CHAT);
    openai = new OpenAI(config);
    console.log("OpenAI API配置成功完成");
  } catch (error) {
    console.error("OpenAI API配置失败:", error);
  }
})();

// AI评分函数 - 仅在学生提交后调用
async function gradeWithAI(question, studentAnswer, questionType) {
  try {
    // 确保AI客户端已初始化
    if (!openai) {
      console.log('AI客户端尚未初始化，正在尝试初始化...');
      const config = await createOpenAIConfig(API_USAGES.CHAT);
      openai = new OpenAI(config);
      console.log('成功初始化AI客户端');
    }
    
    let prompt = '';
    let isImageAnswer = false;
    
    // 检查是否是图片路径
    if (studentAnswer && typeof studentAnswer === 'string' && studentAnswer.startsWith('/uploads/')) {
      isImageAnswer = true;
    }
    
    if (questionType === 4) { // 简答题
      prompt = `
请评分以下学生的简答题回答，并以JSON格式返回结果。

问题: ${question}

学生答案: ${studentAnswer}

请根据以下标准进行评分：
- 答案的完整性和准确性（4分）
- 概念理解的深度（3分）
- 表达的清晰度和逻辑性（3分）

总分10分。

请以下面的JSON格式返回评分结果：
{
  "score": [分数，数值类型，0-10之间],
  "evaluation": [详细的评分理由]
}

你必须严格按照上述JSON格式返回，不要有任何额外文字。对于质量很差或完全无关的答案，请给予0分并说明原因。
`;

      // 文本评分
      const response = await openai.chat.completions.create({
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        model: 'ep-20250310134707-kcdj5',
        response_format: { type: "json_object" }
      });
      
      try {
        // 解析JSON响应
        const result = JSON.parse(response.choices[0].message.content);
        
        // 不设置默认分数，直接使用API返回的值
        return {
          score: result.score !== undefined ? result.score : 0,
          evaluation: result.evaluation || "无评分理由"
        };
      } catch (parseError) {
        console.error('解析JSON响应失败:', parseError);
        // 解析失败给0分
        return {
          score: 0,
          evaluation: "解析评分结果失败，可能是系统错误"
        };
      }
      
    } else if (questionType === 5) { // 计算题
      // 对于计算题，如果没有图片就返回错误
      if (!isImageAnswer) {
        return {
          score: 5, // 默认给5分
          evaluation: "未提供计算过程图片，无法进行详细评分。请上传清晰的计算过程图片获取更准确的评分。"
        };
      }

      // 获取图片路径 - 简化路径处理逻辑
      const uploadDir = path.join(process.cwd(), 'uploads', 'student_exercise');
      const fileName = path.basename(studentAnswer);
      let imagePath = path.join(uploadDir, fileName);
      
      console.log('计算题图片评分 - 图片路径:', imagePath);
      
      // 简化图片检查 - 如果主路径不存在，尝试备用路径
      if (!fs.existsSync(imagePath)) {
        console.log('主路径图片不存在，尝试备用路径');
        imagePath = path.join(process.cwd(), studentAnswer);
      }
      
      // 读取图片 - 如果不存在则返回默认评分
      let imageData, base64Image, imageUrl;
      try {
        imageData = fs.readFileSync(imagePath);
        base64Image = imageData.toString('base64');
        
        // 检查图片头部字节以验证格式
        const isValidImage = checkImageFormat(imageData);
        if (!isValidImage) {
          console.error('图片格式无效或不支持');
          return {
            score: 0,
            evaluation: "上传的图片格式无效或不受支持。请确保上传的是标准的JPG或PNG图片。"
          };
        }
        
        // 设置MIME类型
        const fileExtension = path.extname(imagePath).toLowerCase();
        const mimeType = fileExtension === '.png' ? 'image/png' : 'image/jpeg';
        imageUrl = `data:${mimeType};base64,${base64Image}`;
      } catch (error) {
        console.error('读取图片失败:', error);
        return {
          score: 0,
          evaluation: "系统无法读取计算题图片。请确保上传了有效的图片文件，或联系教师获取更准确的评分。"
        };
      }
      
      // 构建评分提示 - 要求返回结构化JSON
      prompt = `
请评分以下学生的计算题解答，并以JSON格式返回结果。

问题: ${question}

请查看学生提供的计算过程图片。
警告：无论图片内容是什么（即使是不相关内容如猫、风景等），你必须以指定的JSON格式返回。
如果图片内容与计算过程无关，请给0分并在评价中说明原因。

请根据以下标准进行评分：
- 基本思路和解题方法是否正确（3分）
- 公式使用是否准确（3分）
- 计算过程是否有错误（2分）
- 最终结果是否正确（2分）

总分10分。

请以下面的JSON格式返回评分结果：
{
  "score": [分数，数值类型，0-10之间],
  "evaluation": [详细的评分理由]
}

你必须严格按照上述JSON格式返回，不要有任何额外文字，不管图片是什么内容都必须遵守这个要求。
`;
      
      try {
        // 使用多模态API调用，传递图片
        const response = await openai.chat.completions.create({
          messages: [
            {
              role: 'user',
              content: [
                { type: 'text', text: prompt },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageUrl
                  }
                }
              ]
            }
          ],
          model: 'ep-20250310134707-kcdj5',
          response_format: { type: "json_object" }
        });
        
        const responseText = response.choices[0].message.content;
        console.log('计算题图片评分结果:', responseText.substring(0, 100) + '...');
        
        try {
          // 解析JSON响应
          const result = JSON.parse(responseText);
          
          // 不设置默认分数，直接使用API返回的值
          return {
            score: result.score !== undefined ? result.score : 0,
            evaluation: result.evaluation || "无评分理由"
          };
        } catch (parseError) {
          console.error('解析JSON响应失败:', parseError);
          // 解析失败给0分
          return {
            score: 0,
            evaluation: "解析评分结果失败，可能是系统错误"
          };
        }
      } catch (error) {
        // 提供更详细的错误报告
        console.error('API调用失败:', error);
        
        let errorMessage = "评分系统无法处理您的请求。请确保上传了包含计算过程的清晰图片。";
        
        // 针对不同类型的错误提供具体的提示
        if (error.error && error.error.code) {
          if (error.error.code === 'InvalidParameter.UnsupportedImageFormat') {
            errorMessage = "图片格式不受支持。请确保上传标准的JPG或PNG格式图片。";
          } else if (error.error.code === 'InvalidParameter.InvalidImageDimension') {
            errorMessage = "图片尺寸不符合要求。请确保图片尺寸适中，不要太大或太小。";
          } else if (error.error.code === 'InvalidParameter.ImageTooLarge') {
            errorMessage = "图片文件过大。请压缩图片或使用较小的图片。";
          } else if (error.error.code.includes('RateLimitExceeded')) {
            errorMessage = "系统当前请求过多。请稍后再试。";
          }
        }
        
        return {
          score: 0, // 错误情况给0分，不设默认分数
          evaluation: errorMessage
        };
      }
    }
    
    // 默认情况，应该不会走到这里
    return {
      score: 5,
      evaluation: "无法确定题型，默认给予5分。"
    };
  } catch (error) {
    console.error('AI评分过程中出错:', error);
    return {
      score: 5, // 出错时默认给5分
      evaluation: '系统评分错误，请联系老师手动评分'
    };
  }
}

// 在文件顶部添加这个函数
function checkImageFormat(buffer) {
  if (buffer.length < 8) return false;
  
  // 检查PNG格式 (89 50 4E 47 0D 0A 1A 0A)
  if (buffer[0] === 0x89 && 
      buffer[1] === 0x50 && 
      buffer[2] === 0x4E && 
      buffer[3] === 0x47 && 
      buffer[4] === 0x0D && 
      buffer[5] === 0x0A && 
      buffer[6] === 0x1A && 
      buffer[7] === 0x0A) {
    return true;
  }
  
  // 检查JPEG格式 (FF D8)
  if (buffer[0] === 0xFF && buffer[1] === 0xD8) {
    return true;
  }
  
  return false;
}

// 获取作业题目列表
export const getExercises = async (req, res) => {
  try {
    const { classId, batchId } = req.params;
    const { chapterId } = req.query;
    const studentId = req.user?.student_id || req.user?.id;
    
    if (!classId || !batchId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    const conn = await mysql.createConnection(dbConfig);
    const queryChapterId = chapterId ? parseInt(chapterId, 10) : 0;
    
    let sql = `SELECT 
        pe.id as publish_id,
        eb.exercise_id,
        eb.content,
        eb.question_type,
        eb.option,
        eb.title_id,
        eb.difficulty,
        eb.title,
        ser.student_answer,
        ser.is_correct,
        ser.score,
        ser.evaluation,
        pe.time as time_limit
      FROM publish_exercise pe
      JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
      LEFT JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
      WHERE pe.class_id = ? AND pe.release_batch = ? AND pe.study_phase = 2`;
    
    sql += ` AND eb.title_id = ?`;
    const params = [studentId.toString(), classId, batchId, queryChapterId];
    sql += ` ORDER BY pe.id ASC`;
    
    const [exercises] = await conn.query(sql, params);
    
    const [timerInfo] = await conn.query(
      `SELECT * FROM student_homework_timer 
       WHERE student_id = ? AND class_id = ? AND release_batch = ? AND title_id = ?
       ORDER BY start_time DESC LIMIT 1`,
      [studentId.toString(), classId, batchId, queryChapterId]
    );
    
    const [timeLimitInfo] = await conn.query(
      `SELECT time, deadline FROM publish_exercise 
       WHERE class_id = ? AND release_batch = ? AND title_id = ? 
       LIMIT 1`,
      [classId, batchId, queryChapterId]
    );
    
    let timeLimit = 0;
    let deadline = null;
    if (timeLimitInfo.length > 0) {
      timeLimit = parseInt(timeLimitInfo[0].time, 10) || 0;
      deadline = timeLimitInfo[0].deadline;
    }
    
    const processedExercises = exercises.map(exercise => {
      let options = [];
      if (exercise.option) {
        try {
          const optionObj = JSON.parse(exercise.option);
          options = Object.entries(optionObj).map(([key, value]) => ({
            key,
            value
          }));
        } catch (e) {
          console.error(`解析题目选项出错 (ID:${exercise.publish_id}):`, e);
        }
      }
      
      switch (exercise.question_type) {
        case 1: // 单选题
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
          
        case 2: // 填空题
          return {
            ...exercise,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
          
        case 3: // 判断题
          options = [
            { key: 'true', value: '正确' },
            { key: 'false', value: '错误' }
          ];
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
          
        case 4: // 简答题
        case 5: // 计算题
          return {
            ...exercise,
            student_answer: exercise.student_answer || '',
            is_correct: exercise.is_correct
          };
          
        default:
          return {
            ...exercise,
            options,
            student_answer: exercise.student_answer || ''
          };
      }
    });
    
    let timer = null;
    if (timerInfo.length > 0) {
      timer = {
        start_time: timerInfo[0].start_time,
        end_time: timerInfo[0].end_time,
        is_completed: !!timerInfo[0].is_completed,
        is_timeout: !!timerInfo[0].is_timeout
      };
    }
    
    await conn.end();
    
    res.json({
      success: true,
      data: {
        exercises: processedExercises,
        timer: timer,
        time_limit: timeLimit,
        deadline: deadline
      }
    });
  } catch (error) {
    console.error('获取作业题目失败:', error);
    res.status(500).json({
      success: false,
      message: '获取作业题目失败: ' + error.message
    });
  }
};

// 记录作业开始时间
export const startHomework = async (req, res) => {
  try {
    const { classId, batchId } = req.params;
    const chapterId = req.query.chapterId || req.body.chapterId;
    const studentId = req.user?.student_id || req.user?.id;
    
    if (!classId || !batchId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    const conn = await mysql.createConnection(dbConfig);
    const queryChapterId = chapterId ? parseInt(chapterId, 10) : 0;
    
    const [existingTimers] = await conn.query(
      `SELECT * FROM student_homework_timer 
       WHERE student_id = ? AND class_id = ? AND release_batch = ? AND title_id = ?`,
      [studentId.toString(), classId, batchId, queryChapterId]
    );
    
    let startTime = new Date();
    
    const [timeLimitInfo] = await conn.query(
      `SELECT time FROM publish_exercise 
       WHERE class_id = ? AND release_batch = ? AND title_id = ? 
       LIMIT 1`,
      [classId, batchId, queryChapterId]
    );
    
    let timeLimit = 0;
    if (timeLimitInfo.length > 0 && timeLimitInfo[0].time) {
      timeLimit = parseInt(timeLimitInfo[0].time, 10);
    }
    
    if (existingTimers.length > 0) {
      startTime = existingTimers[0].start_time;
    } else {
      await conn.query(
        `INSERT INTO student_homework_timer 
         (student_id, class_id, release_batch, title_id, start_time, is_completed, is_timeout) 
         VALUES (?, ?, ?, ?, ?, false, false)`,
        [studentId.toString(), classId, batchId, queryChapterId, startTime]
      );
    }
    
    await conn.end();
    
    res.json({
      success: true,
      data: {
        start_time: startTime,
        time_limit: timeLimit,
        title_id: queryChapterId
      }
    });
  } catch (error) {
    console.error('记录作业开始时间失败:', error);
    res.status(500).json({
      success: false,
      message: '记录作业开始时间失败: ' + error.message
    });
  }
};

// 上传计算题图片
export const uploadImage = async (req, res) => {
  try {
    // 检查请求中是否包含文件
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未找到上传的图片'
      });
    }
    
    // 获取文件信息 - 使用multer已处理好的文件
    const file = req.file;
    
    // 打印完整文件信息进行调试
    console.log('接收到的文件信息:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      destination: file.destination,
      filename: file.filename
    });
    
    // 使用相对路径返回给前端 - 确保URL路径正确
    const relativePath = `/uploads/student_exercise/${file.filename}`;
    
    console.log('计算题图片上传成功:', {
      filename: file.filename,
      relativePath,
      size: file.size,
      path: file.path // 记录实际保存的完整路径用于调试
    });
    
    // 返回文件路径给前端
    res.json({
      success: true,
      data: {
        url: relativePath,
        filename: file.filename
      }
    });
  } catch (error) {
    console.error('上传图片失败:', error);
    res.status(500).json({
      success: false,
      message: '上传图片失败: ' + error.message
    });
  }
};

// 获取作业完成情况
export const getCompletion = async (req, res) => {
  try {
    const { classId, batchId } = req.params;
    const studentId = req.user?.student_id || req.user?.id;
    
    if (!classId || !batchId || !studentId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    const conn = await mysql.createConnection(dbConfig);
    
    // 获取作业总分和正确数量，按照题型分别计算分数
    const [records] = await conn.query(
      `SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN ser.is_correct = 1 THEN 1 ELSE 0 END) as correct,
        SUM(ser.score) as total_score
      FROM student_exercise_records ser
      JOIN publish_exercise pe ON ser.publish_exercise_id = pe.id
      WHERE ser.student_id = ? AND pe.class_id = ? AND pe.release_batch = ?`,
      [studentId.toString(), classId, batchId] // 确保studentId作为字符串传递
    );
    
    // 获取所有题目信息用于计算满分
    const [exercises] = await conn.query(
      `SELECT 
        pe.id as publish_id,
        eb.question_type
      FROM publish_exercise pe
      JOIN exercise_bank eb ON pe.exercise_id = eb.exercise_id
      WHERE pe.class_id = ? AND pe.release_batch = ? AND pe.study_phase = 2`,
      [classId, batchId]
    );
    
    // 计算满分
    let fullScore = 0;
    for (const exercise of exercises) {
      const questionType = exercise.question_type;
      // 1-3类型题5分，4-5类型题10分
      if (questionType >= 1 && questionType <= 3) {
        fullScore += 5;
      } else if (questionType >= 4 && questionType <= 5) {
        fullScore += 10;
      }
    }
    
    await conn.end();
    
    if (records.length > 0) {
      res.json({
        success: true,
        data: {
          total: records[0].total || 0,
          correct: records[0].correct || 0,
          total_score: records[0].total_score || 0,
          full_score: fullScore || 0
        }
      });
    } else {
      res.json({
        success: true,
        data: {
          total: 0,
          correct: 0,
          total_score: 0,
          full_score: fullScore || 0
        }
      });
    }
  } catch (error) {
    console.error('获取作业完成情况失败:', error);
    res.status(500).json({
      success: false,
      message: '获取作业完成情况失败: ' + error.message
    });
  }
};

// 保存单个答案 - 仅保存到前端，不写入数据库
export const saveAnswer = async (req, res) => {
  try {
    const { publishId, studentAnswer } = req.body;
    
    if (!publishId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    // 发送成功响应，但不实际保存到数据库
    res.json({
      success: true,
      message: '答案暂存成功',
      data: {
        publish_id: publishId,
        student_answer: studentAnswer
      }
    });
  } catch (error) {
    console.error('暂存答案失败:', error);
    res.status(500).json({
      success: false,
      message: '暂存答案失败: ' + error.message
    });
  }
};

// 提交作业答案
export const submitHomework = async (req, res) => {
  try {
    const { classId, batchId } = req.params;
    const { answers, studentId, chapterId } = req.body;
    const userIdToUse = studentId || req.user?.student_id || req.user?.id;
    
    if (!classId || !batchId || !userIdToUse || !answers) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    let conn = null;
    try {
      conn = await mysql.createConnection(dbConfig);
      await conn.beginTransaction();
      
      const queryChapterId = chapterId ? parseInt(chapterId, 10) : 0;
      const endTime = new Date();
      
      // 更新作业计时器
      await conn.query(
        `UPDATE student_homework_timer 
         SET end_time = ?, is_completed = true 
         WHERE student_id = ? AND class_id = ? AND release_batch = ? AND title_id = ?`,
        [endTime, userIdToUse, classId, batchId, queryChapterId]
      );
      
      // 获取计时信息
      const [timerInfo] = await conn.query(
        `SELECT start_time FROM student_homework_timer 
         WHERE student_id = ? AND class_id = ? AND release_batch = ? AND title_id = ?`,
        [userIdToUse, classId, batchId, queryChapterId]
      );
      
      // 获取时间限制
      const [timeLimitInfo] = await conn.query(
        `SELECT time FROM publish_exercise 
         WHERE class_id = ? AND release_batch = ? AND title_id = ? 
         LIMIT 1`,
        [classId, batchId, queryChapterId]
      );
      
      let timeLimit = 0;
      if (timeLimitInfo.length > 0 && timeLimitInfo[0].time) {
        timeLimit = parseInt(timeLimitInfo[0].time, 10);
      }
      
      // 计算作业用时
      let elapsedMinutes = 0;
      if (timerInfo.length > 0) {
        const startTime = new Date(timerInfo[0].start_time);
        elapsedMinutes = Math.round((endTime - startTime) / (1000 * 60));
        
        // 检查是否超时
        if (timeLimit > 0 && elapsedMinutes > timeLimit) {
          await conn.query(
            `UPDATE student_homework_timer 
             SET is_timeout = true 
             WHERE student_id = ? AND class_id = ? AND release_batch = ? AND title_id = ?`,
            [userIdToUse, classId, batchId, queryChapterId]
          );
        }
      }
      
      let totalCorrect = 0;
      let totalScore = 0;
      let totalQuestions = answers.length;
      
      for (const answer of answers) {
        // 获取题目信息
        const [exerciseInfo] = await conn.query(
          `SELECT eb.exercise_id, eb.answer, eb.question_type, eb.content
           FROM exercise_bank eb
           JOIN publish_exercise pe ON eb.exercise_id = pe.exercise_id 
           WHERE pe.id = ?`,
          [answer.publish_id]
        );
        
        if (exerciseInfo.length === 0) continue;
        
        const correctAnswer = exerciseInfo[0].answer;
        const questionType = exerciseInfo[0].question_type;
        const questionContent = exerciseInfo[0].content;
        const studentAnswer = answer.student_answer || '';
        
        let isCorrect = 0;
        let score = 0;
        let evaluation = "";
        
        // 根据题型进行评分
        switch (questionType) {
          case 1: // 选择题
            isCorrect = studentAnswer.trim() === correctAnswer.trim() ? 1 : 0;
            score = isCorrect ? 5 : 0;
            evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
            break;
            
          case 2: // 填空题
            const normalizedCorrect = correctAnswer.trim().toLowerCase();
            const normalizedStudent = studentAnswer.trim().toLowerCase();
            isCorrect = normalizedStudent === normalizedCorrect ? 1 : 0;
            score = isCorrect ? 5 : 0;
            evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
            break;
            
          case 3: // 判断题
            let standardizedCorrect = correctAnswer.trim().toUpperCase();
            let standardizedStudent = studentAnswer.trim().toUpperCase();
            
            if (standardizedCorrect === "T" || standardizedCorrect === "TRUE" || standardizedCorrect === "对" || standardizedCorrect === "正确" || standardizedCorrect === "1") {
              standardizedCorrect = "T";
            } else if (standardizedCorrect === "F" || standardizedCorrect === "FALSE" || standardizedCorrect === "错" || standardizedCorrect === "错误" || standardizedCorrect === "0") {
              standardizedCorrect = "F";
            }
            
            if (standardizedStudent === "T" || standardizedStudent === "TRUE" || standardizedStudent === "对" || standardizedStudent === "正确" || standardizedStudent === "1") {
              standardizedStudent = "T";
            } else if (standardizedStudent === "F" || standardizedStudent === "FALSE" || standardizedStudent === "错" || standardizedStudent === "错误" || standardizedStudent === "0") {
              standardizedStudent = "F";
            }
            
            isCorrect = standardizedStudent === standardizedCorrect ? 1 : 0;
            score = isCorrect ? 5 : 0;
            evaluation = isCorrect ? "正确" : `错误，正确答案是：${correctAnswer}`;
            break;
            
          case 4: // 简答题
          case 5: // 计算题
            // 使用AI评分
            console.log(`正在评分${questionType === 4 ? '简答题' : '计算题'}: 题目ID=${answer.publish_id}`);
            console.log(`学生答案: ${studentAnswer.substring(0, 100)}${studentAnswer.length > 100 ? '...' : ''}`);
            
            const aiResult = await gradeWithAI(questionContent, studentAnswer, questionType);
            // 直接使用API返回的结果，不进行覆盖
            isCorrect = aiResult.score >= 6 ? 1 : 0; // 6分以上认为是正确的
            score = aiResult.score; // 直接使用AI返回的分数
            evaluation = aiResult.evaluation; // 直接使用AI返回的评价
            
            console.log(`AI原始评分结果: 分数=${aiResult.score}, 评价=${aiResult.evaluation.substring(0, 100)}...`);
            console.log(`最终使用的评分: 分数=${score}, 是否正确=${isCorrect}`);
            break;
            
          default:
            isCorrect = null;
            score = null;
            evaluation = "未知题型";
        }
        
        if (isCorrect === 1) {
          totalCorrect++;
          totalScore += score || 0;
        }
        
        // 首先检查是否已存在记录，避免重复插入
        const [existingRecord] = await conn.query(
          `SELECT * FROM student_exercise_records 
           WHERE student_id = ? AND publish_exercise_id = ?`,
          [userIdToUse, answer.publish_id]
        );
        
        if (existingRecord.length > 0) {
          // 更新现有记录
          await conn.query(
            `UPDATE student_exercise_records 
             SET student_answer = ?, is_correct = ?, score = ?, evaluation = ? 
             WHERE id = ?`,
            [studentAnswer, isCorrect, score, evaluation, existingRecord[0].id]
          );
        } else {
          // 插入新记录
          await conn.query(
            `INSERT INTO student_exercise_records 
             (student_id, publish_exercise_id, student_answer, is_correct, score, evaluation) 
             VALUES (?, ?, ?, ?, ?, ?)`,
            [userIdToUse, answer.publish_id, studentAnswer, isCorrect, score, evaluation]
          );
        }
      }
      
      // 提交事务
      await conn.commit();
      
      // 返回提交结果
      res.json({
        success: true,
        message: '作业提交成功',
        data: {
          total: totalQuestions,
          correct: totalCorrect,
          score: totalScore,
          timeUsed: elapsedMinutes,
          isTimeout: timeLimit > 0 && elapsedMinutes > timeLimit
        }
      });
    } catch (error) {
      // 回滚事务
      if (conn) {
        await conn.rollback();
      }
      throw error;
    } finally {
      // 关闭数据库连接
      if (conn) {
        await conn.end();
      }
    }
  } catch (error) {
    console.error('提交作业失败:', error);
    res.status(500).json({
      success: false,
      message: '提交作业失败: ' + (error.message || '未知错误')
    });
  }
}; 
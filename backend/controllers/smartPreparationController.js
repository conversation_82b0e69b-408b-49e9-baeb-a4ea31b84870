import pool from '../config/db.js';

// 获取最近的教案列表
export const getRecentPreparations = async (req, res) => {
  try {
    const teacherId = req.user?.system_teacher_id;
    
    if (!teacherId) {
      return res.status(400).json({
        success: false,
        message: '未找到教师ID'
      });
    }

    // 获取最近5条教案记录，并关联课程信息
    const [lessonPlans] = await pool.execute(
      `SELECT 
        lp.id,
        lp.title,
        lp.create_time,
        lp.course_code,
        c.course_name
       FROM lesson_plans lp
       LEFT JOIN courses c ON lp.course_code = c.course_code
       WHERE lp.teacher_id = ?
       ORDER BY lp.create_time DESC
       LIMIT 5`,
      [teacherId]
    );

    // 格式化返回数据
    const formattedPlans = lessonPlans.map(plan => ({
      id: plan.id,
      title: plan.title,
      course_code: plan.course_code,
      courseName: plan.course_name,
      date: new Date(plan.create_time).toLocaleDateString('zh-CN')
    }));

    res.json({
      success: true,
      data: formattedPlans
    });

  } catch (error) {
    console.error('获取最近教案失败:', error);
    res.status(500).json({
      success: false,
      message: '获取最近教案失败'
    });
  }
};

// 开始智能备课
export const startPreparation = async (req, res) => {
  try {
    const teacherId = req.user?.system_teacher_id;
    const { courseCode, templateId, title, goals } = req.body;
    
    if (!teacherId || !courseCode || !title) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 创建新的教案记录
    const [result] = await pool.execute(
      `INSERT INTO lesson_plans (
        teacher_id,
        course_code,
        title,
        teaching_goals,
        create_time
      ) VALUES (?, ?, ?, ?, NOW())`,
      [teacherId, courseCode, title, goals]
    );

    res.json({
      success: true,
      message: '创建教案成功',
      data: {
        id: result.insertId,
        title,
        courseCode
      }
    });

  } catch (error) {
    console.error('开始备课失败:', error);
    res.status(500).json({
      success: false,
      message: '开始备课失败'
    });
  }
};

// 获取教学建议
export const getTeachingAdvice = async (req, res) => {
  try {
    const { lessonPlanId } = req.params;
    const teacherId = req.user?.system_teacher_id;

    if (!teacherId || !lessonPlanId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 这里可以实现AI生成教学建议的逻辑
    // 现在先返回模拟数据
    const advice = {
      teachingMethods: [
        '采用案例教学法，增强学生理解',
        '设计小组讨论环节，促进互动',
        '使用多媒体资源，提高课堂趣味性'
      ],
      keyPoints: [
        '重点概念解释要深入浅出',
        '多举实际应用例子',
        '注意与学生已有知识建立联系'
      ],
      timeAllocation: {
        introduction: '10分钟',
        mainContent: '25分钟',
        practice: '20分钟',
        summary: '5分钟'
      }
    };

    res.json({
      success: true,
      data: advice
    });

  } catch (error) {
    console.error('获取教学建议失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教学建议失败'
    });
  }
};
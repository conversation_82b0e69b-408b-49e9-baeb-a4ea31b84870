import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

const pool = mysql.createPool(dbConfig);

/**
 * 获取班级预习总体统计数据
 */
export const getClassPreviewStats = async (req, res) => {
  const { classId, chapterId } = req.params;

  try {
    // 1. 获取班级学生列表
    const [students] = await pool.query(
      `SELECT s.student_id, s.name
       FROM students s
       JOIN student_course_registration scr ON s.student_id = scr.student_id
       WHERE scr.class_id = ?`,
      [classId]
    );

    if (students.length === 0) {
      return res.json({
        success: true,
        data: {
          totalStudents: 0,
          completedStudents: 0,
          completionRate: 0,
          avgViewTime: 0,
          avgCorrectRate: 0
        }
      });
    }

    // 2. 获取该章节的预习资料发布记录
    const [previewPublishes] = await pool.query(
      `SELECT pp.id, pp.title, pp.deadline
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       WHERE pp.class_id = ? AND pm.title_id = ?
       ORDER BY pp.create_time DESC`,
      [classId, chapterId]
    );

    if (previewPublishes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该章节的预习资料'
      });
    }

    // 取最新的预习发布
    const latestPreviewPublish = previewPublishes[0];

    // 3. 获取预习完成情况
    const [previewCompletions] = await pool.query(
      `SELECT ps.student_id, ps.status, ps.total_view_duration, ps.total_view_count
       FROM preview_student ps
       WHERE ps.preview_publish_id = ?`,
      [latestPreviewPublish.id]
    );

    // 4. 获取预习习题完成情况（study_phase=1表示预习题）
    const [exerciseResults] = await pool.query(
      `SELECT
         ser.student_id,
         COUNT(ser.id) AS total_answered,
         SUM(CASE WHEN ser.is_correct = 1 THEN 1 ELSE 0 END) AS correct_count
       FROM student_exercise_records ser
       JOIN publish_exercise pe ON ser.publish_exercise_id = pe.id
       WHERE pe.class_id = ? AND pe.title_id = ? AND pe.study_phase = 1
       GROUP BY ser.student_id`,
      [classId, chapterId]
    );

    // 5. 计算统计数据
    const totalStudents = students.length;
    const completedStudents = previewCompletions.filter(p => p.status === 1).length;
    const completionRate = totalStudents > 0 ? (completedStudents / totalStudents) * 100 : 0;

    // 计算平均预习时长（分钟）
    const totalViewDuration = previewCompletions.reduce((sum, item) => sum + (item.total_view_duration || 0), 0);
    const avgViewTime = previewCompletions.length > 0 ? totalViewDuration / previewCompletions.length / 60 : 0;

    // 计算平均正确率
    let totalCorrectRate = 0;
    let studentsWithExercises = 0;

    exerciseResults.forEach(result => {
      if (result.total_answered > 0) {
        const correctRate = (result.correct_count / result.total_answered) * 100;
        totalCorrectRate += correctRate;
        studentsWithExercises++;
      }
    });

    const avgCorrectRate = studentsWithExercises > 0 ? totalCorrectRate / studentsWithExercises : 0;

    // 6. 准备学生数据
    const studentsData = students.map(student => {
      const previewCompletion = previewCompletions.find(p => p.student_id === student.student_id) || {
        status: 0,
        total_view_duration: 0,
        total_view_count: 0
      };

      const exerciseResult = exerciseResults.find(e => e.student_id === student.student_id) || {
        total_answered: 0,
        correct_count: 0
      };

      const correctRate = exerciseResult.total_answered > 0
        ? (exerciseResult.correct_count / exerciseResult.total_answered) * 100
        : 0;

      return {
        student_id: student.student_id,
        name: student.name,
        status: previewCompletion.status,
        view_duration: previewCompletion.total_view_duration || 0,
        view_times: previewCompletion.total_view_count || 0,
        view_minutes: Math.round(((previewCompletion.total_view_duration || 0) / 60) * 10) / 10,
        total_answered: exerciseResult.total_answered,
        correct_count: exerciseResult.correct_count,
        correct_rate: Math.round(correctRate)
      };
    });

    // 7. 返回数据
    return res.json({
      success: true,
      data: {
        totalStudents,
        completedStudents,
        completionRate: Math.round(completionRate),
        avgViewTime: Math.round(avgViewTime * 10) / 10,
        avgCorrectRate: Math.round(avgCorrectRate),
        previewInfo: {
          id: latestPreviewPublish.id,
          title: latestPreviewPublish.title,
          deadline: latestPreviewPublish.deadline
        },
        students: studentsData
      }
    });

  } catch (error) {
    console.error('获取班级预习统计数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取班级预习统计数据失败',
      error: error.message
    });
  }
};

/**
 * 获取班级的章节列表（有预习资料的章节）
 */
export const getClassChapters = async (req, res) => {
  const { classId } = req.params;

  try {
    // 获取该班级同时有预习资料和预习题的章节列表
    const [chapters] = await pool.query(
      `SELECT DISTINCT pm.title_id as id, pm.chapter_title as title
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       JOIN publish_exercise pe ON pm.title_id = pe.title_id AND pe.study_phase = 1
       WHERE pp.class_id = ? AND pe.class_id = ?
       ORDER BY pm.title_id`,
      [classId, classId]
    );

    return res.json({
      success: true,
      data: chapters
    });

  } catch (error) {
    console.error('获取班级章节列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取班级章节列表失败',
      error: error.message
    });
  }
};

/**
 * 获取班级学生列表
 */
export const getStudentsList = async (req, res) => {
  const { classId } = req.params;

  try {
    // 获取班级学生列表
    const [students] = await pool.query(
      `SELECT s.student_id as id, s.name
       FROM students s
       JOIN student_course_registration scr ON s.student_id = scr.student_id
       WHERE scr.class_id = ?
       ORDER BY s.name`,
      [classId]
    );

    return res.json({
      success: true,
      data: students
    });

  } catch (error) {
    console.error('获取班级学生列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取班级学生列表失败',
      error: error.message
    });
  }
};

/**
 * 获取预习查看时间分布
 */
export const getPreviewViewTimeDistribution = async (req, res) => {
  const { classId, chapterId } = req.params;

  try {
    // 1. 获取该章节的预习资料发布记录
    const [previewPublishes] = await pool.query(
      `SELECT pp.id, pp.title
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       WHERE pp.class_id = ? AND pm.title_id = ?
       ORDER BY pp.create_time DESC`,
      [classId, chapterId]
    );

    if (previewPublishes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该章节的预习资料'
      });
    }

    // 取最新的预习发布
    const latestPreviewPublish = previewPublishes[0];

    // 2. 获取预习查看记录
    const [viewLogs] = await pool.query(
      `SELECT
         vl.student_id,
         s.name as student_name,
         DATE_FORMAT(vl.view_time, '%Y-%m-%d') as view_date,
         DATE_FORMAT(vl.view_time, '%H') as view_hour,
         SUM(vl.view_duration) as total_duration
       FROM preview_view_logs vl
       JOIN students s ON vl.student_id = s.student_id
       WHERE vl.preview_id = ?
       GROUP BY vl.student_id, view_date, view_hour
       ORDER BY view_date, view_hour`,
      [latestPreviewPublish.id]
    );

    // 3. 处理数据，按日期和小时统计
    const dateHourMap = {};
    viewLogs.forEach(log => {
      if (!dateHourMap[log.view_date]) {
        dateHourMap[log.view_date] = {};
      }

      if (!dateHourMap[log.view_date][log.view_hour]) {
        dateHourMap[log.view_date][log.view_hour] = {
          count: 0,
          duration: 0,
          students: []
        };
      }

      dateHourMap[log.view_date][log.view_hour].count++;
      dateHourMap[log.view_date][log.view_hour].duration += log.total_duration;
      dateHourMap[log.view_date][log.view_hour].students.push({
        student_id: log.student_id,
        name: log.student_name,
        duration: log.total_duration
      });
    });

    // 4. 转换为数组格式
    const result = [];
    for (const date in dateHourMap) {
      for (const hour in dateHourMap[date]) {
        result.push({
          date,
          hour: parseInt(hour),
          time_range: `${hour}:00-${hour}:59`,
          count: dateHourMap[date][hour].count,
          total_duration: dateHourMap[date][hour].duration,
          duration_minutes: Math.round((dateHourMap[date][hour].duration / 60) * 10) / 10,
          students: dateHourMap[date][hour].students
        });
      }
    }

    // 5. 返回数据
    return res.json({
      success: true,
      data: {
        preview: {
          id: latestPreviewPublish.id,
          title: latestPreviewPublish.title
        },
        distribution: result
      }
    });

  } catch (error) {
    console.error('获取预习查看时间分布失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取预习查看时间分布失败',
      error: error.message
    });
  }
};

/**
 * 获取预习习题统计
 */
export const getChapterQuestionStats = async (req, res) => {
  const { classId, chapterId } = req.params;

  try {
    // 1. 获取章节预习习题列表
    const [exercises] = await pool.query(
      `SELECT
         pe.id AS publish_id,
         e.exercise_id,
         e.title,
         e.content,
         e.answer,
         COUNT(DISTINCT ser.student_id) AS answer_count,
         SUM(CASE WHEN ser.is_correct = 1 THEN 1 ELSE 0 END) AS correct_count
       FROM publish_exercise pe
       JOIN exercise_bank e ON pe.exercise_id = e.exercise_id
       LEFT JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id
       WHERE pe.class_id = ? AND pe.title_id = ? AND pe.study_phase = 1
       GROUP BY pe.id
       ORDER BY pe.id`,
      [classId, chapterId]
    );

    if (exercises.length === 0) {
      return res.json({
        success: true,
        message: '该章节暂无预习习题',
        data: {
          exercises: []
        }
      });
    }

    // 2. 获取班级学生总数
    const [studentCountResult] = await pool.query(
      `SELECT COUNT(*) AS total
       FROM students s
       JOIN student_course_registration scr ON s.student_id = scr.student_id
       WHERE scr.class_id = ?`,
      [classId]
    );

    const totalStudents = studentCountResult[0].total;

    // 3. 为每道题获取错题分布
    const exercisesWithDetails = await Promise.all(exercises.map(async (exercise) => {
      // 获取每道题的错误回答分布
      const [wrongAnswers] = await pool.query(
        `SELECT
           student_answer,
           COUNT(*) AS count
         FROM student_exercise_records
         WHERE publish_exercise_id = ? AND is_correct = 0
         GROUP BY student_answer
         ORDER BY count DESC
         LIMIT 3`,
        [exercise.publish_id]
      );

      // 计算正确率和参与率
      const participationRate = totalStudents > 0 ? (exercise.answer_count / totalStudents) * 100 : 0;
      const correctRate = exercise.answer_count > 0 ? (exercise.correct_count / exercise.answer_count) * 100 : 0;

      return {
        ...exercise,
        participation_rate: Math.round(participationRate),
        correct_rate: Math.round(correctRate),
        wrong_answers: wrongAnswers
      };
    }));

    // 4. 返回数据
    return res.json({
      success: true,
      data: {
        total_students: totalStudents,
        exercises: exercisesWithDetails
      }
    });

  } catch (error) {
    console.error('获取预习习题统计失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取预习习题统计失败',
      error: error.message
    });
  }
};

/**
 * 获取知识点覆盖率
 */
export const getKnowledgePointCoverage = async (req, res) => {
  const { classId, chapterId } = req.params;

  try {
    // 本功能需要从exercise_bank表中提取知识点(point字段)信息
    // 并统计学生对各知识点的掌握情况

    // 1. 获取章节所有知识点
    const [knowledgePoints] = await pool.query(
      `SELECT DISTINCT point
       FROM exercise_bank
       WHERE title_id = ? AND point IS NOT NULL AND point != ''`,
      [chapterId]
    );

    if (knowledgePoints.length === 0) {
      return res.json({
        success: true,
        message: '该章节暂无知识点记录',
        data: {
          knowledge_points: []
        }
      });
    }

    // 解析知识点（假设point字段是JSON字符串或逗号分隔的字符串）
    const pointsSet = new Set();
    knowledgePoints.forEach(kp => {
      try {
        // 尝试解析为JSON
        const pointObj = JSON.parse(kp.point);
        if (Array.isArray(pointObj)) {
          pointObj.forEach(p => pointsSet.add(p));
        } else if (typeof pointObj === 'object') {
          Object.values(pointObj).forEach(p => pointsSet.add(p));
        } else {
          pointsSet.add(pointObj.toString());
        }
      } catch (e) {
        // 如果不是JSON，尝试按逗号分隔
        kp.point.split(',').forEach(p => pointsSet.add(p.trim()));
      }
    });

    const pointsList = Array.from(pointsSet);

    // 2. 获取学生列表
    const [students] = await pool.query(
      `SELECT s.student_id, s.name
       FROM students s
       JOIN student_course_registration scr ON s.student_id = scr.student_id
       WHERE scr.class_id = ?
       ORDER BY s.name`,
      [classId]
    );

    // 3. 统计每个知识点的掌握情况
    const pointsStats = await Promise.all(pointsList.map(async (point) => {
      // 获取包含该知识点的习题
      const [exercises] = await pool.query(
        `SELECT e.exercise_id
         FROM exercise_bank e
         WHERE e.title_id = ? AND e.point LIKE ?`,
        [chapterId, `%${point}%`]
      );

      if (exercises.length === 0) {
        return {
          point,
          exercise_count: 0,
          mastery_rate: 0,
          students_mastered: []
        };
      }

      const exerciseIds = exercises.map(e => e.exercise_id);

      // 获取学生在这些习题上的表现
      const [studentResults] = await pool.query(
        `SELECT
           ser.student_id,
           s.name as student_name,
           COUNT(DISTINCT pe.exercise_id) as answered_count,
           SUM(CASE WHEN ser.is_correct = 1 THEN 1 ELSE 0 END) as correct_count
         FROM student_exercise_records ser
         JOIN publish_exercise pe ON ser.publish_exercise_id = pe.id
         JOIN students s ON ser.student_id = s.student_id
         WHERE pe.exercise_id IN (?) AND pe.class_id = ?
         GROUP BY ser.student_id`,
        [exerciseIds, classId]
      );

      // 计算掌握该知识点的学生（正确率>=70%认为掌握）
      const masteredStudents = studentResults.filter(sr => {
        return sr.answered_count > 0 && (sr.correct_count / sr.answered_count) >= 0.7;
      });

      const masteryRate = students.length > 0 ? (masteredStudents.length / students.length) * 100 : 0;

      return {
        point,
        exercise_count: exercises.length,
        mastery_rate: Math.round(masteryRate),
        students_mastered: masteredStudents.map(s => ({
          student_id: s.student_id,
          name: s.student_name,
          correct_rate: Math.round((s.correct_count / s.answered_count) * 100)
        }))
      };
    }));

    // 4. 计算每个学生的知识点掌握情况
    const studentKnowledgeCoverage = students.map(student => {
      const masteredPoints = pointsStats.filter(ps =>
        ps.students_mastered.some(sm => sm.student_id === student.student_id)
      );

      const coverageRate = pointsList.length > 0 ? (masteredPoints.length / pointsList.length) * 100 : 0;

      return {
        student_id: student.student_id,
        name: student.name,
        mastered_points: masteredPoints.map(mp => mp.point),
        mastered_count: masteredPoints.length,
        total_points: pointsList.length,
        coverage_rate: Math.round(coverageRate)
      };
    });

    // 5. 返回数据
    return res.json({
      success: true,
      data: {
        knowledge_points: pointsStats,
        students_coverage: studentKnowledgeCoverage
      }
    });

  } catch (error) {
    console.error('获取知识点覆盖率失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取知识点覆盖率失败',
      error: error.message
    });
  }
};

/**
 * 获取学生预习详情（用于新的预习详情页面）
 */
export const getStudentPreviewDetails = async (req, res) => {
  const { classId, chapterId, studentId } = req.params;

  try {
    // 1. 获取学生信息
    const [studentRows] = await pool.query(
      `SELECT student_id, name FROM students WHERE student_id = ?`,
      [studentId]
    );

    if (studentRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该学生'
      });
    }

    // 2. 获取该章节的预习资料发布记录
    const [previewPublishes] = await pool.query(
      `SELECT pp.id, pp.title, pp.deadline, pm.content
       FROM preview_publish pp
       JOIN preview_materials pm ON pp.preview_material_id = pm.id
       WHERE pp.class_id = ? AND pm.title_id = ?
       ORDER BY pp.create_time DESC`,
      [classId, chapterId]
    );

    if (previewPublishes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该章节的预习资料'
      });
    }

    // 取最新的预习发布
    const latestPreviewPublish = previewPublishes[0];

    // 3. 获取学生预习完成情况
    const [previewStudentRows] = await pool.query(
      `SELECT
         ps.status,
         ps.submit_time,
         ps.total_view_duration,
         ps.total_view_count,
         ps.content as student_content
       FROM preview_student ps
       WHERE ps.preview_publish_id = ? AND ps.student_id = ?`,
      [latestPreviewPublish.id, studentId]
    );

    const previewStudent = previewStudentRows.length > 0 ? previewStudentRows[0] : {
      status: 0,
      submit_time: null,
      total_view_duration: 0,
      total_view_count: 0,
      student_content: ''
    };

    // 4. 获取预习查看记录
    const [viewLogs] = await pool.query(
      `SELECT vl.view_time, vl.view_duration
       FROM preview_view_logs vl
       WHERE vl.preview_id = ? AND vl.student_id = ?
       ORDER BY vl.view_time DESC`,
      [latestPreviewPublish.id, studentId]
    );

    // 5. 获取预习习题完成情况
    const [exerciseRows] = await pool.query(
      `SELECT
         pe.id AS publish_exercise_id,
         e.exercise_id,
         e.title,
         e.content,
         e.syllabus_id,
         e.answer,
         ser.student_answer,
         ser.is_correct,
         ser.score,
         ser.time_spent,
         ser.create_time as submit_time
       FROM publish_exercise pe
       JOIN exercise_bank e ON pe.exercise_id = e.exercise_id
       LEFT JOIN student_exercise_records ser ON pe.id = ser.publish_exercise_id AND ser.student_id = ?
       WHERE pe.class_id = ? AND pe.title_id = ? AND pe.study_phase = 1`,
      [studentId, classId, chapterId]
    );

    // 6. 计算统计数据
    const viewMinutes = Math.round(((previewStudent.total_view_duration || 0) / 60) * 10) / 10;
    const totalExercises = exerciseRows.length;
    const answeredExercises = exerciseRows.filter(ex => ex.submit_time !== null).length;
    const correctExercises = exerciseRows.filter(ex => ex.is_correct === 1).length;
    const correctRate = answeredExercises > 0 ? (correctExercises / answeredExercises) * 100 : 0;
    const completionRate = totalExercises > 0 ? (answeredExercises / totalExercises) * 100 : 0;

    // 7. 准备习题数据
    const exercisesData = exerciseRows.map(ex => ({
      id: ex.publish_exercise_id,
      title: ex.title,
      content: ex.content,
      is_answered: ex.submit_time !== null,
      is_correct: ex.is_correct === 1,
      score: ex.score,
      student_answer: ex.student_answer,
      answer: ex.answer,
      time_spent: ex.time_spent,
      submit_time: ex.submit_time
    }));

    // 8. 返回数据
    return res.json({
      success: true,
      data: {
        student: {
          id: studentRows[0].student_id,
          name: studentRows[0].name
        },
        preview: {
          id: latestPreviewPublish.id,
          title: latestPreviewPublish.title,
          deadline: latestPreviewPublish.deadline,
          content: latestPreviewPublish.content
        },
        stats: {
          status: previewStudent.status,
          submit_time: previewStudent.submit_time,
          view_duration: previewStudent.total_view_duration || 0,
          view_minutes: viewMinutes,
          view_count: previewStudent.total_view_count || 0,
          total_exercises: totalExercises,
          answered_exercises: answeredExercises,
          correct_exercises: correctExercises,
          correct_rate: Math.round(correctRate),
          completion_rate: Math.round(completionRate)
        },
        student_content: previewStudent.student_content,
        view_logs: viewLogs.map(log => ({
          view_time: log.view_time,
          view_duration: log.view_duration,
          duration_minutes: Math.round((log.view_duration / 60) * 10) / 10
        })),
        exercises: exercisesData
      }
    });

  } catch (error) {
    console.error('获取学生预习详情失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取学生预习详情失败',
      error: error.message
    });
  }
};
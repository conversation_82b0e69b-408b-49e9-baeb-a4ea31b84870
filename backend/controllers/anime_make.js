import OpenAI from "openai";
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { loadModelConfigs } from '../config/modelService.js';
import { v4 as uuidv4 } from 'uuid';
import pool from '../config/db.js';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 存储生成任务的状态
const animationTasks = {};

// 任务状态常量
const TASK_STATUS = {
  PENDING: 'pending',    // 等待中
  PROCESSING: 'processing', // 处理中
  COMPLETED: 'completed',  // 完成
  FAILED: 'failed'      // 失败
};

// 从数据库获取API配置
async function getOpenAIClient() {
  try {
    // 加载模型配置
    const configs = await loadModelConfigs();

    // 获取deepseek配置
    const deepseekConfig = configs['deepseek'];

    if (!deepseekConfig) {
      console.error('未找到deepseek配置');
      // 使用备用配置
      return new OpenAI({
        baseURL: 'https://api.deepseek.com/v1/',
        apiKey: '***********************************'
      });
    }

    return new OpenAI({
      baseURL: `${deepseekConfig.baseURL}/v1/`,
      apiKey: deepseekConfig.apiKey
    });
  } catch (error) {
    console.error('获取OpenAI客户端失败:', error);
    // 使用备用配置
    return new OpenAI({
      baseURL: 'https://api.deepseek.com/v1/',
      apiKey: '***********************************'
    });
  }
}

// 生成动画HTML
async function generateAnimation(content) {
  console.log("开始调用 deepseek API 生成动画...");
  console.log("动画内容:", content);

  // 获取OpenAI客户端
  const openai = await getOpenAIClient();

  const completion = await openai.chat.completions.create({
    messages: [
      { role: "system", content: 'You are a helpful assistant that creates educational animations using HTML, CSS, and JavaScript.'},
      { role: "user", content: `
请使用JavaScript和HTML来动态展示${content}，只返回HTML文件的代码内容，不要返回其他的内容。一个示例如下：
<!DOCTYPE html>
<html>
<head>
    <title>导数可视化</title>
    <style>
        canvas {
            border: 1px solid #000;
        }
        .container {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>导数演示 (f(x) = x³)</h2>
        <canvas id="canvas" width="600" height="400"></canvas>
        <p>拖动红点观察切线变化 | 导数 f'(x) = 3x²</p>
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        let dragging = false;

        // 初始点坐标
        let a = 0;
        const scale = 40; // 像素/单位

        // 数学坐标转换
        function toScreenX(x) { return canvas.width/2 + x*scale; }
        function toScreenY(y) { return canvas.height/2 - y*scale; }

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制网格
            ctx.strokeStyle = '#ddd';
            ctx.beginPath();
            for(let x = -7; x <= 7; x++) {
                ctx.moveTo(toScreenX(x), 0);
                ctx.lineTo(toScreenX(x), canvas.height);
            }
            for(let y = -5; y <= 5; y++) {
                ctx.moveTo(0, toScreenY(y));
                ctx.lineTo(canvas.width, toScreenY(y));
            }
            ctx.stroke();

            // 绘制坐标轴
            ctx.strokeStyle = '#000';
            ctx.beginPath();
            ctx.moveTo(canvas.width/2, 0);
            ctx.lineTo(canvas.width/2, canvas.height);
            ctx.moveTo(0, canvas.height/2);
            ctx.lineTo(canvas.width, canvas.height/2);
            ctx.stroke();

            // 绘制函数曲线 f(x) = x³
            ctx.strokeStyle = '#00f';
            ctx.beginPath();
            ctx.moveTo(toScreenX(-5), toScreenY(Math.pow(-5, 3)));
            for(let x = -5; x <= 5; x += 0.1) {
                ctx.lineTo(toScreenX(x), toScreenY(Math.pow(x, 3)));
            }
            ctx.stroke();

            // 计算导数和切线
            const fa = Math.pow(a, 3);
            const df = 3 * Math.pow(a, 2);
            const tanLength = 2;

            // 绘制切线
            ctx.strokeStyle = '#f00';
            ctx.beginPath();
            const x1 = a - tanLength;
            const y1 = fa - df * tanLength;
            const x2 = a + tanLength;
            const y2 = fa + df * tanLength;
            ctx.moveTo(toScreenX(x1), toScreenY(y1));
            ctx.lineTo(toScreenX(x2), toScreenY(y2));
            ctx.stroke();

            // 绘制可拖动点
            ctx.fillStyle = '#f00';
            ctx.beginPath();
            ctx.arc(toScreenX(a), toScreenY(fa), 5, 0, Math.PI*2);
            ctx.fill();
        }

        // 鼠标交互
        canvas.addEventListener('mousedown', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left - canvas.width/2) / scale;
            const y = (canvas.height/2 - (e.clientY - rect.top)) / scale;

            if(Math.abs(x - a) < 0.5 && Math.abs(y - Math.pow(a, 3)) < 0.5) {
                dragging = true;
            }
        });

        canvas.addEventListener('mousemove', (e) => {
            if(dragging) {
                const rect = canvas.getBoundingClientRect();
                a = (e.clientX - rect.left - canvas.width/2) / scale;
                a = Math.max(-5, Math.min(5, a)); // 限制范围
                draw();
            }
        });

        canvas.addEventListener('mouseup', () => dragging = false);
        canvas.addEventListener('mouseout', () => dragging = false);

        // 初始绘制
        draw();
    </script>
</body>
</html>
请严格遵守下面的约定：
1、只返回上面格式的代码，不要返回其他的内容。
2、使用JavaScript和HTML语言完成，动画一定要精美的，不要简单重复。
3、代码一定要稳定可运行
` }
    ],
    model: "deepseek-reasoner",
    max_tokens: 5000,
    temperature: 0.7,
    timeout: 120000
  });

  return completion.choices[0].message.content;
}

async function saveAnimationHtml(htmlContent, teacherId, animeText) {
  try {
    // 确保public目录存在
    const publicDir = path.join(__dirname, '..', '..', 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    // 保存HTML文件
    const filePath = path.join(publicDir, 'anime_make.html');
    fs.writeFileSync(filePath, htmlContent, 'utf8');

    // 将记录保存到数据库
    try {
      // 插入记录到animation_records表
      const [result] = await pool.execute(
        `INSERT INTO animation_records (
          teacher_id,
          anime_text,
          anime_code
        ) VALUES (?, ?, ?)`,
        [teacherId, animeText, htmlContent]
      );

      console.log('动画记录已保存到数据库，ID:', result.insertId);
    } catch (dbError) {
      console.error('保存动画记录到数据库失败:', dbError);
      // 数据库错误不应该影响文件保存的结果
    }

    return {
      success: true,
      filePath: '/anime_make.html' // 返回相对路径，用于前端访问
    };
  } catch (error) {
    console.error('保存HTML文件失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 清理HTML代码，移除Markdown代码块标记
function cleanHtmlCode(content) {
  if (!content) return '';

  // 移除开头的 ```html 或 ```
  let cleanedContent = content.replace(/^\s*```(?:html)?\s*\n/i, '');

  // 移除结尾的 ```
  cleanedContent = cleanedContent.replace(/\n\s*```\s*$/i, '');

  return cleanedContent;
}

// 异步生成动画
async function processAnimationTask(taskId, content, teacherId, animeText) {
  try {
    // 更新任务状态为处理中
    animationTasks[taskId].status = TASK_STATUS.PROCESSING;

    // 生成动画HTML
    let htmlContent = await generateAnimation(content);

    // 清理HTML代码
    htmlContent = cleanHtmlCode(htmlContent);

    // 保存HTML文件并将记录保存到数据库
    const saveResult = await saveAnimationHtml(htmlContent, teacherId, animeText || content);

    if (saveResult.success) {
      // 更新任务状态为完成
      animationTasks[taskId].status = TASK_STATUS.COMPLETED;
      animationTasks[taskId].result = {
        filePath: saveResult.filePath
      };
    } else {
      throw new Error(saveResult.error || '保存HTML文件失败');
    }
  } catch (error) {
    console.error(`任务 ${taskId} 处理失败:`, error);

    // 更新任务状态为失败
    animationTasks[taskId].status = TASK_STATUS.FAILED;
    animationTasks[taskId].error = error.message;
  }
}

// 创建动画生成任务
export const createAnimation = async (req, res) => {
  try {
    const { content } = req.body;
    // 从请求中获取教师ID
    const teacherId = req.user?.system_teacher_id;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: '缺少动画内容'
      });
    }

    if (!teacherId) {
      console.warn('未找到教师ID，使用默认值');
    }

    // 创建任务ID
    const taskId = uuidv4();

    // 初始化任务状态
    animationTasks[taskId] = {
      status: TASK_STATUS.PENDING,
      content,
      teacherId,
      createdAt: new Date(),
      result: null,
      error: null
    };

    // 处理动画内容，去除交互要求部分
    let animeText = content;
    if (content.includes('，交互要求：')) {
      animeText = content.split('，交互要求：')[0]; // 只保留交互要求前的内容
    }

    // 异步处理任务
    processAnimationTask(taskId, content, teacherId, animeText).catch(err => {
      console.error(`异步处理任务 ${taskId} 失败:`, err);
    });

    // 返回任务ID
    res.json({
      success: true,
      message: '动画生成任务已创建',
      taskId
    });
  } catch (error) {
    console.error('创建动画生成任务失败:', error);
    res.status(500).json({
      success: false,
      message: `创建动画生成任务失败: ${error.message}`
    });
  }
};

// 获取动画记录列表
export const getAnimationRecords = async (req, res) => {
  try {
    const teacherId = req.user?.system_teacher_id;

    if (!teacherId) {
      return res.status(401).json({
        success: false,
        message: '未登录或无法获取教师ID'
      });
    }

    // 从数据库获取动画记录
    const [records] = await pool.execute(
      `SELECT id, teacher_id, anime_text, create_time
       FROM animation_records
       WHERE teacher_id = ? AND status = 1
       ORDER BY create_time DESC`,
      [teacherId]
    );

    res.json({
      success: true,
      data: records
    });
  } catch (error) {
    console.error('获取动画记录失败:', error);
    res.status(500).json({
      success: false,
      message: `获取动画记录失败: ${error.message}`
    });
  }
};

// 获取动画记录详情
export const getAnimationRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const teacherId = req.user?.system_teacher_id;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '缺少记录ID'
      });
    }

    // 从数据库获取动画记录
    const [records] = await pool.execute(
      `SELECT id, teacher_id, anime_text, anime_code, create_time
       FROM animation_records
       WHERE id = ? AND (teacher_id = ? OR ? IS NULL)`,
      [id, teacherId, teacherId]
    );

    if (records.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到记录'
      });
    }

    // 保存HTML文件
    const htmlContent = records[0].anime_code;
    const publicDir = path.join(__dirname, '..', '..', 'public');
    const filePath = path.join(publicDir, 'anime_make.html');
    fs.writeFileSync(filePath, htmlContent, 'utf8');

    res.json({
      success: true,
      data: {
        ...records[0],
        filePath: '/anime_make.html'
      }
    });
  } catch (error) {
    console.error('获取动画记录详情失败:', error);
    res.status(500).json({
      success: false,
      message: `获取动画记录详情失败: ${error.message}`
    });
  }
};

// 获取任务状态
export const getAnimationStatus = async (req, res) => {
  try {
    const { taskId } = req.params;

    if (!taskId || !animationTasks[taskId]) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }

    const task = animationTasks[taskId];

    // 根据任务状态返回不同的响应
    switch (task.status) {
      case TASK_STATUS.PENDING:
      case TASK_STATUS.PROCESSING:
        return res.json({
          success: true,
          status: task.status,
          message: task.status === TASK_STATUS.PENDING ? '任务等待中' : '正在生成动画'
        });

      case TASK_STATUS.COMPLETED:
        return res.json({
          success: true,
          status: task.status,
          message: '动画生成成功',
          filePath: task.result.filePath
        });

      case TASK_STATUS.FAILED:
        return res.json({
          success: false,
          status: task.status,
          message: `动画生成失败: ${task.error || '未知错误'}`
        });

      default:
        return res.status(500).json({
          success: false,
          message: '未知任务状态'
        });
    }
  } catch (error) {
    console.error('获取任务状态失败:', error);
    res.status(500).json({
      success: false,
      message: `获取任务状态失败: ${error.message}`
    });
  }
};


import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 获取教师开设的课程列表
export const getTeacherCourses = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const { teacherId } = req.params;

    console.log('开始获取教师课程列表，教师ID:', teacherId);

    // 验证teacherId
    if (!teacherId) {
      return res.status(400).json({
        success: false,
        message: '缺少教师ID参数'
      });
    }

    // 查询教师开设的课程
    const [courses] = await connection.execute(
      `SELECT
        id,
        course_code,
        course_name,
        description,
        credit,
        teacher_id,
        course_type,
        status,
        semester
       FROM courses
       WHERE teacher_id = ?
       ORDER BY semester DESC, course_code ASC`,
      [teacherId]
    );

    console.log('查询结果:', courses);

    // 处理课程数据
    const processedCourses = courses.map(course => ({
      ...course,
      course_type: course.course_type || 'required',
      status: course.status === 1,
      credit: course.credit || 0
    }));

    res.json({
      success: true,
      data: processedCourses
    });

  } catch (error) {
    console.error('获取教师课程列表失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取课程列表失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 更新课程状态
export const updateCourseStatus = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const { courseId } = req.params;
    const { status } = req.body;

    // 更新课程状态
    const [result] = await connection.execute(
      'UPDATE courses SET status = ? WHERE id = ?',
      [status ? 1 : 0, courseId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到指定课程'
      });
    }

    res.json({
      success: true,
      message: `课程已${status ? '开放' : '关闭'}`
    });

  } catch (error) {
    console.error('更新课程状态失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '更新课程状态失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 获取课程详细信息
export const getCourseDetails = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const { courseId } = req.params;

    // 查询课程详细信息
    const [courses] = await connection.execute(
      `SELECT
        c.*,
        cs.semester,
        cs.classroom,
        cs.total_hours,
        cs.current_week,
        cs.total_weeks,
        cs.start_date,
        cs.end_date
       FROM courses c
       LEFT JOIN class_schedules cs ON cs.course_id = c.id
       WHERE c.id = ?`,
      [courseId]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到指定课程'
      });
    }

    res.json({
      success: true,
      data: courses[0]
    });

  } catch (error) {
    console.error('获取课程详情失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取课程详情失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 获取下一个可用的课程代码
export const getNextCourseCode = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);

    // 获取最大的课程代码
    const [result] = await connection.execute(
      'SELECT MAX(course_code) as maxCode FROM courses'
    );

    let nextCode = '0001';
    if (result[0].maxCode) {
      // 将当前最大值加1
      const currentNumber = parseInt(result[0].maxCode);
      nextCode = (currentNumber + 1).toString().padStart(4, '0');
    }

    res.json({
      success: true,
      code: nextCode
    });
  } catch (error) {
    console.error('获取课程代码失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '获取课程代码失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 创建新课程
export const createCourse = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    const {
      course_code,
      course_name,
      description,
      credit,
      teacher_id,
      course_type,
      status,
      semester
    } = req.body;

    // 验证必要字段
    if (!course_code || !course_name || !teacher_id || !semester) {
      throw new Error('缺少必要字段');
    }

    // 检查课程代码是否已存在
    const [existing] = await connection.execute(
      'SELECT id FROM courses WHERE course_code = ?',
      [course_code]
    );

    if (existing.length > 0) {
      throw new Error('课程代码已存在');
    }

    // 创建课程
    const [result] = await connection.execute(
      `INSERT INTO courses (
        course_code, course_name, description,
        credit, teacher_id, course_type, status, semester
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        course_code,
        course_name,
        description || null,
        credit || 0,
        teacher_id,
        course_type || 'required',
        status ? 1 : 0,
        semester
      ]
    );

    await connection.commit();

    res.json({
      success: true,
      message: '课程创建成功',
      data: {
        id: result.insertId,
        course_code
      }
    });

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('创建课程失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '创建课程失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 更新课程
export const updateCourse = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    // 直接使用 req.params.courseId 而不是解构
    const courseId = req.params.courseId;
    console.log('更新课程 - 路由参数:', req.params);
    console.log('课程 ID:', courseId);

    const {
      course_name,
      description,
      credit,
      teacher_id,
      course_type,
      status,
      semester
    } = req.body;

    // 验证必要字段
    if (!course_name || !teacher_id || !semester) {
      throw new Error('缺少必要字段');
    }

    // 检查课程是否存在
    const [existing] = await connection.execute(
      'SELECT id FROM courses WHERE id = ?',
      [courseId]
    );

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到指定课程'
      });
    }

    // 更新课程
    const [result] = await connection.execute(
      `UPDATE courses SET
        course_name = ?,
        description = ?,
        credit = ?,
        teacher_id = ?,
        course_type = ?,
        status = ?,
        semester = ?
      WHERE id = ?`,
      [
        course_name,
        description || null,
        credit || 0,
        teacher_id,
        course_type || 'required',
        status ? 1 : 0,
        semester,
        courseId
      ]
    );

    await connection.commit();

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '更新失败，未找到指定课程'
      });
    }

    res.json({
      success: true,
      message: '课程更新成功'
    });

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('更新课程失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '更新课程失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 删除课程
export const deleteCourse = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    // 直接使用 req.params.courseId 而不是解构
    const courseId = req.params.courseId;
    console.log('删除课程 - 路由参数:', req.params);
    console.log('课程 ID:', courseId);

    // 检查课程是否存在
    const [existing] = await connection.execute(
      'SELECT id FROM courses WHERE id = ?',
      [courseId]
    );

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到指定课程'
      });
    }

    // 删除课程
    const [result] = await connection.execute(
      'DELETE FROM courses WHERE id = ?',
      [courseId]
    );

    await connection.commit();

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '删除失败，未找到指定课程'
      });
    }

    res.json({
      success: true,
      message: '课程删除成功'
    });

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('删除课程失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '删除课程失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};
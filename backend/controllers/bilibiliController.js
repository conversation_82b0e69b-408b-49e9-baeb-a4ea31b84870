import axios from 'axios';

// 代理B站图片
export const proxyImage = async (req, res) => {
  try {
    const { url } = req.query;
    if (!url) {
      return res.status(400).json({ message: '缺少图片URL参数' });
    }

    const response = await axios.get(url, {
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Referer': 'https://www.bilibili.com'
      }
    });

    // 设置响应头
    res.setHeader('Content-Type', response.headers['content-type']);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // 缓存一年

    // 转发图片流
    response.data.pipe(res);
  } catch (error) {
    console.error('图片代理失败:', error);
    res.status(500).json({ message: '图片加载失败' });
  }
};

// 计算字符串相似度（使用编辑距离算法）
function calculateSimilarity(str1, str2) {
  const matrix = [];
  
  // 初始化矩阵
  for (let i = 0; i <= str1.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= str2.length; j++) {
    matrix[0][j] = j;
  }
  
  // 填充矩阵
  for (let i = 1; i <= str1.length; i++) {
    for (let j = 1; j <= str2.length; j++) {
      if (str1[i-1] === str2[j-1]) {
        matrix[i][j] = matrix[i-1][j-1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i-1][j-1] + 1, // 替换
          matrix[i][j-1] + 1,   // 插入
          matrix[i-1][j] + 1    // 删除
        );
      }
    }
  }
  
  // 计算相似度得分（0-1之间，1表示完全匹配）
  const maxLength = Math.max(str1.length, str2.length);
  return 1 - (matrix[str1.length][str2.length] / maxLength);
}

// 计算视频的综合得分
function calculateVideoScore(video, keyword) {
  // 标题相似度得分（权重0.6）
  const titleScore = calculateSimilarity(video.title.toLowerCase(), keyword.toLowerCase()) * 0.6;
  
  // 播放量得分（权重0.4）
  // 假设最大播放量为1000万，用对数来平滑数值
  const viewScore = Math.min(Math.log10(video.play || video.view) / 7, 1) * 0.4;
  
  // 返回综合得分
  return titleScore + viewScore;
}

// 处理视频时长格式
function formatDuration(duration) {
  if (!duration) return '00:00';
  
  // 如果已经是格式化的时间，直接返回
  if (duration.includes(':')) return duration;
  
  // 将秒数转换为时分秒格式
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// 处理播放量格式
function formatPlayCount(count) {
  if (!count) return 0;
  // 如果是字符串中包含"万"，转换为数字
  if (typeof count === 'string' && count.includes('万')) {
    return parseFloat(count) * 10000;
  }
  return parseInt(count);
}

// B站视频搜索
export const searchVideos = async (req, res) => {
  try {
    const { keyword, page = 1, order = 'score', pageSize = 24 } = req.query;
    
    console.log('\n=== B站视频搜索信息 ===');
    console.log(`搜索关键词: ${keyword}`);
    console.log(`页码: ${page}`);
    console.log(`每页视频数: ${pageSize}`);
    console.log('------------------------');

    // 需要返回的视频数量
    const numToReturn = parseInt(pageSize);
    
    // 尝试获取两页的数据以确保有足够的视频
    let allVideos = [];
    let currentPage = parseInt(page);

    // 构建B站搜索URL
    const searchUrl = `https://api.bilibili.com/x/web-interface/search/type`;
    
    // 获取第一页数据
    const response = await axios.get(searchUrl, {
      params: {
        keyword,
        page: currentPage,
        search_type: 'video',
        order: 'totalrank',
        platform: 'pc',
        page_size: 42 // 从B站请求更多视频，确保经过筛选后仍有足够的24个视频
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cookie': 'buvid3=8B57D2E6-DCF6-C194-E477-A43235E5566937150infoc',
        'Referer': 'https://www.bilibili.com'
      }
    });

    // 处理响应
    if (response.data.code === 0 && response.data.data?.result) {
      // 记录原始结果数量
      const originalCount = response.data.data.result.length;
      console.log(`B站返回的原始视频数: ${originalCount}`);
      
      // 处理视频数据并计算得分
      let videos = response.data.data.result.map(video => {
        // 处理视频数据
        const processedVideo = {
          bvid: video.bvid,
          title: video.title.replace(/<[^>]+>/g, ''), // 移除HTML标签
          pic: video.pic || 'https://i0.hdslb.com/bfs/archive/default.jpg', // 使用默认封面
          author: video.author,
          play: formatPlayCount(video.play || video.view),
          duration: formatDuration(video.duration),
          pubdate: new Date(video.pubdate * 1000).toLocaleDateString('zh-CN')
        };

        // 计算视频得分
        processedVideo.score = calculateVideoScore(processedVideo, keyword);
        return processedVideo;
      });

      console.log(`处理后的视频数: ${videos.length}`);

      // 如果第一页数据不足，并且不是第一页(防止无限请求)，则获取下一页数据
      if (videos.length < numToReturn && currentPage > 1) {
        try {
          const nextPageResponse = await axios.get(searchUrl, {
            params: {
              keyword,
              page: currentPage + 1,
              search_type: 'video',
              order: 'totalrank',
              platform: 'pc',
              page_size: 42
            },
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              'Accept': 'application/json, text/plain, */*',
              'Accept-Language': 'zh-CN,zh;q=0.9',
              'Cookie': 'buvid3=8B57D2E6-DCF6-C194-E477-A43235E5566937150infoc',
              'Referer': 'https://www.bilibili.com'
            }
          });
          
          if (nextPageResponse.data.code === 0 && nextPageResponse.data.data?.result) {
            const nextPageVideos = nextPageResponse.data.data.result.map(video => {
              const processedVideo = {
                bvid: video.bvid,
                title: video.title.replace(/<[^>]+>/g, ''),
                pic: video.pic || 'https://i0.hdslb.com/bfs/archive/default.jpg',
                author: video.author,
                play: formatPlayCount(video.play || video.view),
                duration: formatDuration(video.duration),
                pubdate: new Date(video.pubdate * 1000).toLocaleDateString('zh-CN')
              };
              processedVideo.score = calculateVideoScore(processedVideo, keyword);
              return processedVideo;
            });
            
            // 合并两页的视频并去重
            const allVideoIds = new Set(videos.map(v => v.bvid));
            const uniqueNextPageVideos = nextPageVideos.filter(v => !allVideoIds.has(v.bvid));
            videos = [...videos, ...uniqueNextPageVideos];
            
            console.log(`合并后总视频数: ${videos.length}`);
          }
        } catch (nextPageError) {
          console.error('获取下一页数据失败:', nextPageError.message);
        }
      }

      // 按关键词匹配度排序
      videos.sort((a, b) => b.score - a.score);

      // 返回用户请求的数量，默认为24个
      const finalVideos = videos.slice(0, numToReturn);
      console.log(`最终返回视频数: ${finalVideos.length}/${numToReturn}`);

      // 打印详细信息
      console.log('\n=== 搜索结果统计 ===');
      console.log(`总视频数量: ${response.data.data.numResults || 0}`);
      console.log(`当前页视频数量: ${finalVideos.length}`);
      console.log('------------------------');

      res.json({
        code: 0,
        data: finalVideos,
        total: response.data.data.numResults || videos.length,
        message: 'success'
      });
    } else {
      throw new Error(response.data.message || '未找到视频结果');
    }
  } catch (error) {
    console.error('\n=== 搜索错误 ===');
    console.error('错误信息:', error.message);
    console.error('------------------------\n');

    res.status(500).json({
      code: 500,
      message: '搜索失败，请稍后重试',
      error: error.message
    });
  }
};

// 获取视频详情
export const getVideoDetail = async (req, res) => {
  try {
    const { bvid } = req.query;
    
    if (!bvid) {
      return res.status(400).json({
        code: 400,
        message: '请提供视频BV号'
      });
    }

    console.log('\n=== 获取视频详情 ===');
    console.log(`视频BV号: ${bvid}`);
    console.log('------------------------');

    const videoInfoUrl = `https://api.bilibili.com/x/web-interface/view?bvid=${bvid}`;
    const response = await axios.get(videoInfoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.bilibili.com'
      }
    });

    if (response.data.code === 0 && response.data.data) {
      res.json({
        code: 0,
        data: response.data.data,
        message: 'success'
      });
    } else {
      throw new Error(response.data.message || '获取视频详情失败');
    }
  } catch (error) {
    console.error('\n=== 获取视频详情错误 ===');
    console.error('错误信息:', error.message);
    console.error('------------------------\n');

    res.status(500).json({
      code: 500,
      message: '获取视频详情失败，请稍后重试',
      error: error.message
    });
  }
}; 
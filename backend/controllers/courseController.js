import pool from '../config/db.js';

// 创建课程和课表
export const createCourse = async (req, res) => {
  let connection;
  try {
    connection = await pool.getConnection();
    await connection.beginTransaction();

    const {
      course_code,
      course_name,
      description,
      credit,
      teacher_id,
      course_type,
      semester,
      start_date,
      end_date,
      total_weeks,
      total_periods,
      weekday,
      period,
      classroom
    } = req.body;

    // 验证必要字段
    if (!course_code || !course_name || !teacher_id) {
      throw new Error('缺少必要字段');
    }

    // 1. 创建课程记录
    const [courseResult] = await connection.query(
      `INSERT INTO courses (
        course_code, course_name, description,
        credit, teacher_id, course_type, status
      ) VALUES (?, ?, ?, ?, ?, ?, 1)`,
      [
        course_code,
        course_name,
        description || null,
        credit || 0,
        teacher_id,
        course_type || 'required'
      ]
    );

    const course_id = courseResult.insertId;

    // 2. 创建课表记录
    const [scheduleResult] = await connection.query(
      `INSERT INTO class_schedules (
        user_id, course_id, schedule_name, semester,
        is_current, current_week, total_weeks,
        start_date, end_date, total_periods,
        weekday, period, classroom
      ) VALUES (?, ?, ?, ?, 1, 1, ?, ?, ?, ?, ?, ?, ?)`,
      [
        teacher_id,
        course_id,
        `${course_name}-${semester}`,
        semester,
        total_weeks,
        start_date,
        end_date,
        total_periods,
        weekday,
        period,
        classroom
      ]
    );

    await connection.commit();

    res.json({
      success: true,
      message: '课程创建成功',
      data: {
        course_id,
        schedule_id: scheduleResult.insertId
      }
    });

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('创建课程失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '创建课程失败'
    });
  } finally {
    if (connection) {
      connection.release();
    }
  }
};

// 创建课程班级
export const createCourseClass = async (req, res) => {
  const { course_id, class_id, semester, max_students } = req.body;

  try {
    const [result] = await pool.query(
      `INSERT INTO course_classes (course_id, class_id, semester, max_students) 
       VALUES (?, ?, ?, ?)`,
      [course_id, class_id, semester, max_students]
    );

    res.json({
      success: true,
      message: '课程班级创建成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建课程班级失败:', error);
    res.status(500).json({
      success: false,
      message: '创建课程班级失败'
    });
  }
};

// 获取教师的课程列表（包含课表信息）
export const getTeacherCourses = async (req, res) => {
  const teacherId = req.params.teacherId;
  
  console.log('开始获取教师课程列表，教师ID:', teacherId);
  
  try {
    // 验证teacherId
    if (!teacherId) {
      console.error('缺少teacherId参数');
      return res.status(400).json({
        success: false,
        message: '缺少教师ID参数'
      });
    }

    // 获取课程基本信息和课表，使用正确的关联字段
    const [courses] = await pool.query(
      `SELECT 
        c.course_code,
        c.course_name,
        c.description,
        c.credit,
        c.course_type,
        c.semester,
        MAX(cs.total_periods) as total_periods
       FROM courses c
       LEFT JOIN class_schedules cs ON c.course_code = cs.course_id AND cs.user_id = c.teacher_id
       WHERE c.teacher_id = ? AND c.status = 1
       GROUP BY c.course_code, c.course_name, c.description, c.credit, c.course_type, c.semester`,
      [teacherId]
    );

    console.log('查询到的课程数量:', courses.length);

    res.json({
      success: true,
      data: courses
    });

  } catch (error) {
    console.error('获取课程列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程列表失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 获取教师当前课表
export const getCurrentSchedule = async (req, res) => {
  const systemTeacherId = req.params.teacherId;
  
  try {
    // 简化日志输出
    console.log(`获取教师ID=${systemTeacherId}的当前课表`);

    // 获取课表记录和课程信息
    const [schedules] = await pool.execute(
      `SELECT 
        cs.*,
        c.course_name,
        c.course_type,
        c.credit,
        s.allocation,
        s.total as total_periods
       FROM class_schedules cs
       JOIN courses c ON cs.course_id = c.course_code
       LEFT JOIN syllabus s ON s.course_code = c.course_code 
         AND s.teacher_id = cs.user_id
       WHERE cs.user_id = ? AND cs.is_current = 1`,
      [systemTeacherId]
    );
    
    if (schedules.length === 0) {
      return res.json({
        success: true,
        data: [],
        message: '未找到课表'
      });
    }

    // 简化日志输出
    console.log(`找到 ${schedules.length} 个课表记录`);
    
    // 处理每个课表记录
    const processedSchedules = schedules.map(schedule => {
      // 简化日志输出，只在调试模式下保留详细信息
      if (process.env.NODE_ENV === 'development') {
        console.log(`处理课表: ID=${schedule.id}, 课程=${schedule.course_id}`);
      }
      
      // 解析教学周JSON
      const teachingWeeks = schedule.teaching_weeks ? 
        (typeof schedule.teaching_weeks === 'string' ? 
          JSON.parse(schedule.teaching_weeks) : 
          schedule.teaching_weeks) :
        { start: 1, end: 20, exclude: [] };
      
      // 解析上课时间JSON，处理两种不同的数据格式
      let classTimes;
      let classTime;
      
      if (schedule.class_time) {
        // 将字符串转换为JSON对象
        let parsedClassTime;
        try {
          parsedClassTime = typeof schedule.class_time === 'string' ? 
            JSON.parse(schedule.class_time) : schedule.class_time;
        } catch (error) {
          console.error('解析class_time失败:', error);
          parsedClassTime = { day: 1, periods: [1], location: '' };
        }
        
        // 检测是数组格式还是对象格式
        if (Array.isArray(parsedClassTime)) {
          // 仅在开发环境输出详细日志
          if (process.env.NODE_ENV === 'development') {
            console.log('识别为数组格式的class_times');
          }
          classTimes = parsedClassTime;
          // 为了向后兼容，取第一个时间作为主要显示时间
          classTime = parsedClassTime[0] || { day: 1, periods: [1], location: '' };
        } else {
          // 旧格式，直接使用对象
          if (process.env.NODE_ENV === 'development') {
            console.log('识别为对象格式的class_time');
          }
          classTime = parsedClassTime;
          // 将旧格式转换为新格式数组
          classTimes = [parsedClassTime];
        }
      } else {
        // 默认值
        if (process.env.NODE_ENV === 'development') {
          console.log('未找到class_time数据，使用默认值');
        }
        classTime = { day: 1, periods: [1], location: '' };
        classTimes = [{ day: 1, periods: [1], location: '' }];
      }

      // 仅在开发环境输出详细信息
      if (process.env.NODE_ENV === 'development') {
        console.log('处理后的class_times:', classTimes);
        console.log('向后兼容的classTime:', classTime);
      }

      // 解析课时分配数据
      let allocation = {};
      try {
        allocation = schedule.allocation ? JSON.parse(schedule.allocation) : {};
      } catch (e) {
        console.warn('解析课时分配数据失败:', e);
      }

      // 计算当前教学周
      const startDate = schedule.start_date ? new Date(schedule.start_date) : new Date();
      const today = new Date();
      const diffTime = Math.abs(today - startDate);
      const currentWeek = Math.min(
        Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7)),
        schedule.total_weeks || 20
      );

      // 检查当前周是否在教学周范围内
      const isInRange = currentWeek >= teachingWeeks.start && 
                       currentWeek <= teachingWeeks.end && 
                       !teachingWeeks.exclude.includes(currentWeek);

      const processedSchedule = {
        id: schedule.id,
        course_id: schedule.course_id,
        course_name: schedule.course_name,
        schedule_name: schedule.schedule_name,
        semester: schedule.semester,
        total_weeks: schedule.total_weeks || 20,
        current_week: currentWeek,
        // 同时保留新旧两种字段格式，确保兼容性
        weekday: classTime.day,
        periods: classTime.periods,
        location: classTime.location,
        class_times: classTimes,
        teaching_weeks: teachingWeeks,
        total_periods: schedule.total_periods || 32,
        course_type: schedule.course_type || 'required',
        credit: schedule.credit || 0,
        is_active: isInRange,
        allocation: allocation
      };
      
      return processedSchedule;
    });
    
    // 仅输出简单统计信息
    console.log(`返回 ${processedSchedules.length} 个处理后的课表数据`);
    
    res.json({
      success: true,
      data: processedSchedules
    });

  } catch (error) {
    console.error('获取当前课表失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取课表信息失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 更新课表信息
export const updateSchedule = async (req, res) => {
  const { id } = req.params;
  const updates = req.body;
  
  try {
    // 如果有class_time或teaching_weeks字段，确保它们是JSON格式
    if (updates.class_times && typeof updates.class_times === 'object') {
      updates.class_time = JSON.stringify(updates.class_times);
      delete updates.class_times; // 删除class_times字段，因为数据库中是class_time
    } else if (updates.class_time && typeof updates.class_time === 'object') {
      updates.class_time = JSON.stringify(updates.class_time);
    }
    
    if (updates.teaching_weeks && typeof updates.teaching_weeks === 'object') {
      updates.teaching_weeks = JSON.stringify(updates.teaching_weeks);
    }

    const [result] = await pool.execute(
      'UPDATE class_schedules SET ? WHERE id = ?',
      [updates, id]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到指定课表'
      });
    }
    
    res.json({
      success: true,
      message: '课表更新成功'
    });
  } catch (error) {
    console.error('更新课表失败:', error);
    res.status(500).json({
      success: false,
      message: '更新课表失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 获取历史课表
export const getScheduleHistory = async (req, res) => {
  const teacherId = req.params.teacherId;
  
  try {
    const [schedules] = await pool.execute(
      `SELECT 
        cs.*,
        c.course_name,
        c.course_type,
        c.credit
       FROM class_schedules cs
       JOIN courses c ON cs.course_id = c.course_code
       WHERE cs.user_id = ?
       ORDER BY cs.created_at DESC`,
      [teacherId]
    );
    
    // 处理JSON字段
    const processedSchedules = schedules.map(schedule => {
      // 解析教学周
      const teachingWeeks = JSON.parse(schedule.teaching_weeks || '{"start": 1, "end": 20, "exclude": []}');
      
      // 解析上课时间JSON，处理两种不同的数据格式
      let classTimes;
      let classTime;
      
      if (schedule.class_time) {
        // 将字符串转换为JSON对象
        const parsedClassTime = JSON.parse(schedule.class_time);
        
        // 检测是数组格式还是对象格式
        if (Array.isArray(parsedClassTime)) {
          classTimes = parsedClassTime;
          classTime = parsedClassTime[0] || { day: 1, periods: [1], location: '' };
        } else {
          classTime = parsedClassTime;
          classTimes = [parsedClassTime];
        }
      } else {
        classTime = { day: 1, periods: [1], location: '' };
        classTimes = [{ day: 1, periods: [1], location: '' }];
      }
      
      return {
        ...schedule,
        teaching_weeks: teachingWeeks,
        class_time: classTime,
        class_times: classTimes,
        // 保留兼容性字段
        weekday: classTime.day,
        periods: classTime.periods,
        location: classTime.location
      };
    });

    res.json({
      success: true,
      data: processedSchedules
    });
  } catch (error) {
    console.error('获取历史课表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取历史课表失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 修改 testDatabaseQuery 函数
export const testDatabaseQuery = async (req, res) => {
  try {
    const teacherId = req.user?.system_teacher_id;
    
    if (!teacherId) {
      return res.status(400).json({
        success: false,
        message: '未找到教师ID信息'
      });
    }

    // 只查询必要的字段
    const [result] = await pool.query(
      `SELECT 
        s.allocation,
        s.total
       FROM syllabus s
       WHERE s.teacher_id = ?`,
      [teacherId]
    );
    
    // 只处理第一条记录的 allocation 字段
    let allocation = {};
    let total = 0;
    
    if (result.length > 0) {
      try {
        allocation = result[0].allocation ? 
          (typeof result[0].allocation === 'string' ? 
            JSON.parse(result[0].allocation) : 
            result[0].allocation) : 
          {};
        total = result[0].total || 0;
      } catch (e) {
        console.warn('解析 allocation 数据失败:', e);
      }
    }

    res.json({
      success: true,
      data: {
        schedules: [], // 保持原有结构
        structure: {
          allocation: allocation,
          total: total
        }
      }
    });
  } catch (error) {
    console.error('测试查询失败:', error);
    res.status(500).json({
      success: false,
      message: '测试查询失败',
      error: error.message
    });
  }
};

// 创建课程表
export const createSchedule = async (req, res) => {
  let connection;
  try {
    connection = await pool.getConnection();
    await connection.beginTransaction();

    const {
      course_id,
      total_weeks,
      start_date,
      total_periods,
      is_current,
      teaching_weeks,
      class_times
    } = req.body;

    // 验证必要字段
    if (!course_id || !start_date || !class_times || !class_times.length) {
      throw new Error('缺少必要字段');
    }

    // 获取教师ID
    const user_id = req.user?.system_teacher_id;
    if (!user_id) {
      throw new Error('未找到教师ID');
    }

    // 获取课程信息以生成课表名称
    const [courseInfo] = await connection.execute(
      'SELECT course_name FROM courses WHERE course_code = ?',
      [course_id]
    );

    if (courseInfo.length === 0) {
      throw new Error('未找到课程信息');
    }

    // 格式化日期为 YYYY-MM-DD
    const startDate = new Date(start_date);
    const year = startDate.getFullYear();
    const month = String(startDate.getMonth() + 1).padStart(2, '0');
    const day = String(startDate.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;

    // 生成学期信息
    const semester = parseInt(month) >= 2 && parseInt(month) <= 7 ? 
      `${year}春季` : 
      `${year}秋季`;

    // 如果设置为当前使用，需要先将其他课表设置为非当前
    if (is_current) {
      await connection.execute(
        'UPDATE class_schedules SET is_current = 0 WHERE user_id = ? AND course_id = ?',
        [user_id, course_id]
      );
    }

    // 创建课表记录
    const [result] = await connection.execute(
      `INSERT INTO class_schedules (
        user_id,
        course_id,
        schedule_name,
        semester,
        is_current,
        total_weeks,
        start_date,
        teaching_weeks,
        class_time,
        total_periods,
        completed_periods
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        user_id,
        course_id,
        `${courseInfo[0].course_name}-${semester}`,
        semester,
        is_current ? 1 : 0,
        total_weeks,
        formattedDate,
        JSON.stringify(teaching_weeks),
        JSON.stringify(class_times),
        total_periods,
        0
      ]
    );

    await connection.commit();

    res.json({
      success: true,
      message: '课程表创建成功',
      data: {
        id: result.insertId
      }
    });

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('创建课程表失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '创建课程表失败'
    });
  } finally {
    if (connection) {
      connection.release();
    }
  }
};

// 获取指定学期的课表
export const getScheduleBySemester = async (req, res) => {
  const systemTeacherId = req.params.teacherId;
  const semester = req.query.semester;
  
  try {
    // 简化日志输出
    console.log(`获取教师ID=${systemTeacherId}的${semester}学期课表`);

    if (!semester) {
      return res.status(400).json({
        success: false,
        message: '缺少学期参数'
      });
    }

    // 获取课表记录和课程信息
    const [schedules] = await pool.execute(
      `SELECT 
        cs.*,
        c.course_name,
        c.course_type,
        c.credit,
        s.allocation,
        s.total as total_periods
       FROM class_schedules cs
       JOIN courses c ON cs.course_id = c.course_code
       LEFT JOIN syllabus s ON s.course_code = c.course_code 
         AND s.teacher_id = cs.user_id
       WHERE cs.user_id = ? AND cs.semester = ?`,
      [systemTeacherId, semester]
    );
    
    if (schedules.length === 0) {
      return res.json({
        success: true,
        data: [],
        message: '未找到该学期的课表'
      });
    }

    // 简化日志输出
    console.log(`找到 ${schedules.length} 个${semester}学期的课表记录`);

    // 处理每个课表记录
    const processedSchedules = schedules.map(schedule => {
      // 简化日志输出，只在开发环境下打印详细日志
      if (process.env.NODE_ENV === 'development') {
        console.log(`处理学期课表: ID=${schedule.id}, 课程=${schedule.course_id}`);
      }
      
      // 解析教学周JSON
      const teachingWeeks = schedule.teaching_weeks ? 
        (typeof schedule.teaching_weeks === 'string' ? 
          JSON.parse(schedule.teaching_weeks) : 
          schedule.teaching_weeks) :
        { start: 1, end: 20, exclude: [] };
      
      // 解析上课时间JSON，处理两种不同的数据格式
      let classTimes;
      let classTime;
      
      if (schedule.class_time) {
        // 将字符串转换为JSON对象
        let parsedClassTime;
        try {
          parsedClassTime = typeof schedule.class_time === 'string' ? 
            JSON.parse(schedule.class_time) : schedule.class_time;
        } catch (error) {
          console.error('解析class_time失败:', error);
          parsedClassTime = { day: 1, periods: [1], location: '' };
        }
        
        // 检测是数组格式还是对象格式
        if (Array.isArray(parsedClassTime)) {
          // 仅在开发环境输出详细日志
          if (process.env.NODE_ENV === 'development') {
            console.log('识别为数组格式的class_times');
          }
          classTimes = parsedClassTime;
          // 为了向后兼容，取第一个时间作为主要显示时间
          classTime = parsedClassTime[0] || { day: 1, periods: [1], location: '' };
        } else {
          // 旧格式，直接使用对象
          if (process.env.NODE_ENV === 'development') {
            console.log('识别为对象格式的class_time');
          }
          classTime = parsedClassTime;
          // 将旧格式转换为新格式数组
          classTimes = [parsedClassTime];
        }
      } else {
        // 默认值
        if (process.env.NODE_ENV === 'development') {
          console.log('未找到class_time数据，使用默认值');
        }
        classTime = { day: 1, periods: [1], location: '' };
        classTimes = [{ day: 1, periods: [1], location: '' }];
      }

      // 解析课时分配数据
      let allocation = {};
      try {
        allocation = schedule.allocation ? JSON.parse(schedule.allocation) : {};
      } catch (e) {
        console.warn('解析课时分配数据失败:', e);
      }

      // 计算当前教学周
      const startDate = schedule.start_date ? new Date(schedule.start_date) : new Date();
      const today = new Date();
      const diffTime = Math.abs(today - startDate);
      const currentWeek = Math.min(
        Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7)),
        schedule.total_weeks || 20
      );

      // 检查当前周是否在教学周范围内
      const isInRange = currentWeek >= teachingWeeks.start && 
                       currentWeek <= teachingWeeks.end && 
                       !teachingWeeks.exclude.includes(currentWeek);

      const processedSchedule = {
        id: schedule.id,
        course_id: schedule.course_id,
        course_name: schedule.course_name,
        schedule_name: schedule.schedule_name,
        semester: schedule.semester,
        total_weeks: schedule.total_weeks || 20,
        current_week: currentWeek,
        // 同时保留新旧两种字段格式，确保兼容性
        weekday: classTime.day,
        periods: classTime.periods,
        location: classTime.location,
        class_times: classTimes,
        teaching_weeks: teachingWeeks,
        total_periods: schedule.total_periods || 32,
        course_type: schedule.course_type || 'required',
        credit: schedule.credit || 0,
        is_active: isInRange,
        allocation: allocation
      };
      
      return processedSchedule;
    });
    
    // 仅输出简单统计信息
    console.log(`返回 ${processedSchedules.length} 个处理后的${semester}学期课表数据`);
    
    res.json({
      success: true,
      data: processedSchedules
    });

  } catch (error) {
    console.error('获取指定学期课表失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取课表信息失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 删除课程表中的特定上课时间
export const deleteClassTime = async (req, res) => {
  const { scheduleId } = req.params;
  const { dayToRemove, periodsToRemove } = req.body;
  
  // 简化日志输出，只保留关键信息
  console.log(`正在删除课程表ID=${scheduleId}的上课时间: 星期${dayToRemove}，节次${periodsToRemove.join(',')}`);
  
  if (!scheduleId || !dayToRemove || !periodsToRemove || !Array.isArray(periodsToRemove)) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数'
    });
  }

  let connection;
  try {
    connection = await pool.getConnection();
    await connection.beginTransaction();

    // 首先获取课程表信息
    const [schedules] = await connection.execute(
      'SELECT * FROM class_schedules WHERE id = ?',
      [scheduleId]
    );

    if (schedules.length === 0) {
      await connection.rollback();
      return res.status(404).json({
        success: false,
        message: '未找到指定课程表'
      });
    }

    const schedule = schedules[0];
    let classTime;
    
    // 增强JSON解析错误处理
    try {
      // 仅在开发环境打印原始数据
      if (process.env.NODE_ENV === 'development') {
        console.log('解析前的class_time数据:', schedule.class_time);
      }
      
      // 检查是否为空或null
      if (!schedule.class_time) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: '课程时间数据为空'
        });
      }
      
      // 确保class_time是有效的JSON字符串
      if (typeof schedule.class_time === 'string') {
        classTime = JSON.parse(schedule.class_time);
      } else {
        // 已经是对象，无需解析
        classTime = schedule.class_time;
      }
    } catch (error) {
      console.error('解析class_time失败:', error.message);
      
      await connection.rollback();
      return res.status(500).json({
        success: false,
        message: '解析课程时间数据失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    // 处理不同的数据格式
    if (Array.isArray(classTime)) {
      // 新格式：数组形式
      // 查找匹配的时间段并移除
      const filteredTimes = classTime.filter(time => {
        // 如果日期不同，保留
        if (time.day !== parseInt(dayToRemove)) return true;
        
        // 如果日期相同但节次完全不同，保留
        // 如果有部分节次相同，则需要从periods中移除匹配的节次
        if (time.day === parseInt(dayToRemove)) {
          // 过滤掉需要移除的periods
          time.periods = time.periods.filter(
            period => !periodsToRemove.includes(period)
          );
          
          // 如果还有剩余periods，保留此时间段
          return time.periods.length > 0;
        }
        
        return true;
      });
      
      // 更新课程表
      await connection.execute(
        'UPDATE class_schedules SET class_time = ? WHERE id = ?',
        [JSON.stringify(filteredTimes), scheduleId]
      );
      
      console.log(`成功更新课程表时间: ${filteredTimes.length}个时间段保留`);
    } else {
      // 旧格式：单一对象
      // 如果不是要删除的日期，直接返回
      if (classTime.day !== parseInt(dayToRemove)) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: '无法删除：课程时间与请求不匹配'
        });
      }
      
      // 过滤掉要删除的节次
      classTime.periods = classTime.periods.filter(
        period => !periodsToRemove.includes(period)
      );
      
      // 如果没有剩余节次，返回错误
      if (classTime.periods.length === 0) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: '无法删除：这是课程的唯一时间段，请使用删除课程功能'
        });
      }
      
      // 更新课程表
      await connection.execute(
        'UPDATE class_schedules SET class_time = ? WHERE id = ?',
        [JSON.stringify(classTime), scheduleId]
      );
      
      console.log(`成功更新课程表时间: 保留${classTime.periods.length}个节次`);
    }

    await connection.commit();
    
    res.json({
      success: true,
      message: '成功删除指定上课时间'
    });
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('删除上课时间失败:', error.message);
    res.status(500).json({
      success: false,
      message: '删除上课时间失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  } finally {
    if (connection) {
      connection.release();
    }
  }
}; 
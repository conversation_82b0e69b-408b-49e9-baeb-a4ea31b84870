import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';
import { sharedResourceModel } from '../models/sharedResourceModel.js';
import axios from 'axios';

// 从B站URL中提取视频ID
const extractBilibiliVideoId = (url) => {
  if (!url) return null;

  // 尝试匹配BV号
  const bvMatch = url.match(/\/video\/(BV[a-zA-Z0-9]+)/);
  if (bvMatch && bvMatch[1]) {
    return bvMatch[1];
  }

  // 尝试匹配av号
  const avMatch = url.match(/\/video\/av(\d+)/);
  if (avMatch && avMatch[1]) {
    return `av${avMatch[1]}`;
  }

  return null;
};

// 获取B站视频封面
const getBilibiliCover = async (videoUrl) => {
  try {
    const videoId = extractBilibiliVideoId(videoUrl);
    if (!videoId) return null;

    // 使用B站API获取视频信息
    const apiUrl = `https://api.bilibili.com/x/web-interface/view?bvid=${videoId}`;
    const response = await axios.get(apiUrl);

    if (response.data && response.data.code === 0 && response.data.data) {
      return response.data.data.pic || null;
    }

    return null;
  } catch (error) {
    console.error('获取B站视频封面失败:', error);
    return null;
  }
};

// 获取分享给学生的视频列表
export const getStudentSharedVideos = async (req, res) => {
  const pool = mysql.createPool(dbConfig);

  try {
    const studentId = req.user?.student_id; // 从认证中间件中获取学生ID

    if (!studentId) {
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }

    console.log(`正在获取学生(ID: ${studentId})的分享视频列表`);

    // 使用模型方法获取分享给学生的视频
    const videos = await sharedResourceModel.getStudentSharedVideos(studentId);

    // 格式化返回数据，添加额外信息
    const formattedVideos = await Promise.all(videos.map(async video => {
      // 如果是B站视频且没有封面，尝试获取B站封面
      let coverUrl = video.cover_url;
      if (!coverUrl && video.source === 'bilibili') {
        const biliCover = await getBilibiliCover(video.video_url);
        if (biliCover) {
          coverUrl = biliCover;
          // 更新数据库中的封面URL
          try {
            await pool.query(
              'UPDATE shared_videos SET cover_url = ? WHERE id = ?',
              [biliCover, video.id]
            );
            console.log(`已更新视频ID ${video.id} 的封面URL`);
          } catch (err) {
            console.error('更新视频封面URL失败:', err);
          }
        }
      }

      return {
        id: video.id,
        title: video.title,
        videoUrl: video.video_url,
        coverUrl: coverUrl || 'https://img.yzcdn.cn/vant/cat.jpeg', // 默认封面
        source: video.source,
        className: video.class_name,
        teacherRemark: video.remark,
        isRequired: video.is_required === 1,
        viewed: video.viewed === 1,
        viewTime: video.view_time,
        createTime: video.create_time
      };
    }));

    res.json({
      success: true,
      data: formattedVideos
    });
  } catch (error) {
    console.error('获取学生分享视频列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分享视频列表失败',
      error: error.message
    });
  } finally {
    pool.end();
  }
};

// 获取分享视频详情
export const getSharedVideoDetail = async (req, res) => {
  const pool = mysql.createPool(dbConfig);

  try {
    const { videoId } = req.params;
    const studentId = req.user?.student_id;

    if (!studentId) {
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }

    console.log(`正在获取分享视频(ID: ${videoId})的详情，学生ID: ${studentId}`);

    // 查询视频详情
    const [videos] = await pool.query(
      `SELECT sv.*, cc.class_name, scs.is_required, scs.viewed, scs.view_time,
       t.name as teacher_name
       FROM shared_videos sv
       JOIN shared_video_students scs ON sv.id = scs.shared_video_id
       JOIN course_classes cc ON sv.class_id = cc.id
       JOIN teaching_assistant t ON sv.teacher_id = t.system_teacher_id
       WHERE sv.id = ? AND scs.student_id = ? AND sv.status = 1`,
      [videoId, studentId]
    );

    if (videos.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到视频或无权访问'
      });
    }

    const video = videos[0];

    // 如果是B站视频且没有封面，尝试获取B站封面
    let coverUrl = video.cover_url;
    if (!coverUrl && video.source === 'bilibili') {
      const biliCover = await getBilibiliCover(video.video_url);
      if (biliCover) {
        coverUrl = biliCover;
        // 更新数据库中的封面URL
        try {
          await pool.query(
            'UPDATE shared_videos SET cover_url = ? WHERE id = ?',
            [biliCover, video.id]
          );
          console.log(`已更新视频ID ${video.id} 的封面URL`);
        } catch (err) {
          console.error('更新视频封面URL失败:', err);
        }
      }
    }

    // 格式化返回数据
    const videoDetail = {
      id: video.id,
      title: video.title,
      videoUrl: video.video_url,
      coverUrl: coverUrl || 'https://img.yzcdn.cn/vant/cat.jpeg', // 默认封面
      source: video.source,
      className: video.class_name,
      teacherName: video.teacher_name,
      teacherRemark: video.remark,
      isRequired: video.is_required === 1,
      viewed: video.viewed === 1,
      viewTime: video.view_time,
      createTime: video.create_time
    };

    res.json({
      success: true,
      data: videoDetail
    });
  } catch (error) {
    console.error('获取分享视频详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分享视频详情失败',
      error: error.message
    });
  } finally {
    pool.end();
  }
};

// 标记视频为已观看
export const markVideoAsViewed = async (req, res) => {
  const pool = mysql.createPool(dbConfig);

  try {
    const { videoId } = req.params;
    const studentId = req.user?.student_id;

    if (!studentId) {
      return res.status(401).json({
        success: false,
        message: '未授权，请先登录'
      });
    }

    console.log(`正在标记视频(ID: ${videoId})为已观看，学生ID: ${studentId}`);

    // 检查视频是否存在且分享给了该学生
    const [videoCheck] = await pool.query(
      `SELECT 1 FROM shared_video_students
       WHERE shared_video_id = ? AND student_id = ?`,
      [videoId, studentId]
    );

    if (videoCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到视频或无权访问'
      });
    }

    // 更新为已观看状态
    await pool.query(
      `UPDATE shared_video_students
       SET viewed = 1, view_time = NOW()
       WHERE shared_video_id = ? AND student_id = ?`,
      [videoId, studentId]
    );

    res.json({
      success: true,
      message: '已成功标记为已观看'
    });
  } catch (error) {
    console.error('标记视频为已观看失败:', error);
    res.status(500).json({
      success: false,
      message: '标记视频为已观看失败',
      error: error.message
    });
  } finally {
    pool.end();
  }
};

import pool from '../config/db.js';

// 创建一个简单的内存缓存
const cache = new Map();
const CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存时间

// 定期清理缓存的函数
const cleanCache = () => {
  const now = Date.now();
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      cache.delete(key);
    }
  }
};

// 每15分钟执行一次缓存清理
setInterval(cleanCache, 15 * 60 * 1000);

// 将JSON格式转换为树形结构
const convertToTreeData = (content) => {
  const convertNode = (obj, parentKey = '0') => {
    let index = 0;
    return Object.entries(obj).map(([key, value]) => {
      index++;
      const currentKey = `${parentKey}-${index}`;
      
      if (typeof value === 'object' && value !== null) {
        return {
          title: key,
          key: currentKey,
          children: convertNode(value, currentKey)
        };
      }
      
      return {
        title: key,
        key: currentKey,
        isLeaf: true
      };
    });
  };

  return convertNode(content);
};

// 获取课程大纲
export const getSyllabus = async (req, res) => {
  try {
    const teacherId = req.user?.system_teacher_id;
    const { courseCode } = req.params;

    console.log(`[大纲获取] 开始获取课程大纲, 教师ID: ${teacherId}, 课程代码: ${courseCode}`);

    if (!teacherId || !courseCode) {
      console.log('[大纲获取] 错误: 缺少必要参数');
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 检查缓存
    const cacheKey = `syllabus_${teacherId}_${courseCode}`;
    const cachedData = cache.get(cacheKey);
    if (cachedData && (Date.now() - cachedData.timestamp < CACHE_DURATION)) {
      console.log(`[大纲获取] 从缓存获取数据, 缓存键: ${cacheKey}`);
      return res.json({
        success: true,
        data: cachedData.data,
        fromCache: true
      });
    }

    console.log('[大纲获取] 缓存未命中, 从数据库查询');

    // 获取最新的课程大纲记录
    console.log(`[大纲获取] 执行SQL查询: teacherId=${teacherId}, courseCode=${courseCode}`);
    const [syllabus] = await pool.execute(
      `SELECT s.id, s.content, s.allocation, s.total, s.create_time, s.update_time
       FROM syllabus s
       INNER JOIN courses c ON s.course_code = c.course_code
       WHERE s.teacher_id = ? 
       AND s.course_code = ?
       AND c.status = 1
       ORDER BY s.create_time DESC, s.id DESC
       LIMIT 1`,
      [teacherId, courseCode]
    );

    console.log(`[大纲获取] 查询结果记录数: ${syllabus.length}`);
    
    if (syllabus.length > 0) {
      console.log(`[大纲获取] 找到记录, ID: ${syllabus[0].id}`);
    }

    if (syllabus.length === 0) {
      console.log('[大纲获取] 未找到对应的大纲记录');
      return res.json({
        success: true,
        data: null
      });
    }

    // 解析JSON数据
    console.log('[大纲获取] 开始解析大纲JSON数据');
    let content = null;
    let allocation = null;
    let treeData = null;

    try {
      // 检查内容是否存在以及是否是有效的JSON字符串
      if (syllabus[0].content) {
        console.log('[大纲获取] 原始content字段长度:', syllabus[0].content.length);
        console.log('[大纲获取] content字段前100个字符:', syllabus[0].content.substring(0, 100));
        
        content = JSON.parse(syllabus[0].content);
        console.log('[大纲获取] 解析content成功, 结构:', Object.keys(content));
        
        if (content) {
          console.log('[大纲获取] 开始转换内容为树形结构');
          treeData = convertToTreeData(content);
          console.log('[大纲获取] 树形结构转换完成, 顶层节点数量:', treeData.length);
        }
      } else {
        console.warn('[大纲获取] content字段为空');
      }
    } catch (e) {
      console.error('[大纲获取] 解析大纲内容失败:', e);
      console.error('[大纲获取] 错误详情:', e.stack);
      content = null;
      treeData = null;
    }

    try {
      if (syllabus[0].allocation) {
        console.log('[大纲获取] 原始allocation字段长度:', syllabus[0].allocation.length);
        allocation = JSON.parse(syllabus[0].allocation);
        console.log('[大纲获取] 解析allocation成功, 课时数量:', Object.keys(allocation).length);
      } else {
        console.warn('[大纲获取] allocation字段为空');
      }
    } catch (e) {
      console.error('[大纲获取] 解析课时分配失败:', e);
      console.error('[大纲获取] 错误详情:', e.stack);
      allocation = null;
    }

    const syllabusData = {
      content,
      treeData,
      allocation,
      total: syllabus[0].total || 0,
      createTime: syllabus[0].create_time,
      updateTime: syllabus[0].update_time,
      expandedKeys: treeData ? treeData.map(node => node.key) : [] // 添加默认展开的节点key
    };

    // 记录准备返回的数据结构
    console.log('[大纲获取] 准备返回的数据结构:', JSON.stringify({
      hasContent: !!content,
      contentKeys: content ? Object.keys(content) : [],
      hasTreeData: !!treeData,
      treeDataLength: treeData ? treeData.length : 0,
      hasAllocation: !!allocation,
      allocationKeys: allocation ? Object.keys(allocation).length : 0,
      total: syllabusData.total
    }));

    // 更新缓存
    cache.set(cacheKey, {
      data: syllabusData,
      timestamp: Date.now()
    });
    console.log(`[大纲获取] 已更新缓存, 缓存键: ${cacheKey}`);

    res.json({
      success: true,
      data: syllabusData
    });
    console.log('[大纲获取] 成功返回大纲数据');

  } catch (error) {
    console.error('[大纲获取] 获取课程大纲失败:', error);
    console.error('[大纲获取] 错误详情:', error.stack);
    res.status(500).json({
      success: false,
      message: '获取课程大纲失败'
    });
  }
};

// 清除指定课程的缓存
export const clearSyllabusCache = (teacherId, courseCode) => {
  const cacheKey = `syllabus_${teacherId}_${courseCode}`;
  console.log(`[缓存管理] 清除特定课程缓存: ${cacheKey}`);
  cache.delete(cacheKey);
};

// 手动清除特定课程缓存的接口
export const clearCourseCache = async (req, res) => {
  try {
    const { courseCode } = req.params;
    const teacherId = req.user?.system_teacher_id;
    
    if (!teacherId || !courseCode) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    clearSyllabusCache(teacherId, courseCode);
    
    res.json({
      success: true,
      message: `已清除课程 ${courseCode} 的缓存`
    });
  } catch (error) {
    console.error('[缓存管理] 清除课程缓存失败:', error);
    res.status(500).json({
      success: false,
      message: '清除缓存失败'
    });
  }
};

// 手动清除所有缓存的接口
export const clearAllCache = async (req, res) => {
  try {
    const cacheSize = cache.size;
    console.log(`[缓存管理] 清除所有缓存, 当前缓存条目数: ${cacheSize}`);
    cache.clear();
    res.json({
      success: true,
      message: `所有缓存已清除, 共清除 ${cacheSize} 条缓存`
    });
  } catch (error) {
    console.error('[缓存管理] 清除所有缓存失败:', error);
    res.status(500).json({
      success: false,
      message: '清除缓存失败'
    });
  }
}; 
import express from 'express';
import cors from 'cors';
import { register, login, updateProfile } from './controllers/userController.js';
import {
  uploadSchedule,
  getCurrentSchedule,
  getScheduleHistory,
  downloadSchedule,
  deleteClass,
  downloadTemplate,
  getTeacherCourses
} from './controllers/scheduleController.js';
import { dbConfig } from './config/db.js';
import mysql from 'mysql2/promise';
import authRoutes from './routes/authRoutes.js';
import studentRoutes from './routes/studentRoutes.js';
import courseRegistrationRoutes from './routes/courseRegistrationRoutes.js';
import bilibiliRoutes from './routes/bilibili.js';
import aiSearchRoutes from './routes/aiSearch.js';
import pptRoutes from './routes/pptRoutes.js';
import audioNoteRoutes from './routes/audioNoteRoutes.js';
import favoriteRoutes from './routes/favoriteRoutes.js';
import previewRoutes from './routes/previewRoutes.js';
import sharedResourceRoutes from './routes/sharedResourceRoutes.js';
import videoNoteRoutes from './routes/videoNoteRoutes.js';

import lessonPlanRoutes from './routes/lessonPlanRoutes.js';
import lessonDetailRoutes from './routes/lessonDetailRoutes.js';
import courseRoutes from './routes/courseRoutes.js';
import scheduleRoutes from './routes/scheduleRoutes.js';
import teacherCourseRoutes from './routes/teacherCourseRoutes.js';
import exerciseRoutes from './routes/exerciseRoutes.js';
import classRoutes from './routes/classRoutes.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { ensureDirectories } from './utils/init.js';
import syllabusRoutes from './routes/syllabusRoutes.js';
import testRoutes from './routes/testRoutes.js';
import smartPreparationRoutes from './routes/smartPreparationRoutes.js';
import publishRoutes from './routes/publishRoutes.js';
import aiChatRoutes from './routes/aiChatRoutes.js';
import { sharedResourceModel } from './models/sharedResourceModel.js';
//import previewRoutes from './routes/previewRoutes.js';

import preview_publishRoute from './routes/preview_publishRoute.js'; //这里有问题！我们是应用了的但是显示灰色没应用

import studentPreviewRoutes from './routes/student_preview_routes.js';
import studentSharedResourceRoutes from './routes/studentSharedResourceRoutes.js';
import dataAnalysisRoutes from './routes/DataAnalysisRoutes.js';
import courseTimeRoutes from './routes/courseTimeRoutes.js';
import exerciseHomeworkRoutes from './routes/exercise_homeworkRoutes.js';
import homeworkLookRoutes from './routes/homework_lookRoutes.js';
import exercise_homework_learningRoutes from './routes/exercise_homework_learningRoutes.js';
import { loadModelConfigs } from './config/modelService.js';
import modelService from './config/modelService.js';
import modelRoutes from './routes/modelRoutes.js';
import exercise_previewRoutes from './routes/exercise_previewRoutes.js';
import exercise_preview_learningRoutes from './routes/exercise_preview_learningRoutes.js';
import exercise_preview_lookRoutes from './routes/exercise_preview_lookRoutes.js';
import gradeAnalysisRoutes from './routes/GradeAnalysisRoutes.js';
import previewAnalysisRoutes from './routes/previewAnalysisRoutes.js';
import previewEvaluationRoutes from './routes/previewEvaluationRoutes.js';
import studentAnswersRoutes from './routes/studentAnswersRoutes.js';
import plagiarismAnalysisRoutes from './routes/plagiarismAnalysisRoutes.js';
import animationRoutes from './routes/animationRoutes.js';

// 获取 __dirname 的替代方案
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();

// 确保必要的目录存在
ensureDirectories();

// 基础中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS 配置
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  optionsSuccessStatus: 204
}));

// 添加OPTIONS请求处理
app.options('*', cors());

// 添加静态文件服务，确保uploads目录可访问
import path from 'path';
app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

// 将API密钥配置注册到应用全局
app.set('modelService', modelService);

// 为确保静态文件路径能够正常工作，添加路径打印
console.log('静态文件路径:', path.join(process.cwd(), 'uploads'));

// 添加请求日志中间件
app.use((req, res, next) => {
  console.log('\n--- 新请求 ---');
  console.log('请求方法:', req.method);
  console.log('请求路径:', req.path);
  console.log('路由参数:', req.params);
  console.log('查询参数:', req.query);
  console.log('请求头:', req.headers);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('请求体:', req.body);
  }
  next();
});

// 测试路由
app.get('/test', (req, res) => {
  res.json({ message: 'Server is working' });
});

// 确保数据分析路由在其他路由之前注册
app.use('/api/analysis', dataAnalysisRoutes);

// API 路由
app.use('/api', authRoutes);

// 学生预览相关路由
app.use('/api/student', studentPreviewRoutes);

// 学生分享资源相关路由
app.use('/api/student', studentSharedResourceRoutes);

app.use('/api/student', studentRoutes);
app.use('/api/course-registration', courseRegistrationRoutes);
app.use('/api/bilibili', bilibiliRoutes);
app.use('/api/ai-search', aiSearchRoutes);
app.use('/api/ppt', pptRoutes);
app.use('/api/favorites', favoriteRoutes);
app.use('/api/previews', previewRoutes);
app.use('/api/resources', sharedResourceRoutes);  // 添加资源分享路由

// 其他路由
app.use('/api/schedules', scheduleRoutes);
app.use('/api/courses', courseRoutes);
app.use('/api', teacherCourseRoutes);
app.use('/api/lesson-plan', lessonPlanRoutes);
app.use('/api/lesson-detail', lessonDetailRoutes);
app.use('/api/test', testRoutes);
app.use('/api/syllabus', syllabusRoutes);
app.use('/api/smart-preparation', smartPreparationRoutes);
app.use('/api/exercises', exerciseRoutes);
app.use('/api/classes', classRoutes);
app.use('/api', publishRoutes);
app.use('/api', aiChatRoutes);
app.use('/api/models', modelRoutes); // 添加模型管理路由

// 添加临时路由处理笔记生成请求
app.post('/api/video-summary/summarize', (req, res) => {
  res.status(200).json({
    success: false,
    message: '视频摘要服务正在维护中，请稍后再试'
  });
});

app.get('/api/video-summary/list', (req, res) => {
  res.status(200).json({
    success: true,
    data: []
  });
});

app.get('/api/video-summary/:id', (req, res) => {
  res.status(200).json({
    success: false,
    message: '找不到指定的视频摘要'
  });
});

app.put('/api/video-summary/:id', (req, res) => {
  res.status(200).json({
    success: false,
    message: '视频摘要更新服务正在维护中，请稍后再试'
  });
});

app.delete('/api/video-summary/:id', (req, res) => {
  res.status(200).json({
    success: false,
    message: '视频摘要删除服务正在维护中，请稍后再试'
  });
});

app.use('/api/class-preview', preview_publishRoute); // 注册预习发布路由

// 注册路由
app.use('/api/course-times', courseTimeRoutes);

// 添加一个测试路由用于调试
app.get('/api/analysis/test', (req, res) => {
  res.json({
    success: true,
    message: '数据分析测试路由响应成功'
  });
});

// 添加课后习题路由
app.use('/api/homework', exerciseHomeworkRoutes);

// 添加作业查看路由
app.use('/api/homework-look', homeworkLookRoutes);

// 添加作业学习路由
app.use('/api/homework-learning', exercise_homework_learningRoutes);

// 添加视频笔记路由
app.use('/api', videoNoteRoutes);

// 添加录音笔记路由
import audioSummaryRoutes from './routes/audio_summary.js';
app.use('/api/audio-summary', audioSummaryRoutes);
// 添加预习习题路由
app.use('/api/preview-exercises', exercise_previewRoutes);

// 添加预习习题学习路由
app.use('/api/preview-learning', exercise_preview_learningRoutes);

// 添加预习习题查看提交记录路由
app.use('/api/preview-look', exercise_preview_lookRoutes);

// 添加成绩分析路由
app.use('/api/analysis', gradeAnalysisRoutes);

// 添加预习分析路由
app.use('/api/preview-analysis', previewAnalysisRoutes);

// 添加预习评估路由
app.use('/api/preview-evaluation', previewEvaluationRoutes);

// 添加学生答案路由
app.use('/api/student-answers', studentAnswersRoutes);

// 添加查重分析路由
app.use('/api/plagiarism-analysis', plagiarismAnalysisRoutes);

// 添加动画生成路由
app.use('/api/animation', animationRoutes);

// 404 处理
app.use((req, res) => {
  console.log('\n404 - 未找到路由:', req.path);
  console.log('可用路由信息:');
  console.log('- 预习分析相关路由:');
  console.log('  GET /api/analysis/preview/:classId');
  console.log('  GET /api/analysis/preview/detail/:previewId');
  console.log('  GET /api/analysis/preview/trend/:classId');
  console.log('请求参数:', req.params);

  res.status(404).json({
    error: '未找到请求的资源',
    path: req.path
  });
});

// 全局错误处理中间件
app.use((err, req, res, next) => {
  console.error('全局错误:', err);
  res.status(err.status || 500).json({
    success: false,
    message: err.message || '服务器内部错误'
  });
});

// 在应用启动前，预加载模型配置
async function preloadModelConfigs() {
  try {
    console.log('正在从数据库加载模型配置...');
    const configs = await modelService.loadModelConfigs();
    console.log('模型配置加载成功');
    return configs;
  } catch (error) {
    console.error('加载模型配置失败，将使用备用配置:', error);
    return null;
  }
}

// 初始化数据表
const initializeTables = async () => {
  try {
    await sharedResourceModel.createSharedVideoStudentsTable();
    console.log('数据表初始化完成');
  } catch (error) {
    console.error('数据表初始化失败:', error);
  }
};

// 在应用初始化过程中，使用await加载配置
(async () => {
  try {
    // 预加载模型配置
    const configs = await preloadModelConfigs();

    // 将模型配置注册到应用全局
    app.set('modelService', modelService);

    // 初始化数据表
    await initializeTables();

    // 启动服务器
    const PORT = process.env.PORT || 3000;
    const HOST = '0.0.0.0';
    app.listen(PORT, HOST, () => {
      console.log(`服务器已启动，监听端口 ${PORT}，地址 ${HOST}`);
    });
  } catch (error) {
    console.error('应用初始化失败:', error);
    process.exit(1);
  }
})();

export default app;
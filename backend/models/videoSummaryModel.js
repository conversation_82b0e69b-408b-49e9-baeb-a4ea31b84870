import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 获取教师的所有视频总结
export async function getTeacherSummaries(teacherId) {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const [rows] = await connection.execute(
      `SELECT 
        vs.*, 
        tf.title as video_title, 
        tf.url as video_url,
        tf.cover_url,
        tf.source,
        tf.author
      FROM 
        video_summaries vs
      LEFT JOIN 
        teacher_favorites tf ON vs.favorite_id = tf.id
      WHERE 
        vs.system_teacher_id = ? 
        AND vs.status = 1
      ORDER BY 
        vs.create_time DESC`,
      [teacherId]
    );
    
    return rows;
  } catch (error) {
    console.error('获取视频总结列表失败:', error);
    throw error;
  } finally {
    if (connection) await connection.end();
  }
}

// 获取单个视频总结
export async function getSummaryById(summaryId, teacherId) {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const [rows] = await connection.execute(
      `SELECT 
        vs.*, 
        tf.title as video_title, 
        tf.url as video_url,
        tf.cover_url,
        tf.source,
        tf.author
      FROM 
        video_summaries vs
      LEFT JOIN 
        teacher_favorites tf ON vs.favorite_id = tf.id
      WHERE 
        vs.id = ? 
        AND vs.system_teacher_id = ? 
        AND vs.status = 1`,
      [summaryId, teacherId]
    );
    
    return rows.length ? rows[0] : null;
  } catch (error) {
    console.error('获取视频总结详情失败:', error);
    throw error;
  } finally {
    if (connection) await connection.end();
  }
}

// 创建视频总结
export async function createSummary(summaryData) {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    // 创建新记录
    const [result] = await connection.execute(
      `INSERT INTO video_summaries (
        system_teacher_id,
        favorite_id,
        resource_id,
        title,
        url,
        transcription,
        summary
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        summaryData.system_teacher_id,
        summaryData.favorite_id || null,
        summaryData.resource_id || null,
        summaryData.title,
        summaryData.url,
        summaryData.transcription || null,
        summaryData.summary
      ]
    );
    
    return {
      id: result.insertId,
      ...summaryData
    };
  } catch (error) {
    console.error('创建视频总结失败:', error);
    throw error;
  } finally {
    if (connection) await connection.end();
  }
}

// 更新视频总结
export async function updateSummary(summaryId, teacherId, updateData) {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    // 构建更新SQL
    const fields = Object.keys(updateData)
      .filter(key => ['title', 'summary'].includes(key))
      .map(key => `${key} = ?`);
    
    if (!fields.length) return false;
    
    const values = fields.map(field => updateData[field.split(' = ')[0]]);
    values.push(summaryId, teacherId);
    
    const [result] = await connection.execute(
      `UPDATE video_summaries 
       SET ${fields.join(', ')} 
       WHERE id = ? AND system_teacher_id = ? AND status = 1`,
      values
    );
    
    return result.affectedRows > 0;
  } catch (error) {
    console.error('更新视频总结失败:', error);
    throw error;
  } finally {
    if (connection) await connection.end();
  }
}

// 删除视频总结(软删除)
export async function deleteSummary(summaryId, teacherId) {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const [result] = await connection.execute(
      `UPDATE video_summaries 
       SET status = 0 
       WHERE id = ? AND system_teacher_id = ?`,
      [summaryId, teacherId]
    );
    
    return result.affectedRows > 0;
  } catch (error) {
    console.error('删除视频总结失败:', error);
    throw error;
  } finally {
    if (connection) await connection.end();
  }
} 
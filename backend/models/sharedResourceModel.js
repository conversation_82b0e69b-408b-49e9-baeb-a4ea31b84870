import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

export const sharedResourceModel = {
  // 创建分享表（如果不存在）
  async createSharedVideoStudentsTable() {
    const pool = mysql.createPool(dbConfig);
    try {
      // 首先检查表是否已存在
      const [tables] = await pool.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = ? 
        AND table_name = ?`, 
        [dbConfig.database, 'shared_video_students']
      );
      
      if (tables.length > 0) {
        console.log('shared_video_students表已存在，无需创建');
        return true;
      }
      
      console.log('正在创建shared_video_students表...');
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS shared_video_students (
          id INT NOT NULL AUTO_INCREMENT,
          shared_video_id INT NOT NULL,
          student_id VARCHAR(20) NOT NULL,
          is_required TINYINT DEFAULT 0 COMMENT '是否必看: 0-非必看 1-必看',
          viewed TINYINT DEFAULT 0 COMMENT '是否已观看: 0-未观看 1-已观看',
          view_time DATETIME DEFAULT NULL COMMENT '观看时间',
          create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
          update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY uk_video_student (shared_video_id, student_id),
          KEY idx_student_id (student_id),
          CONSTRAINT fk_shared_videos_id FOREIGN KEY (shared_video_id) REFERENCES shared_videos (id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频分享给学生的关系表';
      `);
      
      console.log('成功创建shared_video_students表');
      return true;
    } catch (error) {
      console.error('创建shared_video_students表失败:', error);
      throw error;
    } finally {
      pool.end();
    }
  },
  
  // 获取教师分享的所有视频
  async getTeacherSharedVideos(teacherId) {
    const pool = mysql.createPool(dbConfig);
    try {
      const [videos] = await pool.query(
        `SELECT sv.*, cc.class_name, 
         (SELECT COUNT(*) FROM shared_video_students WHERE shared_video_id = sv.id AND viewed = 1) as viewed_count,
         (SELECT COUNT(*) FROM shared_video_students WHERE shared_video_id = sv.id) as total_students
         FROM shared_videos sv
         JOIN course_classes cc ON sv.class_id = cc.id
         WHERE sv.teacher_id = ? AND sv.status = 1
         ORDER BY sv.create_time DESC`,
        [teacherId]
      );
      
      return videos;
    } catch (error) {
      console.error('获取教师分享视频失败:', error);
      throw error;
    } finally {
      pool.end();
    }
  },
  
  // 获取分享给特定班级的视频
  async getClassSharedVideos(classId, teacherId) {
    const pool = mysql.createPool(dbConfig);
    try {
      const [videos] = await pool.query(
        `SELECT sv.*, 
         (SELECT COUNT(*) FROM shared_video_students WHERE shared_video_id = sv.id AND viewed = 1) as viewed_count,
         (SELECT COUNT(*) FROM shared_video_students WHERE shared_video_id = sv.id) as total_students
         FROM shared_videos sv
         WHERE sv.class_id = ? AND sv.teacher_id = ? AND sv.status = 1
         ORDER BY sv.create_time DESC`,
        [classId, teacherId]
      );
      
      return videos;
    } catch (error) {
      console.error('获取班级分享视频失败:', error);
      throw error;
    } finally {
      pool.end();
    }
  },
  
  // 获取分享给特定学生的视频
  async getStudentSharedVideos(studentId) {
    const pool = mysql.createPool(dbConfig);
    try {
      const [videos] = await pool.query(
        `SELECT sv.*, cc.class_name, scs.is_required, scs.viewed, scs.view_time
         FROM shared_videos sv
         JOIN shared_video_students scs ON sv.id = scs.shared_video_id
         JOIN course_classes cc ON sv.class_id = cc.id
         WHERE scs.student_id = ? AND sv.status = 1
         ORDER BY sv.create_time DESC`,
        [studentId]
      );
      
      return videos;
    } catch (error) {
      console.error('获取学生分享视频失败:', error);
      throw error;
    } finally {
      pool.end();
    }
  }
}; 
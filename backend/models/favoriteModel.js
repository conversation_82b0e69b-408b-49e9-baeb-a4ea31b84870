import db from '../config/db.js';

class Favorite {
  // 添加收藏
  static async create(data) {
    try {
      console.log('开始执行添加收藏SQL');
      const sql = `
        INSERT INTO teacher_favorites (
          system_teacher_id,
          resource_type,
          resource_id,
          title,
          url,
          description,
          cover_url,
          source,
          source_type,
          author,
          create_time,
          update_time,
          status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), 1)
      `;
      
      const params = [
        data.system_teacher_id,
        data.resource_type,
        data.resource_id,
        data.title,
        data.url,
        data.description,
        data.cover_url,
        data.source,
        data.source_type,
        data.author
      ];
      
      console.log('SQL语句:', sql);
      console.log('参数:', params);
      
      const [result] = await db.execute(sql, params);
      console.log('SQL执行结果:', result);
      
      if (result.affectedRows === 1) {
        return {
          id: result.insertId,
          ...data
        };
      }
      throw new Error('添加收藏失败');
    } catch (error) {
      console.error('添加收藏SQL执行错误:', error);
      throw error;
    }
  }

  // 根据教师ID获取收藏列表
  static async findByTeacherId(teacherId) {
    try {
      console.log('开始查询收藏列表');
      const sql = `
        SELECT * FROM teacher_favorites 
        WHERE system_teacher_id = ? 
        AND status = 1 
        ORDER BY create_time DESC
      `;
      
      const [rows] = await db.execute(sql, [teacherId]);
      console.log('查询到收藏数量:', rows.length);
      return rows;
    } catch (error) {
      console.error('查询收藏列表错误:', error);
      throw error;
    }
  }

  // 检查是否已收藏
  static async checkFavorite(teacherId, resourceType, resourceId) {
    try {
      console.log('检查是否已收藏:', { teacherId, resourceType, resourceId });
      const sql = `
        SELECT * FROM teacher_favorites 
        WHERE system_teacher_id = ? 
        AND resource_type = ? 
        AND resource_id = ?
        LIMIT 1
      `;
      
      const [rows] = await db.execute(sql, [teacherId, resourceType, resourceId]);
      console.log('检查结果:', rows[0] || null);
      return rows[0] || null;
    } catch (error) {
      console.error('检查收藏状态错误:', error);
      throw error;
    }
  }

  // 取消收藏（软删除）
  static async remove(id, teacherId) {
    try {
      console.log('开始取消收藏:', { id, teacherId });
      const sql = `
        UPDATE teacher_favorites 
        SET status = 0, update_time = NOW() 
        WHERE id = ? AND system_teacher_id = ?
      `;
      
      const [result] = await db.execute(sql, [id, teacherId]);
      console.log('取消收藏结果:', result);
      
      if (result.affectedRows === 0) {
        throw new Error('取消收藏失败，可能记录不存在或无权限');
      }
      return true;
    } catch (error) {
      console.error('取消收藏错误:', error);
      throw error;
    }
  }
}

export default Favorite; 
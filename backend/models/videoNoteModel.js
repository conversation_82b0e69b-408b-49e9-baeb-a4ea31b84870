import mysql from 'mysql2/promise';
import { dbConfig } from '../config/db.js';

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

/**
 * 初始化数据库表
 */
async function initializeTable() {
  const connection = await pool.getConnection();
  try {
    // 创建视频转录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS video_transcripts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        video_id VARCHAR(255) NOT NULL,
        transcript TEXT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME,
        INDEX (user_id),
        INDEX (video_id),
        UNIQUE KEY (user_id, video_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 创建视频摘要表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS video_summaries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        system_teacher_id VARCHAR(10) NOT NULL COMMENT '教师ID',
        favorite_id INT NULL COMMENT '关联的收藏ID',
        resource_id VARCHAR(50) NULL COMMENT '资源ID',
        title VARCHAR(255) NOT NULL COMMENT '视频标题',
        url VARCHAR(500) NOT NULL COMMENT '视频链接',
        transcription TEXT NULL COMMENT '视频转写文本',
        summary TEXT NOT NULL COMMENT '视频总结内容',
        status TINYINT(1) DEFAULT 1 COMMENT '状态:1-有效,0-删除',
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_teacher_resource (system_teacher_id, resource_id),
        INDEX idx_favorite (favorite_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频总结笔记表';
    `);

    // 创建视频笔记表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS video_notes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        video_id VARCHAR(255) NOT NULL,
        video_url VARCHAR(1024),
        video_title VARCHAR(512),
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME,
        status TINYINT DEFAULT 1,
        INDEX (user_id),
        INDEX (video_id),
        INDEX (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    console.log('视频笔记相关数据表初始化成功');
  } catch (error) {
    console.error('初始化数据表失败:', error);
    throw error;
  } finally {
    connection.release();
  }
}

// 应用启动时初始化表
initializeTable().catch(console.error);

/**
 * 获取视频转录文本
 * @param {string} userId - 用户ID 
 * @param {string} videoId - 视频ID
 * @returns {Promise<Object|null>} - 转录结果
 */
export async function getTranscript(userId, videoId) {
  try {
    const [rows] = await pool.execute(
      'SELECT transcription FROM video_summaries WHERE system_teacher_id = ? AND resource_id = ? AND status = 1',
      [userId, videoId]
    );
    
    return rows.length > 0 ? rows[0].transcription : null;
  } catch (error) {
    console.error('获取转录失败:', error);
    throw error;
  }
}

/**
 * 保存视频转录和处理任务
 * @param {Object} data - 转录数据
 * @returns {Promise<Object>} - 插入结果
 */
export async function saveTranscript(data) {
  const { userId, videoId, videoUrl, videoTitle, transcript } = data;
  const connection = await pool.getConnection();
  const teacherId = parseInt(userId.replace(/\D/g, '')) || 1;
  
  try {
    await connection.beginTransaction();
    
    // 获取favorite_id (如果存在)
    const [favoriteRows] = await connection.execute(
      'SELECT id FROM teacher_favorites WHERE system_teacher_id = ? AND resource_id = ? AND resource_type = "video" LIMIT 1',
      [userId, videoId]
    );
    
    const favoriteId = favoriteRows.length > 0 ? favoriteRows[0].id : null;
    
    // 检查是否存在摘要记录
    const [summaryRows] = await connection.execute(
      'SELECT id FROM video_summaries WHERE system_teacher_id = ? AND resource_id = ? AND status = 1',
      [userId, videoId]
    );
    
    let summaryId;
    
    if (summaryRows.length > 0) {
      // 更新现有记录
      summaryId = summaryRows[0].id;
      await connection.execute(
        'UPDATE video_summaries SET transcription = ?, favorite_id = ? WHERE id = ?',
        [transcript, favoriteId, summaryId]
      );
    } else {
      // 创建新记录
      const [result] = await connection.execute(
        `INSERT INTO video_summaries 
         (system_teacher_id, resource_id, title, url, transcription, summary, favorite_id, status) 
         VALUES (?, ?, ?, ?, ?, '', ?, 1)`,
        [userId, videoId, videoTitle || '未命名视频', videoUrl, transcript, favoriteId]
      );
      summaryId = result.insertId;
    }
    
    // 更新任务状态
    // 注意：如果已删除video_process_tasks表，下面这部分代码应该被注释掉或移除
    try {
      const [taskRows] = await connection.execute(
        'SELECT id FROM video_process_tasks WHERE system_teacher_id = ? AND video_id = ? AND status != "completed"',
        [teacherId, videoId]
      );
      
      if (taskRows.length > 0) {
        await connection.execute(
          `UPDATE video_process_tasks 
           SET transcript_status = 'completed', 
               updated_at = NOW() 
           WHERE id = ?`,
          [taskRows[0].id]
        );
      } else {
        await connection.execute(
          `INSERT INTO video_process_tasks 
           (system_teacher_id, video_url, video_title, video_id, status, transcript_status, created_at, updated_at, summary_id) 
           VALUES (?, ?, ?, ?, 'processing', 'completed', NOW(), NOW(), ?)`,
          [teacherId, videoUrl, videoTitle, videoId, summaryId]
        );
      }
    } catch (taskError) {
      // 如果video_process_tasks表已删除，这里会捕获错误并继续执行
      console.log('注意: video_process_tasks表操作失败，可能表已删除', taskError.message);
    }
    
    await connection.commit();
    return { id: summaryId, favoriteId };
  } catch (error) {
    await connection.rollback();
    console.error('保存转录失败:', error);
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * 保存视频摘要
 * @param {Object} data - 摘要数据
 * @returns {Promise<Object>} - 插入结果
 */
export async function saveSummary(data) {
  const { userId, videoId, videoUrl, title, summary } = data;
  const connection = await pool.getConnection();
  const teacherId = parseInt(userId.replace(/\D/g, '')) || 1;
  
  try {
    await connection.beginTransaction();
    
    // 获取favorite_id (如果存在)
    const [favoriteRows] = await connection.execute(
      'SELECT id FROM teacher_favorites WHERE system_teacher_id = ? AND resource_id = ? AND resource_type = "video" LIMIT 1',
      [userId, videoId]
    );
    
    const favoriteId = favoriteRows.length > 0 ? favoriteRows[0].id : null;
    
    // 检查是否存在记录
    const [summaryRows] = await connection.execute(
      'SELECT id FROM video_summaries WHERE system_teacher_id = ? AND resource_id = ? AND status = 1',
      [userId, videoId]
    );
    
    let summaryId;
    
    if (summaryRows.length > 0) {
      // 更新现有记录
      summaryId = summaryRows[0].id;
      await connection.execute(
        'UPDATE video_summaries SET summary = ?, favorite_id = ?, title = ? WHERE id = ?',
        [summary, favoriteId, title || '未命名视频', summaryId]
      );
    } else {
      // 创建新记录
      const [result] = await connection.execute(
        `INSERT INTO video_summaries 
         (system_teacher_id, resource_id, title, url, transcription, summary, favorite_id, status) 
         VALUES (?, ?, ?, ?, '', ?, ?, 1)`,
        [userId, videoId, title || '未命名视频', videoUrl, summary, favoriteId]
      );
      summaryId = result.insertId;
    }
    
    // 更新任务状态
    // 注意：如果已删除video_process_tasks表，下面这部分代码应该被注释掉或移除
    try {
      const [taskRows] = await connection.execute(
        'SELECT id FROM video_process_tasks WHERE system_teacher_id = ? AND video_id = ?',
        [teacherId, videoId]
      );
      
      if (taskRows.length > 0) {
        await connection.execute(
          `UPDATE video_process_tasks 
           SET status = 'completed', 
               summary_status = 'completed', 
               updated_at = NOW() 
           WHERE id = ?`,
          [taskRows[0].id]
        );
      }
    } catch (taskError) {
      // 如果video_process_tasks表已删除，这里会捕获错误并继续执行
      console.log('注意: video_process_tasks表操作失败，可能表已删除', taskError.message);
    }
    
    await connection.commit();
    return { id: summaryId, favoriteId };
  } catch (error) {
    await connection.rollback();
    console.error('保存摘要失败:', error);
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * 获取视频摘要
 * @param {string} userId - 用户ID
 * @param {string} videoId - 视频ID
 * @returns {Promise<Object|null>} - 摘要结果
 */
export async function getSummary(userId, videoId) {
  try {
    console.log(`查询视频摘要数据，用户ID: ${userId}, 视频ID: ${videoId}`);
    
    // 查询可能的字段差异，确保能够找到记录
    const [rows] = await pool.execute(
      `SELECT 
         id, 
         system_teacher_id, 
         resource_id, 
         title, 
         url, 
         transcription, 
         summary, 
         status, 
         create_time, 
         update_time
       FROM video_summaries 
       WHERE system_teacher_id = ? AND resource_id = ? AND status = 1`,
      [userId, videoId]
    );
    
    console.log(`查询结果: 找到 ${rows.length} 条记录`);
    
    if (rows.length === 0) {
      // 尝试用数字ID查询
      const numericUserId = parseInt(String(userId).replace(/\D/g, '')) || 1;
      console.log(`尝试使用数字ID查询: ${numericUserId}`);
      
      const [numericRows] = await pool.execute(
        `SELECT 
           id, 
           system_teacher_id, 
           resource_id, 
           title, 
           url, 
           transcription, 
           summary, 
           status, 
           create_time, 
           update_time
         FROM video_summaries 
         WHERE system_teacher_id = ? AND resource_id = ? AND status = 1`,
        [numericUserId, videoId]
      );
      
      console.log(`数字ID查询结果: 找到 ${numericRows.length} 条记录`);
      return numericRows.length > 0 ? numericRows[0] : null;
    }
    
    return rows[0];
  } catch (error) {
    console.error('获取摘要失败:', error);
    throw error;
  }
}

/**
 * 保存笔记
 * @param {Object} data - 笔记数据
 * @returns {Promise<Object>} - 插入结果
 */
export async function saveNote(data) {
  const { userId, videoId, videoUrl, videoTitle, title, content } = data;
  
  try {
    // 检查是否存在记录
    const [rows] = await pool.execute(
      'SELECT id FROM video_notes WHERE system_teacher_id = ? AND video_id = ? AND status = 1',
      [userId, videoId]
    );
    
    if (rows.length > 0) {
      // 更新现有记录
      const [result] = await pool.execute(
        `UPDATE video_notes 
         SET title = ?, content = ?, updated_at = NOW() 
         WHERE id = ?`,
        [title, content, rows[0].id]
      );
      return { id: rows[0].id, updated: true };
    } else {
      // 创建新记录
      const [result] = await pool.execute(
        `INSERT INTO video_notes 
         (system_teacher_id, video_id, video_url, video_title, title, content, created_at) 
         VALUES (?, ?, ?, ?, ?, ?, NOW())`,
        [userId, videoId, videoUrl, videoTitle, title, content]
      );
      return { id: result.insertId, updated: false };
    }
  } catch (error) {
    console.error('保存笔记失败:', error);
    throw error;
  }
}

/**
 * 获取用户的所有笔记
 * @param {string} userId - 用户ID
 * @returns {Promise<Array>} - 笔记列表
 */
export async function getUserNotes(userId) {
  try {
    const [rows] = await pool.execute(
      `SELECT id, system_teacher_id, video_id, video_url, video_title, title, content, created_at, updated_at, status
       FROM video_notes 
       WHERE system_teacher_id = ? AND status = 1
       ORDER BY updated_at DESC`,
      [userId]
    );
    
    return rows;
  } catch (error) {
    console.error('获取用户笔记失败:', error);
    throw error;
  }
}

/**
 * 获取特定笔记
 * @param {number} noteId - 笔记ID
 * @param {string} userId - 用户ID
 * @returns {Promise<Object|null>} - 笔记内容
 */
export async function getNoteById(noteId, userId) {
  try {
    const [rows] = await pool.execute(
      `SELECT id, system_teacher_id, video_id, video_url, video_title, title, content, created_at, updated_at, status
       FROM video_notes 
       WHERE id = ? AND system_teacher_id = ? AND status = 1`,
      [noteId, userId]
    );
    
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    console.error('获取笔记详情失败:', error);
    throw error;
  }
}

/**
 * 删除笔记(软删除)
 * @param {number} noteId - 笔记ID
 * @param {string} userId - 用户ID
 * @returns {Promise<boolean>} - 删除结果
 */
export async function deleteNote(noteId, userId) {
  try {
    const [result] = await pool.execute(
      `UPDATE video_notes 
       SET status = 0, updated_at = NOW() 
       WHERE id = ? AND system_teacher_id = ?`,
      [noteId, userId]
    );
    
    return result.affectedRows > 0;
  } catch (error) {
    console.error('删除笔记失败:', error);
    throw error;
  }
}

/**
 * 创建视频处理任务
 * @param {Object} data - 任务数据
 * @returns {Promise<Object>} - 任务创建结果
 */
export async function createProcessTask(data) {
  const { userId, videoId, videoUrl, videoTitle } = data;
  
  try {
    // 检查是否已存在任务
    const [existingTasks] = await pool.execute(
      `SELECT id, status FROM video_process_tasks 
       WHERE system_teacher_id = ? AND video_id = ?`,
      [parseInt(userId.replace(/\D/g, '')) || 1, videoId]
    );
    
    // 如果有正在进行的任务，返回该任务
    if (existingTasks.length > 0) {
      const task = existingTasks[0];
      if (task.status !== 'failed') {
        return { id: task.id, isNew: false };
      }
    }
    
    // 创建新任务
    const [result] = await pool.execute(
      `INSERT INTO video_process_tasks 
       (system_teacher_id, video_url, video_title, video_id, status, created_at, updated_at) 
       VALUES (?, ?, ?, ?, 'pending', NOW(), NOW())`,
      [parseInt(userId.replace(/\D/g, '')) || 1, videoUrl, videoTitle, videoId]
    );
    
    return { id: result.insertId, isNew: true };
  } catch (error) {
    console.error('创建处理任务失败:', error);
    throw error;
  }
}

/**
 * 更新任务状态
 * @param {number} taskId - 任务ID
 * @param {string} status - 新状态
 * @param {string} errorMessage - 错误信息(可选)
 * @returns {Promise<boolean>} - 更新结果
 */
export async function updateTaskStatus(taskId, status, errorMessage = null) {
  try {
    if (errorMessage) {
      const [result] = await pool.execute(
        `UPDATE video_process_tasks 
         SET status = ?, error_message = ?, updated_at = NOW() 
         WHERE id = ?`,
        [status, errorMessage, taskId]
      );
      return result.affectedRows > 0;
    } else {
      const [result] = await pool.execute(
        `UPDATE video_process_tasks 
         SET status = ?, updated_at = NOW() 
         WHERE id = ?`,
        [status, taskId]
      );
      return result.affectedRows > 0;
    }
  } catch (error) {
    console.error('更新任务状态失败:', error);
    throw error;
  }
}

/**
 * 更新视频标题
 * @param {string|number} userId - 用户ID
 * @param {string} videoId - 视频ID
 * @param {string} title - 新标题
 * @returns {Promise<Object>} - 更新结果
 */
export const updateVideoTitle = async (userId, videoId, title) => {
  const connection = await pool.getConnection();
  try {
    // 转换userId为字符串类型(根据表结构)
    const teacherId = String(userId).replace(/^T/, '');
    const numericTeacherId = parseInt(teacherId) || 1;
    
    // 获取favorite_id (如果存在)
    const [favoriteRows] = await connection.execute(
      'SELECT id FROM teacher_favorites WHERE system_teacher_id = ? AND resource_id = ? AND resource_type = "video" LIMIT 1',
      [numericTeacherId, videoId]
    );
    
    const favoriteId = favoriteRows.length > 0 ? favoriteRows[0].id : null;
    
    await connection.beginTransaction();
    
    // 更新video_summaries表中的title
    await connection.execute(
      `UPDATE video_summaries 
       SET title = ?, favorite_id = ?
       WHERE system_teacher_id = ? AND resource_id = ? AND status = 1`,
      [title, favoriteId, userId, videoId]
    );
    
    // 更新video_notes表中的video_title
    await connection.execute(
      `UPDATE video_notes 
       SET video_title = ? 
       WHERE system_teacher_id = ? AND video_id = ? AND status = 1`,
      [title, userId, videoId]
    );
    
    // 更新video_process_tasks表中的video_title
    await connection.execute(
      `UPDATE video_process_tasks 
       SET video_title = ? 
       WHERE system_teacher_id = ? AND video_id = ?`,
      [title, numericTeacherId, videoId]
    );
    
    await connection.commit();
    
    console.log(`已更新视频标题，用户ID: ${userId}, 视频ID: ${videoId}, 标题: ${title}, 收藏ID: ${favoriteId || 'null'}`);
    return { success: true, favoriteId };
  } catch (error) {
    await connection.rollback();
    console.error('更新视频标题失败:', error);
    throw error;
  } finally {
    connection.release();
  }
}; 
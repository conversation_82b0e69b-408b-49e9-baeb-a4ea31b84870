// 验证课表数据
export const validateSchedule = (scheduleData) => {
  const errors = [];

  // 验证必填字段
  const requiredFields = ['weekday', 'period', 'class_name'];
  requiredFields.forEach(field => {
    if (!scheduleData[field]) {
      errors.push(`${field} 是必填字段`);
    }
  });

  // 验证星期
  if (scheduleData.weekday && (scheduleData.weekday < 1 || scheduleData.weekday > 7)) {
    errors.push('星期必须在 1-7 之间');
  }

  // 验证节次
  if (scheduleData.period && (scheduleData.period < 1 || scheduleData.period > 8)) {
    errors.push('节次必须在 1-8 之间');
  }

  // 验证周数
  if (scheduleData.current_week && scheduleData.total_weeks) {
    if (scheduleData.current_week < 1 || scheduleData.current_week > scheduleData.total_weeks) {
      errors.push('当前周数必须在 1 到总周数之间');
    }
  }

  // 验证日期
  if (scheduleData.start_date && scheduleData.end_date) {
    const start = new Date(scheduleData.start_date);
    const end = new Date(scheduleData.end_date);
    if (start > end) {
      errors.push('开始日期不能晚于结束日期');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// 验证课程数据
export const validateCourse = (courseData) => {
  const errors = [];

  // 验证必填字段
  const requiredFields = ['course_code', 'course_name', 'teacher_id'];
  requiredFields.forEach(field => {
    if (!courseData[field]) {
      errors.push(`${field} 是必填字段`);
    }
  });

  // 验证课程代码格式
  if (courseData.course_code && !/^[A-Z0-9]{4,10}$/.test(courseData.course_code)) {
    errors.push('课程代码必须是4-10位的大写字母和数字组合');
  }

  // 验证学分
  if (courseData.credit && (courseData.credit < 0 || courseData.credit > 10)) {
    errors.push('学分必须在 0-10 之间');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// 验证教案数据
export const validateLessonPlan = (lessonPlanData) => {
  const errors = [];

  // 验证必填字段
  const requiredFields = ['syllabus_id', 'teacher_id', 'content', 'chapter'];
  requiredFields.forEach(field => {
    if (!lessonPlanData[field]) {
      errors.push(`${field} 是必填字段`);
    }
  });

  // 验证章节格式
  if (lessonPlanData.chapter && !/^[0-9.]+/.test(lessonPlanData.chapter)) {
    errors.push('章节必须以数字开头');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}; 
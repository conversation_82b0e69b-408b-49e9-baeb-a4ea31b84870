import crypto from 'crypto';
import axios from 'axios';
import { getXfyunPptConfig } from '../config/modelService.js';

class XFYunHelper {
    constructor() {
        this.APP_ID = '';
        this.SECRET = '';
        this.BASE_URL = "https://zwapi.xfyun.cn";
        
        // 初始化时异步加载配置
        this.loadConfig();
    }
    
    // 异步加载配置
    async loadConfig() {
        try {
            const config = await getXfyunPptConfig();
            this.APP_ID = config.appId || '';
            this.SECRET = config.secret || '';
            console.log(`讯飞PPT API配置已加载: APPID=${this.APP_ID ? '已设置' : '未设置'}, SECRET=${this.SECRET ? '已设置' : '未设置'}`);
        } catch (error) {
            console.error('加载讯飞PPT API配置失败:', error);
        }
    }

    // 获取签名
    getSignature(timestamp) {
        const rawStr = `${this.APP_ID}${timestamp}`;
        const md5Result = crypto.createHash('md5').update(rawStr).digest('hex');
        const hmacResult = crypto.createHmac('sha1', this.SECRET)
            .update(md5Result)
            .digest('base64');
        return hmacResult;
    }

    // 获取请求头
    async getHeaders() {
        // 确保配置已加载
        if (!this.APP_ID || !this.SECRET) {
            await this.loadConfig();
        }
        
        const timestamp = Math.floor(Date.now() / 1000);
        return {
            "appId": this.APP_ID,
            "timestamp": timestamp.toString(),
            "signature": this.getSignature(timestamp)
        };
    }

    // 通过大纲生成PPT
    async createPptByOutline(data) {
        try {
            console.log('开始调用讯飞API生成PPT，参数:', data);
            const headers = {
                ...await this.getHeaders(),
                "Content-Type": "application/json"
            };
            console.log('请求头:', headers);

            const response = await axios.post(
                `${this.BASE_URL}/api/ppt/v2/createPptByOutline`,
                data,
                { headers }
            );

            console.log('讯飞API响应:', response.data);
            console.log('生成任务已创建，SID:', response.data.data.sid);
            return response.data;
        } catch (error) {
            console.error('讯飞API调用失败:', error.response?.data || error.message);
            throw error;
        }
    }

    // 查询PPT生成进度
    async getPptProgress(sid) {
        try {
            console.log('查询PPT生成进度，sid:', sid);
            const headers = await this.getHeaders();
            console.log('请求头:', headers);

            const response = await axios.get(
                `${this.BASE_URL}/api/ppt/v2/progress`,
                {
                    headers,
                    params: { sid }
                }
            );

            console.log('进度查询响应:', response.data);
            
            // 添加completedSteps计算逻辑
            let completedSteps = 0;
            const data = response.data.data;
            
            // 检查各个步骤状态
            if (data.pptStatus === 'done') completedSteps++;
            if (data.aiImageStatus === 'done') completedSteps++;
            if (data.cardNoteStatus === 'done') completedSteps++;
            
            // 计算完成的百分比
            const percent = Math.floor((completedSteps / 3) * 100);
            
            console.log('当前生成进度:', {
                status: data.pptStatus,
                percent: percent
            });
            
            return response.data;
        } catch (error) {
            console.error('进度查询失败:', error.response?.data || error.message);
            throw error;
        }
    }

    // 添加文件上传方法
    async createPptByFile(data) {
        const formData = new FormData();
        formData.append('file', data.file, data.fileName);
        formData.append('data', JSON.stringify(data));
        
        const response = await axios.post(`${this.BASE_URL}/api/ppt/v2/create`, formData, {
            headers: {
                ...await this.getHeaders(),
                'Content-Type': 'multipart/form-data'
            }
        });
        return response.data;
    }
}

export default new XFYunHelper(); 
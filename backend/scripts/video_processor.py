#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import time
import hashlib
import hmac
import base64
import urllib.parse
import requests
import subprocess
import openai
from bs4 import BeautifulSoup

# 配置 - 环境变量中获取API密钥，没有默认值
XFYUN_APPID = os.environ.get('XFYUN_APPID')
XFYUN_SECRET_KEY = os.environ.get('XFYUN_SECRET_KEY')
DEEPSEEK_API_KEY = os.environ.get('DEEPSEEK_API_KEY')
DEEPSEEK_API_BASE = os.environ.get('DEEPSEEK_API_BASE', 'https://api.deepseek.com')

# 错误类
class VideoProcessorError(Exception):
    """视频处理器错误基类"""
    pass

class DownloadError(VideoProcessorError):
    """下载错误"""
    pass

class TranscriptionError(VideoProcessorError):
    """转录错误"""
    pass

class SummaryError(VideoProcessorError):
    """摘要错误"""
    pass

class ConfigError(VideoProcessorError):
    """配置错误"""
    pass

def download_video(video_url, output_path):
    """下载视频"""
    try:
        # 检查是否要使用公共目录
        if '/uploads/' in output_path:
            new_output_path = output_path.replace('/uploads/', '/public/note_videos/')
            # 确保公共目录存在
            os.makedirs(os.path.dirname(new_output_path), exist_ok=True)
            output_path = new_output_path
            print(f"视频将下载到公共目录: {output_path}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 使用you-get下载视频
        command = ['you-get', '-o', os.path.dirname(output_path), '-O', os.path.splitext(os.path.basename(output_path))[0]]
        
        # 如果存在下载cookie则添加
        cookie_path = os.path.join(os.path.dirname(__file__), 'bilibili_cookie.txt')
        if os.path.exists(cookie_path):
            command += ['--cookies', cookie_path]
            
        command.append(video_url)
        
        # 执行下载命令
        result = subprocess.run(command, check=True, capture_output=True, text=True, encoding='utf-8')
        
        # 检查文件是否存在
        if not os.path.exists(output_path):
            # 检查是否以其他扩展名下载
            base_name = os.path.splitext(os.path.basename(output_path))[0]
            dir_name = os.path.dirname(output_path)
            
            for file in os.listdir(dir_name):
                if file.startswith(base_name):
                    file_path = os.path.join(dir_name, file)
                    # 重命名为期望的输出路径
                    os.rename(file_path, output_path)
                    break
            
        # 再次检查文件是否存在
        if not os.path.exists(output_path):
            raise DownloadError(f"下载视频后未找到文件: {output_path}")
        
        return output_path
    
    except subprocess.CalledProcessError as e:
        raise DownloadError(f"下载视频失败: {e.stdout}\n{e.stderr}")
    except Exception as e:
        raise DownloadError(f"下载视频过程中发生错误: {str(e)}")

def extract_audio(video_path, audio_path):
    """从视频提取音频"""
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(audio_path), exist_ok=True)
        
        # 使用ffmpeg提取音频
        command = [
            'ffmpeg', 
            '-i', video_path, 
            '-q:a', '0', 
            '-map', 'a', 
            '-y',  # 覆盖现有文件
            audio_path
        ]
        
        # 执行命令
        result = subprocess.run(command, check=True, capture_output=True, text=True, encoding='utf-8')
        
        # 检查文件是否存在
        if not os.path.exists(audio_path):
            raise Exception(f"音频提取后未找到文件: {audio_path}")
        
        return audio_path
    
    except subprocess.CalledProcessError as e:
        raise Exception(f"提取音频失败: {e.stdout}\n{e.stderr}")
    except Exception as e:
        raise Exception(f"提取音频过程中发生错误: {str(e)}")

def get_audio_duration(audio_path):
    """获取音频时长（秒）"""
    try:
        command = [
            'ffprobe',
            '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            audio_path
        ]
        
        result = subprocess.run(command, check=True, capture_output=True, text=True, encoding='utf-8')
        duration = int(float(result.stdout.strip()))
        return duration
    
    except:
        # 如果获取失败，返回0，调用方将使用默认估计
        return 0

class XfyunAsr:
    """讯飞语音转写API"""
    def __init__(self, appid, secret_key, upload_file_path):
        self.appid = appid
        self.secret_key = secret_key
        self.upload_file_path = upload_file_path
        self.ts = str(int(time.time()))
        self.signa = self.get_signa()
        self.file_size = os.path.getsize(self.upload_file_path)
        self.file_name = os.path.basename(self.upload_file_path)

    def get_signa(self):
        """计算签名"""
        appid = self.appid
        secret_key = self.secret_key
        m2 = hashlib.md5()
        m2.update((appid + self.ts).encode('utf-8'))
        md5 = m2.hexdigest()
        md5_bytes = md5.encode('utf-8')
        
        # 使用HMAC-SHA1算法结合apiKey生成签名
        signa = hmac.new(secret_key.encode('utf-8'), md5_bytes, hashlib.sha1).digest()
        signa = base64.b64encode(signa).decode('utf-8')
        return signa

    def upload(self):
        """上传音频文件"""
        print(f"开始上传音频文件: {self.file_name}, 大小: {self.file_size}字节")
        upload_url = "https://raasr.xfyun.cn/v2/api/upload"
        
        file_len = self.file_size
        param_dict = {
            'appId': self.appid,
            'signa': self.signa,
            'ts': self.ts,
            "fileSize": file_len,
            "fileName": self.file_name,
            "duration": "200"  # 与app.py保持一致
        }
        
        # 读取文件内容
        with open(self.upload_file_path, 'rb') as f:
            file_content = f.read()
        
        # 构建URL参数
        url = upload_url + "?" + urllib.parse.urlencode(param_dict)
        print(f"上传URL: {url}")
        
        # 发送POST请求
        try:
            # 关键：这里不要设置headers，让requests自动处理Content-Type
            response = requests.post(url=url, data=file_content)
            
            print(f"上传响应状态码: {response.status_code}")
            print(f"上传响应内容: {response.text}")
            
            # 检查响应
            if response.status_code != 200:
                raise TranscriptionError(f"上传音频失败: HTTP {response.status_code}, {response.text}")
            
            try:
                result = response.json()
            except json.JSONDecodeError:
                raise TranscriptionError(f"解析响应失败，响应不是有效的JSON: {response.text}")
            
            # 检查API返回的错误代码
            if result.get('code') != "000000":
                error_code = result.get('code', 'unknown')
                error_msg = result.get('message', '未知错误')
                error_desc = result.get('descInfo', '')
                
                # 处理特定错误代码
                if error_code == "26625":
                    raise TranscriptionError(f"讯飞API余额不足: {error_desc}，请充值或领取免费时长")
                elif error_code.startswith("105"):
                    raise TranscriptionError(f"讯飞API签名验证失败: {error_desc}，请检查APPID和Secret Key")
                elif error_code.startswith("10"):
                    raise TranscriptionError(f"讯飞API参数错误: {error_desc}")
                else:
                    raise TranscriptionError(f"讯飞API错误 (代码: {error_code}): {error_desc or error_msg}")
            
            if 'content' not in result or 'orderId' not in result['content']:
                raise TranscriptionError(f"上传音频失败: 响应格式不正确 {response.text}")
                
            print(f"音频上传成功，订单ID: {result['content']['orderId']}")
            return result['content']['orderId']
        except requests.RequestException as e:
            raise TranscriptionError(f"请求异常: {str(e)}")
        except json.JSONDecodeError as e:
            raise TranscriptionError(f"解析响应JSON失败: {str(e)}, 响应内容: {response.text}")

    def get_result(self, order_id, max_retry=60, interval=5):
        """获取转写结果 - 处理特殊状态码"""
        print(f"等待转写结果，订单ID: {order_id}")
        get_result_url = "https://raasr.xfyun.cn/v2/api/getResult"
        
        # 轮询结果
        for i in range(max_retry):
            # 每次请求都重新计算时间戳和签名（非常重要）
            ts = str(int(time.time()))
            m2 = hashlib.md5()
            m2.update((self.appid + ts).encode('utf-8'))
            md5 = m2.hexdigest()
            signa = hmac.new(self.secret_key.encode('utf-8'), md5.encode('utf-8'), hashlib.sha1).digest()
            signa = base64.b64encode(signa).decode('utf-8')
            
            param_dict = {
                'appId': self.appid,
                'signa': signa,
                'ts': ts,
                'orderId': order_id,
                'resultType': 'transfer,predict'
            }
            
            url = get_result_url + "?" + urllib.parse.urlencode(param_dict)
            print(f"第 {i+1}/{max_retry} 次查询结果，URL: {url}")
            
            try:
                # 使用GET请求获取结果
                response = requests.get(url=url)
                
                print(f"获取结果响应状态码: {response.status_code}")
                content = response.text[:200] + "..." if len(response.text) > 200 else response.text
                print(f"获取结果响应内容: {content}")
                
                if response.status_code != 200:
                    print(f"获取结果失败: HTTP {response.status_code}")
                    time.sleep(interval)
                    continue
                
                result = response.json()
                if result.get('code') != "000000":
                    print(f"获取结果错误 (代码: {result.get('code')}): {result.get('descInfo', '')}")
                    time.sleep(interval)
                    continue
                
                # 检查处理状态
                if 'content' not in result:
                    print(f"获取结果响应格式不正确")
                    time.sleep(interval)
                    continue
                
                # 重要：如果orderResult字段存在，即使状态不是4，也尝试解析结果
                if 'orderResult' in result['content'] and result['content']['orderResult']:
                    print("检测到orderResult字段，尝试解析结果")
                    return self.parse_result(result['content']['orderResult'])
                
                # 正常处理状态检查
                if 'orderInfo' not in result['content']:
                    print(f"未找到orderInfo字段")
                    time.sleep(interval)
                    continue
                
                status = result['content']['orderInfo']['status']
                print(f"当前处理状态: {status}")
                
                # 状态码-1，但有orderResult字段时，也尝试解析
                if status == -1 and 'orderResult' in result['content'] and result['content']['orderResult']:
                    print("状态码为-1，但存在结果数据，尝试解析")
                    return self.parse_result(result['content']['orderResult'])
                
                # 状态码4：转写完成
                if status == 4:
                    print("转写完成，正在解析结果")
                    
                    # 检查多种可能的结果位置
                    if 'orderResult' in result['content']:
                        print("发现orderResult字段，解析中...")
                        return self.parse_result(result['content']['orderResult'])
                    elif 'lattice' in result['content']:
                        print("发现lattice字段，解析中...")
                        return self.parse_lattice(result['content']['lattice'])
                    else:
                        # 如果没有找到结果但状态是完成，等待一次再试
                        print("转写完成但未找到结果字段，再等待一次...")
                        time.sleep(interval * 2)
                        continue
                
                elif status == 3:  # 处理中
                    print(f"转写处理中... ({i+1}/{max_retry})")
                    time.sleep(interval)
                
                else:
                    # 状态为其他值，但仍检查是否有结果数据
                    if 'orderResult' in result['content'] and result['content']['orderResult']:
                        print(f"状态码异常({status})但存在数据，尝试解析")
                        return self.parse_result(result['content']['orderResult'])
                    
                    # 如果没有结果数据，则判定为失败
                    reason = result['content']['orderInfo'].get('reason', '未知原因')
                    print(f"转写失败或状态异常: 状态码={status}, 原因={reason}")
                    # 不立即报错，继续尝试，因为可能是临时状态
                    time.sleep(interval)
            
            except requests.RequestException as e:
                print(f"请求异常: {str(e)}")
                time.sleep(interval)
            except json.JSONDecodeError as e:
                print(f"解析结果JSON失败: {str(e)}")
                time.sleep(interval)
            except Exception as e:
                print(f"获取结果异常: {str(e)}")
                time.sleep(interval)
        
        # 达到最大重试次数
        raise TranscriptionError(f"转写超时，最大重试次数: {max_retry}")
    
    def parse_lattice(self, lattice):
        """直接解析lattice字段的转写结果"""
        print("开始解析lattice字段")
        try:
            transcribed_text = ""
            
            if not lattice:
                raise TranscriptionError("lattice字段为空")
            
            for sentence in lattice:
                if 'json_1best' not in sentence or not sentence['json_1best']:
                    continue
                
                try:
                    # 解析json_1best字段
                    json_1best = sentence['json_1best']
                    if isinstance(json_1best, str):
                        json_1best = json.loads(json_1best)
                    
                    if 'st' not in json_1best or 'rt' not in json_1best['st']:
                        continue
                    
                    # 从rt字段中提取文本
                    for rt in json_1best['st']['rt']:
                        if 'ws' not in rt:
                            continue
                        
                        for ws in rt['ws']:
                            if 'cw' not in ws:
                                continue
                            
                            for cw in ws['cw']:
                                if 'w' in cw:
                                    transcribed_text += cw['w']
                except Exception as e:
                    print(f"解析句子时出错: {str(e)}")
                    continue
            
            print(f"lattice解析完成, 获取到文本长度: {len(transcribed_text)}")
            return transcribed_text.strip()
        except Exception as e:
            raise TranscriptionError(f"解析lattice字段失败: {str(e)}")
    
    def parse_result(self, order_result):
        """解析orderResult字段"""
        print("开始解析orderResult字段")
        try:
            # 先将字符串转换为JSON对象(如果需要)
            if isinstance(order_result, str):
                order_result = json.loads(order_result)
                
            transcribed_text = ""
            
            # 检查结果格式，适应不同的返回结构
            if 'lattice' in order_result:
                # 如果存在lattice字段，使用lattice解析
                return self.parse_lattice(order_result['lattice'])
            
            # 遍历所有可能的结果位置
            possible_fields = ['lattice', 'sentences', 'results']
            for field in possible_fields:
                if field in order_result:
                    print(f"发现字段: {field}")
                    if field == 'sentences':
                        # 直接从sentences中获取文本
                        for sentence in order_result['sentences']:
                            if 'text' in sentence:
                                transcribed_text += sentence['text']
                    elif field == 'results':
                        # 从results中获取文本
                        for result in order_result['results']:
                            if 'text' in result:
                                transcribed_text += result['text']
            
            # 如果以上字段都没有找到文本
            if not transcribed_text:
                print("使用备选解析方法")
                # 记录原始JSON以便调试
                print(f"原始JSON结构: {json.dumps(order_result)[:200]}...")
                
                # 使用纯文本方法提取所有可能的文本
                result_text = json.dumps(order_result, ensure_ascii=False)
                # 尝试提取所有""之间的内容作为文本
                import re
                text_parts = re.findall(r'"w":"([^"]+)"', result_text)
                transcribed_text = ''.join(text_parts)
            
            print(f"orderResult解析完成, 获取到文本长度: {len(transcribed_text)}")
            return transcribed_text.strip()
        except Exception as e:
            raise TranscriptionError(f"解析orderResult字段失败: {str(e)}")

    def process(self):
        """处理音频转写的完整流程"""
        try:
            # 1. 上传文件
            print("步骤1: 上传音频文件")
            order_id = self.upload()
            
            # 2. 获取转写结果
            print("步骤2: 获取转写结果")
            transcript = self.get_result(order_id, max_retry=120, interval=5)  # 更新等待时间和重试次数
            
            if not transcript:
                raise TranscriptionError("未获取到转写结果")
                
            return transcript
        except Exception as e:
            raise TranscriptionError(f"转写处理失败: {str(e)}")

def transcribe_audio(audio_path, transcript_path):
    """转录音频为文本"""
    try:
        print(f"开始转录音频: {audio_path}")
        
        # 检查文件存在
        if not os.path.exists(audio_path):
            raise TranscriptionError(f"音频文件不存在: {audio_path}")
            
        # 使用讯飞API转写
        appid = os.environ.get('XFYUN_APPID', XFYUN_APPID)
        secret_key = os.environ.get('XFYUN_SECRET_KEY', XFYUN_SECRET_KEY)
        
        print(f"使用讯飞语音转写API，APPID: {appid}")
        
        asr = XfyunAsr(appid, secret_key, audio_path)
        transcript = asr.process()
        
        # 将结果写入文件
        with open(transcript_path, 'w', encoding='utf-8') as f:
            f.write(transcript)
            
        print(f"转录完成，结果已保存到: {transcript_path}")
        return transcript
        
    except Exception as e:
        error_message = f"转录音频失败: {str(e)}"
        print(f"错误: {error_message}")
        raise TranscriptionError(error_message)

def generate_summary(transcript, summary_path, title=""):
    """生成摘要"""
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(summary_path), exist_ok=True)
        
        # 设置OpenAI API
        openai.api_key = DEEPSEEK_API_KEY
        openai.api_base = DEEPSEEK_API_BASE
        
        # 添加标题上下文
        prompt = f"请帮我总结以下{title}的内容要点，并制作成便于阅读的笔记形式：\n\n{transcript}"
        
        # 调用API生成摘要
        response = openai.ChatCompletion.create(
            model="deepseek-reasoner", 
            messages=[
                {"role": "system", "content": "你是一个擅长笔记整理和知识提炼的助手。根据提供的内容，生成结构化、清晰的学习笔记。请使用中文回答。"},
                {"role": "user", "content": prompt}
            ],
            stream=False
        )
        
        # 提取摘要
        summary = response.choices[0].message.content
        
        # 保存摘要
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        return summary
    
    except Exception as e:
        raise SummaryError(f"生成摘要失败: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: video_processor.py [命令] [参数...]")
        print("命令:")
        print("  download [视频URL] [输出路径]")
        print("  extract_audio [视频路径] [输出路径]")
        print("  transcribe [音频路径] [输出路径]")
        print("  summarize [转录文本] [输出路径] [标题(可选)]")
        print("  get_duration [音频路径]")
        print("  get_title [视频URL]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    try:
        if command == "download":
            if len(sys.argv) < 4:
                print("用法: video_processor.py download [视频URL] [输出路径]")
                sys.exit(1)
            
            video_url = sys.argv[2]
            output_path = sys.argv[3]
            download_video(video_url, output_path)
            print(f"视频已下载到: {output_path}")
        
        elif command == "extract_audio":
            if len(sys.argv) < 4:
                print("用法: video_processor.py extract_audio [视频路径] [输出路径]")
                sys.exit(1)
            
            video_path = sys.argv[2]
            audio_path = sys.argv[3]
            extract_audio(video_path, audio_path)
            print(f"音频已提取到: {audio_path}")
        
        elif command == "transcribe":
            if len(sys.argv) < 4:
                print("用法: video_processor.py transcribe [音频路径] [输出路径]")
                sys.exit(1)
            
            audio_path = sys.argv[2]
            transcript_path = sys.argv[3]
            transcribe_audio(audio_path, transcript_path)
            print(f"转录已保存到: {transcript_path}")
        
        elif command == "summarize":
            if len(sys.argv) < 4:
                print("用法: video_processor.py summarize [转录文本] [输出路径] [标题(可选)]")
                sys.exit(1)
            
            transcript = sys.argv[2]
            summary_path = sys.argv[3]
            title = sys.argv[4] if len(sys.argv) > 4 else ""
            
            # 检查是否是文件路径
            if os.path.exists(transcript):
                with open(transcript, 'r', encoding='utf-8') as f:
                    transcript_text = f.read()
            else:
                transcript_text = transcript
            
            generate_summary(transcript_text, summary_path, title)
            print(f"摘要已保存到: {summary_path}")
        
        elif command == "get_duration":
            if len(sys.argv) < 3:
                print("用法: video_processor.py get_duration [音频路径]")
                sys.exit(1)
            
            audio_path = sys.argv[2]
            duration = get_audio_duration(audio_path)
            print(duration)
        
        elif command == "get_title":
            if len(sys.argv) < 3:
                print("用法: video_processor.py get_title [视频URL]")
                sys.exit(1)
            
            video_url = sys.argv[2]
            title = get_video_title(video_url)
            print(title)
        
        else:
            print(f"未知命令: {command}")
            sys.exit(1)
        
    except Exception as e:
        print(f"错误: {str(e)}", file=sys.stderr)
        sys.exit(1)

def get_video_title(video_url):
    """获取视频标题，目前支持bilibili"""
    try:
        if "bilibili.com" in video_url:
            return get_bilibili_title(video_url)
        else:
            return ""
    except Exception as e:
        print(f"获取视频标题失败: {str(e)}", file=sys.stderr)
        return ""

def get_bilibili_title(url):
    """获取B站视频标题"""
    try:
        response = requests.get(url, headers={
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        })
        
        if response.status_code != 200:
            return ""
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 方法1：尝试获取视频标题
        title_element = soup.find('meta', {'property': 'og:title'})
        if title_element and title_element.get('content'):
            return title_element.get('content')
        
        # 方法2：尝试获取标题
        title_element = soup.find('title')
        if title_element:
            title = title_element.text
            # 移除B站固定后缀
            if " - bilibili" in title:
                title = title.replace(" - bilibili", "")
            if "_哔哩哔哩_bilibili" in title:
                title = title.replace("_哔哩哔哩_bilibili", "")
            return title
        
        # 方法3: 尝试获取h1标题
        h1_element = soup.find('h1', {'class': 'video-title'})
        if h1_element:
            return h1_element.text.strip()
        
        return ""
    except Exception as e:
        print(f"获取B站视频标题失败: {str(e)}", file=sys.stderr)
        return ""

if __name__ == "__main__":
    main() 
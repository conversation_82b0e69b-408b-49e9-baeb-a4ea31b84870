#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import base64
import hmac
import hashlib
import time
import requests
import subprocess
import re
from datetime import datetime
from urllib.parse import urlencode, quote
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 讯飞API配置
XFYUN_APPID = os.environ.get('XFYUN_APPID', '')
XFYUN_SECRET_KEY = os.environ.get('XFYUN_SECRET_KEY', '')
XFYUN_API_URL = "https://raasr.xfyun.cn/api/v2"

class TranscriptionError(Exception):
    """转录过程中的错误"""
    pass

class XfyunAsr:
    """讯飞语音识别API封装"""

    def __init__(self, appid, secret_key, audio_path):
        self.appid = appid
        self.secret_key = secret_key
        self.audio_path = audio_path

    def upload(self):
        """上传音频文件"""
        try:
            # 准备请求参数
            current_time = str(int(time.time()))
            param_dict = {
                "app_id": self.appid,
                "signa": self._generate_signature(current_time),
                "ts": current_time,
                "file_len": os.path.getsize(self.audio_path),
                "file_name": os.path.basename(self.audio_path),
                "slice_num": 1
            }

            # 读取音频文件
            with open(self.audio_path, 'rb') as f:
                audio_data = f.read()

            # 准备表单数据
            files = {
                'content': (None, json.dumps(param_dict), 'application/json'),
                'file': (os.path.basename(self.audio_path), audio_data, 'audio/mpeg')
            }

            # 发送请求
            response = requests.post(f"{XFYUN_API_URL}/upload", files=files)

            # 检查响应
            if response.status_code != 200:
                raise TranscriptionError(f"上传失败，状态码: {response.status_code}, 响应: {response.text}")

            result = response.json()
            if result.get('ok') != 0:
                raise TranscriptionError(f"上传失败，错误码: {result.get('err_no')}, 错误信息: {result.get('failed')}")

            # 返回订单ID
            order_id = result.get('data', {}).get('orderid')
            if not order_id:
                raise TranscriptionError("未获取到订单ID")

            print(f"上传成功，订单ID: {order_id}")
            return order_id

        except Exception as e:
            raise TranscriptionError(f"上传音频失败: {str(e)}")

    def _generate_signature(self, ts):
        """生成签名"""
        base_string = self.appid + ts
        hash_obj = hmac.new(self.secret_key.encode('utf-8'), base_string.encode('utf-8'), hashlib.sha1)
        return base64.b64encode(hash_obj.digest()).decode('utf-8')

    def get_result(self, order_id, max_retry=60, interval=5):
        """获取转写结果"""
        try:
            # 准备请求参数
            current_time = str(int(time.time()))
            param_dict = {
                "app_id": self.appid,
                "signa": self._generate_signature(current_time),
                "ts": current_time,
                "orderid": order_id
            }

            # 查询结果
            for i in range(max_retry):
                print(f"第{i+1}次查询结果...")

                response = requests.post(f"{XFYUN_API_URL}/getResult", data=param_dict)

                if response.status_code != 200:
                    print(f"查询失败，状态码: {response.status_code}, 响应: {response.text}")
                    time.sleep(interval)
                    continue

                result = response.json()
                if result.get('ok') != 0:
                    print(f"查询失败，错误码: {result.get('err_no')}, 错误信息: {result.get('failed')}")
                    time.sleep(interval)
                    continue

                # 检查转写状态
                data = result.get('data', {})
                status = data.get('status')

                if status == 4:  # 转写完成
                    print("转写完成，获取结果")
                    return self._parse_result(data)
                elif status == 3:  # 转写失败
                    raise TranscriptionError("转写失败")
                else:  # 其他状态，继续等待
                    progress = data.get('process')
                    print(f"转写进行中，进度: {progress}%")
                    time.sleep(interval)

            raise TranscriptionError(f"超过最大重试次数 {max_retry}，未获取到结果")

        except Exception as e:
            raise TranscriptionError(f"获取转写结果失败: {str(e)}")

    def _parse_result(self, data):
        """解析转写结果"""
        try:
            # 获取转写结果字段
            order_result = data.get('orderResult')
            if not order_result:
                raise TranscriptionError("未找到转写结果字段")

            # 解析JSON
            result_data = json.loads(order_result)

            # 提取文本
            transcribed_text = ""
            for sentence in result_data.get('lattice', []):
                json_result = json.loads(sentence.get('json_1best', '{}'))
                for word in json_result.get('st', {}).get('rt', []):
                    for w in word.get('ws', []):
                        transcribed_text += w.get('cw', [{}])[0].get('w', '')

            print(f"orderResult解析完成, 获取到文本长度: {len(transcribed_text)}")
            return transcribed_text.strip()
        except Exception as e:
            raise TranscriptionError(f"解析orderResult字段失败: {str(e)}")

    def process(self):
        """处理音频转写的完整流程"""
        try:
            # 1. 上传文件
            print("步骤1: 上传音频文件")
            order_id = self.upload()

            # 2. 获取转写结果
            print("步骤2: 获取转写结果")
            transcript = self.get_result(order_id, max_retry=120, interval=5)  # 更新等待时间和重试次数

            if not transcript:
                raise TranscriptionError("未获取到转写结果")

            return transcript
        except Exception as e:
            raise TranscriptionError(f"转写处理失败: {str(e)}")

def transcribe_audio(audio_path, transcript_path):
    """转录音频为文本"""
    try:
        print(f"开始转录音频: {audio_path}")

        # 检查文件存在
        if not os.path.exists(audio_path):
            raise TranscriptionError(f"音频文件不存在: {audio_path}")

        # 使用讯飞API转写
        appid = os.environ.get('XFYUN_APPID', XFYUN_APPID)
        secret_key = os.environ.get('XFYUN_SECRET_KEY', XFYUN_SECRET_KEY)

        # 如果没有配置讯飞API，使用模拟数据
        if not appid or not secret_key:
            print("未配置讯飞API，使用模拟数据")
            mock_transcript = """这是一段模拟的音频转录文本。
由于未配置讯飞API，系统生成了这段模拟文本。
实际使用时，这里将是真实的音频转录内容。
音频内容可能包含重要信息、会议记录、课程内容等。
AI将帮助您分析这些内容并生成结构化的笔记。"""

            # 将结果写入文件
            with open(transcript_path, 'w', encoding='utf-8') as f:
                f.write(mock_transcript)

            print(f"使用模拟数据，结果已保存到: {transcript_path}")
            return mock_transcript

        print(f"使用讯飞语音转写API，APPID: {appid}")

        asr = XfyunAsr(appid, secret_key, audio_path)
        transcript = asr.process()

        # 将结果写入文件
        with open(transcript_path, 'w', encoding='utf-8') as f:
            f.write(transcript)

        print(f"转录完成，结果已保存到: {transcript_path}")
        return transcript

    except Exception as e:
        error_message = f"转录音频失败: {str(e)}"
        print(f"错误: {error_message}")
        raise TranscriptionError(error_message)

def get_audio_duration(audio_path):
    """获取音频文件时长（秒）"""
    try:
        # 使用ffprobe获取音频时长
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            audio_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            print(f"获取音频时长失败: {result.stderr}")
            return 0

        duration = float(result.stdout.strip())
        print(f"音频时长: {duration}秒")
        return duration

    except Exception as e:
        print(f"获取音频时长时出错: {str(e)}")
        return 0

def convert_audio_format(input_path, output_path):
    """转换音频格式为MP3"""
    try:
        # 使用ffmpeg转换音频格式
        cmd = [
            'ffmpeg',
            '-i', input_path,
            '-vn',  # 不处理视频
            '-ar', '16000',  # 采样率16kHz
            '-ac', '1',  # 单声道
            '-b:a', '128k',  # 比特率
            '-y',  # 覆盖现有文件
            output_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            print(f"转换音频格式失败: {result.stderr}")
            return False

        print(f"音频格式转换成功: {output_path}")
        return True

    except Exception as e:
        print(f"转换音频格式时出错: {str(e)}")
        return False

def generate_summary(transcript_path, summary_path, title="录音笔记"):
    """生成笔记摘要"""
    try:
        print(f"开始生成笔记摘要: {transcript_path}")

        # 检查文件存在
        if not os.path.exists(transcript_path):
            raise Exception(f"转录文件不存在: {transcript_path}")

        # 读取转录文本
        with open(transcript_path, 'r', encoding='utf-8') as f:
            transcript = f.read()

        if not transcript.strip():
            raise Exception("转录文本为空")

        # 使用模型生成摘要
        try:
            # 尝试使用模型服务
            # 这里可以集成各种模型服务，如讯飞星火、火山引擎等
            # 当前使用模拟数据
            raise Exception("模型服务暂时不可用")

        except Exception as model_error:
            print(f"使用模型服务失败: {model_error}，使用备选方法")

            # 备选方法：使用简单的规则生成摘要
            summary = generate_mock_summary(transcript, title)

        # 将结果写入文件
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary)

        print(f"笔记生成完成，结果已保存到: {summary_path}")
        return summary

    except Exception as e:
        print(f"生成笔记失败: {e}")

        # 使用模拟数据作为备选
        mock_summary = generate_mock_summary("模拟转录内容", title)

        # 将结果写入文件
        try:
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(mock_summary)
            print(f"使用模拟数据，结果已保存到: {summary_path}")
        except Exception as write_error:
            print(f"写入模拟数据失败: {write_error}")

        return mock_summary

def generate_mock_summary(transcript, title="录音笔记"):
    """生成模拟笔记摘要"""
    # 生成一个基于转录文本的摘要
    summary_intro = f"# {title}学习笔记\n\n"

    # 提取关键句子
    sentences = [s for s in re.split(r'[。！？.!?]\s*', transcript) if s.strip()]

    # 生成标题
    sections = [
        "核心知识点",
        "要点总结",
        "重要概念",
        "学习收获",
        "应用思考"
    ]

    mock_summary = summary_intro

    # 生成模拟笔记内容
    for i in range(min(len(sections), 3)):
        mock_summary += f"## {sections[i]}\n\n"

        # 每个部分选择不同数量的句子
        start_idx = int((len(sentences) / 5) * i) if sentences else 0
        end_idx = int((len(sentences) / 5) * (i + 1)) if sentences else 0
        section_sentences = sentences[start_idx:end_idx] if start_idx < len(sentences) else []

        for j in range(min(len(section_sentences), 3)):
            if len(section_sentences[j]) > 10:
                mock_summary += f"- {section_sentences[j]}\n"

        mock_summary += "\n"

    mock_summary += "## 总结\n\n本笔记根据录音内容自动生成，由于转录服务暂时不可用，部分内容可能不准确。请参考原始录音进行核对和补充。"

    return mock_summary

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python audio_processor.py <命令> [参数...]")
        print("命令:")
        print("  transcribe <音频文件路径> <输出文件路径>")
        print("  get_duration <音频文件路径>")
        print("  convert <输入文件路径> <输出文件路径>")
        print("  summarize <转录文件路径> <输出文件路径> [标题]")
        return

    command = sys.argv[1]

    if command == "transcribe" and len(sys.argv) >= 4:
        audio_path = sys.argv[2]
        transcript_path = sys.argv[3]
        try:
            transcribe_audio(audio_path, transcript_path)
        except Exception as e:
            print(f"转录失败: {str(e)}")
            sys.exit(1)

    elif command == "get_duration" and len(sys.argv) >= 3:
        audio_path = sys.argv[2]
        duration = get_audio_duration(audio_path)
        print(duration)

    elif command == "convert" and len(sys.argv) >= 4:
        input_path = sys.argv[2]
        output_path = sys.argv[3]
        success = convert_audio_format(input_path, output_path)
        if not success:
            sys.exit(1)

    elif command == "summarize" and len(sys.argv) >= 4:
        transcript_path = sys.argv[2]
        summary_path = sys.argv[3]
        title = sys.argv[4] if len(sys.argv) > 4 else "录音笔记"

        try:
            generate_summary(transcript_path, summary_path, title)
        except Exception as e:
            print(f"生成笔记失败: {str(e)}")
            sys.exit(1)

    else:
        print("无效的命令或参数不足")
        sys.exit(1)

if __name__ == "__main__":
    main()

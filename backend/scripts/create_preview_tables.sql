-- 创建预习发布表
CREATE TABLE IF NOT EXISTS preview_publish (
  id INT NOT NULL AUTO_INCREMENT,
  course_code VARCHAR(20) NOT NULL,
  class_id INT NOT NULL,
  preview_material_id INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  deadline DATETIME NOT NULL,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY fk_preview_material (preview_material_id),
  KEY preview_publish_course_FK (course_code),
  KEY fk_preview_class_id (class_id),
  CONSTRAINT fk_preview_class_id FOREIGN KEY (class_id) REFERENCES course_classes(id) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT fk_preview_material FOREIGN KEY (preview_material_id) REFERENCES preview_materials(id),
  CONSTRAINT preview_publish_course_FK FOREIGN KEY (course_code) REFERENCES courses(course_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预习发布表';

-- 创建学生预习状态表
CREATE TABLE IF NOT EXISTS preview_student (
  id INT NOT NULL AUTO_INCREMENT,
  preview_publish_id INT NOT NULL,
  student_id INT NOT NULL,
  status TINYINT DEFAULT 0 COMMENT '0: 未完成, 1: 已完成',
  submit_time DATETIME,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY uk_preview_student (preview_publish_id, student_id),
  KEY fk_preview_publish (preview_publish_id),
  KEY fk_preview_student (student_id),
  CONSTRAINT fk_preview_publish FOREIGN KEY (preview_publish_id) REFERENCES preview_publish(id) ON DELETE CASCADE,
  CONSTRAINT fk_preview_student FOREIGN KEY (student_id) REFERENCES students(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生预习状态表'; 
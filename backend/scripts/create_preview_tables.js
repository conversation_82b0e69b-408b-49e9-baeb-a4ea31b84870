import fs from 'fs';
import path from 'path';
import pool from '../config/db.js';

async function createPreviewTables() {
  try {
    // 读取SQL文件
    const sqlPath = path.join(process.cwd(), 'scripts', 'create_preview_tables.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // 分割SQL语句
    const statements = sqlContent.split(';').filter(stmt => stmt.trim());

    // 执行每个SQL语句
    for (const statement of statements) {
      if (statement.trim()) {
        await pool.execute(statement);
        console.log('✓ 执行SQL语句成功');
      }
    }

    console.log('✓ 所有表创建成功');
    process.exit(0);
  } catch (error) {
    console.error('❌ 创建表失败:', error);
    process.exit(1);
  }
}

createPreviewTables(); 
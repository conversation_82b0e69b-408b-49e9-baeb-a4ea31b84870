import pool from '../config/db.js';

async function createPreviewMaterialsTable() {
  try {
    console.log('开始创建预习资料表...');
    
    const createTableSQL = `
    CREATE TABLE IF NOT EXISTS \`preview_materials\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`syllabus_id\` int(11) NOT NULL COMMENT '大纲ID',
      \`teacher_id\` varchar(50) NOT NULL COMMENT '教师ID',
      \`title_id\` varchar(50) NOT NULL COMMENT '章节ID',
      \`content\` text NOT NULL COMMENT '预习资料内容',
      \`course_code\` varchar(50) NOT NULL COMMENT '课程代码',
      \`chapter_title\` varchar(255) NOT NULL COMMENT '章节标题',
      \`create_time\` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      \`update_time\` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (\`id\`),
      KEY \`idx_syllabus_title\` (\`syllabus_id\`, \`title_id\`),
      KEY \`idx_teacher_course\` (\`teacher_id\`, \`course_code\`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预习资料表';
    `;
    
    await pool.query(createTableSQL);
    console.log('预习资料表创建成功！');
    
    process.exit(0);
  } catch (error) {
    console.error('创建预习资料表失败:', error);
    process.exit(1);
  }
}

createPreviewMaterialsTable(); 
-- 为 preview_view_logs 表添加观看时长字段
ALTER TABLE preview_view_logs
ADD COLUMN view_duration INT DEFAULT 0 COMMENT '本次观看时长(秒)' AFTER view_time;

-- 为 preview_student 表添加总观看时长和总观看次数字段
ALTER TABLE preview_student
ADD COLUMN total_view_duration INT DEFAULT 0 COMMENT '总观看时长(秒)' AFTER content,
ADD COLUMN total_view_count INT DEFAULT 0 COMMENT '总观看次数' AFTER total_view_duration;

-- 更新索引
CREATE INDEX idx_preview_view_student ON preview_view_logs(preview_id, student_id); 
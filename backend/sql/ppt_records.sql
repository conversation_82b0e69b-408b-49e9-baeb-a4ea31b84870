CREATE TABLE IF NOT EXISTS `ppt_records` (
  `id` INT AUTO_INCREMENT,
  `teacher_id` VARCHAR(10) NOT NULL COMMENT '教师系统ID',
  `course_code` VARCHAR(4) NOT NULL COMMENT '课程代码',
  `title` VARCHAR(50) NOT NULL COMMENT '课时标题',
  `sid` VARCHAR(100) NOT NULL COMMENT '讯飞API任务ID',
  `status` ENUM('processing', 'completed', 'failed') NOT NULL DEFAULT 'processing' COMMENT 'PPT生成状态',
  `download_url` TEXT COMMENT '下载链接',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_teacher_course` (`teacher_id`, `course_code`),
  KEY `idx_sid` (`sid`),
  
  CONSTRAINT `ppt_records_teacher_fk` 
    FOREIGN KEY (`teacher_id`) 
    REFERENCES `teaching_assistant` (`system_teacher_id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
    
  CONSTRAINT `ppt_records_course_fk` 
    FOREIGN KEY (`course_code`) 
    REFERENCES `courses` (`course_code`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PPT生成记录表'; 
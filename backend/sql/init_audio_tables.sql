-- 创建音频转录表
CREATE TABLE IF NOT EXISTS `audio_transcriptions` (
  `id` VARCHAR(36) PRIMARY KEY,
  `user_id` INT NOT NULL,
  `transcript` TEXT NOT NULL,
  `duration` INT DEFAULT 0,
  `note_id` VARCHAR(36) DEFAULT NULL,
  `created_at` DATETIME NOT NULL,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_note_id` (`note_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建音频笔记摘要表
CREATE TABLE IF NOT EXISTS `audio_summaries` (
  `id` VARCHAR(36) PRIMARY KEY,
  `user_id` INT NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `transcript` TEXT NOT NULL,
  `summary` TEXT NOT NULL,
  `created_at` DATETIME NOT NULL,
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 确保笔记表存在（如果已存在则不会重复创建）
CREATE TABLE IF NOT EXISTS `notes` (
  `id` VARCHAR(36) PRIMARY KEY,
  `user_id` INT NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `content` TEXT NOT NULL,
  `source_type` ENUM('video', 'audio', 'text') NOT NULL DEFAULT 'text',
  `created_at` DATETIME NOT NULL,
  `updated_at` DATETIME NULL,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_source_type` (`source_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 初始化模型配置表数据
-- 在model_configs表中插入现有的API密钥配置

-- 清空表数据
TRUNCATE TABLE `model_configs`;

-- 插入火山引擎配置
INSERT INTO `model_configs` 
(`name`, `provider`, `api_key`, `app_id`, `base_url`, `usage_scenario`, `status`) 
VALUES 
('火山引擎', 'volcano', '1dfa2f7b-c0f7-486c-bd5a-068ff9c8d677', NULL, 'https://ark.cn-beijing.volces.com/api/v3', 'chat', 1);

-- 插入DeepSeek配置
INSERT INTO `model_configs` 
(`name`, `provider`, `api_key`, `app_id`, `base_url`, `usage_scenario`, `status`) 
VALUES 
('DeepSeek Chat', 'deepseek', '***********************************', NULL, 'https://api.deepseek.com', 'exercise', 1);

-- 插入Moonshot配置
INSERT INTO `model_configs` 
(`name`, `provider`, `api_key`, `app_id`, `base_url`, `usage_scenario`, `status`) 
VALUES 
('Moonshot AI', 'moonshot', 'sk-d2CeAhiTHf7OcFOnPZnJYZN3dgeVyXSY4ei3tsPFydpCmsEi', NULL, 'https://api.moonshot.cn/v1', 'search', 1);

-- 插入讯飞语音转写API配置
INSERT INTO `model_configs` 
(`name`, `provider`, `api_key`, `app_id`, `base_url`, `usage_scenario`, `status`) 
VALUES 
('讯飞语音转写', 'xfyun', 'c1873354be83d841f8102078adedb789', '183534df', 'https://raasr.xfyun.cn/v2/api', 'transcription', 1);

-- 确认已插入的数据
SELECT * FROM `model_configs`; 
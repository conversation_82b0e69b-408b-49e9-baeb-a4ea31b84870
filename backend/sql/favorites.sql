CREATE TABLE IF NOT EXISTS favorites (
  id INT PRIMARY KEY AUTO_INCREMENT,
  system_teacher_id INT NOT NULL,
  resource_type ENUM('video', 'article') NOT NULL,
  resource_id VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  url TEXT NOT NULL,
  description TEXT,
  cover_url TEXT,
  source VARCHAR(100) NOT NULL,
  source_type VARCHAR(50) NOT NULL,
  author VARCHA<PERSON>(100),
  status TINYINT DEFAULT 1,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_teacher_resource (system_teacher_id, resource_type, resource_id),
  INDEX idx_status (status),
  FOREIGN KEY (system_teacher_id) REFERENCES system_teacher(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 
{"name": "teacher-assistant", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "ant-design-vue": "^4.1.2", "axios": "^1.8.4", "child_process": "^1.0.2", "connect-history-api-fallback": "^2.0.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "echarts": "^5.5.0", "element-plus": "^2.9.6", "file-saver": "^2.0.5", "form-data": "^4.0.2", "fs": "^0.0.1-security", "katex": "^0.16.22", "marked": "^15.0.7", "mime-types": "^3.0.1", "multer": "^1.4.5-lts.2", "openai": "^4.91.0", "path": "^0.12.7", "pinia": "^3.0.1", "vue": "^3.3.4", "vue-router": "^4.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "cross-env": "^7.0.3", "less": "^4.2.2", "vite": "^4.4.5"}}
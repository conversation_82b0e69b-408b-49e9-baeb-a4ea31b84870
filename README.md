# 启智星（EduSpark）- AI教学助手平台

启智星（EduSpark）是一个基于人工智能技术的教学助手平台，旨在为教师和学生提供全方位的智能教学解决方案。平台集成了智能备课、班级管理、作业与考试、学习分析等核心功能，通过AI技术赋能教育，提升教学效率和学习效果。

## 项目简介

启智星平台致力于解决教育工作者在教学过程中面临的备课繁重、批改耗时、学情分析困难等问题。通过人工智能技术，平台能够自动生成教案、智能批改作业、分析学习数据，为教师提供精准的教学决策支持，同时为学生提供个性化的学习路径。

### 核心功能

- **智能备课**：AI辅助教案编写，自动生成教学资源，提供个性化教学建议
- **班级管理**：便捷的学生信息管理，班级数据分析，提升教学效率
- **作业与考试**：智能生成习题，自动批改作业，快速生成分析报告
- **学习分析**：数据可视化分析，个性化学习路径推荐，精准掌握学情动态
- **视频笔记生成**：自动转录视频内容，生成结构化笔记
- **录音笔记生成**：将录音内容转换为文字，并生成结构化笔记

## 技术架构

### 前端技术栈

- **框架**：Vue 3
- **UI组件库**：Ant Design Vue 4.x、Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router 4.x
- **构建工具**：Vite
- **HTTP客户端**：Axios
- **图表库**：ECharts

### 后端技术栈

- **运行环境**：Node.js
- **Web框架**：Express.js
- **数据库**：MySQL
- **ORM**：MySQL2
- **身份验证**：JWT (jsonwebtoken)
- **文件处理**：Multer
- **AI集成**：OpenAI、讯飞、DeepSeek、Moonshot等多种大模型API

### 其他技术

- **Python**：用于视频处理、音频转录等功能
- **FFmpeg**：用于音视频处理
- **you-get**：用于视频下载

## 环境要求

### 基础环境

- **Node.js**: v16.0.0 或更高版本
- **Python**: v3.6 或更高版本
- **MySQL**: v8.0 或更高版本
- **FFmpeg**: 最新稳定版

### 前端依赖

```bash
npm install
```

### 后端依赖

```bash
cd backend
npm install
```

### Python依赖

```bash
pip install requests openai beautifulsoup4 you-get
```

## 安装与部署

### 开发环境配置

1. **克隆项目**

```bash
git clone https://github.com/yourusername/teachpilot.git
cd teachpilot
```

2. **安装依赖**

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd backend
npm install
cd ..
```

3. **配置环境变量**

创建 `.env` 文件，添加以下配置：

```
# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=yourpassword
DB_NAME=teaching_assistant

# API密钥
XFYUN_APPID=your_xfyun_appid
XFYUN_SECRET_KEY=your_xfyun_secret_key
OPENAI_API_KEY=your_openai_api_key

# 其他配置
PYTHONIOENCODING=utf-8
JWT_SECRET=your_jwt_secret
```

4. **初始化数据库**

```bash
mysql -u root -p < backend/sql/init.sql
```

5. **启动开发服务器**

```bash
# 启动前端开发服务器
npm run dev

# 启动后端服务器
cd backend
npm run dev
```

### 生产环境部署

1. **构建前端**

```bash
npm run build
```

2. **配置Nginx**

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        root /path/to/teachpilot/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /uploads {
        proxy_pass http://localhost:3000;
    }
}
```

3. **启动后端服务**

```bash
cd backend
npm start
```

## 文件结构

```
teachpilot/
├── public/                 # 静态资源
│   └── note_videos/        # 视频笔记相关文件
├── src/                    # 前端源代码
│   ├── assets/             # 静态资源
│   ├── components/         # 组件
│   ├── router/             # 路由配置
│   ├── stores/             # Pinia状态管理
│   ├── views/              # 页面视图
│   └── App.vue             # 根组件
├── backend/                # 后端源代码
│   ├── config/             # 配置文件
│   ├── controllers/        # 控制器
│   ├── models/             # 数据模型
│   ├── routes/             # 路由
│   ├── scripts/            # Python脚本
│   ├── sql/                # SQL脚本
│   ├── uploads/            # 上传文件目录
│   └── app.js              # 后端入口文件
├── AdministratorConsole/   # 管理员控制台
└── vite.config.js          # Vite配置
```

## 常见问题

### 1. 视频转录失败

- 检查讯飞API密钥是否正确
- 确保FFmpeg正确安装并添加到PATH
- 检查网络连接
- 查看日志获取详细错误信息

### 2. 音频转录问题

- 确保录音格式支持（推荐WebM或MP3）
- 检查麦克风权限
- 如果使用模拟数据，确保相关配置正确

### 3. 数据库连接问题

- 检查数据库凭据是否正确
- 确保MySQL服务正在运行
- 检查数据库表是否已正确创建

## 联系方式

- 邮箱：<EMAIL>
- 电话：400-123-4567
- 地址：北京市海淀区中关村

## 许可证

© 2024 启智星 EduSpark. All rights reserved.
